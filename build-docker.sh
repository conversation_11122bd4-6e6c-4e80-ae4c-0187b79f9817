#!/bin/bash

# 汉邦高科前端Docker构建脚本
# 确保包含所有最新变更

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

echo "=========================================="
echo "汉邦高科前端Docker构建脚本"
echo "=========================================="
echo

# 检查Git状态
log_info "检查Git状态..."
if git status --porcelain | grep -q .; then
    log_warning "检测到未提交的变更:"
    git status --porcelain
    echo
    read -p "是否继续构建? (y/N): " continue_build
    if [[ ! $continue_build =~ ^[Yy]$ ]]; then
        log_info "构建已取消"
        exit 0
    fi
fi

# 显示最新的源代码修改时间
log_info "最新源代码修改时间:"
find src -name "*.tsx" -o -name "*.ts" -o -name "*.less" | grep -v ".umi" | xargs ls -lt | head -5
echo

# 本地构建测试
log_info "执行本地构建测试..."
npm run build
if [ $? -eq 0 ]; then
    log_success "本地构建成功"
    log_info "本地构建文件时间:"
    ls -la dist/ | head -3
else
    log_error "本地构建失败，请检查代码"
    exit 1
fi
echo

# 选择构建选项
echo "请选择构建选项:"
echo "1) 标准构建 (使用缓存)"
echo "2) 强制重新构建 (--no-cache)"
echo "3) 使用预构建的dist目录"
read -p "请输入选项 (1-3): " build_option

case $build_option in
    1)
        log_info "执行标准Docker构建..."
        docker build -t hbgk-frontend:latest .
        ;;
    2)
        log_info "执行强制重新构建..."
        docker build --no-cache -t hbgk-frontend:latest .
        ;;
    3)
        log_info "使用预构建的dist目录..."
        # 创建临时Dockerfile使用预构建的dist
        cat > Dockerfile.prebuilt << 'EOF'
FROM nginx:alpine

# Add China mirror for faster package installation
RUN sed -i 's/dl-cdn.alpinelinux.org/mirrors.aliyun.com/g' /etc/apk/repositories

# Install required packages
RUN apk add --no-cache \
    gettext \
    curl \
    tzdata

# Set timezone
ENV TZ=Asia/Shanghai
RUN ln -snf /usr/share/zoneinfo/$TZ /etc/localtime && echo $TZ > /etc/timezone

# Copy pre-built assets
COPY dist /usr/share/nginx/html

# Copy nginx configuration template
COPY nginx.conf.template /etc/nginx/templates/default.conf.template

# Copy startup script
COPY docker-entrypoint.sh /docker-entrypoint.sh
RUN chmod +x /docker-entrypoint.sh

# Set proper permissions
RUN chown -R nginx:nginx /usr/share/nginx/html && \
    chown -R nginx:nginx /var/cache/nginx && \
    chown -R nginx:nginx /var/log/nginx && \
    chown -R nginx:nginx /etc/nginx/conf.d && \
    chmod -R 755 /usr/share/nginx/html

# Expose port
EXPOSE 80

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=30s --retries=3 \
  CMD curl -f http://localhost/ || exit 1

# Use custom entrypoint
ENTRYPOINT ["/docker-entrypoint.sh"]
CMD ["nginx", "-g", "daemon off;"]
EOF
        docker build -f Dockerfile.prebuilt -t hbgk-frontend:latest .
        rm Dockerfile.prebuilt
        ;;
    *)
        log_error "无效的选项"
        exit 1
        ;;
esac

if [ $? -eq 0 ]; then
    log_success "Docker镜像构建成功"
    
    # 显示镜像信息
    log_info "镜像信息:"
    docker images hbgk-frontend:latest
    
    # 询问是否立即测试
    echo
    read -p "是否立即测试镜像? (y/N): " test_image
    if [[ $test_image =~ ^[Yy]$ ]]; then
        log_info "启动测试容器..."
        
        # 停止旧的测试容器
        docker stop hbgk-frontend-test 2>/dev/null || true
        docker rm hbgk-frontend-test 2>/dev/null || true
        
        # 启动新的测试容器
        docker run -d \
            --name hbgk-frontend-test \
            -p 3001:80 \
            -e API_BASE_URL=http://localhost:9380 \
            hbgk-frontend:latest
        
        if [ $? -eq 0 ]; then
            log_success "测试容器已启动"
            log_info "访问地址: http://localhost:3001"
            log_info "查看日志: docker logs -f hbgk-frontend-test"
            log_info "停止测试: docker stop hbgk-frontend-test && docker rm hbgk-frontend-test"
        else
            log_error "测试容器启动失败"
        fi
    fi
else
    log_error "Docker镜像构建失败"
    exit 1
fi

echo
log_success "构建脚本执行完成!"
