version: '3.8'

services:
  hbgk-frontend:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: hbgk-frontend
    ports:
      - "3000:80"
    environment:
      - NODE_ENV=production
      # 在Docker环境中使用服务名进行容器间通信
      - API_BASE_URL=http://hbgk-api:9380
      - API_KEY=${API_KEY:-hbgk-lhYmE2YTc2M2FiYjExZjA5YTdlMDAwYz}
      - TEXT_TO_IMAGE_URL=${TEXT_TO_IMAGE_URL:-http://*************:8090/gradio}
    networks:
      - hbgk-network
    restart: unless-stopped
    depends_on:
      - hbgk-api
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost/"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  hbgk-api:
    image: hbgk-api:latest
    container_name: hbgk-api
    ports:
      - "${API_PORT:-9380}:9380"
    environment:
      - PYTHONPATH=/hbgk
      # 后端服务监听所有接口以支持容器间通信
      - HOST_IP=0.0.0.0
      - HTTP_PORT=9380
    networks:
      - hbgk-network
    restart: unless-stopped
    volumes:
      - hbgk-data:/hbgk/data
      - hbgk-logs:/hbgk/logs

networks:
  hbgk-network:
    driver: bridge

volumes:
  hbgk-data:
    driver: local
  hbgk-logs:
    driver: local
