#
#  Copyright 2024 The InfiniFlow Authors. All Rights Reserved.
#
#  Licensed under the Apache License, Version 2.0 (the "License");
#  you may not use this file except in compliance with the License.
#  You may obtain a copy of the License at
#
#      http://www.apache.org/licenses/LICENSE-2.0
#
#  Unless required by applicable law or agreed to in writing, software
#  distributed under the License is distributed on an "AS IS" BASIS,
#  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
#  See the License for the specific language governing permissions and
#  limitations under the License.
#

"""
RAGFlow应用集成模块

提供将聊天机器人模块集成到RAGFlow主应用的功能。
"""

import logging
from flask import Flask

from .chatbot_api import chatbot_bp
from .config import ChatBotConfig


def init_chatbot_module(app: Flask) -> None:
    """初始化聊天机器人模块
    
    Args:
        app: Flask应用实例
    """
    try:
        # 配置日志
        logging.basicConfig(
            level=getattr(logging, ChatBotConfig.LOG_LEVEL),
            format=ChatBotConfig.LOG_FORMAT
        )
        
        logger = logging.getLogger(__name__)
        logger.info("Initializing ChatBot module...")
        
        # 注册API蓝图
        try:
            app.register_blueprint(chatbot_bp)
            logger.info(f"Registered ChatBot API blueprint at {ChatBotConfig.API_PREFIX}")

            # 验证路由注册
            routes = [rule.rule for rule in app.url_map.iter_rules() if rule.rule.startswith(ChatBotConfig.API_PREFIX)]
            logger.info(f"Registered ChatBot routes: {routes}")

        except Exception as e:
            logger.error(f"Failed to register ChatBot blueprint: {e}")
            raise
        
        # 配置CORS（如果启用）
        if ChatBotConfig.ENABLE_CORS:
            try:
                from flask_cors import CORS
                CORS(app, resources={
                    f"{ChatBotConfig.API_PREFIX}/*": {
                        "origins": "*",
                        "methods": ["GET", "POST", "PUT", "DELETE", "OPTIONS"],
                        "allow_headers": ["Content-Type", "Authorization"]
                    }
                })
                logger.info("CORS enabled for ChatBot API")
            except ImportError:
                logger.warning("flask-cors not installed, CORS not enabled")
        
        # 打印配置信息
        config_info = ChatBotConfig.get_all_config()
        logger.info(f"ChatBot module initialized with config: {config_info}")
        
        # 注册健康检查
        @app.route('/health/chatbot')
        def chatbot_health():
            """聊天机器人健康检查"""
            return {"status": "healthy", "module": "chatbot"}
        
        logger.info("ChatBot module initialization completed successfully")
        
    except Exception as e:
        logger.error(f"Failed to initialize ChatBot module: {str(e)}", exc_info=True)
        raise


def get_chatbot_info() -> dict:
    """获取聊天机器人模块信息"""
    return {
        "name": "RAGFlow ChatBot Module",
        "version": "1.0.0",
        "description": "Independent chatbot service for RAGFlow",
        "api_prefix": ChatBotConfig.API_PREFIX,
        "endpoints": [
            "POST /sessions - Create new chat session",
            "POST /sessions/{id}/messages - Send message",
            "GET /sessions/{id}/history - Get conversation history", 
            "DELETE /sessions/{id} - Clear session",
            "GET /stats - Get service statistics",
            "GET /health - Health check"
        ],
        "config": ChatBotConfig.get_all_config()
    }


# 用于在RAGFlow主应用中导入的便捷函数
def register_chatbot(app: Flask) -> None:
    """在RAGFlow主应用中注册聊天机器人模块
    
    使用示例:
    from ai.app_integration import register_chatbot
    register_chatbot(app)
    """
    init_chatbot_module(app)
