# RAGFlow Frontend Deployment Guide

This guide covers different deployment strategies for the RAGFlow frontend application.

## Prerequisites

- Node.js 18+
- Docker (for containerized deployment)
- Access to RAGFlow backend API
- Web server (Nginx, Apache) for production deployment

## Environment Configuration

### 1. Environment Variables

Create a `.env` file based on `.env.example`:

```bash
cp .env.example .env
```

Configure the following variables:

```env
# API Configuration
API_BASE_URL=http://your-api-server:9380
API_KEY=your-api-key

# Application Configuration
NODE_ENV=production
PORT=3000
```

### 2. API Server Configuration

Ensure your RAGFlow backend API is:
- Running and accessible
- Configured with CORS headers for your frontend domain
- Using the correct API key

## Deployment Methods

### Method 1: Docker Deployment (Recommended)

#### Quick Start with Docker Compose

1. **Clone and configure**
```bash
git clone <repository>
cd szweb
cp .env.example .env
# Edit .env with your configuration
```

2. **Start with Docker Compose**
```bash
docker-compose up -d
```

3. **Access the application**
```
http://localhost:3000
```

#### Custom Docker Build

1. **Build the image**
```bash
docker build -t ragflow-frontend .
```

2. **Run the container**
```bash
docker run -d \
  --name ragflow-frontend \
  -p 3000:80 \
  -e API_BASE_URL=http://your-api-server:9380 \
  -e API_KEY=your-api-key \
  ragflow-frontend
```

### Method 2: Manual Deployment

#### Development Server

1. **Install dependencies**
```bash
npm install
```

2. **Start development server**
```bash
npm run dev
```

3. **Access at http://localhost:8000**

#### Production Build

1. **Install dependencies**
```bash
npm ci --only=production
```

2. **Build the application**
```bash
npm run build
```

3. **Serve the built files**
```bash
# The built files are in the 'dist' directory
# Serve with any static file server
```

### Method 3: Nginx Deployment

#### 1. Build the Application

```bash
npm run build
```

#### 2. Configure Nginx

Create an Nginx configuration file:

```nginx
server {
    listen 80;
    server_name your-domain.com;
    root /path/to/szweb/dist;
    index index.html;

    # Handle client routing
    location / {
        try_files $uri $uri/ /index.html;
    }

    # API proxy
    location /v1/ {
        proxy_pass http://your-api-server:9380;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # CORS headers
        add_header Access-Control-Allow-Origin *;
        add_header Access-Control-Allow-Methods "GET, POST, PUT, DELETE, OPTIONS";
        add_header Access-Control-Allow-Headers "DNT,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Range,Authorization,X-API-KEY";
    }

    # Static assets caching
    location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
    }
}
```

#### 3. Restart Nginx

```bash
sudo nginx -t
sudo systemctl restart nginx
```

### Method 4: Apache Deployment

#### 1. Build the Application

```bash
npm run build
```

#### 2. Configure Apache

Create a virtual host configuration:

```apache
<VirtualHost *:80>
    ServerName your-domain.com
    DocumentRoot /path/to/szweb/dist
    
    # Handle client routing
    <Directory "/path/to/szweb/dist">
        Options Indexes FollowSymLinks
        AllowOverride All
        Require all granted
        
        # Fallback to index.html for client routing
        FallbackResource /index.html
    </Directory>
    
    # API proxy
    ProxyPreserveHost On
    ProxyPass /v1/ http://your-api-server:9380/v1/
    ProxyPassReverse /v1/ http://your-api-server:9380/v1/
    
    # Static assets caching
    <LocationMatch "\.(js|css|png|jpg|jpeg|gif|ico|svg)$">
        ExpiresActive On
        ExpiresDefault "access plus 1 year"
        Header set Cache-Control "public, immutable"
    </LocationMatch>
</VirtualHost>
```

#### 3. Enable modules and restart Apache

```bash
sudo a2enmod rewrite proxy proxy_http expires headers
sudo systemctl restart apache2
```

## SSL/HTTPS Configuration

### Using Let's Encrypt with Certbot

1. **Install Certbot**
```bash
sudo apt install certbot python3-certbot-nginx
```

2. **Obtain SSL certificate**
```bash
sudo certbot --nginx -d your-domain.com
```

3. **Auto-renewal**
```bash
sudo crontab -e
# Add: 0 12 * * * /usr/bin/certbot renew --quiet
```

## Performance Optimization

### 1. Enable Gzip Compression

Nginx:
```nginx
gzip on;
gzip_vary on;
gzip_min_length 1024;
gzip_types text/plain text/css text/xml text/javascript application/javascript application/xml+rss application/json;
```

Apache:
```apache
LoadModule deflate_module modules/mod_deflate.so
<Location />
    SetOutputFilter DEFLATE
    SetEnvIfNoCase Request_URI \
        \.(?:gif|jpe?g|png)$ no-gzip dont-vary
    SetEnvIfNoCase Request_URI \
        \.(?:exe|t?gz|zip|bz2|sit|rar)$ no-gzip dont-vary
</Location>
```

### 2. Browser Caching

Set appropriate cache headers for static assets:

```nginx
location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg)$ {
    expires 1y;
    add_header Cache-Control "public, immutable";
}
```

### 3. CDN Integration

Consider using a CDN for static assets:
- CloudFlare
- AWS CloudFront
- Azure CDN
- Google Cloud CDN

## Monitoring and Logging

### 1. Application Logs

Monitor application logs:
```bash
# Docker logs
docker logs ragflow-frontend

# Nginx logs
tail -f /var/log/nginx/access.log
tail -f /var/log/nginx/error.log
```

### 2. Health Checks

The Docker image includes health checks:
```bash
# Check container health
docker ps
```

### 3. Performance Monitoring

Consider integrating:
- Google Analytics
- Application performance monitoring (APM) tools
- Error tracking (Sentry, Bugsnag)

## Troubleshooting

### Common Issues

1. **API Connection Issues**
   - Check API_BASE_URL configuration
   - Verify API server is running
   - Check CORS configuration
   - Verify API key

2. **Build Failures**
   - Check Node.js version (18+ required)
   - Clear node_modules and reinstall
   - Check for TypeScript errors

3. **Routing Issues**
   - Ensure fallback to index.html is configured
   - Check web server configuration
   - Verify client-side routing setup

4. **Performance Issues**
   - Enable gzip compression
   - Configure proper caching headers
   - Optimize bundle size
   - Use CDN for static assets

### Debug Mode

Enable debug mode for troubleshooting:
```bash
NODE_ENV=development npm run dev
```

## Security Considerations

1. **HTTPS Only**
   - Always use HTTPS in production
   - Configure HSTS headers
   - Use secure cookies

2. **Content Security Policy**
   - Configure CSP headers
   - Restrict resource loading
   - Prevent XSS attacks

3. **API Security**
   - Use secure API keys
   - Implement rate limiting
   - Validate all inputs

4. **Regular Updates**
   - Keep dependencies updated
   - Monitor security advisories
   - Regular security audits

## Backup and Recovery

1. **Configuration Backup**
   - Backup .env files
   - Backup web server configurations
   - Document deployment procedures

2. **Disaster Recovery**
   - Maintain deployment scripts
   - Test recovery procedures
   - Monitor application health

## Support

For deployment issues:
1. Check the logs first
2. Verify configuration
3. Test API connectivity
4. Contact support team

---

This deployment guide should help you successfully deploy the RAGFlow frontend in various environments. Choose the method that best fits your infrastructure and requirements.
