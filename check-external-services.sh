#!/bin/bash

# 外部服务检查脚本
# 用于简化部署前检查外部数据库服务是否可用

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 读取配置文件
load_config() {
    local config_file="${1:-.env.simple}"
    
    if [ ! -f "$config_file" ]; then
        log_error "配置文件不存在: $config_file"
        return 1
    fi
    
    log_info "读取配置文件: $config_file"
    
    # 导出环境变量
    export $(grep -v '^#' "$config_file" | grep -v '^$' | xargs)
    
    log_success "配置文件加载完成"
}

# 检查MySQL连接
check_mysql() {
    log_info "检查MySQL连接..."
    
    local host="${MYSQL_HOST:-localhost}"
    local port="${MYSQL_PORT:-3306}"
    local user="${MYSQL_USER:-root}"
    local password="${MYSQL_PASSWORD:-}"
    
    if command -v mysql &> /dev/null; then
        if mysql -h "$host" -P "$port" -u "$user" -p"$password" -e "SELECT 1;" &> /dev/null; then
            log_success "MySQL连接成功 ($host:$port)"
            return 0
        else
            log_error "MySQL连接失败 ($host:$port)"
            return 1
        fi
    else
        log_warning "mysql客户端未安装，跳过MySQL连接测试"
        log_info "请确保MySQL服务在 $host:$port 上运行"
        return 0
    fi
}

# 检查Redis连接
check_redis() {
    log_info "检查Redis连接..."
    
    local host="${REDIS_HOST:-localhost}"
    local port="${REDIS_PORT:-6379}"
    local password="${REDIS_PASSWORD:-}"
    
    if command -v redis-cli &> /dev/null; then
        if [ -n "$password" ]; then
            if redis-cli -h "$host" -p "$port" -a "$password" ping &> /dev/null; then
                log_success "Redis连接成功 ($host:$port)"
                return 0
            else
                log_error "Redis连接失败 ($host:$port)"
                return 1
            fi
        else
            if redis-cli -h "$host" -p "$port" ping &> /dev/null; then
                log_success "Redis连接成功 ($host:$port)"
                return 0
            else
                log_error "Redis连接失败 ($host:$port)"
                return 1
            fi
        fi
    else
        log_warning "redis-cli未安装，跳过Redis连接测试"
        log_info "请确保Redis服务在 $host:$port 上运行"
        return 0
    fi
}

# 检查Elasticsearch连接
check_elasticsearch() {
    log_info "检查Elasticsearch连接..."
    
    local host="${ES_HOST:-localhost}"
    local port="${ES_PORT:-9200}"
    local user="${ES_USER:-elastic}"
    local password="${ELASTIC_PASSWORD:-}"
    
    local url="http://$host:$port"
    
    if command -v curl &> /dev/null; then
        if [ -n "$user" ] && [ -n "$password" ]; then
            if curl -s -u "$user:$password" "$url" &> /dev/null; then
                log_success "Elasticsearch连接成功 ($url)"
                return 0
            else
                log_error "Elasticsearch连接失败 ($url)"
                return 1
            fi
        else
            if curl -s "$url" &> /dev/null; then
                log_success "Elasticsearch连接成功 ($url)"
                return 0
            else
                log_error "Elasticsearch连接失败 ($url)"
                return 1
            fi
        fi
    else
        log_warning "curl未安装，跳过Elasticsearch连接测试"
        log_info "请确保Elasticsearch服务在 $url 上运行"
        return 0
    fi
}

# 检查MinIO连接
check_minio() {
    log_info "检查MinIO连接..."
    
    local host="${MINIO_HOST:-localhost}"
    local port="${MINIO_PORT:-9000}"
    
    local url="http://$host:$port/minio/health/live"
    
    if command -v curl &> /dev/null; then
        if curl -s "$url" &> /dev/null; then
            log_success "MinIO连接成功 ($host:$port)"
            return 0
        else
            log_error "MinIO连接失败 ($host:$port)"
            return 1
        fi
    else
        log_warning "curl未安装，跳过MinIO连接测试"
        log_info "请确保MinIO服务在 $host:$port 上运行"
        return 0
    fi
}

# 主函数
main() {
    echo "========================================"
    echo "汉邦高科 外部服务检查"
    echo "========================================"
    echo
    
    local config_file="${1:-.env.simple}"
    
    # 加载配置
    if ! load_config "$config_file"; then
        exit 1
    fi
    
    echo
    log_info "开始检查外部服务..."
    
    local failed_services=0
    
    # 检查各个服务
    if ! check_mysql; then
        ((failed_services++))
    fi
    
    echo
    if ! check_redis; then
        ((failed_services++))
    fi
    
    echo
    if ! check_elasticsearch; then
        ((failed_services++))
    fi
    
    echo
    if ! check_minio; then
        ((failed_services++))
    fi
    
    echo
    log_info "=== 检查结果 ==="
    
    if [ $failed_services -eq 0 ]; then
        log_success "所有外部服务检查通过！"
        log_info "现在可以运行简化部署: ./deploy.sh -m simple --robust"
    else
        log_error "有 $failed_services 个服务检查失败"
        log_warning "请确保所有外部服务正常运行后再进行部署"
        
        echo
        log_info "常见解决方案："
        echo "1. 检查服务是否已启动"
        echo "2. 检查防火墙设置"
        echo "3. 检查配置文件中的主机地址和端口"
        echo "4. 如果服务在其他机器上，确保网络连通性"
        
        exit 1
    fi
}

# 运行主函数
main "$@"
