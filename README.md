# RAGFlow 聊天机器人模块

这是一个独立的聊天机器人模块，为RAGFlow系统提供完整的聊天机器人功能。该模块直接与LLM模型交互，支持会话管理、内存组件和输出限制。

## 功能特性

- **独立服务**: 不依赖原系统chat服务，直接与LLM模型交互
- **会话管理**: 支持多用户、多会话的并发管理
- **内存组件**: 维护会话上下文，支持短期和长期记忆
- **消息处理**: 输入验证、输出格式化和敏感内容过滤
- **流式支持**: 支持流式和非流式响应
- **输出限制**: 单次聊天最大输出8000字符
- **多模型支持**: 优先使用vLLM部署的Deepseek或Qwen3系列模型

## 目录结构

```
ai/
├── __init__.py              # 模块初始化
├── chatbot_service.py       # 核心聊天机器人服务
├── memory_manager.py        # 内存管理器
├── message_processor.py     # 消息处理器
├── chatbot_api.py          # API接口层
├── config.py               # 配置管理
├── test_chatbot.py         # 单元测试
└── README.md               # 文档说明
```

## 快速开始

### 1. 安装依赖

确保已安装RAGFlow的基础依赖，聊天机器人模块使用相同的依赖环境。

### 2. 配置环境变量

```bash
# 内存管理配置
export CHATBOT_MAX_MESSAGES_PER_SESSION=50
export CHATBOT_MAX_SESSION_AGE_HOURS=24
export CHATBOT_MAX_TOTAL_SESSIONS=1000

# 消息处理配置
export CHATBOT_MAX_INPUT_LENGTH=4000
export CHATBOT_MAX_OUTPUT_LENGTH=8000
export CHATBOT_ENABLE_CONTENT_FILTER=true

# LLM生成配置
export CHATBOT_DEFAULT_TEMPERATURE=0.7
export CHATBOT_DEFAULT_TOP_P=0.9
export CHATBOT_DEFAULT_MAX_TOKENS=8000

# 系统提示配置
export CHATBOT_DEFAULT_SYSTEM_PROMPT="你是一个有用的AI助手。请用中文回答问题，保持友好和专业的语调。"
```

### 3. 集成到RAGFlow

在RAGFlow的主应用中注册聊天机器人API：

```python
from ai.chatbot_api import chatbot_bp

# 注册蓝图
app.register_blueprint(chatbot_bp)
```

### 4. 使用示例

#### 创建会话

```bash
curl -X POST http://localhost:9380/api/v1/chatbot/sessions \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{
    "user_id": "user123"
  }'
```

#### 发送消息

```bash
curl -X POST http://localhost:9380/api/v1/chatbot/sessions/SESSION_ID/messages \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{
    "message": "你好，请介绍一下自己",
    "stream": false
  }'
```

#### 流式聊天

```bash
curl -X POST http://localhost:9380/api/v1/chatbot/sessions/SESSION_ID/messages \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{
    "message": "请详细解释机器学习的基本概念",
    "stream": true
  }'
```

#### 获取会话历史

```bash
curl -X GET http://localhost:9380/api/v1/chatbot/sessions/SESSION_ID/history \
  -H "Authorization: Bearer YOUR_TOKEN"
```

## API 接口

### 创建会话
- **POST** `/api/v1/chatbot/sessions`
- 创建新的聊天会话

### 发送消息
- **POST** `/api/v1/chatbot/sessions/{session_id}/messages`
- 向指定会话发送消息，支持流式和非流式响应

### 获取历史记录
- **GET** `/api/v1/chatbot/sessions/{session_id}/history`
- 获取会话的历史消息记录

### 清空会话
- **DELETE** `/api/v1/chatbot/sessions/{session_id}`
- 清空指定会话的所有消息

### 服务统计
- **GET** `/api/v1/chatbot/stats`
- 获取服务运行统计信息

### 健康检查
- **GET** `/api/v1/chatbot/health`
- 服务健康状态检查

## 核心组件

### MemoryManager (内存管理器)

负责管理聊天会话的上下文记忆：

- **短期记忆**: 当前会话的消息历史
- **长期记忆**: 跨会话的用户偏好和重要信息
- **自动清理**: 基于时间和大小的记忆清理

### MessageProcessor (消息处理器)

负责处理用户输入和AI响应：

- **输入验证**: 长度检查、内容清理、敏感词过滤
- **输出格式化**: 长度限制、格式优化、错误处理
- **内容安全**: 敏感信息检测和过滤

### ChatBotService (聊天机器人服务)

核心服务组件：

- **会话管理**: 创建、维护和清理聊天会话
- **LLM交互**: 直接与RAGFlow的LLM模型交互
- **流式支持**: 支持实时流式响应
- **错误处理**: 完善的错误处理和恢复机制

## 配置说明

### 内存管理配置

- `MAX_MESSAGES_PER_SESSION`: 每个会话最大消息数量 (默认: 50)
- `MAX_SESSION_AGE_HOURS`: 会话最大存活时间 (默认: 24小时)
- `MAX_TOTAL_SESSIONS`: 系统最大会话总数 (默认: 1000)

### 消息处理配置

- `MAX_INPUT_LENGTH`: 用户输入最大长度 (默认: 4000字符)
- `MAX_OUTPUT_LENGTH`: AI输出最大长度 (默认: 8000字符)
- `ENABLE_CONTENT_FILTER`: 是否启用内容过滤 (默认: true)

### LLM生成配置

- `DEFAULT_TEMPERATURE`: 生成温度 (默认: 0.7)
- `DEFAULT_TOP_P`: Top-p采样 (默认: 0.9)
- `DEFAULT_MAX_TOKENS`: 最大生成token数 (默认: 8000)

## 测试

运行单元测试：

```bash
cd ai
python -m pytest test_chatbot.py -v
```

或者直接运行测试文件：

```bash
python test_chatbot.py
```

## 性能优化

1. **内存管理**: 定期清理过期会话，控制内存使用
2. **消息缓存**: 对频繁访问的会话进行缓存优化
3. **异步处理**: 支持异步消息处理，提高并发性能
4. **连接池**: 复用LLM模型连接，减少初始化开销

## 安全考虑

1. **输入验证**: 严格的输入验证和清理
2. **敏感信息**: 自动检测和过滤敏感信息
3. **访问控制**: 基于用户和租户的访问控制
4. **日志记录**: 完整的操作日志和审计跟踪

## 故障排除

### 常见问题

1. **LLM模型无法访问**: 检查租户LLM配置和API密钥
2. **会话创建失败**: 检查用户ID和租户ID是否有效
3. **消息发送超时**: 检查LLM模型响应时间和网络连接
4. **内存使用过高**: 调整会话清理策略和消息限制

### 日志查看

```bash
# 查看聊天机器人相关日志
grep "chatbot" /path/to/ragflow/logs/ragflow_server.log
```

## 贡献指南

1. 遵循Python PEP 8编码规范
2. 使用类型提示和文档字符串
3. 编写相应的单元测试
4. 更新相关文档

## 许可证

本模块遵循Apache License 2.0许可证。
