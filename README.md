# 汉邦高科 Frontend

A modern React-based frontend for the 汉邦高科 Knowledge Base System, built with TypeScript, Ant Design, and UmiJS.

## Features

- 🔐 **Secure Authentication** - RSA encrypted login with JWT token management
- 📚 **Knowledge Base Management** - Create, edit, and manage knowledge repositories
- 📄 **Document Management** - Upload and organize documents within knowledge bases
- 💬 **AI Conversations** - Chat with AI assistants powered by your knowledge base
- 🎨 **Modern UI** - Clean, responsive design with Ant Design components
- 🚀 **High Performance** - Built with React 18 and modern build tools
- 🐳 **Docker Ready** - Containerized deployment with Docker and docker-compose

## Tech Stack

- **Framework**: React 18 + TypeScript
- **Build Tool**: UmiJS 4
- **UI Library**: Ant Design 5
- **State Management**: TanStack Query (React Query)
- **HTTP Client**: umi-request
- **Styling**: Less + CSS Modules
- **Authentication**: JWT + RSA encryption
- **Deployment**: <PERSON><PERSON> + Nginx

## Quick Start

### Prerequisites

- Node.js 18+ 
- npm or yarn
- Docker (for containerized deployment)

### Development

1. **Clone and install dependencies**
```bash
cd szweb
npm install
```

2. **Configure environment**
```bash
cp .env.example .env
# Edit .env with your API configuration
```

3. **Start development server**
```bash
npm run dev
```

The application will be available at:
- Local: `http://localhost:8000`
- Network: `http://0.0.0.0:8000` (accessible from other devices on the network)

### Production Build

```bash
npm run build
```

### Docker Deployment

1. **Build and run with docker-compose**
```bash
docker-compose up -d
```

2. **Or build Docker image manually**
```bash
docker build -t hbgk-frontend .
docker run -p 3000:80 hbgk-frontend
```

## Configuration

### Environment Variables

| Variable | Description | Default |
|----------|-------------|---------|
| `API_BASE_URL` | Backend API base URL | `http://***************:9380` |
| `API_KEY` | API authentication key | `hbgk-lhYmE2YTc2M2FiYjExZjA5YTdlMDAwYz` |
| `NODE_ENV` | Environment mode | `development` |
| `PORT` | Development server port | `8000` |
| `HOST` | Development server host | `0.0.0.0` |

### API Configuration

The frontend communicates with the 汉邦高科 backend API. Make sure the backend is running and accessible at the configured `API_BASE_URL`.

Key API endpoints:
- `POST /v1/user/login` - User authentication
- `POST /v1/kb/list` - Knowledge base listing
- `POST /v1/kb/create` - Create knowledge base
- `GET /v1/user/info` - User information

## Project Structure

```
szweb/
├── src/
│   ├── components/          # Reusable UI components
│   ├── constants/           # Application constants
│   ├── hooks/              # Custom React hooks
│   ├── interfaces/         # TypeScript interfaces
│   ├── pages/              # Page components
│   │   ├── login/          # Login page
│   │   ├── dashboard/      # Dashboard page
│   │   └── knowledge/      # Knowledge base page
│   ├── services/           # API service layer
│   ├── utils/              # Utility functions
│   ├── wrappers/           # Route wrappers (auth, etc.)
│   ├── app.tsx             # App configuration
│   └── global.less         # Global styles
├── public/                 # Static assets
├── Dockerfile              # Docker configuration
├── docker-compose.yml      # Docker compose configuration
├── nginx.conf              # Nginx configuration
└── package.json            # Dependencies and scripts
```

## Key Features

### Authentication System

- RSA encryption for password security
- JWT token-based authentication
- Automatic token refresh and management
- Protected routes with authentication wrapper

### Knowledge Base Management

- Create and manage multiple knowledge bases
- Search and filter functionality
- Infinite scroll for large datasets
- Real-time statistics and metrics

### Responsive Design

- Mobile-first responsive design
- Optimized for desktop, tablet, and mobile
- Modern UI with smooth animations
- Dark/light theme support (coming soon)

## Development

### Available Scripts

- `npm run dev` - Start development server
- `npm run build` - Build for production
- `npm run lint` - Run ESLint
- `npm run lint:fix` - Fix ESLint issues
- `npm run prettier` - Format code with Prettier
- `npm test` - Run tests

### Code Style

This project uses:
- ESLint for code linting
- Prettier for code formatting
- TypeScript for type safety
- Conventional commit messages

### Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Run tests and linting
5. Submit a pull request

## Deployment

### Docker Deployment

The application includes Docker configuration for easy deployment:

1. **Development**
```bash
docker-compose -f docker-compose.dev.yml up
```

2. **Production**
```bash
docker-compose up -d
```

### Manual Deployment

1. Build the application
```bash
npm run build
```

2. Serve the `dist` folder with any static file server (Nginx, Apache, etc.)

## API Integration

The frontend integrates with the HanBangGaoKe backend API. Key integration points:

- **Authentication**: RSA-encrypted login with JWT tokens
- **Knowledge Bases**: CRUD operations for knowledge base management
- **Documents**: File upload and document management
- **Conversations**: AI chat functionality
- **User Management**: Profile and settings management

## Browser Support

- Chrome 90+
- Firefox 88+
- Safari 14+
- Edge 90+

## License

Apache License 2.0

## Support

For issues and questions:
- Create an issue in the repository
- Check the documentation
- Contact the development team

---

Built with ❤️ by the 汉邦高科 Team
