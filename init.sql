-- 汉邦高科 MySQL 初始化脚本
-- 创建数据库和用户权限配置

-- 创建数据库（如果不存在）
CREATE DATABASE IF NOT EXISTS rag_flow CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- 使用数据库
USE rag_flow;

-- 更新root用户权限（允许从任何主机连接）
UPDATE mysql.user SET host='%' WHERE user='root' AND host='localhost';

-- 创建专用的应用用户
CREATE USER IF NOT EXISTS 'ragflow'@'%' IDENTIFIED BY 'infini_rag_flow';
GRANT ALL PRIVILEGES ON rag_flow.* TO 'ragflow'@'%';

-- 允许从Docker网络连接
GRANT ALL PRIVILEGES ON *.* TO 'root'@'172.%.%.%' WITH GRANT OPTION;
GRANT ALL PRIVILEGES ON rag_flow.* TO 'ragflow'@'172.%.%.%';

-- 刷新权限
FLUSH PRIVILEGES;

-- 创建基础表结构（如果需要的话）
-- 这里可以添加应用需要的初始表结构

-- 显示创建的用户
SELECT User, Host FROM mysql.user WHERE User IN ('root', 'ragflow');
