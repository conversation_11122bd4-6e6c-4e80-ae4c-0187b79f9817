# AI Reading Module Dependencies
# 
# 这个文件列出了AI阅读模块的特定依赖项

# Gradio客户端用于与Mineru API交互
gradio_client==1.10.4

# HTTP请求库
requests>=2.28.0

# 环境变量管理
python-dotenv>=0.19.0

# 数据处理
pydantic>=1.10.0

# 注意：以下依赖项应该已经在RAGFlow主项目中安装
# - peewee (用于数据库ORM)
# - logging (Python标准库)
# - json (Python标准库)
# - os (Python标准库)
# - subprocess (Python标准库)
# - tempfile (Python标准库)
# - uuid (Python标准库)
# - datetime (Python标准库)
# - typing (Python标准库)
