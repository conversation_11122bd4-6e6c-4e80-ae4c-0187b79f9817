# RAGFlow ChatBot Module Dependencies
# 
# 这个文件列出了聊天机器人模块的特定依赖项
# 大部分依赖项已经包含在RAGFlow的主要依赖中

# Web框架和API
flask>=2.3.0
flask-login>=0.6.0
flask-cors>=4.0.0

# 数据处理
pydantic>=1.10.0

# 测试框架
pytest>=7.0.0
pytest-cov>=4.0.0

# 类型检查
mypy>=1.0.0

# 代码格式化
black>=23.0.0
isort>=5.12.0

# 注意：以下依赖项应该已经在RAGFlow主项目中安装
# - openai (用于LLM交互)
# - dashscope (用于通义千问)
# - zhipuai (用于智谱AI)
# - requests (用于HTTP请求)
# - python-dotenv (用于环境变量)
# - logging (Python标准库)
# - json (Python标准库)
# - time (Python标准库)
# - uuid (Python标准库)
# - re (Python标准库)
# - typing (Python标准库)
# - dataclasses (Python标准库)
# - collections (Python标准库)
# - os (Python标准库)
# - abc (Python标准库)
