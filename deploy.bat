@echo off
setlocal enabledelayedexpansion

echo ==========================================
echo RAGFlow 前端部署脚本
echo ==========================================
echo.

:: 检查 Node.js
echo 检查 Node.js 环境...
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo 错误: 未找到 Node.js，请先安装 Node.js
    pause
    exit /b 1
)

:: 检查 npm
echo 检查 npm 环境...
npm --version >nul 2>&1
if %errorlevel% neq 0 (
    echo 错误: 未找到 npm
    pause
    exit /b 1
)

:: 检查 Docker (可选)
docker --version >nul 2>&1
if %errorlevel% neq 0 (
    set DOCKER_AVAILABLE=false
    echo 警告: 未找到 Docker，Docker 部署选项将不可用
) else (
    set DOCKER_AVAILABLE=true
)

:: 显示版本信息
echo.
echo 环境信息:
node --version
npm --version
if "%DOCKER_AVAILABLE%"=="true" (
    docker --version
)
echo.

:: 选择部署模式
echo 请选择部署模式:
echo 1) 开发环境 (Development)
echo 2) 生产环境 (Production)
if "%DOCKER_AVAILABLE%"=="true" (
    echo 3) Docker 部署
    echo 4) Docker Compose 部署
)
set /p deploy_mode="请输入选项: "

if "%deploy_mode%"=="1" (
    echo.
    echo 启动开发环境...
    
    :: 检查依赖
    if not exist "node_modules" (
        echo 安装依赖包...
        npm install
        if %errorlevel% neq 0 (
            echo 错误: 依赖安装失败
            pause
            exit /b 1
        )
    )
    
    :: 启动开发服务器
    echo 启动开发服务器...
    npm run dev
    
) else if "%deploy_mode%"=="2" (
    echo.
    echo 构建生产环境...
    
    :: 安装依赖
    echo 安装依赖包...
    npm ci --only=production
    if %errorlevel% neq 0 (
        echo 错误: 依赖安装失败
        pause
        exit /b 1
    )
    
    :: 构建项目
    echo 构建项目...
    npm run build
    if %errorlevel% neq 0 (
        echo 错误: 项目构建失败
        pause
        exit /b 1
    )
    
    :: 预览构建结果
    echo 预览构建结果...
    npm run preview
    
) else if "%deploy_mode%"=="3" (
    if "%DOCKER_AVAILABLE%"=="false" (
        echo 错误: Docker 不可用
        pause
        exit /b 1
    )
    
    echo.
    echo Docker 部署...
    
    :: 构建 Docker 镜像
    echo 构建 Docker 镜像...
    docker build -t ragflow-frontend:latest .
    if %errorlevel% neq 0 (
        echo 错误: Docker 镜像构建失败
        pause
        exit /b 1
    )
    
    :: 停止并删除旧容器
    echo 停止旧容器...
    docker stop ragflow-frontend 2>nul
    docker rm ragflow-frontend 2>nul
    
    :: 运行新容器
    echo 启动新容器...
    docker run -d --name ragflow-frontend -p 3000:80 --restart unless-stopped ragflow-frontend:latest
    if %errorlevel% neq 0 (
        echo 错误: 容器启动失败
        pause
        exit /b 1
    )
    
    echo.
    echo 成功: Docker 容器已启动，访问地址: http://localhost:3000
    
) else if "%deploy_mode%"=="4" (
    if "%DOCKER_AVAILABLE%"=="false" (
        echo 错误: Docker 不可用
        pause
        exit /b 1
    )
    
    echo.
    echo Docker Compose 部署...
    
    :: 停止旧服务
    echo 停止旧服务...
    docker-compose down 2>nul
    
    :: 构建并启动服务
    echo 构建并启动服务...
    docker-compose up --build -d
    if %errorlevel% neq 0 (
        echo 错误: Docker Compose 启动失败
        pause
        exit /b 1
    )
    
    :: 显示服务状态
    echo.
    echo 服务状态:
    docker-compose ps
    
    echo.
    echo 成功: Docker Compose 服务已启动，访问地址: http://localhost:3000
    
) else (
    echo 错误: 无效的选项
    pause
    exit /b 1
)

echo.
echo 部署完成!

:: 显示有用的命令
echo.
echo 有用的命令:
if "%deploy_mode%"=="3" (
    echo 查看容器日志: docker logs -f ragflow-frontend
    echo 停止容器: docker stop ragflow-frontend
    echo 重启容器: docker restart ragflow-frontend
) else if "%deploy_mode%"=="4" (
    echo 查看服务日志: docker-compose logs -f
    echo 停止服务: docker-compose down
    echo 重启服务: docker-compose restart
)

echo.
echo 如有问题，请查看日志或联系开发团队
pause
