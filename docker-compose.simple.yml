
version: '3.8'

# 简化版Docker Compose配置
# 仅包含前端和后端服务，适用于已有外部数据库的环境

services:
  # 前端服务
  hbgk-frontend:
    image: hbgk-frontend:${IMAGE_TAG:-latest}
    container_name: hbgk-frontend
    env_file: .env
    ports:
      - "${FRONTEND_PORT:-3000}:80"
    environment:
      - NODE_ENV=production
      # 使用容器间通信
      - API_BASE_URL=http://hbgk-backend:9380
      - API_KEY=${API_KEY:-hbgk-lhYmE2YTc2M2FiYjExZjA5YTdlMDAwYz}
      - TEXT_TO_IMAGE_URL=${TEXT_TO_IMAGE_URL:-http://*************:8090}
    networks:
      - hbgk-network
    restart: on-failure
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost/"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # 后端服务
  hbgk-backend:
    image: hbgk-api:${IMAGE_TAG:-latest}
    container_name: hbgk-backend
    env_file: .env
    ports:
      - "${BACKEND_PORT:-9380}:9380"
    environment:
      - PYTHONPATH=/ragflow
      - HOST_IP=0.0.0.0
      - HTTP_PORT=9380
      # 外部数据库配置 - 请根据实际情况修改
      - MYSQL_HOST=${MYSQL_HOST:-localhost}
      - MYSQL_USER=${MYSQL_USER:-root}
      - MYSQL_PASSWORD=${MYSQL_PASSWORD:-infini_rag_flow}
      - MYSQL_DBNAME=${MYSQL_DBNAME:-rag_flow}
      # 外部Redis配置
      - REDIS_HOST=${REDIS_HOST:-localhost}
      - REDIS_PASSWORD=${REDIS_PASSWORD:-infini_rag_flow}
      # 外部MinIO配置
      - MINIO_HOST=${MINIO_HOST:-localhost}
      - MINIO_USER=${MINIO_USER:-rag_flow}
      - MINIO_PASSWORD=${MINIO_PASSWORD:-infini_rag_flow}
      # 外部Elasticsearch配置
      - ES_HOST=${ES_HOST:-localhost}
      - ES_USER=${ES_USER:-elastic}
      - ELASTIC_PASSWORD=${ELASTIC_PASSWORD:-infini_rag_flow}
    networks:
      - hbgk-network
    restart: on-failure
    volumes:
      - hbgk-data:/ragflow/data
      - hbgk-logs:/ragflow/logs
      - hbgk-temp:/ragflow/temp
    # 如果需要访问宿主机上的服务，可以使用host网络模式
    # network_mode: "host"

  # 基础服务 - 从 docker-compose-base.yml 复制
  es01:
    container_name: ragflow-es-01
    profiles:
      - elasticsearch
    image: elasticsearch:${STACK_VERSION}
    volumes:
      - esdata01:/usr/share/elasticsearch/data
    ports:
      - ${ES_PORT}:9200
    env_file: .env
    environment:
      - node.name=es01
      - ELASTIC_PASSWORD=${ELASTIC_PASSWORD}
      - bootstrap.memory_lock=false
      - discovery.type=single-node
      - xpack.security.enabled=true
      - xpack.security.http.ssl.enabled=false
      - xpack.security.transport.ssl.enabled=false
      - cluster.routing.allocation.disk.watermark.low=5gb
      - cluster.routing.allocation.disk.watermark.high=3gb
      - cluster.routing.allocation.disk.watermark.flood_stage=2gb
      - TZ=${TIMEZONE}
    mem_limit: ${MEM_LIMIT}
    ulimits:
      memlock:
        soft: -1
        hard: -1
    healthcheck:
      test: ["CMD-SHELL", "curl http://localhost:9200"]
      interval: 10s
      timeout: 10s
      retries: 120
    networks:
      - hbgk-network
    restart: on-failure

  mysql:
    image: mysql:8.0.39
    container_name: ragflow-mysql
    env_file: .env
    environment:
      - MYSQL_ROOT_PASSWORD=${MYSQL_PASSWORD}
      - TZ=${TIMEZONE}
    command:
      --max_connections=1000
      --character-set-server=utf8mb4
      --collation-server=utf8mb4_unicode_ci
      --default-authentication-plugin=mysql_native_password
      --tls_version="TLSv1.2,TLSv1.3"
      --init-file /data/application/init.sql
      --binlog_expire_logs_seconds=604800
    ports:
      - ${MYSQL_PORT}:3306
    volumes:
      - mysql_data:/var/lib/mysql
      - ./init.sql:/data/application/init.sql
    networks:
      - hbgk-network
    healthcheck:
      test: ["CMD", "mysqladmin" ,"ping", "-uroot", "-p${MYSQL_PASSWORD}"]
      interval: 10s
      timeout: 10s
      retries: 3
    restart: on-failure

  minio:
    image: quay.io/minio/minio:RELEASE.2023-12-20T01-00-02Z
    container_name: ragflow-minio
    command: server --console-address ":9001" /data
    ports:
      - ${MINIO_PORT}:9000
      - ${MINIO_CONSOLE_PORT}:9001
    env_file: .env
    environment:
      - MINIO_ROOT_USER=${MINIO_USER}
      - MINIO_ROOT_PASSWORD=${MINIO_PASSWORD}
      - TZ=${TIMEZONE}
    volumes:
      - minio_data:/data
    networks:
      - hbgk-network
    restart: on-failure

  redis:
    image: valkey/valkey:8
    container_name: ragflow-redis
    command: redis-server --requirepass ${REDIS_PASSWORD} --maxmemory 128mb --maxmemory-policy allkeys-lru
    env_file: .env
    ports:
      - ${REDIS_PORT}:6379
    volumes:
      - redis_data:/data
    networks:
      - hbgk-network
    restart: on-failure

volumes:
  esdata01:
    driver: local
  osdata01:
    driver: local
  infinity_data:
    driver: local
  mysql_data:
    driver: local
  minio_data:
    driver: local
  redis_data:
    driver: local
  hbgk-data:
    driver: local
    name: hbgk-data
  hbgk-logs:
    driver: local
    name: hbgk-logs
  hbgk-temp:
    driver: local
    name: hbgk-temp

networks:
  hbgk-network:
    driver: bridge
    name: hbgk-network
