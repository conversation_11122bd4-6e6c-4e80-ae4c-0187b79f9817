{"compilerOptions": {"target": "esnext", "lib": ["dom", "dom.iterable", "es6"], "allowJs": true, "skipLibCheck": true, "esModuleInterop": true, "allowSyntheticDefaultImports": true, "strict": true, "forceConsistentCasingInFileNames": true, "moduleResolution": "node", "resolveJsonModule": true, "isolatedModules": true, "noEmit": true, "jsx": "react-jsx", "baseUrl": ".", "paths": {"@/*": ["src/*"]}, "declaration": false, "declarationMap": false, "experimentalDecorators": true, "emitDecoratorMetadata": true}, "include": ["src/**/*", "typings.d.ts"], "exclude": ["node_modules", "dist", "build"]}