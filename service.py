#
#  AI Reading Service - AI阅读文件处理服务
#  负责文件转换、解析  API调用、内容摘要生成等
#

import os
import sys
import logging
import requests
import zipfile
import tempfile
import subprocess
from datetime import datetime
from typing import Optional, Dict, Any
from dotenv import load_dotenv

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, project_root)

# 加载环境变量
load_dotenv(os.path.join(project_root, 'sz', '.env'))

try:
    from api.db.db_models import AIReadingFile, AIReadingConversation
    from api.utils.file_utils import get_project_base_directory
except ImportError:
    # 如果无法导入，使用备用实现
    def get_project_base_directory():
        return project_root
    
    class AIReadingFile:
        def __init__(self, **kwargs):
            for key, value in kwargs.items():
                setattr(self, key, value)
        def save(self): pass
        @classmethod
        def get(cls, *args, **kwargs): pass

class AIReadingService:
    """AI阅读服务类"""
    
    def __init__(self):
        # 从环境变量获取解析 服务器配置
        self.mineru_server_url = os.getenv('MINERU_SERVER_URL', 'http://192.168.1.138:7860')
        self.mineru_api_key = os.getenv('MINERU_API_KEY', '')
        self.temp_dir = os.path.join(get_project_base_directory(), 'temp', 'ai_reading')
        
        # Ensure temp directory exists
        os.makedirs(self.temp_dir, exist_ok=True)

    def ensure_db_connection(self):
        """确保数据库连接正常"""
        try:
            from api.db.db_models import DB
            if DB.is_closed():
                DB.connect()
            # 测试连接
            DB.execute_sql("SELECT 1")
        except Exception as e:
            logging.warning(f"数据库连接异常，尝试重连: {e}")
            try:
                from api.db.db_models import DB
                if not DB.is_closed():
                    DB.close()
                DB.connect()
            except Exception as reconnect_error:
                logging.error(f"数据库重连失败: {reconnect_error}")
                raise

    def convert_to_pdf(self, file_path: str, file_type: str) -> Optional[str]:
        """
        将Word/PPT文件转换为PDF
        
        Args:
            file_path: 原始文件路径
            file_type: 文件类型 (docx, pptx, doc, ppt)
            
        Returns:
            转换后的PDF文件路径，失败返回None
        """
        try:
            if file_type.lower() == 'pdf':
                return file_path  # 已经是PDF，直接返回
            
            # 生成PDF文件路径
            base_name = os.path.splitext(os.path.basename(file_path))[0]
            pdf_path = os.path.join(self.temp_dir, f"{base_name}.pdf")
            
            # 使用LibreOffice进行转换
            cmd = [
                'libreoffice',
                '--headless',
                '--convert-to', 'pdf',
                '--outdir', self.temp_dir,
                file_path
            ]

            # 设置环境变量以确保LibreOffice能找到所需的库
            env = os.environ.copy()
            env['LD_LIBRARY_PATH'] = '/usr/lib/libreoffice/program:' + env.get('LD_LIBRARY_PATH', '')

            logging.info(f"开始转换文件: {file_path} -> {pdf_path}")
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=60, env=env)

            if result.returncode == 0 and os.path.exists(pdf_path):
                logging.info(f"文件转换成功: {pdf_path}")
                # 如果有stderr输出但转换成功，记录为警告而不是错误
                if result.stderr and result.stderr.strip():
                    logging.warning(f"转换过程中的警告信息: {result.stderr}")
                return pdf_path
            else:
                logging.error(f"文件转换失败: {result.stderr}")
                return None
                
        except subprocess.TimeoutExpired:
            logging.error("文件转换超时")
            return None
        except Exception as e:
            logging.exception(f"文件转换异常: {str(e)}")
            return None
    
    def upload_file_to_cdn(self, file_path: str) -> Optional[str]:
        """
        上传文件到本地存储
        由于使用本地解析 服务器，不需要上传到外部CDN
        """
        # 使用本地解析 服务器，直接返回本地文件路径
        if os.path.exists(file_path):
            return file_path
        else:
            logging.error(f"文件不存在: {file_path}")
            return None
    
    def call_mineru_api(self, pdf_path: str) -> Optional[str]:
        """
        调用解析 API进行PDF解析

        Args:
            pdf_path: PDF文件路径

        Returns:
            解析后的Markdown内容，失败返回None
        """
        try:
            from gradio_client import Client, handle_file

            # 连接到配置的解析 服务器
            logging.info(f"连接到解析 服务器: {self.mineru_server_url}")
            client = Client(self.mineru_server_url)

            # 调用解析 API
            result = client.predict(
                file_path=handle_file(pdf_path),
                end_pages=500,
                is_ocr=False,
                formula_enable=True,
                table_enable=True,
                language="ch",
                backend="pipeline",
                url="http://localhost:30000",
                api_name="/to_markdown"
            )

            # 返回结果是一个包含4个元素的元组
            # [0] str - Markdown 文件内容
            # [1] str - 暂时不处理
            # [2] filepath - 暂时不处理
            # [3] filepath - 暂时不处理

            if result and len(result) > 0:
                markdown_content = result[0]
                logging.info(f"解析  API成功返回内容，长度: {len(markdown_content)}")
                return markdown_content
            else:
                logging.error("解析  API返回空结果")
                return None

        except Exception as e:
            logging.exception(f"调用解析 API异常: {str(e)}")
            return None
    
    def check_mineru_status(self, task_id: str) -> Optional[Dict[str, Any]]:
        """
        检查解析 任务状态
        由于使用本地解析 服务器，直接返回完成状态

        Args:
            task_id: 任务ID

        Returns:
            状态信息，失败返回None
        """
        try:
            # 使用本地解析 服务器，处理是同步的，直接返回完成状态
            return {
                'task_id': task_id,
                'state': 'done',  # pending, running, done, failed
                'result_path': None  # 本地处理不需要下载
            }

        except Exception as e:
            logging.exception(f"查询解析 状态异常: {str(e)}")
            return None
    
    def download_and_extract_result(self, result_data: str, file_id: str) -> Optional[str]:
        """
        保存解析 处理结果

        Args:
            result_data: Markdown内容数据
            file_id: 文件ID

        Returns:
            Markdown文件路径，失败返回None
        """
        try:
            # 直接保存Markdown内容到文件
            markdown_path = os.path.join(self.temp_dir, f"{file_id}.md")

            with open(markdown_path, 'w', encoding='utf-8') as f:
                f.write(result_data)

            logging.info(f"Markdown文件已保存: {markdown_path}")
            return markdown_path
            
            # 查找Markdown文件
            for root, dirs, files in os.walk(extract_dir):
                for file in files:
                    if file.endswith('.md'):
                        md_path = os.path.join(root, file)
                        # 移动到最终位置
                        final_md_path = os.path.join(self.temp_dir, f"{file_id}.md")
                        os.rename(md_path, final_md_path)
                        
                        # 清理临时文件
                        os.remove(zip_path)
                        import shutil
                        shutil.rmtree(extract_dir)
                        
                        return final_md_path
            
            logging.error("未找到Markdown文件")
            return None
            
        except Exception as e:
            logging.exception(f"下载解压结果异常: {str(e)}")
            return None

    def process_file(self, file_id: str, user_id: str = None, tenant_id: str = None) -> bool:
        """
        处理上传的文件

        Args:
            file_id: 文件ID
            user_id: 用户ID（可选，用于后台线程中的认证）
            tenant_id: 租户ID（可选，用于后台线程中的认证）

        Returns:
            处理是否成功
        """
        try:
            # 确保数据库连接正常
            self.ensure_db_connection()

            # 获取文件记录
            ai_file = AIReadingFile.get(AIReadingFile.id == file_id)

            # 1. 转换为PDF（如果需要）
            ai_file.processing_status = 'converting_to_pdf'
            ai_file.processing_progress = 20.0
            ai_file.processing_message = '正在转换文件格式'
            ai_file.save()

            pdf_path = self.convert_to_pdf(ai_file.original_file_path, ai_file.file_type)
            if not pdf_path:
                ai_file.processing_status = 'failed'
                ai_file.processing_message = '文件格式转换失败'
                ai_file.save()
                return False

            ai_file.pdf_file_path = pdf_path
            ai_file.save()

            # 2. 调用解析  API
            ai_file.processing_status = 'calling_parser'
            ai_file.processing_progress = 40.0
            ai_file.processing_message = '正在调用解析  API解析文档'
            ai_file.save()

            markdown_content = self.call_mineru_api(pdf_path)
            if not markdown_content:
                ai_file.processing_status = 'failed'
                ai_file.processing_message = '解析  API调用失败'
                ai_file.save()
                return False

            # 3. 保存Markdown文件
            # 重新连接数据库以避免连接超时
            self.ensure_db_connection()
            ai_file = AIReadingFile.get(AIReadingFile.id == file_id)
            ai_file.processing_status = 'saving_results'
            ai_file.processing_progress = 60.0
            ai_file.processing_message = '正在保存Markdown文件'
            ai_file.save()

            # 保存Markdown文件
            base_name = os.path.splitext(os.path.basename(pdf_path))[0]
            markdown_path = os.path.join(self.temp_dir, f"{base_name}.md")

            with open(markdown_path, 'w', encoding='utf-8') as f:
                f.write(markdown_content)

            ai_file.markdown_file_path = markdown_path
            ai_file.save()

            logging.info(f"Markdown文件保存成功: {markdown_path}")

            # 4. 生成内容摘要
            # 重新连接数据库以避免连接超时
            self.ensure_db_connection()
            ai_file = AIReadingFile.get(AIReadingFile.id == file_id)
            ai_file.processing_status = 'generating_summary'
            ai_file.processing_progress = 80.0
            ai_file.processing_message = '正在生成内容摘要'
            ai_file.save()

            summary = self.generate_summary(markdown_content, tenant_id=tenant_id)

            # 5. 完成处理
            # 重新连接数据库以避免连接超时
            self.ensure_db_connection()
            ai_file = AIReadingFile.get(AIReadingFile.id == file_id)

            # 设置处理完成状态和摘要
            ai_file.processing_status = 'completed'
            ai_file.processing_progress = 100.0
            ai_file.processing_message = '文档处理完成'

            if summary:
                ai_file.content_summary = summary
                ai_file.summary_generated_at = datetime.now()
                logging.info(f"内容摘要生成成功: {summary[:100]}...")
            else:
                logging.warning("内容摘要生成失败")
                ai_file.content_summary = "摘要生成失败"

            ai_file.save()

            logging.info(f"文件处理完成: {file_id}")
            return True

        except Exception as e:
            logging.exception(f"处理文件异常: {str(e)}")
            try:
                # 重新连接数据库以避免连接超时
                self.ensure_db_connection()
                ai_file = AIReadingFile.get(AIReadingFile.id == file_id)
                ai_file.processing_status = 'failed'
                ai_file.processing_message = f'处理失败: {str(e)}'
                ai_file.save()
            except:
                pass
            return False

    def get_default_tenant_id(self) -> Optional[str]:
        """
        获取默认租户ID（获取第一个有效的租户）

        Returns:
            默认租户ID，失败返回None
        """
        try:
            from api.db.services.user_service import TenantService

            # 获取第一个有效的租户
            tenants = TenantService.query(status="1")  # status="1" 表示有效
            if tenants:
                tenant = tenants[0]
                logging.info(f"使用默认租户: {tenant.id}, 名称: {tenant.name}")
                return tenant.id
            else:
                logging.error("未找到有效的租户")
                return None

        except Exception as e:
            logging.exception(f"获取默认租户ID异常: {str(e)}")
            return None

    def get_llm_bundle(self, tenant_id: str) -> Optional['LLMBundle']:
        """
        获取LLM模型实例（参考AI chat模块的实现）

        Args:
            tenant_id: 租户ID

        Returns:
            LLMBundle实例，失败返回None
        """
        try:
            from api.db.services.user_service import TenantService
            from api.db.services.llm_service import LLMBundle, TenantLLMService
            from api.db import LLMType

            # 获取租户信息
            success, tenant = TenantService.get_by_id(tenant_id)
            if not success or not tenant:
                logging.error(f"租户不存在: {tenant_id}")
                return None

            # 获取租户的默认聊天模型
            llm_name = tenant.llm_id
            if not llm_name:
                logging.error(f"租户 {tenant_id} 没有配置默认聊天模型")
                return None

            logging.info(f"使用租户默认LLM: {llm_name}")

            # 检查模型名称格式并修正（参考AI chat模块的逻辑）
            mdlnm, fid = TenantLLMService.split_model_name_and_factory(llm_name)
            logging.info(f"分割模型名称: mdlnm={mdlnm}, fid={fid}")

            # 获取租户可用的聊天模型列表
            tenant_llms = TenantLLMService.get_my_llms(tenant_id)
            chat_llms = [llm for llm in tenant_llms if llm['model_type'] == 'chat']
            chat_llm_names = [f"{llm['llm_name']}@{llm['llm_factory']}" for llm in chat_llms]
            logging.info(f"可用的聊天模型: {chat_llm_names}")

            # 如果模型名称格式不正确，尝试修正
            if not fid and ':' in llm_name:
                # 格式: "Factory:ModelName" -> "ModelName@Factory"
                parts = llm_name.split(':', 1)
                if len(parts) == 2:
                    factory, model = parts
                    corrected_name = f"{model}@{factory}"
                    logging.info(f"尝试修正模型名称: {corrected_name}")

                    # 检查修正后的名称是否存在
                    corrected_mdlnm, corrected_fid = TenantLLMService.split_model_name_and_factory(corrected_name)
                    if corrected_fid:
                        matching_llm = next((llm for llm in chat_llms
                                           if llm['llm_name'] == corrected_mdlnm and llm['llm_factory'] == corrected_fid), None)
                        if matching_llm:
                            logging.info(f"找到匹配的LLM，使用修正名称: {corrected_name}")
                            llm_name = corrected_name

            # 创建LLMBundle实例
            llm_bundle = LLMBundle(tenant_id, LLMType.CHAT, llm_name)
            logging.info(f"成功创建LLMBundle: tenant_id={tenant_id}, llm_name={llm_name}")
            return llm_bundle

        except Exception as e:
            logging.exception(f"获取LLM模型实例异常: {str(e)}")
            return None

    def generate_summary(self, markdown_content: str, tenant_id: str = None) -> Optional[str]:
        """
        使用默认大模型生成内容摘要

        Args:
            markdown_content: Markdown文档内容
            tenant_id: 租户ID（可选，如果提供则使用指定租户，否则使用默认租户）

        Returns:
            生成的摘要，失败返回None
        """
        try:
            # 限制输入内容长度，避免超过模型限制
            max_content_length = 8000  # 约8000字符
            if len(markdown_content) > max_content_length:
                # 取前面部分内容进行摘要
                content_for_summary = markdown_content[:max_content_length] + "..."
                logging.info(f"内容过长，截取前{max_content_length}字符进行摘要")
            else:
                content_for_summary = markdown_content

            # 构建摘要提示词
            system_prompt = """你是一个专业的文档摘要助手。请对用户提供的文档内容进行总结摘要，要求：
1. 提取文档的主要内容和关键信息
2. 摘要长度控制在400-800字之间
3. 使用简洁明了的语言
4. 保持客观中性的语调
5. 突出文档的核心观点和重要细节"""

            user_message = f"请对以下文档内容进行总结摘要：\n\n{content_for_summary}"

            # 获取租户ID（优先使用传入的，否则使用默认的）
            if not tenant_id:
                tenant_id = self.get_default_tenant_id()
                if not tenant_id:
                    logging.warning("无法获取租户ID，使用简单摘要生成")
                    return self._generate_simple_summary(markdown_content)

            # 获取LLM模型实例
            llm_bundle = self.get_llm_bundle(tenant_id)
            if not llm_bundle:
                logging.warning("无法获取LLM模型实例，使用简单摘要生成")
                return self._generate_simple_summary(markdown_content)

            # 准备对话历史
            history = [{"role": "user", "content": user_message}]

            # 生成配置
            gen_conf = {
                "temperature": 0.3,  # 较低的温度以获得更稳定的摘要
                "max_tokens": 1000,  # 限制输出长度
                "top_p": 0.9,
                "extra_body": {"enable_thinking": False}  # 正确的方式设置enable_thinking
            }

            # 调用LLM生成摘要
            summary = llm_bundle.chat(
                system=system_prompt,
                history=history,
                gen_conf=gen_conf
            )

            if summary and not summary.startswith("**ERROR**"):
                logging.info(f"LLM摘要生成成功")
                return summary.strip()
            else:
                logging.error(f"LLM摘要生成失败: {summary}")
                return self._generate_simple_summary(markdown_content)

        except Exception as e:
            logging.exception(f"生成摘要异常: {str(e)}")
            return self._generate_simple_summary(markdown_content)

    def _generate_simple_summary(self, markdown_content: str) -> str:
        """
        生成简单的摘要（备用方案）

        Args:
            markdown_content: Markdown文档内容

        Returns:
            简单的摘要
        """
        try:
            lines = markdown_content.split('\n')
            non_empty_lines = [line.strip() for line in lines if line.strip()]

            if len(non_empty_lines) > 0:
                # 取前几行作为简单摘要
                summary_lines = non_empty_lines[:5]
                summary = "文档主要内容包括：" + "；".join(summary_lines[:3])
                if len(summary) > 200:
                    summary = summary[:200] + "..."
                return summary
            else:
                return "文档内容为空或无法解析"

        except Exception as e:
            logging.exception(f"生成简单摘要异常: {str(e)}")
            return "摘要生成失败"
