#!/usr/bin/env python3
"""
快速诊断AI chatbot问题

检查：
1. 前端显示问题
2. 会话数量限制
3. 删除对话空记录问题
4. 空对话记录保存问题
5. 新对话创建卡住问题
"""

import requests
import json

def test_api_endpoints():
    """测试API端点"""
    print("🔍 测试API端点...")
    
    try:
        # 测试会话列表API
        url = "http://localhost:9380/api/v1/chatbot/sessions"
        response = requests.get(url, timeout=5)
        
        print(f"状态码: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            if data.get('code') == 401:
                print("✅ API正常工作，返回认证错误（需要登录）")
            elif data.get('code') == 0:
                print("✅ API正常工作，返回数据")
                print(f"会话数量: {len(data.get('data', {}).get('sessions', []))}")
            else:
                print(f"⚠️  API返回错误: {data}")
        else:
            print(f"❌ API响应异常: {response.status_code}")
        
        return True
        
    except requests.exceptions.RequestException as e:
        print(f"❌ API请求失败: {e}")
        return False

def check_frontend_files():
    """检查前端文件"""
    print("\n🔍 检查前端文件...")
    
    import os
    
    files_to_check = [
        ('/home/<USER>/ragflow/hbweb/src/components/ChatBot/ChatHistoryList.tsx', '对话记录列表组件'),
        ('/home/<USER>/ragflow/hbweb/src/components/ChatBot/index.tsx', 'ChatBot主组件'),
        ('/home/<USER>/ragflow/hbweb/src/services/chatbot-service.ts', 'ChatBot服务'),
        ('/home/<USER>/ragflow/hbweb/src/hooks/use-chatbot-hooks.ts', 'ChatBot Hooks'),
        ('/home/<USER>/ragflow/hbweb/src/pages/chatbot/index.tsx', 'ChatBot页面'),
    ]
    
    all_exist = True
    for file_path, description in files_to_check:
        if os.path.exists(file_path):
            print(f"✅ {description}")
        else:
            print(f"❌ {description} - 文件不存在")
            all_exist = False
    
    return all_exist

def check_backend_files():
    """检查后端文件"""
    print("\n🔍 检查后端文件...")
    
    import os
    
    files_to_check = [
        ('/home/<USER>/ragflow/ai/chatbot_api.py', 'ChatBot API'),
        ('/home/<USER>/ragflow/ai/chatbot_service.py', 'ChatBot服务'),
        ('/home/<USER>/ragflow/api/db/db_models.py', '数据库模型'),
    ]
    
    all_exist = True
    for file_path, description in files_to_check:
        if os.path.exists(file_path):
            print(f"✅ {description}")
        else:
            print(f"❌ {description} - 文件不存在")
            all_exist = False
    
    return all_exist

def check_session_creation_logic():
    """检查会话创建逻辑"""
    print("\n🔍 检查会话创建逻辑...")
    
    import os
    
    # 检查ChatBot组件中的会话创建逻辑
    chatbot_file = '/home/<USER>/ragflow/hbweb/src/components/ChatBot/index.tsx'
    if not os.path.exists(chatbot_file):
        print("❌ ChatBot组件文件不存在")
        return False
    
    with open(chatbot_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    issues = []
    
    # 检查是否有自动创建会话的逻辑
    if "useEffect" in content and "createSession" in content:
        if "移除自动创建会话的逻辑" in content:
            print("✅ 自动创建会话逻辑已禁用")
        else:
            issues.append("可能存在自动创建会话的逻辑")
    
    # 检查初始化状态管理
    if "isInitializing" in content:
        print("✅ 有初始化状态管理")
    else:
        issues.append("缺少初始化状态管理")
    
    # 检查会话ID处理
    if "selectedSessionId" in content:
        print("✅ 有会话ID处理逻辑")
    else:
        issues.append("缺少会话ID处理逻辑")
    
    if issues:
        print("⚠️  发现的问题:")
        for issue in issues:
            print(f"   - {issue}")
        return False
    else:
        print("✅ 会话创建逻辑检查通过")
        return True

def check_backend_session_logic():
    """检查后端会话逻辑"""
    print("\n🔍 检查后端会话逻辑...")
    
    import os
    
    service_file = '/home/<USER>/ragflow/ai/chatbot_service.py'
    if not os.path.exists(service_file):
        print("❌ ChatBot服务文件不存在")
        return False
    
    with open(service_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    checks = [
        ("临时会话创建", "Created temporary chat session" in content),
        ("按需保存会话", "_save_session_to_database" in content),
        ("会话清理逻辑", "_cleanup_old_sessions" in content),
        ("10条限制", "max_sessions: int = 10" in content),
        ("正确的条件判断", "if len(sessions) > max_sessions:" in content),
    ]
    
    all_passed = True
    for check_name, condition in checks:
        if condition:
            print(f"✅ {check_name}")
        else:
            print(f"❌ {check_name}")
            all_passed = False
    
    return all_passed

def generate_debug_guide():
    """生成调试指南"""
    print("\n📋 调试指南:")
    
    print("\n🔧 前端调试步骤:")
    print("1. 打开浏览器，访问AI chatbot页面")
    print("2. 按F12打开开发者工具")
    print("3. 查看Console标签页的日志:")
    print("   - 应该看到'开始加载对话记录...'")
    print("   - 应该看到'API响应: {...}'")
    print("   - 如果有401错误，说明需要登录")
    print("4. 查看Network标签页:")
    print("   - 检查/api/v1/chatbot/sessions请求")
    print("   - 检查请求头中的Authorization")
    
    print("\n🔧 后端调试步骤:")
    print("1. 检查后端服务是否运行在9380端口")
    print("2. 查看后端日志输出")
    print("3. 检查数据库连接是否正常")
    print("4. 验证用户认证是否正常")
    
    print("\n🔧 常见问题解决:")
    print("1. 前端不显示对话记录:")
    print("   - 检查用户是否已登录")
    print("   - 检查API请求是否成功")
    print("   - 检查数据库中是否有数据")
    
    print("2. 新对话一直在创建中:")
    print("   - 检查createSession API是否正常")
    print("   - 检查前端状态管理逻辑")
    print("   - 检查后端会话创建逻辑")
    
    print("3. 删除对话后出现空记录:")
    print("   - 检查是否禁用了自动创建会话")
    print("   - 检查删除后的状态重置逻辑")

def create_browser_test_script():
    """创建浏览器测试脚本"""
    print("\n📝 创建浏览器测试脚本...")
    
    script_content = '''
// AI chatbot问题诊断脚本
// 在浏览器控制台中运行

console.log("=== AI chatbot问题诊断 ===");

// 1. 检查认证状态
const auth = localStorage.getItem('Authorization');
console.log("认证状态:", auth ? "已登录" : "未登录");

// 2. 测试会话列表API
async function testSessionsAPI() {
    try {
        console.log("\\n--- 测试会话列表API ---");
        const response = await fetch('/api/v1/chatbot/sessions', {
            headers: { 'Authorization': auth || '' }
        });
        
        const data = await response.json();
        console.log("API响应:", data);
        
        if (data.code === 0) {
            console.log("✅ 会话列表获取成功");
            console.log("会话数量:", data.data?.sessions?.length || 0);
        } else if (data.code === 401) {
            console.log("❌ 需要重新登录");
        } else {
            console.log("❌ API调用失败:", data.message);
        }
        
    } catch (error) {
        console.error("❌ API测试失败:", error);
    }
}

// 3. 测试创建会话API
async function testCreateSessionAPI() {
    try {
        console.log("\\n--- 测试创建会话API ---");
        const response = await fetch('/api/v1/chatbot/sessions', {
            method: 'POST',
            headers: { 
                'Authorization': auth || '',
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({})
        });
        
        const data = await response.json();
        console.log("创建会话响应:", data);
        
        if (data.code === 0) {
            console.log("✅ 会话创建成功");
            console.log("会话ID:", data.data?.session_id);
        } else {
            console.log("❌ 会话创建失败:", data.message);
        }
        
    } catch (error) {
        console.error("❌ 创建会话测试失败:", error);
    }
}

// 4. 检查前端组件状态
function checkFrontendState() {
    console.log("\\n--- 检查前端组件状态 ---");
    
    // 检查ChatBot页面
    const chatbotPage = document.querySelector('[class*="chatBotPage"]');
    console.log("ChatBot页面:", chatbotPage ? "已加载" : "未找到");
    
    // 检查对话记录列表
    const historyList = document.querySelector('[class*="chatHistoryList"]');
    console.log("对话记录列表:", historyList ? "已加载" : "未找到");
    
    // 检查加载状态
    const loadingElements = document.querySelectorAll('[class*="loading"]');
    console.log("加载状态元素数量:", loadingElements.length);
}

// 运行测试
if (auth) {
    testSessionsAPI();
    testCreateSessionAPI();
} else {
    console.log("❌ 请先登录系统");
}

checkFrontendState();

console.log("\\n=== 诊断完成 ===");
'''
    
    script_path = '/home/<USER>/ragflow/hbweb/browser_diagnose.js'
    with open(script_path, 'w', encoding='utf-8') as f:
        f.write(script_content)
    
    print(f"✅ 浏览器诊断脚本已创建: {script_path}")

def main():
    """主函数"""
    print("🚀 开始AI chatbot问题诊断")
    print("="*60)
    
    tests = [
        ("API端点测试", test_api_endpoints),
        ("前端文件检查", check_frontend_files),
        ("后端文件检查", check_backend_files),
        ("会话创建逻辑检查", check_session_creation_logic),
        ("后端会话逻辑检查", check_backend_session_logic),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n{'='*50}")
        print(f"测试: {test_name}")
        print('='*50)
        
        try:
            if test_func():
                passed += 1
                print(f"✅ {test_name} 通过")
            else:
                print(f"❌ {test_name} 失败")
        except Exception as e:
            print(f"❌ {test_name} 异常: {e}")
    
    print(f"\n{'='*60}")
    print(f"诊断结果: {passed}/{total} 通过")
    print('='*60)
    
    # 生成调试指南
    generate_debug_guide()
    
    # 创建浏览器测试脚本
    create_browser_test_script()
    
    print(f"\n💡 下一步建议:")
    print("1. 在浏览器中运行诊断脚本")
    print("2. 检查用户登录状态")
    print("3. 查看浏览器控制台日志")
    print("4. 根据错误信息进行针对性修复")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
