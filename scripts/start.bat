@echo off
setlocal enabledelayedexpansion

:: RAGFlow Frontend Start Script for Windows
:: This script helps you start the RAGFlow frontend application

title RAGFlow Frontend Setup

:: Colors for output (using echo with special characters)
set "RED=[91m"
set "GREEN=[92m"
set "YELLOW=[93m"
set "BLUE=[94m"
set "NC=[0m"

:: Function to print colored output
:print_status
echo %BLUE%[INFO]%NC% %~1
goto :eof

:print_success
echo %GREEN%[SUCCESS]%NC% %~1
goto :eof

:print_warning
echo %YELLOW%[WARNING]%NC% %~1
goto :eof

:print_error
echo %RED%[ERROR]%NC% %~1
goto :eof

:: Check if Node.js is installed
:check_node
where node >nul 2>nul
if %errorlevel% neq 0 (
    call :print_error "Node.js is not installed. Please install Node.js 18+ first."
    pause
    exit /b 1
)

for /f "tokens=1 delims=." %%a in ('node -v') do (
    set "NODE_MAJOR=%%a"
    set "NODE_MAJOR=!NODE_MAJOR:v=!"
)

if !NODE_MAJOR! lss 18 (
    call :print_error "Node.js version 18+ is required. Current version: %NODE_VERSION%"
    pause
    exit /b 1
)

call :print_success "Node.js is installed"
goto :eof

:: Check if npm is installed
:check_npm
where npm >nul 2>nul
if %errorlevel% neq 0 (
    call :print_error "npm is not installed. Please install npm first."
    pause
    exit /b 1
)

call :print_success "npm is installed"
goto :eof

:: Install dependencies
:install_deps
call :print_status "Installing dependencies..."

if exist "package-lock.json" (
    npm ci
) else (
    npm install
)

if %errorlevel% neq 0 (
    call :print_error "Failed to install dependencies"
    pause
    exit /b 1
)

call :print_success "Dependencies installed successfully"
goto :eof

:: Setup environment
:setup_env
if not exist ".env" (
    if exist ".env.example" (
        call :print_status "Creating .env file from .env.example..."
        copy ".env.example" ".env" >nul
        call :print_warning "Please edit .env file with your configuration"
    ) else (
        call :print_warning "No .env.example found. Please create .env file manually"
    )
) else (
    call :print_success ".env file already exists"
)
goto :eof

:: Start development server
:start_dev
call :print_status "Starting development server..."
call :print_status "Server will be available at:"
call :print_status "  - Local:   http://localhost:8000"
call :print_status "  - Network: http://0.0.0.0:8000"
call :print_status "  - LAN:     http://[your-ip]:8000"
set HOST=0.0.0.0
set PORT=8000
npm run dev
goto :eof

:: Build for production
:build_prod
call :print_status "Building for production..."
npm run build
if %errorlevel% equ 0 (
    call :print_success "Production build completed"
) else (
    call :print_error "Build failed"
    pause
    exit /b 1
)
goto :eof

:: Start with Docker
:start_docker
where docker >nul 2>nul
if %errorlevel% neq 0 (
    call :print_error "Docker is not installed. Please install Docker first."
    pause
    exit /b 1
)

where docker-compose >nul 2>nul
if %errorlevel% neq 0 (
    call :print_error "Docker Compose is not installed. Please install Docker Compose first."
    pause
    exit /b 1
)

call :print_status "Starting with Docker..."
docker-compose up -d
if %errorlevel% equ 0 (
    call :print_success "Application started with Docker"
    call :print_status "Access the application at http://localhost:3000"
) else (
    call :print_error "Failed to start with Docker"
    pause
    exit /b 1
)
goto :eof

:: Show help
:show_help
echo RAGFlow Frontend Start Script
echo.
echo Usage: %~nx0 [OPTION]
echo.
echo Options:
echo   dev       Start development server (default)
echo   build     Build for production
echo   docker    Start with Docker
echo   help      Show this help message
echo.
echo Examples:
echo   %~nx0          # Start development server
echo   %~nx0 dev      # Start development server
echo   %~nx0 build    # Build for production
echo   %~nx0 docker   # Start with Docker
goto :eof

:: Main function
:main
call :print_status "RAGFlow Frontend Setup"
call :print_status "======================"

:: Check prerequisites
call :check_node
call :check_npm

:: Setup environment
call :setup_env

:: Handle command line arguments
set "ACTION=%~1"
if "%ACTION%"=="" set "ACTION=dev"

if "%ACTION%"=="dev" (
    call :install_deps
    call :start_dev
) else if "%ACTION%"=="build" (
    call :install_deps
    call :build_prod
) else if "%ACTION%"=="docker" (
    call :start_docker
) else if "%ACTION%"=="help" (
    call :show_help
) else if "%ACTION%"=="-h" (
    call :show_help
) else if "%ACTION%"=="--help" (
    call :show_help
) else (
    call :print_error "Unknown option: %ACTION%"
    call :show_help
    pause
    exit /b 1
)

goto :eof

:: Run main function
call :main %*

pause
