#!/bin/bash

# 配置检查和修复脚本
# 用于检查前端和后端API配置是否正确

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查环境变量配置
check_env_config() {
    log_info "检查环境变量配置..."
    
    # 检查.env文件
    if [ ! -f ".env" ]; then
        log_warning ".env 文件不存在，从示例文件创建"
        cp .env.example .env
    fi
    
    # 检查Docker环境配置
    if [ ! -f ".env.docker" ]; then
        log_warning ".env.docker 文件不存在，从示例文件创建"
        cp .env.example .env.docker
        # 修改为Docker环境配置
        sed -i 's|API_BASE_URL=.*|API_BASE_URL=http://hbgk-api:9380|g' .env.docker
    fi
    
    # 检查生产环境配置
    if [ ! -f ".env.production" ]; then
        log_warning ".env.production 文件不存在，从示例文件创建"
        cp .env.example .env.production
    fi
    
    log_success "环境变量配置检查完成"
}

# 检查nginx配置
check_nginx_config() {
    log_info "检查nginx配置..."
    
    if [ ! -f "nginx.conf.template" ]; then
        log_error "nginx.conf.template 文件不存在"
        return 1
    fi
    
    if [ ! -f "docker-entrypoint.sh" ]; then
        log_error "docker-entrypoint.sh 文件不存在"
        return 1
    fi
    
    # 检查docker-entrypoint.sh是否可执行
    if [ ! -x "docker-entrypoint.sh" ]; then
        log_warning "docker-entrypoint.sh 不可执行，正在修复..."
        chmod +x docker-entrypoint.sh
    fi
    
    log_success "nginx配置检查完成"
}

# 检查Docker配置
check_docker_config() {
    log_info "检查Docker配置..."
    
    if [ ! -f "Dockerfile" ]; then
        log_error "Dockerfile 文件不存在"
        return 1
    fi
    
    if [ ! -f "docker-compose.yml" ]; then
        log_error "docker-compose.yml 文件不存在"
        return 1
    fi
    
    log_success "Docker配置检查完成"
}

# 检查后端配置
check_backend_config() {
    log_info "检查后端配置..."
    
    if [ -f "../conf/service_conf.yaml" ]; then
        # 检查后端是否监听所有接口
        if grep -q "host: 0.0.0.0" ../conf/service_conf.yaml; then
            log_success "后端配置正确，监听所有接口"
        else
            log_warning "后端配置可能需要修改，建议设置 host: 0.0.0.0"
        fi
    else
        log_warning "未找到后端配置文件 ../conf/service_conf.yaml"
    fi
}

# 显示配置信息
show_config_info() {
    log_info "当前配置信息:"
    
    echo "=== 环境配置文件 ==="
    if [ -f ".env" ]; then
        echo "开发环境 (.env):"
        grep -E "^(API_BASE_URL|API_KEY)" .env | head -2
    fi
    
    if [ -f ".env.docker" ]; then
        echo "Docker环境 (.env.docker):"
        grep -E "^(API_BASE_URL|API_KEY)" .env.docker | head -2
    fi
    
    if [ -f ".env.production" ]; then
        echo "生产环境 (.env.production):"
        grep -E "^(API_BASE_URL|API_KEY)" .env.production | head -2
    fi
    
    echo ""
    echo "=== 部署说明 ==="
    echo "1. 开发环境: npm run dev"
    echo "2. Docker部署: ./deploy.sh 选择选项4"
    echo "3. 生产环境: 使用 .env.production 配置"
    echo ""
    echo "=== 配置说明 ==="
    echo "- 开发环境: 使用 localhost 或具体IP地址"
    echo "- Docker环境: 使用服务名 hbgk-api 进行容器间通信"
    echo "- 生产环境: 根据实际部署情况配置API地址"
}

# 主函数
main() {
    echo "=== HBGK 前端配置检查工具 ==="
    echo ""
    
    check_env_config
    check_nginx_config
    check_docker_config
    check_backend_config
    
    echo ""
    show_config_info
    
    echo ""
    log_success "配置检查完成！"
    log_info "如需修改配置，请编辑相应的 .env 文件"
}

# 运行主函数
main "$@"
