#!/bin/bash

# RAGFlow Frontend Start Script
# This script helps you start the RAGFlow frontend application

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if Node.js is installed
check_node() {
    if ! command -v node &> /dev/null; then
        print_error "Node.js is not installed. Please install Node.js 18+ first."
        exit 1
    fi
    
    NODE_VERSION=$(node -v | cut -d'v' -f2 | cut -d'.' -f1)
    if [ "$NODE_VERSION" -lt 18 ]; then
        print_error "Node.js version 18+ is required. Current version: $(node -v)"
        exit 1
    fi
    
    print_success "Node.js $(node -v) is installed"
}

# Check if npm is installed
check_npm() {
    if ! command -v npm &> /dev/null; then
        print_error "npm is not installed. Please install npm first."
        exit 1
    fi
    
    print_success "npm $(npm -v) is installed"
}

# Install dependencies
install_deps() {
    print_status "Installing dependencies..."
    
    if [ -f "package-lock.json" ]; then
        npm ci
    else
        npm install
    fi
    
    print_success "Dependencies installed successfully"
}

# Setup environment
setup_env() {
    if [ ! -f ".env" ]; then
        if [ -f ".env.example" ]; then
            print_status "Creating .env file from .env.example..."
            cp .env.example .env
            print_warning "Please edit .env file with your configuration"
        else
            print_warning "No .env.example found. Please create .env file manually"
        fi
    else
        print_success ".env file already exists"
    fi
}

# Start development server
start_dev() {
    print_status "Starting development server..."
    print_status "Server will be available at:"
    print_status "  - Local:   http://localhost:8000"
    print_status "  - Network: http://0.0.0.0:8000"
    print_status "  - LAN:     http://[your-ip]:8000"
    export HOST=0.0.0.0
    export PORT=8000
    npm run dev
}

# Build for production
build_prod() {
    print_status "Building for production..."
    npm run build
    print_success "Production build completed"
}

# Start with Docker
start_docker() {
    if ! command -v docker &> /dev/null; then
        print_error "Docker is not installed. Please install Docker first."
        exit 1
    fi
    
    if ! command -v docker-compose &> /dev/null; then
        print_error "Docker Compose is not installed. Please install Docker Compose first."
        exit 1
    fi
    
    print_status "Starting with Docker..."
    docker-compose up -d
    print_success "Application started with Docker"
    print_status "Access the application at http://localhost:3000"
}

# Show help
show_help() {
    echo "RAGFlow Frontend Start Script"
    echo ""
    echo "Usage: $0 [OPTION]"
    echo ""
    echo "Options:"
    echo "  dev       Start development server (default)"
    echo "  build     Build for production"
    echo "  docker    Start with Docker"
    echo "  help      Show this help message"
    echo ""
    echo "Examples:"
    echo "  $0          # Start development server"
    echo "  $0 dev      # Start development server"
    echo "  $0 build    # Build for production"
    echo "  $0 docker   # Start with Docker"
}

# Main function
main() {
    print_status "RAGFlow Frontend Setup"
    print_status "======================"
    
    # Check prerequisites
    check_node
    check_npm
    
    # Setup environment
    setup_env
    
    # Handle command line arguments
    case "${1:-dev}" in
        "dev")
            install_deps
            start_dev
            ;;
        "build")
            install_deps
            build_prod
            ;;
        "docker")
            start_docker
            ;;
        "help"|"-h"|"--help")
            show_help
            ;;
        *)
            print_error "Unknown option: $1"
            show_help
            exit 1
            ;;
    esac
}

# Run main function
main "$@"
