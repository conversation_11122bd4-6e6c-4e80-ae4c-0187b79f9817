/.umirc.ts.bak
/AI聊天功能增强说明.md
/dist
/node_modules
/test.js
test_final_chatbot_fixes.py
test_code_structure.py
test_loop_fix.py
test_message_order_fix.py
verify_loop_fix.py
test_chatbot_integration.py
test_chatbot_fixes.py
debug_chatbot_display.py
debug_message_order_detailed.py
debug_message_order.py
debug_specific_message_order.py
message_order_fix_patch.txt
message_order_debug_patches.txt
test_all_chatbot_fixes.py
test_auth_in_browser.js
test_chatbot_display.py
test_chatbot_enhancements.py
test_chatbot_final.py
test_chatbot_fixes_final.py
test_chatbot_history.py
test_cleanup_deleted_messages.py
browser_test_cleanup.js
browser_test_enhancements.js
browser_test_final_fixes.js
browser_test_fixes.js
browser_test_message_order.js
create_test_data.py
AI_CHATBOT_ENHANCEMENT_SUMMARY.md
DOCKER_BUILD_ISSUE_ANALYSIS.md
verify-docker-build.sh
chunk-id-field-fix.md
chunk-status-fix-summary.md
chunk-status-precise-update-fix.md
chunk-status-switch-fix.md
chunk-status-optimistic-update-fix.md
conversation_analysis_report.md
conversation_first_message_analysis.md
conversation_first_message_fix_complete.md
conversation_first_message_fix_summary.md
IMPLEMENTATION_SUMMARY.md
knowledge_markdown_content_analysis.md
knowledge_markdown_content_fix_summary.md
test_welcome_message_logic.md
test-chunks-modifications.md
ENTRYPOINT_BACKEND_FIX.md
