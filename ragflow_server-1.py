#
#  Copyright 2024 The InfiniFlow Authors. All Rights Reserved.
#
#  Licensed under the Apache License, Version 2.0 (the "License");
#  you may not use this file except in compliance with the License.
#  You may obtain a copy of the License at
#
#      http://www.apache.org/licenses/LICENSE-2.0
#
#  Unless required by applicable law or agreed to in writing, software
#  distributed under the License is distributed on an "AS IS" BASIS,
#  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
#  See the License for the specific language governing permissions and
#  limitations under the License.
#

# from beartype import BeartypeConf
# from beartype.claw import beartype_all  # <-- you didn't sign up for this
# beartype_all(conf=BeartypeConf(violation_type=UserWarning))    # <-- emit warnings from all code

from api.utils.log_utils import init_root_logger
from plugin import GlobalPluginManager
init_root_logger("ragflow_server")

import logging
import os
import signal
import sys
import time
import traceback
import threading
import uuid
import subprocess

from werkzeug.serving import run_simple
from api import settings
from api.apps import app
from api.db.runtime_config import RuntimeConfig
from api.db.services.document_service import DocumentService
from api import utils

from api.db.db_models import init_database_tables as init_web_db
from api.db.init_data import init_web_data
from api.versions import get_ragflow_version
from api.utils import show_configs
from rag.settings import print_rag_settings
from rag.utils.redis_conn import RedisDistributedLock

stop_event = threading.Event()
task_executor_process = None

RAGFLOW_DEBUGPY_LISTEN = int(os.environ.get('RAGFLOW_DEBUGPY_LISTEN', "0"))

def update_progress():
    lock_value = str(uuid.uuid4())
    redis_lock = RedisDistributedLock("update_progress", lock_value=lock_value, timeout=60)
    logging.info(f"update_progress lock_value: {lock_value}")
    while not stop_event.is_set():
        try:
            if redis_lock.acquire():
                DocumentService.update_progress()
                redis_lock.release()
            stop_event.wait(6)
        except Exception:
            logging.exception("update_progress exception")
        finally:
            redis_lock.release()


def start_task_executor():
    """启动 Task Executor 进程"""
    global task_executor_process
    try:
        # 获取项目根目录 (从 /home/<USER>/ragflow/api 到 /home/<USER>/ragflow)
        project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
        executor_path = os.path.join(project_root, "rag", "svr", "task_executor.py")

        logging.info(f"Current file: {__file__}")
        logging.info(f"Project root: {project_root}")
        logging.info(f"Task Executor path: {executor_path}")

        # 检查 task_executor.py 文件是否存在
        if not os.path.exists(executor_path):
            logging.error(f"Task Executor file not found: {executor_path}")
            # 尝试备用路径
            alt_executor_path = "/ragflow/rag/svr/task_executor.py"
            if os.path.exists(alt_executor_path):
                logging.info(f"Using alternative path: {alt_executor_path}")
                executor_path = alt_executor_path
                project_root = "/ragflow"
            else:
                logging.error(f"Alternative path also not found: {alt_executor_path}")
                return False

        # 启动 Task Executor 进程
        logging.info(f"Starting Task Executor from: {executor_path}")

        # 准备环境变量
        env = os.environ.copy()
        env['PYTHONPATH'] = project_root

        # 尝试使用虚拟环境中的 Python，如果不存在则使用当前 Python
        venv_python = os.path.join(project_root, ".venv", "bin", "python")
        python_executable = venv_python if os.path.exists(venv_python) else sys.executable

        logging.info(f"Using Python executable: {python_executable}")

        task_executor_process = subprocess.Popen(
            [python_executable, executor_path],
            cwd=project_root,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            env=env
        )

        # 等待一小段时间检查进程是否成功启动
        time.sleep(2)
        if task_executor_process.poll() is None:
            logging.info(f"Task Executor started successfully with PID: {task_executor_process.pid}")
            return True
        else:
            # 进程已经退出，获取错误信息
            stdout, stderr = task_executor_process.communicate()
            logging.error(f"Task Executor failed to start. Return code: {task_executor_process.returncode}")
            if stdout:
                logging.error(f"Task Executor stdout: {stdout.decode()}")
            if stderr:
                logging.error(f"Task Executor stderr: {stderr.decode()}")
            return False

    except Exception as e:
        logging.error(f"Failed to start Task Executor: {e}")
        return False


def monitor_task_executor():
    """监控 Task Executor 进程状态并在需要时重启"""
    global task_executor_process
    while not stop_event.is_set():
        try:
            if task_executor_process and task_executor_process.poll() is not None:
                # 进程已退出，尝试重启
                logging.warning("Task Executor process has exited, attempting to restart...")
                if start_task_executor():
                    logging.info("Task Executor restarted successfully")
                else:
                    logging.error("Failed to restart Task Executor")

            # 每30秒检查一次
            stop_event.wait(30)
        except Exception as e:
            logging.error(f"Error in Task Executor monitor: {e}")
            stop_event.wait(30)


def signal_handler(sig, frame):
    global task_executor_process
    logging.info("Received interrupt signal, shutting down...")
    stop_event.set()

    # 停止 Task Executor 进程
    if task_executor_process and task_executor_process.poll() is None:
        logging.info("Stopping Task Executor process...")
        try:
            task_executor_process.terminate()
            task_executor_process.wait(timeout=10)
            logging.info("Task Executor process stopped successfully")
        except subprocess.TimeoutExpired:
            logging.warning("Task Executor process did not stop gracefully, killing it...")
            task_executor_process.kill()
        except Exception as e:
            logging.error(f"Error stopping Task Executor process: {e}")

    time.sleep(1)
    sys.exit(0)

if __name__ == '__main__':
    logging.info(r"""
        ____   ___    ______ ______ __               
       / __ \ /   |  / ____// ____// /____  _      __
      / /_/ // /| | / / __ / /_   / // __ \| | /| / /
     / _, _// ___ |/ /_/ // __/  / // /_/ /| |/ |/ / 
    /_/ |_|/_/  |_|\____//_/    /_/ \____/ |__/|__/                             

    """)
    logging.info(
        f'RAGFlow version: {get_ragflow_version()}'
    )
    logging.info(
        f'project base: {utils.file_utils.get_project_base_directory()}'
    )
    show_configs()
    settings.init_settings()
    print_rag_settings()

    if RAGFLOW_DEBUGPY_LISTEN > 0:
        logging.info(f"debugpy listen on {RAGFLOW_DEBUGPY_LISTEN}")
        import debugpy
        debugpy.listen(("0.0.0.0", RAGFLOW_DEBUGPY_LISTEN))

    # init db
    init_web_db()
    init_web_data()
    # init runtime config
    import argparse

    parser = argparse.ArgumentParser()
    parser.add_argument(
        "--version", default=False, help="RAGFlow version", action="store_true"
    )
    parser.add_argument(
        "--debug", default=False, help="debug mode", action="store_true"
    )
    args = parser.parse_args()
    if args.version:
        print(get_ragflow_version())
        sys.exit(0)

    RuntimeConfig.DEBUG = args.debug
    if RuntimeConfig.DEBUG:
        logging.info("run on debug mode")

    RuntimeConfig.init_env()
    RuntimeConfig.init_config(JOB_SERVER_HOST=settings.HOST_IP, HTTP_PORT=settings.HOST_PORT)

    GlobalPluginManager.load_plugins()

    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)

    # 启动 Task Executor
    #def delayed_start_task_executor():
    #    logging.info("Starting Task Executor (delayed)")
    #    if start_task_executor():
            # 启动监控线程
    #        monitor_thread = threading.Thread(target=monitor_task_executor, daemon=True)
    #        monitor_thread.start()
     #       logging.info("Task Executor monitor thread started")
     #   else:
     #       logging.error("Failed to start Task Executor")

    def delayed_start_update_progress():
        logging.info("Starting update_progress thread (delayed)")
        t = threading.Thread(target=update_progress, daemon=True)
        t.start()

    if RuntimeConfig.DEBUG:
        if os.environ.get("WERKZEUG_RUN_MAIN") == "true":
            threading.Timer(1.0, delayed_start_update_progress).start()
        #    threading.Timer(2.0, delayed_start_task_executor).start()
    else:
        threading.Timer(1.0, delayed_start_update_progress).start()
        #threading.Timer(2.0, delayed_start_task_executor).start()

    # start http server
    try:
        logging.info("RAGFlow HTTP server start...")
        run_simple(
            hostname=settings.HOST_IP,
            port=settings.HOST_PORT,
            application=app,
            threaded=True,
            use_reloader=RuntimeConfig.DEBUG,
            use_debugger=RuntimeConfig.DEBUG,
        )
    except Exception:
        traceback.print_exc()
        stop_event.set()
        time.sleep(1)
        os.kill(os.getpid(), signal.SIGKILL)
