#
#  Copyright 2024 The InfiniFlow Authors. All Rights Reserved.
#
#  Licensed under the Apache License, Version 2.0 (the "License");
#  you may not use this file except in compliance with the License.
#  You may obtain a copy of the License at
#
#      http://www.apache.org/licenses/LICENSE-2.0
#
#  Unless required by applicable law or agreed to in writing, software
#  distributed under the License is distributed on an "AS IS" BASIS,
#  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
#  See the License for the specific language governing permissions and
#  limitations under the License.
#

"""
聊天机器人服务模块

提供完整的聊天机器人功能，包括会话管理、
消息处理和与LLM模型的直接交互。
"""

import uuid
import time
import json
import logging
from datetime import datetime
from typing import Dict, List, Optional, Any, Generator, Tuple
from dataclasses import dataclass

from api.db import LLMType
from api.db.services.llm_service import TenantLLMService, LLMBundle
from api.db.services.user_service import TenantService
from api.db.db_models import ChatBotSession, ChatBotMessage
from .memory_manager import MemoryManager
from .message_processor import MessageProcessor, ProcessedMessage


@dataclass
class ChatRequest:
    """聊天请求数据结构"""
    session_id: str
    user_id: str
    message: str
    tenant_id: str
    stream: bool = False
    system_prompt: Optional[str] = None
    llm_name: Optional[str] = None
    metadata: Optional[Dict[str, Any]] = None


@dataclass
class ChatResponse:
    """聊天响应数据结构"""
    session_id: str
    message_id: str
    content: str
    is_success: bool
    error_message: Optional[str] = None
    metadata: Optional[Dict[str, Any]] = None


class ChatBotService:
    """聊天机器人服务
    
    核心功能：
    - 会话管理和上下文维护
    - 与RAGFlow LLM模型直接交互
    - 消息处理和验证
    - 流式和非流式响应支持
    """
    
    def __init__(
        self,
        memory_manager: Optional[MemoryManager] = None,
        message_processor: Optional[MessageProcessor] = None,
        default_system_prompt: str = "你是一个有用的AI助手。请用中文回答问题，保持友好和专业的语调。"
    ):
        self.memory_manager = memory_manager or MemoryManager()
        self.message_processor = message_processor or MessageProcessor()
        self.default_system_prompt = default_system_prompt
        
        self.logger = logging.getLogger(__name__)
        
        # 默认生成配置
        self.default_gen_config = {
            "temperature": 0.7,
            "top_p": 0.9,
            "max_tokens": 8000,
            "frequency_penalty": 0.0,
            "presence_penalty": 0.0
        }

    def _format_sse_data(self, data: Dict[str, Any]) -> str:
        """格式化SSE数据"""
        try:
            json_str = json.dumps(data, ensure_ascii=False)
            return f"data: {json_str}\n\n"
        except Exception as e:
            self.logger.error(f"格式化SSE数据失败: {e}")
            return f'data: {{"error": "数据格式化失败"}}\n\n'

    def create_session(self, user_id: str, tenant_id: str) -> str:
        """创建新的聊天会话（仅在内存中，不立即保存到数据库）"""
        if not user_id or not tenant_id:
            raise ValueError("user_id and tenant_id are required")

        session_id = str(uuid.uuid4())

        # 创建会话上下文（仅在内存中）
        session = self.memory_manager.create_session(session_id, user_id)

        # 添加系统提示消息
        self.memory_manager.add_message(
            session_id=session_id,
            role="system",
            content=self.default_system_prompt,
            message_id=str(uuid.uuid4()),
            metadata={"tenant_id": tenant_id, "user_id": user_id}
        )

        self.logger.info(f"Created temporary chat session: {session_id} for user: {user_id}")
        return session_id

    def _save_session_to_database(self, session_id: str, user_id: str, tenant_id: str, first_message: str = None):
        """将会话保存到数据库（仅在用户发送第一条消息时调用）"""
        try:
            # 检查会话是否已存在于数据库中
            existing_session = ChatBotSession.select().where(
                ChatBotSession.id == session_id
            ).first()

            if existing_session:
                self.logger.debug(f"Session {session_id} already exists in database")

                # 如果会话存在但session_name为空，且有first_message，则更新session_name
                if not existing_session.session_name and first_message:
                    # 生成会话名称
                    cleaned_message = ' '.join(first_message.strip().split())
                    if cleaned_message:
                        session_name = cleaned_message[:50]
                        if len(cleaned_message) > 50:
                            session_name += "..."

                        # 更新数据库中的session_name
                        ChatBotSession.update(
                            session_name=session_name,
                            first_message=first_message,
                            update_date=datetime.now(),
                            update_time=int(time.time() * 1000)
                        ).where(ChatBotSession.id == session_id).execute()

                        self.logger.info(f"Updated existing session {session_id} with session_name: {session_name}")

                return

            # 检查用户会话数量，如果超过10个则删除最旧的
            self._cleanup_old_sessions(user_id)

            # 生成会话名称（使用第一条消息的前50个字符）
            session_name = "新对话"
            self.logger.info(f"Generating session_name for session {session_id}, first_message: {first_message[:100] if first_message else 'None'}...")

            if first_message:
                # 清理消息内容，去除多余的空白字符
                cleaned_message = ' '.join(first_message.strip().split())
                if cleaned_message:
                    # 取前50个字符作为会话名称
                    session_name = cleaned_message[:50]
                    if len(cleaned_message) > 50:
                        session_name += "..."
                    self.logger.info(f"Generated session_name: {session_name}")
                else:
                    self.logger.warning(f"first_message is empty after cleaning: '{first_message}'")
            else:
                self.logger.warning(f"No first_message provided for session {session_id}")

            # 创建数据库会话记录
            db_session = ChatBotSession.create(
                id=session_id,
                user_id=user_id,
                tenant_id=tenant_id,
                system_prompt=self.default_system_prompt,
                session_name=session_name,
                first_message=first_message,
                message_count=1,  # 包含用户的第一条消息
                last_message_time=datetime.now(),
                create_date=datetime.now(),
                update_date=datetime.now(),
                create_time=int(time.time() * 1000),
                update_time=int(time.time() * 1000)
            )
            self.logger.info(f"Saved session to database: {session_id}")

        except Exception as e:
            self.logger.error(f"Failed to save session to database: {e}")

    def _update_session_name_if_empty(self, session_id: str):
        """更新空的session_name"""
        try:
            # 获取会话
            session = ChatBotSession.get(ChatBotSession.id == session_id)

            # 如果session_name为空，尝试从first_message生成
            if not session.session_name and session.first_message:
                cleaned_message = ' '.join(session.first_message.strip().split())
                if cleaned_message:
                    session_name = cleaned_message[:50]
                    if len(cleaned_message) > 50:
                        session_name += "..."

                    # 更新数据库
                    ChatBotSession.update(session_name=session_name).where(
                        ChatBotSession.id == session_id
                    ).execute()

                    self.logger.info(f"Updated session_name for session {session_id}: {session_name}")

        except Exception as e:
            self.logger.error(f"Failed to update session_name: {e}")

    def _cleanup_old_sessions(self, user_id: str, max_sessions: int = 10):
        """清理用户的旧会话，保持最多max_sessions个会话"""
        try:
            # 首先清理已删除的数据（status为0的记录）
            self.cleanup_deleted_messages()
            self.cleanup_deleted_sessions()

            # 然后清理message_count为0的无效会话
            empty_sessions = list(ChatBotSession.select().where(
                ChatBotSession.user_id == user_id,
                ChatBotSession.status == "1",
                ChatBotSession.message_count == 0
            ))

            if empty_sessions:
                for session in empty_sessions:
                    self._delete_session_data(session.id)
                self.logger.info(f"Cleaned up {len(empty_sessions)} empty sessions for user {user_id}")

            # 获取用户的所有有效会话，按创建时间排序（使用create_date字段）
            sessions = list(ChatBotSession.select().where(
                ChatBotSession.user_id == user_id,
                ChatBotSession.status == "1",
                ChatBotSession.message_count > 0  # 只考虑有消息的会话
            ).order_by(ChatBotSession.create_date.desc()))

            if len(sessions) > max_sessions:
                # 删除最旧的会话，保留最新的max_sessions个
                sessions_to_delete = sessions[max_sessions:]
                for session in sessions_to_delete:
                    self._delete_session_data(session.id)

                self.logger.info(f"Cleaned up {len(sessions_to_delete)} old sessions for user {user_id}, keeping {max_sessions} sessions")

        except Exception as e:
            self.logger.error(f"Failed to cleanup old sessions: {e}")

    def cleanup_empty_sessions(self):
        """清理所有用户的空会话（message_count为0的会话）"""
        try:
            # 查找所有message_count为0的会话
            empty_sessions = list(ChatBotSession.select().where(
                ChatBotSession.status == "1",
                ChatBotSession.message_count == 0
            ))

            if empty_sessions:
                for session in empty_sessions:
                    self._delete_session_data(session.id)

                self.logger.info(f"Cleaned up {len(empty_sessions)} empty sessions across all users")
                return len(empty_sessions)
            else:
                self.logger.info("No empty sessions found to clean up")
                return 0

        except Exception as e:
            self.logger.error(f"Failed to cleanup empty sessions: {e}")
            return 0

    def cleanup_deleted_messages(self):
        """清理所有status为0的消息记录（物理删除）"""
        try:
            # 查找所有status为0的消息
            deleted_messages = list(ChatBotMessage.select().where(
                ChatBotMessage.status == "0"
            ))

            if deleted_messages:
                # 物理删除这些消息
                deleted_count = ChatBotMessage.delete().where(
                    ChatBotMessage.status == "0"
                ).execute()

                self.logger.info(f"Physically deleted {deleted_count} messages with status=0")
                return deleted_count
            else:
                self.logger.info("No deleted messages found to clean up")
                return 0

        except Exception as e:
            self.logger.error(f"Failed to cleanup deleted messages: {e}")
            return 0

    def cleanup_deleted_sessions(self):
        """清理所有status为0的会话记录（物理删除）"""
        try:
            # 查找所有status为0的会话
            deleted_sessions = list(ChatBotSession.select().where(
                ChatBotSession.status == "0"
            ))

            if deleted_sessions:
                # 物理删除这些会话
                deleted_count = ChatBotSession.delete().where(
                    ChatBotSession.status == "0"
                ).execute()

                self.logger.info(f"Physically deleted {deleted_count} sessions with status=0")
                return deleted_count
            else:
                self.logger.info("No deleted sessions found to clean up")
                return 0

        except Exception as e:
            self.logger.error(f"Failed to cleanup deleted sessions: {e}")
            return 0

    def cleanup_all_deleted_data(self):
        """清理所有已删除的数据（status为0的记录）"""
        try:
            self.logger.info("Starting cleanup of all deleted data...")

            # 清理已删除的消息
            deleted_messages = self.cleanup_deleted_messages()

            # 清理已删除的会话
            deleted_sessions = self.cleanup_deleted_sessions()

            total_cleaned = deleted_messages + deleted_sessions
            self.logger.info(f"Cleanup completed: {deleted_messages} messages + {deleted_sessions} sessions = {total_cleaned} total records deleted")

            return {
                "deleted_messages": deleted_messages,
                "deleted_sessions": deleted_sessions,
                "total_deleted": total_cleaned
            }

        except Exception as e:
            self.logger.error(f"Failed to cleanup all deleted data: {e}")
            return {
                "deleted_messages": 0,
                "deleted_sessions": 0,
                "total_deleted": 0
            }

    def _delete_session_data(self, session_id: str):
        """删除会话及其相关数据"""
        try:
            # 删除会话消息
            ChatBotMessage.update(status="0").where(
                ChatBotMessage.session_id == session_id
            ).execute()

            # 删除会话记录
            ChatBotSession.update(status="0").where(
                ChatBotSession.id == session_id
            ).execute()

            # 清理内存中的会话
            self.memory_manager.clear_session(session_id)

        except Exception as e:
            self.logger.error(f"Failed to delete session data: {e}")

    def _save_message_to_db(self, session_id: str, user_id: str, role: str, content: str, message_id: str, metadata: Optional[Dict] = None):
        """保存消息到数据库"""
        try:
            # 获取当前会话的消息数量作为序号
            message_count = ChatBotMessage.select().where(
                ChatBotMessage.session_id == session_id,
                ChatBotMessage.status == "1"
            ).count()

            sequence_number = message_count + 1
            timestamp = int(time.time() * 1000)

            # 调试日志
            self.logger.info(f"保存消息: session_id={session_id}, role={role}, sequence_number={sequence_number}, content={content[:50]}...")

            # 创建消息记录
            ChatBotMessage.create(
                id=str(uuid.uuid4()),
                session_id=session_id,
                user_id=user_id,
                role=role,
                content=content,
                message_id=message_id,
                sequence_number=sequence_number,
                timestamp=timestamp,
                metadata=metadata or {},
                create_date=datetime.now(),
                update_date=datetime.now(),
                create_time=int(time.time() * 1000),
                update_time=int(time.time() * 1000)
            )

            self.logger.info(f"消息保存成功: sequence_number={sequence_number}, timestamp={timestamp}")

            # 更新会话的消息计数和最后消息时间
            ChatBotSession.update(
                message_count=ChatBotSession.message_count + 1,
                last_message_time=datetime.now(),
                update_date=datetime.now(),
                update_time=int(time.time() * 1000)
            ).where(ChatBotSession.id == session_id).execute()

            # 如果是第一条用户消息，更新会话名称
            if role == "user" and message_count == 0:
                session_name = content[:50] + "..." if len(content) > 50 else content
                ChatBotSession.update(
                    session_name=session_name,
                    first_message=content
                ).where(ChatBotSession.id == session_id).execute()

        except Exception as e:
            self.logger.error(f"Failed to save message to database: {e}")
    
    def chat(self, request: ChatRequest) -> ChatResponse:
        """处理聊天请求（非流式）"""
        try:
            # 验证请求
            validation_error = self._validate_chat_request(request)
            if validation_error:
                return ChatResponse(
                    session_id=request.session_id,
                    message_id="",
                    content="",
                    is_success=False,
                    error_message=validation_error
                )
            
            # 处理用户输入
            processed_input = self.message_processor.process_user_input(request.message)
            if not processed_input.is_valid:
                return ChatResponse(
                    session_id=request.session_id,
                    message_id="",
                    content="",
                    is_success=False,
                    error_message=processed_input.error_message
                )
            
            # 添加用户消息到会话
            user_message_id = str(uuid.uuid4())
            self.memory_manager.add_message(
                session_id=request.session_id,
                role="user",
                content=processed_input.content,
                message_id=user_message_id,
                metadata=request.metadata
            )

            # 检查会话是否已存在于数据库中，如果不存在则先保存会话
            self._save_session_to_database(
                session_id=request.session_id,
                user_id=request.user_id,
                tenant_id=request.tenant_id,
                first_message=processed_input.content
            )

            # 保存用户消息到数据库
            self._save_message_to_db(
                session_id=request.session_id,
                user_id=request.user_id,
                role="user",
                content=processed_input.content,
                message_id=user_message_id,
                metadata=request.metadata
            )
            
            # 获取LLM模型实例
            llm_bundle = self._get_llm_bundle(request.tenant_id, request.llm_name)
            if not llm_bundle:
                return ChatResponse(
                    session_id=request.session_id,
                    message_id="",
                    content="",
                    is_success=False,
                    error_message="无法获取LLM模型实例"
                )
            
            # 准备对话历史
            conversation_history = self._prepare_conversation_history(
                request.session_id, 
                request.system_prompt
            )
            
            # 调用LLM生成回复
            ai_response_text = llm_bundle.chat(
                system=request.system_prompt or self.default_system_prompt,
                history=conversation_history,
                gen_conf=self.default_gen_config
            )

            # 处理AI响应
            processed_response = self.message_processor.format_ai_response(ai_response_text)
            
            # 添加AI回复到会话
            ai_message_id = str(uuid.uuid4())
            self.memory_manager.add_message(
                session_id=request.session_id,
                role="assistant",
                content=processed_response.content,
                message_id=ai_message_id,
                metadata=processed_response.metadata
            )

            # 保存AI回复到数据库
            self._save_message_to_db(
                session_id=request.session_id,
                user_id=request.user_id,
                role="assistant",
                content=processed_response.content,
                message_id=ai_message_id,
                metadata=processed_response.metadata
            )
            
            return ChatResponse(
                session_id=request.session_id,
                message_id=ai_message_id,
                content=processed_response.content,
                is_success=True,
                metadata={
                    "user_message_id": user_message_id,
                    "processing_metadata": processed_response.metadata
                }
            )
            
        except Exception as e:
            self.logger.error(f"Chat error: {str(e)}", exc_info=True)
            return ChatResponse(
                session_id=request.session_id,
                message_id="",
                content="",
                is_success=False,
                error_message=f"处理聊天请求时发生错误: {str(e)}"
            )
    
    def chat_stream(self, request: ChatRequest) -> Generator[str, None, None]:
        """处理聊天请求（流式）"""
        try:
            # 验证请求
            validation_error = self._validate_chat_request(request)
            if validation_error:
                yield self._format_sse_data({"error": validation_error})
                return
            
            # 处理用户输入
            processed_input = self.message_processor.process_user_input(request.message)
            if not processed_input.is_valid:
                yield self._format_sse_data({"error": processed_input.error_message})
                return
            
            # 添加用户消息到会话
            user_message_id = str(uuid.uuid4())
            self.memory_manager.add_message(
                session_id=request.session_id,
                role="user",
                content=processed_input.content,
                message_id=user_message_id,
                metadata=request.metadata
            )

            # 检查会话是否已存在于数据库中，如果不存在则先保存会话
            self._save_session_to_database(
                session_id=request.session_id,
                user_id=request.user_id,
                tenant_id=request.tenant_id,
                first_message=processed_input.content
            )

            # 保存用户消息到数据库
            self._save_message_to_db(
                session_id=request.session_id,
                user_id=request.user_id,
                role="user",
                content=processed_input.content,
                message_id=user_message_id,
                metadata=request.metadata
            )
            
            # 获取LLM模型实例
            llm_bundle = self._get_llm_bundle(request.tenant_id, request.llm_name)
            if not llm_bundle:
                yield self._format_sse_data({"error": "无法获取LLM模型实例"})
                return
            
            # 准备对话历史
            conversation_history = self._prepare_conversation_history(
                request.session_id, 
                request.system_prompt
            )
            
            # 流式生成回复
            full_response = ""
            ai_message_id = str(uuid.uuid4())
            
            for chunk in llm_bundle.chat_streamly(
                system=request.system_prompt or self.default_system_prompt,
                history=conversation_history,
                gen_conf=self.default_gen_config
            ):
                if isinstance(chunk, str):
                    full_response = chunk
                    # 处理并发送流式数据
                    processed_chunk = self.message_processor.format_ai_response(chunk)
                    yield self._format_sse_data({
                        "content": processed_chunk.content,
                        "message_id": ai_message_id
                    })
            
            # 添加完整回复到会话
            if full_response:
                processed_response = self.message_processor.format_ai_response(full_response)
                self.memory_manager.add_message(
                    session_id=request.session_id,
                    role="assistant",
                    content=processed_response.content,
                    message_id=ai_message_id,
                    metadata=processed_response.metadata
                )

                # 保存AI回复到数据库
                self._save_message_to_db(
                    session_id=request.session_id,
                    user_id=request.user_id,
                    role="assistant",
                    content=processed_response.content,
                    message_id=ai_message_id,
                    metadata=processed_response.metadata
                )
            
            yield self._format_sse_data({"done": True, "message_id": ai_message_id})
            
        except Exception as e:
            self.logger.error(f"Stream chat error: {str(e)}", exc_info=True)
            yield self._format_sse_data({"error": f"处理流式聊天请求时发生错误: {str(e)}"})
    
    def get_conversation_history(
        self, 
        session_id: str, 
        limit: Optional[int] = None
    ) -> List[Dict[str, Any]]:
        """获取会话历史"""
        return self.memory_manager.get_conversation_history(
            session_id=session_id,
            limit=limit,
            include_system=False
        )
    
    def clear_session(self, session_id: str) -> bool:
        """清空会话"""
        return self.memory_manager.clear_session(session_id)
    
    def _validate_chat_request(self, request: ChatRequest) -> Optional[str]:
        """验证聊天请求"""
        if not request.session_id:
            return "session_id is required"

        if not request.user_id:
            return "user_id is required"

        if not request.tenant_id:
            return "tenant_id is required"

        if not request.message:
            return "message is required"

        # 检查会话是否存在（先检查内存，再检查数据库）
        session = self.memory_manager.get_session(request.session_id)
        if not session:
            # 如果内存中没有，尝试从数据库加载历史会话
            try:
                db_session = ChatBotSession.get(
                    ChatBotSession.id == request.session_id,
                    ChatBotSession.user_id == request.user_id,
                    ChatBotSession.status == "1"
                )

                # 重新创建内存会话
                session = self.memory_manager.create_session(request.session_id, request.user_id)

                # 添加系统提示消息
                self.memory_manager.add_message(
                    session_id=request.session_id,
                    role="system",
                    content=db_session.system_prompt or self.default_system_prompt,
                    message_id=str(uuid.uuid4()),
                    metadata={"tenant_id": request.tenant_id, "user_id": request.user_id}
                )

                # 加载历史消息到内存
                messages = ChatBotMessage.select().where(
                    ChatBotMessage.session_id == request.session_id,
                    ChatBotMessage.status == "1"
                ).order_by(ChatBotMessage.sequence_number.asc())

                for msg in messages:
                    if msg.role != "system":  # 系统消息已经添加过了
                        self.memory_manager.add_message(
                            session_id=request.session_id,
                            role=msg.role,
                            content=msg.content,
                            message_id=msg.message_id,
                            metadata={"tenant_id": request.tenant_id, "user_id": request.user_id}
                        )

                self.logger.info(f"Restored session from database: {request.session_id}")

            except ChatBotSession.DoesNotExist:
                return "Session not found"
            except Exception as e:
                self.logger.error(f"Failed to restore session from database: {e}")
                return "Session not found"

        return None
    
    def _get_llm_bundle(self, tenant_id: str, llm_name: Optional[str] = None) -> Optional[LLMBundle]:
        """获取LLM模型实例"""
        try:
            # 如果没有指定llm_name，从tenant配置中获取默认的llm_id
            if not llm_name:
                _, tenant = TenantService.get_by_id(tenant_id)
                if tenant and tenant.llm_id:
                    llm_name = tenant.llm_id
                    self.logger.info(f"Using tenant default LLM: {llm_name}")
                else:
                    self.logger.error(f"No LLM configured for tenant: {tenant_id}")
                    return None

            self.logger.info(f"Creating LLMBundle for tenant: {tenant_id}, llm: {llm_name}")

            # 调试：检查模型名称分割
            mdlnm, fid = TenantLLMService.split_model_name_and_factory(llm_name)
            self.logger.info(f"Split model name: mdlnm={mdlnm}, fid={fid}")

            # 调试：检查TenantLLM表中的记录
            tenant_llms = TenantLLMService.get_my_llms(tenant_id)
            chat_llms = [llm for llm in tenant_llms if llm['model_type'] == 'chat']
            chat_llm_names = [f"{llm['llm_name']}@{llm['llm_factory']}" for llm in chat_llms]
            self.logger.info(f"Available tenant chat LLMs: {chat_llm_names}")

            # 尝试修复模型名称格式
            if not fid and ':' in llm_name:
                # 如果模型名称格式是 "Factory:ModelName"，尝试转换为 "ModelName@Factory"
                parts = llm_name.split(':', 1)
                if len(parts) == 2:
                    factory, model = parts
                    corrected_name = f"{model}@{factory}"
                    self.logger.info(f"Trying corrected model name: {corrected_name}")

                    # 检查修正后的名称是否存在
                    corrected_mdlnm, corrected_fid = TenantLLMService.split_model_name_and_factory(corrected_name)
                    if corrected_fid:
                        # 检查是否有匹配的记录
                        matching_llm = next((llm for llm in chat_llms
                                           if llm['llm_name'] == corrected_mdlnm and llm['llm_factory'] == corrected_fid), None)
                        if matching_llm:
                            self.logger.info(f"Found matching LLM with corrected name: {corrected_name}")
                            llm_name = corrected_name

            return LLMBundle(tenant_id, LLMType.CHAT, llm_name)
        except Exception as e:
            self.logger.error(f"Failed to get LLM bundle: {str(e)}")
            return None
    
    def _prepare_conversation_history(
        self, 
        session_id: str, 
        system_prompt: Optional[str] = None
    ) -> List[Dict[str, str]]:
        """准备对话历史"""
        history = self.memory_manager.get_conversation_history(
            session_id=session_id,
            include_system=False
        )
        
        # 转换为LLM期望的格式
        formatted_history = []
        
        # 添加系统提示（如果提供）
        if system_prompt:
            formatted_history.append({
                "role": "system",
                "content": system_prompt
            })
        
        # 添加对话历史
        for msg in history:
            formatted_history.append({
                "role": msg["role"],
                "content": msg["content"]
            })
        
        return formatted_history
    
    def get_service_stats(self) -> Dict[str, Any]:
        """获取服务统计信息"""
        return {
            "memory_stats": self.memory_manager.get_session_stats(),
            "processor_stats": self.message_processor.get_processor_stats(),
            "default_gen_config": self.default_gen_config
        }

    def get_user_sessions(self, user_id: str, limit: int = 10, offset: int = 0) -> Dict[str, Any]:
        """获取用户的会话列表"""
        try:
            # 查询用户的会话，按最后消息时间排序
            sessions_query = ChatBotSession.select().where(
                ChatBotSession.user_id == user_id,
                ChatBotSession.status == "1"
            ).order_by(ChatBotSession.last_message_time.desc()).limit(limit).offset(offset)

            sessions = []
            for session in sessions_query:
                # 如果session_name为空，尝试更新
                if not session.session_name:
                    self._update_session_name_if_empty(session.id)
                    # 重新获取更新后的会话
                    try:
                        updated_session = ChatBotSession.get(ChatBotSession.id == session.id)
                        session_name = updated_session.session_name or "新对话"
                    except:
                        session_name = "新对话"
                else:
                    session_name = session.session_name

                sessions.append({
                    "session_id": session.id,
                    "session_name": session_name,
                    "first_message": session.first_message,
                    "message_count": session.message_count,
                    "last_message_time": session.last_message_time.isoformat() if session.last_message_time else None,
                    "create_time": session.create_date.isoformat() if session.create_date else None
                })

            # 获取总数
            total = ChatBotSession.select().where(
                ChatBotSession.user_id == user_id,
                ChatBotSession.status == "1"
            ).count()

            return {
                "sessions": sessions,
                "total": total
            }

        except Exception as e:
            self.logger.error(f"Failed to get user sessions: {e}")
            return {"sessions": [], "total": 0}

    def get_session_details(self, session_id: str, user_id: str, limit: Optional[int] = None) -> Optional[Dict[str, Any]]:
        """获取会话详情和消息历史"""
        try:
            # 验证会话所有权
            try:
                session = ChatBotSession.get(
                    ChatBotSession.id == session_id,
                    ChatBotSession.user_id == user_id,
                    ChatBotSession.status == "1"
                )
            except ChatBotSession.DoesNotExist:
                return None

            # 获取会话消息
            messages_query = ChatBotMessage.select().where(
                ChatBotMessage.session_id == session_id,
                ChatBotMessage.status == "1"
            ).order_by(ChatBotMessage.sequence_number.asc())

            if limit:
                messages_query = messages_query.limit(limit)

            messages = []
            for msg in messages_query:
                # 跳过系统消息
                if msg.role == "system":
                    continue

                messages.append({
                    "role": msg.role,
                    "content": msg.content,
                    "timestamp": msg.timestamp,
                    "message_id": msg.message_id,
                    "sequence_number": msg.sequence_number
                })

            return {
                "session": {
                    "session_id": session.id,
                    "session_name": session.session_name or "新对话",
                    "first_message": session.first_message,
                    "message_count": session.message_count,
                    "last_message_time": session.last_message_time.isoformat() if session.last_message_time else None,
                    "create_time": session.create_date.isoformat() if session.create_date else None
                },
                "messages": messages
            }

        except Exception as e:
            self.logger.error(f"Failed to get session details: {e}")
            return None

    def delete_session(self, session_id: str, user_id: str) -> bool:
        """删除用户的会话"""
        try:
            # 验证会话所有权
            try:
                session = ChatBotSession.get(
                    ChatBotSession.id == session_id,
                    ChatBotSession.user_id == user_id,
                    ChatBotSession.status == "1"
                )
            except ChatBotSession.DoesNotExist:
                return False

            # 删除会话数据
            self._delete_session_data(session_id)
            return True

        except Exception as e:
            self.logger.error(f"Failed to delete session: {e}")
            return False
