# Backend Dockerfile for HanBangGaoKe API Service
# Multi-stage build for optimized production image
# Based on original RAGFlow Dockerfile architecture

# Base stage with common dependencies
FROM ubuntu:22.04 AS base
USER root
SHELL ["/bin/bash", "-c"]

ARG NEED_MIRROR=1
ARG LIGHTEN=1
ENV LIGHTEN=${LIGHTEN}

WORKDIR /ragflow

# Set environment variables
ENV PYTHONDONTWRITEBYTECODE=1 \
    PYTHONUNBUFFERED=1 \
    DEBIAN_FRONTEND=noninteractive \
    TZ=Asia/Shanghai \
    DOTNET_SYSTEM_GLOBALIZATION_INVARIANT=1 \
    # Python optimization
    PYTHONHASHSEED=random \
    PYTHONIOENCODING=utf-8 \
    # Chrome/Selenium configuration
    CHROME_BIN=/usr/local/bin/chrome \
    CHROMEDRIVER_PATH=/usr/local/bin/chromedriver \
    # Memory optimization
    MALLOC_ARENA_MAX=2 \
    # Locale settings
    LANG=C.UTF-8 \
    LC_ALL=C.UTF-8

# Create necessary directories
RUN mkdir -p /ragflow/rag/res/deepdoc /root/.ragflow /ragflow/logs /ragflow/data /ragflow/temp

# Copy models from huggingface.co directory
COPY huggingface.co /tmp/huggingface.co
COPY maidalun1020 /tmp/maidalun1020
RUN cp /tmp/huggingface.co/InfiniFlow/huqie/huqie.txt.trie /ragflow/rag/res/ && \
    tar --exclude='.*' -cf - \
        /tmp/huggingface.co/InfiniFlow/text_concat_xgb_v1.0 \
        /tmp/huggingface.co/InfiniFlow/deepdoc \
        | tar -xf - --strip-components=4 -C /ragflow/rag/res/deepdoc && \
    if [ "$LIGHTEN" != "1" ]; then \
        (tar -cf - \
            /tmp/huggingface.co/BAAI/bge-large-zh-v1.5 \
            /tmp/maidalun1020/bce-embedding-base_v1 \
            | tar -xf - --strip-components=2 -C /root/.ragflow) \
    fi && \
    rm -rf /tmp/huggingface.co /tmp/maidalun1020

# Copy additional required files
COPY nltk_data /root/nltk_data
COPY tika-server-standard-3.0.0.jar /ragflow/tika-server-standard-3.0.0.jar
COPY cl100k_base.tiktoken /ragflow/9b5ad71b2ce5302211f9c61530b329a4922fc6a4

# Set Tika server environment variable
ENV TIKA_SERVER_JAR="file:///ragflow/tika-server-standard-3.0.0.jar"

# Setup apt with cache optimization
RUN --mount=type=cache,id=ragflow_apt,target=/var/cache/apt,sharing=locked \
    if [ "$NEED_MIRROR" == "1" ]; then \
        sed -i 's|http://ports.ubuntu.com|http://mirrors.tuna.tsinghua.edu.cn|g' /etc/apt/sources.list; \
        sed -i 's|http://archive.ubuntu.com|http://mirrors.tuna.tsinghua.edu.cn|g' /etc/apt/sources.list; \
    fi; \
    rm -f /etc/apt/apt.conf.d/docker-clean && \
    echo 'Binary::apt::APT::Keep-Downloaded-Packages "true";' > /etc/apt/apt.conf.d/keep-cache && \
    chmod 1777 /tmp && \
    apt update && \
    apt --no-install-recommends install -y ca-certificates && \
    apt update

# Install core system dependencies
RUN --mount=type=cache,id=ragflow_apt,target=/var/cache/apt,sharing=locked \
    apt install -y \
        # Python and build tools
        python3-pip python3-dev python3-venv python3.10-venv build-essential \
        # OpenCV dependencies
        libglib2.0-0 libglx-mesa0 libgl1 \
        # Aspose dependencies
        pkg-config libicu-dev libgdiplus \
        # Java for Tika
        default-jdk \
        # Selenium dependencies
        libatk-bridge2.0-0 libgtk-4-1 libnss3 xdg-utils libgbm-dev \
        # Memory optimization
        libjemalloc-dev \
        # System utilities
        curl wget git unzip vim less \
        # Document processing
        ghostscript \
        # LibreOffice for document conversion
        libreoffice \
        # Environment substitution
        gettext-base \
        # Network tools
        nginx \
        # Basic ODBC support (fallback for Microsoft ODBC)
        unixodbc-dev

# Set timezone
RUN ln -snf /usr/share/zoneinfo/$TZ /etc/localtime && echo $TZ > /etc/timezone

# Install pipx and uv for Python package management
RUN if [ "$NEED_MIRROR" == "1" ]; then \
        pip3 config set global.index-url https://mirrors.aliyun.com/pypi/simple && \
        pip3 config set global.trusted-host mirrors.aliyun.com && \
        mkdir -p /etc/uv && \
        echo "[[index]]" > /etc/uv/uv.toml && \
        echo 'url = "https://mirrors.aliyun.com/pypi/simple"' >> /etc/uv/uv.toml && \
        echo "default = true" >> /etc/uv/uv.toml; \
    fi && \
    pip3 install --upgrade pip && \
    pip3 install pipx && \
    export PATH="/root/.local/bin:$PATH" && \
    pipx install uv

ENV PATH=/root/.local/bin:$PATH

# Add Microsoft ODBC driver for SQL Server (optional)
# This is required for pyodbc to work properly with SQL Server
# Made optional to handle network connectivity issues during build
RUN --mount=type=cache,id=ragflow_apt,target=/var/cache/apt,sharing=locked \
    echo "Attempting to install Microsoft ODBC drivers..." && \
    ( \
        # Try to install Microsoft ODBC drivers with timeout
        timeout 60 curl -f https://packages.microsoft.com/keys/microsoft.asc | apt-key add - && \
        timeout 60 curl -f https://packages.microsoft.com/config/ubuntu/22.04/prod.list > /etc/apt/sources.list.d/mssql-release.list && \
        apt update && \
        arch="$(uname -m)"; \
        if [ "$arch" = "arm64" ] || [ "$arch" = "aarch64" ]; then \
            # ARM64 (macOS/Apple Silicon or Linux aarch64)
            ACCEPT_EULA=Y apt install -y unixodbc-dev msodbcsql18; \
        else \
            # x86_64 or others
            ACCEPT_EULA=Y apt install -y unixodbc-dev msodbcsql17; \
        fi && \
        echo "Microsoft ODBC drivers installed successfully" \
    ) || ( \
        echo "Warning: Failed to install Microsoft ODBC drivers - continuing without them" && \
        echo "This may affect SQL Server connectivity features" && \
        # Install basic ODBC support as fallback
        apt install -y unixodbc-dev && \
        echo "Basic ODBC support installed as fallback" \
    )

# Install additional SSL libraries for compatibility
# This addresses potential SSL library issues with various components
RUN --mount=type=cache,id=ragflow_apt,target=/var/cache/apt,sharing=locked \
    apt install -y libssl-dev libssl3 openssl

# Install libssl1.1 for aspose-slides compatibility
COPY libssl1.1_1.1.1f-1ubuntu2_amd64.deb /tmp/libssl1.1_amd64.deb
COPY libssl1.1_1.1.1f-1ubuntu2_arm64.deb /tmp/libssl1.1_arm64.deb
RUN if [ "$(uname -m)" = "x86_64" ]; then \
        dpkg -i /tmp/libssl1.1_amd64.deb; \
    elif [ "$(uname -m)" = "aarch64" ]; then \
        dpkg -i /tmp/libssl1.1_arm64.deb; \
    fi && \
    rm -f /tmp/libssl1.1_*.deb

# Add Chrome and ChromeDriver for Selenium support
# Copy Chrome browser
COPY chrome-linux64.zip /tmp/chrome-linux64.zip
RUN unzip /tmp/chrome-linux64.zip -d /tmp/ && \
    mv /tmp/chrome-linux64 /opt/chrome && \
    ln -s /opt/chrome/chrome /usr/local/bin/ && \
    rm -f /tmp/chrome-linux64.zip

# Copy ChromeDriver
COPY chromedriver-linux64.zip /tmp/chromedriver-linux64.zip
RUN unzip /tmp/chromedriver-linux64.zip -d /tmp/ && \
    mv /tmp/chromedriver-linux64/chromedriver /usr/local/bin/ && \
    chmod +x /usr/local/bin/chromedriver && \
    rm -rf /tmp/chromedriver-linux64* && \
    rm -f /usr/bin/google-chrome

# Note: PyTorch will be installed in the builder stage within the virtual environment

# Clean up temporary files and caches to reduce image size
RUN apt-get clean && \
    rm -rf /var/lib/apt/lists/* && \
    rm -rf /tmp/* && \
    rm -rf /var/tmp/* && \
    find /usr/local -name '*.pyc' -delete && \
    find /usr/local -name '__pycache__' -delete

# Builder stage
FROM base AS builder
USER root

WORKDIR /ragflow

# Install Python dependencies from uv.lock file
COPY pyproject.toml uv.lock ./

# Install Python dependencies with cache optimization
# https://github.com/astral-sh/uv/issues/10462
# uv records index url into uv.lock but doesn't failover among multiple indexes
RUN --mount=type=cache,id=ragflow_uv,target=/root/.cache/uv,sharing=locked \
    if [ "$NEED_MIRROR" == "1" ]; then \
        sed -i 's|pypi.org|mirrors.aliyun.com/pypi|g' uv.lock; \
    else \
        sed -i 's|mirrors.aliyun.com/pypi|pypi.org|g' uv.lock; \
    fi; \
    if [ "$LIGHTEN" == "1" ]; then \
        uv sync --python 3.10 --frozen; \
    else \
        uv sync --python 3.10 --frozen --all-extras; \
    fi

# Install PyTorch CPU version in the virtual environment using uv
RUN echo "Installing PyTorch CPU version in virtual environment..." && \
    uv add torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cpu && \
    echo "PyTorch installation completed. Verifying installation..." && \
    uv run python -c "import torch; import torchvision; import torchaudio; print(f'PyTorch version: {torch.__version__}'); print(f'TorchVision version: {torchvision.__version__}'); print(f'TorchAudio version: {torchaudio.__version__}'); print(f'CUDA available: {torch.cuda.is_available()}'); x = torch.randn(2, 2); y = x.sum(); print(f'Basic tensor test: SUCCESS - {y.item():.2f}'); print('PyTorch CPU installation verified successfully!')"

# Install AI Reading module dependencies
COPY ai_reading/requirements.txt ./ai_reading_requirements.txt
RUN echo "Installing AI Reading module dependencies..." && \
    uv add gradio_client==1.10.4 requests python-dotenv pydantic && \
    echo "AI Reading dependencies installed successfully" && \
    rm -f ./ai_reading_requirements.txt

# Generate version information
RUN echo "HanBangGaoKe Backend $(date '+%Y%m%d-%H%M%S')" > /ragflow/VERSION

# Production stage
FROM base AS production
USER root

WORKDIR /ragflow

# Copy Python environment and packages from builder
ENV VIRTUAL_ENV=/ragflow/.venv
COPY --from=builder ${VIRTUAL_ENV} ${VIRTUAL_ENV}
ENV PATH="${VIRTUAL_ENV}/bin:${PATH}"

# Set Python path
ENV PYTHONPATH=/ragflow/

# Copy application code
COPY api api
COPY ai_reading ai_reading
COPY ai ai
COPY rag rag
COPY deepdoc deepdoc
COPY conf conf
COPY agent agent
COPY graphrag graphrag
COPY agentic_reasoning agentic_reasoning
COPY mcp mcp
COPY mcp_client mcp_client
COPY plugin plugin
COPY pyproject.toml uv.lock ./

# Copy configuration files
COPY docker/service_conf.yaml.template ./conf/service_conf.yaml.template

# Copy version information
COPY --from=builder /ragflow/VERSION /ragflow/VERSION

# Copy NLTK data from host if available
COPY nltk_data /ragflow/nltk_data

# Copy the backend-specific entrypoint script
COPY docker/entrypoint-backend.sh /ragflow/entrypoint.sh

# Set executable permissions
RUN chmod +x /ragflow/entrypoint.sh

# Create a non-root user for security
RUN groupadd -r ragflow && useradd -r -g ragflow ragflow

# Set proper permissions and create necessary directories
RUN chown -R ragflow:ragflow /ragflow && \
    chmod -R 755 /ragflow && \
    # Create directories that might be needed at runtime
    mkdir -p /ragflow/logs /ragflow/data /ragflow/temp /ragflow/.cache && \
    chown -R ragflow:ragflow /ragflow/logs /ragflow/data /ragflow/temp /ragflow/.cache && \
    # Ensure Chrome and ChromeDriver are executable
    chmod +x /usr/local/bin/chrome /usr/local/bin/chromedriver && \
    # Set ragflow user's home directory to /ragflow to avoid /home/<USER>
    usermod -d /ragflow ragflow

# Switch to non-root user for security
USER ragflow

# Set working directory
WORKDIR /ragflow

# Verify PyTorch is available in production environment
RUN echo "=== Final PyTorch Verification in Production Stage ===" && \
    python -c "import torch; print(f'PyTorch version: {torch.__version__}'); print(f'CUDA available: {torch.cuda.is_available()}'); x = torch.tensor([1.0, 2.0, 3.0]); y = x * 2; print(f'Tensor operations: SUCCESS - {y.tolist()}'); print('PyTorch is ready for production use!')" && \
    echo "PyTorch verification completed successfully!"

# Set runtime environment variables
ENV RAGFLOW_HOME=/ragflow \
    NLTK_DATA=/ragflow/nltk_data \
    # Default service configuration
    HOST_IP=0.0.0.0 \
    HTTP_PORT=9380 \
    # Chrome configuration for headless mode
    DISPLAY=:99 \
    CHROME_OPTS="--headless --no-sandbox --disable-dev-shm-usage --disable-gpu"

# Expose port
EXPOSE 9380

# Use the enhanced entrypoint script
ENTRYPOINT ["/ragflow/entrypoint.sh"]
