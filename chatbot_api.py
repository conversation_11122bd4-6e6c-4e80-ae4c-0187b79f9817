#
#  Copyright 2024 The InfiniFlow Authors. All Rights Reserved.
#
#  Licensed under the Apache License, Version 2.0 (the "License");
#  you may not use this file except in compliance with the License.
#  You may obtain a copy of the License at
#
#      http://www.apache.org/licenses/LICENSE-2.0
#
#  Unless required by applicable law or agreed to in writing, software
#  distributed under the License is distributed on an "AS IS" BASIS,
#  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
#  See the License for the specific language governing permissions and
#  limitations under the License.
#

"""
聊天机器人API接口模块

提供RESTful API接口，支持创建会话、发送消息、
获取历史记录等功能。
"""

import json
import logging
from flask import Blueprint, request, Response, jsonify, stream_template
from flask_login import login_required, current_user

from api.utils.api_utils import get_json_result, get_data_error_result, server_error_response
from .chatbot_service import ChatBotService, ChatRequest


# 创建蓝图 - 使用唯一名称避免与RAGFlow自动注册的蓝图冲突
chatbot_bp = Blueprint('ai_chatbot', __name__, url_prefix='/api/v1/chatbot')

# 全局服务实例
_chatbot_service = None


def get_chatbot_service() -> ChatBotService:
    """获取聊天机器人服务实例"""
    global _chatbot_service
    if _chatbot_service is None:
        _chatbot_service = ChatBotService()
    return _chatbot_service


@chatbot_bp.route('/sessions', methods=['POST'])
@login_required
def create_session():
    """创建新的聊天会话
    
    Request Body:
    {
        "user_id": "string (optional, defaults to current_user.id)",
        "system_prompt": "string (optional)"
    }
    
    Response:
    {
        "code": 0,
        "data": {
            "session_id": "string"
        }
    }
    """
    try:
        req_data = request.get_json() or {}
        user_id = req_data.get('user_id', current_user.id)

        # 从请求参数获取tenant_id，如果没有则使用当前用户的ID作为tenant_id
        tenant_id = req_data.get('tenant_id')
        if not tenant_id:
            # 在RAGFlow中，用户注册时会创建一个与用户ID相同的tenant_id
            tenant_id = current_user.id
        
        if not user_id:
            return get_data_error_result(message="user_id is required")
        
        if not tenant_id:
            return get_data_error_result(message="tenant_id is required")
        
        chatbot_service = get_chatbot_service()
        session_id = chatbot_service.create_session(user_id, tenant_id)
        
        return get_json_result(data={"session_id": session_id})
        
    except Exception as e:
        logging.error(f"Create session error: {str(e)}", exc_info=True)
        return server_error_response(e)


@chatbot_bp.route('/sessions/<session_id>/messages', methods=['POST'])
@login_required
def send_message(session_id: str):
    """发送消息到指定会话
    
    Request Body:
    {
        "message": "string",
        "stream": "boolean (optional, default: false)",
        "system_prompt": "string (optional)"
    }
    
    Response (non-stream):
    {
        "code": 0,
        "data": {
            "message_id": "string",
            "content": "string",
            "metadata": {}
        }
    }
    
    Response (stream):
    Server-Sent Events format
    """
    try:
        req_data = request.get_json()
        if not req_data:
            return get_data_error_result(message="Request body is required")
        
        message = req_data.get('message')
        if not message:
            return get_data_error_result(message="message is required")
        
        stream = req_data.get('stream', False)
        system_prompt = req_data.get('system_prompt')
        llm_name = req_data.get('llm_name')  # 从请求中获取指定的LLM模型

        # 从请求数据获取tenant_id，如果没有则使用当前用户ID
        req_data = request.get_json() or {}
        tenant_id = req_data.get('tenant_id', current_user.id)

        chat_request = ChatRequest(
            session_id=session_id,
            user_id=current_user.id,
            message=message,
            tenant_id=tenant_id,
            stream=stream,
            system_prompt=system_prompt,
            llm_name=llm_name,
            metadata={
                "request_timestamp": request.headers.get('X-Request-Timestamp'),
                "user_agent": request.headers.get('User-Agent')
            }
        )
        
        chatbot_service = get_chatbot_service()
        
        if stream:
            # 流式响应
            def generate():
                for chunk in chatbot_service.chat_stream(chat_request):
                    yield chunk
            
            return Response(
                generate(),
                mimetype='text/event-stream',
                headers={
                    'Cache-Control': 'no-cache',
                    'Connection': 'keep-alive',
                    'Access-Control-Allow-Origin': '*'
                }
            )
        else:
            # 非流式响应
            response = chatbot_service.chat(chat_request)
            
            if not response.is_success:
                return get_data_error_result(message=response.error_message)
            
            return get_json_result(data={
                "message_id": response.message_id,
                "content": response.content,
                "metadata": response.metadata
            })
            
    except Exception as e:
        logging.error(f"Send message error: {str(e)}", exc_info=True)
        return server_error_response(e)


@chatbot_bp.route('/sessions/<session_id>/history', methods=['GET'])
@login_required
def get_conversation_history(session_id: str):
    """获取会话历史记录
    
    Query Parameters:
    - limit: int (optional) - 限制返回的消息数量
    
    Response:
    {
        "code": 0,
        "data": {
            "messages": [
                {
                    "role": "user|assistant",
                    "content": "string",
                    "timestamp": "float",
                    "message_id": "string",
                    "metadata": {}
                }
            ]
        }
    }
    """
    try:
        limit = request.args.get('limit', type=int)
        
        chatbot_service = get_chatbot_service()
        history = chatbot_service.get_conversation_history(session_id, limit)
        
        return get_json_result(data={"messages": history})
        
    except Exception as e:
        logging.error(f"Get conversation history error: {str(e)}", exc_info=True)
        return server_error_response(e)


@chatbot_bp.route('/sessions/<session_id>', methods=['DELETE'])
@login_required
def clear_session(session_id: str):
    """清空指定会话
    
    Response:
    {
        "code": 0,
        "data": {
            "success": true
        }
    }
    """
    try:
        chatbot_service = get_chatbot_service()
        success = chatbot_service.clear_session(session_id)
        
        if not success:
            return get_data_error_result(message="Session not found or already cleared")
        
        return get_json_result(data={"success": True})
        
    except Exception as e:
        logging.error(f"Clear session error: {str(e)}", exc_info=True)
        return server_error_response(e)


@chatbot_bp.route('/stats', methods=['GET'])
def get_service_stats():
    """获取服务统计信息
    
    Response:
    {
        "code": 0,
        "data": {
            "memory_stats": {},
            "processor_stats": {},
            "default_gen_config": {}
        }
    }
    """
    try:
        chatbot_service = get_chatbot_service()
        stats = chatbot_service.get_service_stats()
        
        return get_json_result(data=stats)
        
    except Exception as e:
        logging.error(f"Get service stats error: {str(e)}", exc_info=True)
        return server_error_response(e)


@chatbot_bp.route('/health', methods=['GET'])
def health_check():
    """健康检查接口

    Response:
    {
        "code": 0,
        "data": {
            "status": "healthy",
            "timestamp": "float"
        }
    }
    """
    try:
        import time
        return get_json_result(data={
            "status": "healthy",
            "timestamp": time.time()
        })

    except Exception as e:
        logging.error(f"Health check error: {str(e)}", exc_info=True)
        return server_error_response(e)


@chatbot_bp.route('/sessions', methods=['GET'])
@login_required
def get_user_sessions():
    """获取用户的对话记录列表

    Query Parameters:
    - limit: int (optional, default: 10) - 限制返回的会话数量
    - offset: int (optional, default: 0) - 偏移量

    Response:
    {
        "code": 0,
        "data": {
            "sessions": [
                {
                    "session_id": "string",
                    "session_name": "string",
                    "first_message": "string",
                    "message_count": "int",
                    "last_message_time": "string",
                    "create_time": "string"
                }
            ],
            "total": "int"
        }
    }
    """
    try:
        limit = request.args.get('limit', 10, type=int)
        offset = request.args.get('offset', 0, type=int)

        # 限制最大返回数量
        limit = min(limit, 50)

        chatbot_service = get_chatbot_service()
        sessions_data = chatbot_service.get_user_sessions(current_user.id, limit, offset)

        return get_json_result(data=sessions_data)

    except Exception as e:
        logging.error(f"Get user sessions error: {str(e)}", exc_info=True)
        return server_error_response(e)


@chatbot_bp.route('/sessions/<session_id>/details', methods=['GET'])
@login_required
def get_session_details(session_id: str):
    """获取特定会话的详细信息和消息历史

    Query Parameters:
    - limit: int (optional) - 限制返回的消息数量

    Response:
    {
        "code": 0,
        "data": {
            "session": {
                "session_id": "string",
                "session_name": "string",
                "first_message": "string",
                "message_count": "int",
                "last_message_time": "string",
                "create_time": "string"
            },
            "messages": [
                {
                    "role": "user|assistant",
                    "content": "string",
                    "timestamp": "float",
                    "message_id": "string",
                    "sequence_number": "int"
                }
            ]
        }
    }
    """
    try:
        limit = request.args.get('limit', type=int)

        chatbot_service = get_chatbot_service()
        session_details = chatbot_service.get_session_details(session_id, current_user.id, limit)

        if not session_details:
            return get_data_error_result(message="Session not found or access denied")

        return get_json_result(data=session_details)

    except Exception as e:
        logging.error(f"Get session details error: {str(e)}", exc_info=True)
        return server_error_response(e)


@chatbot_bp.route('/sessions/<session_id>/delete', methods=['DELETE'])
@login_required
def delete_session(session_id: str):
    """删除指定的会话记录

    Response:
    {
        "code": 0,
        "data": {
            "success": true
        }
    }
    """
    try:
        chatbot_service = get_chatbot_service()
        success = chatbot_service.delete_session(session_id, current_user.id)

        if not success:
            return get_data_error_result(message="Session not found or access denied")

        return get_json_result(data={"success": True})

    except Exception as e:
        logging.error(f"Delete session error: {str(e)}", exc_info=True)
        return server_error_response(e)


@chatbot_bp.route('/cleanup', methods=['POST'])
@login_required
def cleanup_deleted_data():
    """清理已删除的数据

    清理所有status为0的消息和会话记录（物理删除）

    Response:
    {
        "code": 0,
        "data": {
            "deleted_messages": 10,
            "deleted_sessions": 5,
            "total_deleted": 15
        }
    }
    """
    try:
        chatbot_service = get_chatbot_service()
        result = chatbot_service.cleanup_all_deleted_data()

        return get_json_result(data=result)

    except Exception as e:
        logging.error(f"Cleanup deleted data error: {str(e)}", exc_info=True)
        return server_error_response(e)


# 错误处理器
@chatbot_bp.errorhandler(500)
def internal_error(error):
    """处理500错误"""
    logging.error(f"Internal server error: {str(error)}", exc_info=True)
    return get_data_error_result(message="Internal server error"), 500
