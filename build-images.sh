#!/bin/bash

# 镜像构建脚本
# 只在代码更新时运行，避免每次部署都重新构建

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 显示帮助信息
show_help() {
    echo "汉邦高科 Docker 镜像构建脚本"
    echo ""
    echo "用法: $0 [选项]"
    echo ""
    echo "选项:"
    echo "  --frontend      仅构建前端镜像"
    echo "  --backend       仅构建后端镜像"
    echo "  --all           构建所有镜像 (默认)"
    echo "  --force         强制重新构建 (--no-cache)"
    echo "  --robust        使用robust版本的后端Dockerfile"
    echo "  --tag TAG       指定镜像标签 (默认: latest)"
    echo "  --help, -h      显示此帮助信息"
    echo ""
    echo "示例:"
    echo "  $0                    # 构建所有镜像"
    echo "  $0 --frontend         # 仅构建前端"
    echo "  $0 --backend --robust # 构建robust版本后端"
    echo "  $0 --force            # 强制重新构建所有镜像"
    echo "  $0 --tag v1.0.0       # 使用v1.0.0标签"
}

# 检查Docker
check_docker() {
    if ! command -v docker &> /dev/null; then
        log_error "Docker 未安装"
        exit 1
    fi
    
    if ! docker info &> /dev/null; then
        log_error "Docker 服务未运行"
        exit 1
    fi
}

# 构建前端镜像
build_frontend() {
    local tag="${1:-latest}"
    local force_build="$2"
    
    log_info "构建前端镜像..."
    
    local build_args=""
    if [ "$force_build" = "true" ]; then
        build_args="--no-cache"
    fi
    
    if [ -d "../hbweb" ]; then
        cd ../hbweb
        docker build $build_args -t hbgk-frontend:$tag .
        cd - > /dev/null
        log_success "前端镜像构建完成: hbgk-frontend:$tag"
    else
        log_error "前端源码目录不存在: ../hbweb"
        return 1
    fi
}

# 构建后端镜像
build_backend() {
    local tag="${1:-latest}"
    local force_build="$2"
    local dockerfile="${3:-Dockerfile.backend.robust}"
    
    log_info "构建后端镜像 (使用 $dockerfile)..."
    
    local build_args=""
    if [ "$force_build" = "true" ]; then
        build_args="--no-cache"
    fi
    
    cd ..
    docker build $build_args -f sz/$dockerfile -t hbgk-api:$tag .
    cd - > /dev/null
    log_success "后端镜像构建完成: hbgk-api:$tag"
}

# 主函数
main() {
    local build_frontend=false
    local build_backend=false
    local build_all=true
    local force_build=false
    local use_robust=false
    local tag="latest"
    
    # 解析命令行参数
    while [[ $# -gt 0 ]]; do
        case $1 in
            --frontend)
                build_frontend=true
                build_all=false
                shift
                ;;
            --backend)
                build_backend=true
                build_all=false
                shift
                ;;
            --all)
                build_all=true
                shift
                ;;
            --force)
                force_build=true
                shift
                ;;
            --robust)
                use_robust=true
                shift
                ;;
            --tag)
                tag="$2"
                shift 2
                ;;
            -h|--help)
                show_help
                exit 0
                ;;
            *)
                log_error "未知参数: $1"
                show_help
                exit 1
                ;;
        esac
    done
    
    echo "========================================"
    echo "汉邦高科 Docker 镜像构建"
    echo "========================================"
    echo
    
    check_docker
    
    log_info "构建配置:"
    echo "  标签: $tag"
    echo "  强制重建: $force_build"
    echo "  使用Robust版本: $use_robust"
    echo
    
    local dockerfile="Dockerfile.backend"
    if [ "$use_robust" = "true" ]; then
        dockerfile="Dockerfile.backend.robust"
    fi
    
    # 执行构建
    if [ "$build_all" = "true" ]; then
        build_frontend "$tag" "$force_build"
        build_backend "$tag" "$force_build" "$dockerfile"
    else
        if [ "$build_frontend" = "true" ]; then
            build_frontend "$tag" "$force_build"
        fi
        
        if [ "$build_backend" = "true" ]; then
            build_backend "$tag" "$force_build" "$dockerfile"
        fi
    fi
    
    echo
    log_success "镜像构建完成！"
    
    log_info "构建的镜像:"
    docker images | grep -E "hbgk-(frontend|api)" | head -10
    
    echo
    log_info "现在可以使用以下命令快速部署:"
    echo "  ./deploy-fast.sh -m simple"
    echo "  ./deploy-fast.sh -m full"
}

# 运行主函数
main "$@"
