
// AI chatbot问题诊断脚本
// 在浏览器控制台中运行

console.log("=== AI chatbot问题诊断 ===");

// 1. 检查认证状态
const auth = localStorage.getItem('Authorization');
console.log("认证状态:", auth ? "已登录" : "未登录");

// 2. 测试会话列表API
async function testSessionsAPI() {
    try {
        console.log("\n--- 测试会话列表API ---");
        const response = await fetch('/api/v1/chatbot/sessions', {
            headers: { 'Authorization': auth || '' }
        });
        
        const data = await response.json();
        console.log("API响应:", data);
        
        if (data.code === 0) {
            console.log("✅ 会话列表获取成功");
            console.log("会话数量:", data.data?.sessions?.length || 0);
        } else if (data.code === 401) {
            console.log("❌ 需要重新登录");
        } else {
            console.log("❌ API调用失败:", data.message);
        }
        
    } catch (error) {
        console.error("❌ API测试失败:", error);
    }
}

// 3. 测试创建会话API
async function testCreateSessionAPI() {
    try {
        console.log("\n--- 测试创建会话API ---");
        const response = await fetch('/api/v1/chatbot/sessions', {
            method: 'POST',
            headers: { 
                'Authorization': auth || '',
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({})
        });
        
        const data = await response.json();
        console.log("创建会话响应:", data);
        
        if (data.code === 0) {
            console.log("✅ 会话创建成功");
            console.log("会话ID:", data.data?.session_id);
        } else {
            console.log("❌ 会话创建失败:", data.message);
        }
        
    } catch (error) {
        console.error("❌ 创建会话测试失败:", error);
    }
}

// 4. 检查前端组件状态
function checkFrontendState() {
    console.log("\n--- 检查前端组件状态 ---");
    
    // 检查ChatBot页面
    const chatbotPage = document.querySelector('[class*="chatBotPage"]');
    console.log("ChatBot页面:", chatbotPage ? "已加载" : "未找到");
    
    // 检查对话记录列表
    const historyList = document.querySelector('[class*="chatHistoryList"]');
    console.log("对话记录列表:", historyList ? "已加载" : "未找到");
    
    // 检查加载状态
    const loadingElements = document.querySelectorAll('[class*="loading"]');
    console.log("加载状态元素数量:", loadingElements.length);
}

// 运行测试
if (auth) {
    testSessionsAPI();
    testCreateSessionAPI();
} else {
    console.log("❌ 请先登录系统");
}

checkFrontendState();

console.log("\n=== 诊断完成 ===");
