#!/bin/bash

# 快速部署脚本
# 使用预构建的镜像，避免每次都重新构建

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 显示帮助信息
show_help() {
    echo "汉邦高科 快速部署脚本"
    echo ""
    echo "用法: $0 [选项]"
    echo ""
    echo "选项:"
    echo "  -m, --mode MODE     部署模式: full (完整部署) 或 simple (简化部署)"
    echo "  --tag TAG           使用指定标签的镜像 (默认: latest)"
    echo "  --stop              停止所有服务"
    echo "  --restart           重启服务"
    echo "  --logs              查看服务日志"
    echo "  --status            查看服务状态"
    echo "  --clean             清理停止的容器"
    echo "  -h, --help          显示此帮助信息"
    echo ""
    echo "部署模式说明:"
    echo "  full    - 完整部署，包含前端、后端、MySQL、Redis、MinIO、Elasticsearch"
    echo "  simple  - 简化部署，仅包含前端和后端，需要外部数据库"
    echo ""
    echo "注意:"
    echo "  使用此脚本前，请先运行 ./build-images.sh 构建镜像"
    echo ""
    echo "示例:"
    echo "  $0 -m simple          # 简化部署"
    echo "  $0 -m full            # 完整部署"
    echo "  $0 --tag v1.0.0 -m simple  # 使用v1.0.0标签的镜像"
    echo "  $0 --stop             # 停止所有服务"
    echo "  $0 --restart          # 重启服务"
}

# 检查Docker和Docker Compose
check_requirements() {
    if ! command -v docker &> /dev/null; then
        log_error "Docker 未安装"
        exit 1
    fi
    
    if ! command -v docker-compose &> /dev/null && ! docker compose version &> /dev/null; then
        log_error "Docker Compose 未安装"
        exit 1
    fi
}

# 检查镜像是否存在
check_images() {
    local tag="${1:-latest}"
    local missing_images=()
    
    log_info "检查镜像是否存在..."
    
    if ! docker images | grep -q "hbgk-frontend.*$tag"; then
        missing_images+=("hbgk-frontend:$tag")
    fi
    
    if ! docker images | grep -q "hbgk-api.*$tag"; then
        missing_images+=("hbgk-api:$tag")
    fi
    
    if [ ${#missing_images[@]} -gt 0 ]; then
        log_error "以下镜像不存在:"
        for img in "${missing_images[@]}"; do
            echo "  - $img"
        done
        echo
        log_info "请先运行以下命令构建镜像:"
        echo "  ./build-images.sh --tag $tag"
        exit 1
    fi
    
    log_success "所有必需的镜像都存在"
}

# 检查环境变量文件
check_env_file() {
    local mode="$1"
    
    if [ "$mode" = "simple" ]; then
        if [ -f ".env.simple" ]; then
            log_info "使用简化部署配置: .env.simple"
            export ENV_FILE=".env.simple"
        else
            log_warning "未找到.env.simple，使用默认.env"
            export ENV_FILE=".env"
        fi
    else
        if [ -f ".env" ]; then
            log_info "使用默认配置: .env"
            export ENV_FILE=".env"
        else
            log_error ".env文件不存在"
            exit 1
        fi
    fi
}

# 停止服务
stop_services() {
    log_info "停止所有服务..."
    
    docker-compose -f docker-compose.simple.yml down 2>/dev/null || true
    docker-compose -f docker-compose.production.yml down 2>/dev/null || true
    
    log_success "服务已停止"
}

# 清理容器
clean_containers() {
    log_info "清理停止的容器..."
    
    docker container prune -f
    
    log_success "容器清理完成"
}

# 部署服务
deploy_services() {
    local mode="$1"
    local tag="${2:-latest}"
    
    if [ "$mode" = "full" ]; then
        COMPOSE_FILE="docker-compose.production.yml"
        log_info "开始完整部署..."
    elif [ "$mode" = "simple" ]; then
        COMPOSE_FILE="docker-compose.simple.yml"
        log_info "开始简化部署..."
        log_warning "请确保外部数据库服务已正确配置"
    else
        log_error "无效的部署模式: $mode"
        exit 1
    fi
    
    check_env_file "$mode"
    
    if [ ! -f "$COMPOSE_FILE" ]; then
        log_error "Docker Compose 文件不存在: $COMPOSE_FILE"
        exit 1
    fi
    
    # 设置镜像标签环境变量
    export IMAGE_TAG="$tag"
    
    # 启动服务
    log_info "启动服务..."
    if [ -n "$ENV_FILE" ]; then
        docker-compose -f "$COMPOSE_FILE" --env-file "$ENV_FILE" up -d
    else
        docker-compose -f "$COMPOSE_FILE" up -d
    fi
    
    # 等待服务启动
    log_info "等待服务启动..."
    sleep 10
    
    # 检查服务状态
    log_info "检查服务状态..."
    docker-compose -f "$COMPOSE_FILE" ps
    
    log_success "部署完成！"
    
    if [ "$mode" = "full" ]; then
        echo ""
        echo "服务访问地址:"
        echo "  前端: http://localhost:${FRONTEND_PORT:-3000}"
        echo "  后端API: http://localhost:${BACKEND_PORT:-9380}"
        echo "  MinIO控制台: http://localhost:${MINIO_CONSOLE_PORT:-9001}"
    else
        echo ""
        echo "服务访问地址:"
        echo "  前端: http://localhost:${FRONTEND_PORT:-3000}"
        echo "  后端API: http://localhost:${BACKEND_PORT:-9380}"
    fi
}

# 重启服务
restart_services() {
    log_info "重启服务..."
    
    # 检测当前运行的compose文件
    if docker ps --filter "name=hbgk-" --format "table {{.Names}}" | grep -q "hbgk-"; then
        # 尝试重启simple模式
        if docker-compose -f docker-compose.simple.yml ps | grep -q "hbgk-"; then
            docker-compose -f docker-compose.simple.yml restart
        # 尝试重启production模式
        elif docker-compose -f docker-compose.production.yml ps | grep -q "hbgk-"; then
            docker-compose -f docker-compose.production.yml restart
        else
            log_warning "无法确定当前部署模式，尝试重启所有容器"
            docker restart $(docker ps --filter "name=hbgk-" -q)
        fi
    else
        log_warning "没有运行中的服务"
    fi
    
    log_success "服务重启完成"
}

# 查看日志
show_logs() {
    log_info "显示服务日志..."
    
    if docker ps --filter "name=hbgk-" --format "table {{.Names}}" | grep -q "hbgk-"; then
        docker logs --tail 50 -f $(docker ps --filter "name=hbgk-backend" -q) 2>/dev/null || \
        docker logs --tail 50 -f $(docker ps --filter "name=hbgk-" -q | head -1)
    else
        log_warning "没有运行中的服务"
    fi
}

# 查看状态
show_status() {
    log_info "服务状态:"
    
    docker ps --filter "name=hbgk-" --format "table {{.Names}}\t{{.Status}}\t{{.Ports}}"
    
    echo
    log_info "镜像信息:"
    docker images | grep -E "hbgk-(frontend|api)" | head -5
}

# 主函数
main() {
    local mode=""
    local tag="latest"
    local action="deploy"
    
    # 解析命令行参数
    while [[ $# -gt 0 ]]; do
        case $1 in
            -m|--mode)
                mode="$2"
                shift 2
                ;;
            --tag)
                tag="$2"
                shift 2
                ;;
            --stop)
                action="stop"
                shift
                ;;
            --restart)
                action="restart"
                shift
                ;;
            --logs)
                action="logs"
                shift
                ;;
            --status)
                action="status"
                shift
                ;;
            --clean)
                action="clean"
                shift
                ;;
            -h|--help)
                show_help
                exit 0
                ;;
            *)
                log_error "未知参数: $1"
                show_help
                exit 1
                ;;
        esac
    done
    
    echo "========================================"
    echo "汉邦高科 快速部署"
    echo "========================================"
    echo
    
    check_requirements
    
    case $action in
        "stop")
            stop_services
            ;;
        "restart")
            restart_services
            ;;
        "logs")
            show_logs
            ;;
        "status")
            show_status
            ;;
        "clean")
            clean_containers
            ;;
        "deploy")
            if [ -z "$mode" ]; then
                log_error "请指定部署模式 (-m full 或 -m simple)"
                show_help
                exit 1
            fi
            
            check_images "$tag"
            deploy_services "$mode" "$tag"
            ;;
    esac
}

# 执行主函数
main "$@"
