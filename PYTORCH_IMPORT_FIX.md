# PyTorch Import Fix for Backend Docker

## Problem
The backend Docker container was failing with "can't import package 'torch'" error when running. This was happening because:

1. **Wrong Installation Location**: PyTorch was installed in the base stage using system Python (`pip3`)
2. **Virtual Environment Mismatch**: The production stage uses a virtual environment created by `uv`, but PyTorch wasn't installed in that environment
3. **Missing CPU-only Installation**: Need to ensure PyTorch uses CPU-only version for better compatibility

## Solution

### 1. Moved PyTorch Installation to Builder Stage
**Before**: PyTorch installed in base stage with system Python
```dockerfile
# Base stage
RUN pip3 install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cpu
```

**After**: PyTorch installed in builder stage within virtual environment using uv
```dockerfile
# Builder stage - after uv sync
RUN uv add torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cpu
```

### 2. CPU-Only PyTorch Installation
- Uses `--index-url https://download.pytorch.org/whl/cpu` to ensure CPU-only version
- Avoids CUDA dependencies that might cause issues in containers
- Smaller package size and better compatibility

### 3. Installation Verification
Added verification steps to ensure PyTorch works correctly:

**Builder Stage Verification** (using `uv run`):
```python
import torch; import torchvision; import torchaudio
print(f'PyTorch version: {torch.__version__}')
print(f'CUDA available: {torch.cuda.is_available()}')  # Should be False
x = torch.randn(2, 2); y = x.sum()
print(f'Basic tensor test: SUCCESS - {y.item():.2f}')
```

**Production Stage Verification**:
```python
import torch
x = torch.tensor([1.0, 2.0, 3.0]); y = x * 2
print(f'Tensor operations: SUCCESS - {y.tolist()}')
```

## Files Modified
1. `sz/Dockerfile.backend` - Main backend Dockerfile
2. `sz/Dockerfile.backend.robust` - Robust version (without Microsoft ODBC)

## Key Changes

### Builder Stage
- ✅ Install PyTorch using `uv add` (proper virtual environment management)
- ✅ Use CPU-only PyTorch version
- ✅ Verify installation with comprehensive tests using `uv run`
- ✅ Ensure compatibility with uv-managed virtual environment

### Production Stage  
- ✅ Copy virtual environment from builder stage
- ✅ Verify PyTorch is accessible and functional
- ✅ Test basic tensor operations

## Benefits

### 1. Correct Environment Isolation
- PyTorch installed in same virtual environment as other dependencies
- No conflicts between system Python and virtual environment
- Consistent Python package management

### 2. CPU-Only Optimization
- Smaller Docker image size (no CUDA libraries)
- Better compatibility across different host systems
- Faster startup times

### 3. Build-Time Verification
- Catches PyTorch issues during build, not runtime
- Ensures PyTorch functionality before deployment
- Clear error messages if installation fails

### 4. Production Readiness
- Final verification ensures PyTorch works in production environment
- Tests actual tensor operations, not just imports
- Confirms virtual environment setup is correct

## Expected Output

### Build Time
```
Installing PyTorch CPU version in virtual environment...
PyTorch version: 2.x.x
TorchVision version: 0.x.x
TorchAudio version: 2.x.x
CUDA available: False
Basic tensor test: SUCCESS - 1.23
PyTorch CPU installation verified successfully!

=== Final PyTorch Verification in Production Stage ===
PyTorch version: 2.x.x
CUDA available: False
Tensor operations: SUCCESS - [2.0, 4.0, 6.0]
PyTorch is ready for production use!
```

### Runtime
```python
# This should now work without errors
import torch
print(torch.__version__)  # Shows installed version
x = torch.tensor([1, 2, 3])
print(x * 2)  # tensor([2, 4, 6])
```

## Build Commands
```bash
# Build main backend
docker build -f sz/Dockerfile.backend -t hbgk-backend .

# Build robust backend  
docker build -f sz/Dockerfile.backend.robust -t hbgk-backend-robust .
```

## Troubleshooting

### If PyTorch import still fails:
1. Check if virtual environment is properly activated
2. Verify PyTorch was installed in correct environment during build
3. Check build logs for installation errors
4. Ensure CPU-only version is being used

### If CUDA errors occur:
- Verify `--index-url https://download.pytorch.org/whl/cpu` is used
- Check that `torch.cuda.is_available()` returns `False`
- Ensure no CUDA-specific code is being executed

This fix ensures PyTorch is properly installed and accessible in the Docker container's virtual environment, resolving the import errors and ensuring CPU-only operation.
