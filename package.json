{"name": "汉邦高科-frontend", "version": "1.0.0", "description": "汉邦高科 Knowledge Base System Frontend", "main": "index.js", "scripts": {"dev": "cross-env HOST=0.0.0.0 PORT=8000 UMI_DEV_SERVER_COMPRESS=none umi dev", "build": "umi build", "start": "cross-env HOST=0.0.0.0 PORT=8000 UMI_DEV_SERVER_COMPRESS=none umi dev", "test": "jest", "lint": "eslint src --ext .ts,.tsx", "lint:fix": "eslint src --ext .ts,.tsx --fix", "prettier": "prettier --write src/**/*.{ts,tsx,js,jsx,json,css,less}"}, "dependencies": {"@ant-design/icons": "^5.2.6", "@tanstack/react-query": "^5.8.4", "ahooks": "^3.7.8", "antd": "^5.12.8", "dayjs": "^1.11.10", "i18next": "^23.7.6", "i18next-browser-languagedetector": "^7.2.0", "js-base64": "^3.7.5", "jsencrypt": "^3.3.2", "mammoth": "^1.9.1", "pdfjs-dist": "^5.3.93", "react": "^18.2.0", "react-dom": "^18.2.0", "react-i18next": "^13.5.0", "react-infinite-scroll-component": "^6.1.0", "react-markdown": "^10.1.0", "react-pdf": "^10.0.1", "react-string-replace": "^1.1.1", "rehype-raw": "^7.0.0", "remark-gfm": "^4.0.1", "umi": "^4.0.87", "umi-request": "^1.4.0"}, "devDependencies": {"@types/react": "^18.2.45", "@types/react-dom": "^18.2.18", "@typescript-eslint/eslint-plugin": "^6.14.0", "@typescript-eslint/parser": "^6.14.0", "cross-env": "^7.0.3", "eslint": "^8.55.0", "eslint-plugin-react": "^7.33.2", "eslint-plugin-react-hooks": "^4.6.0", "jest": "^29.7.0", "prettier": "^3.1.1", "typescript": "^5.3.3"}, "keywords": ["hanbangGaoKe", "knowledge-base", "react", "typescript", "antd"], "author": "HanBangGaoKe Team", "license": "Apache-2.0"}