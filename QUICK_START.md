# RAGFlow Frontend Quick Start Guide

## 🚀 快速开始

### 方法一：使用启动脚本 (推荐)

1. **进入项目目录**
```bash
cd szweb
```

2. **运行启动脚本**
```bash
# Linux/Mac
chmod +x scripts/start.sh
./scripts/start.sh

# Windows
scripts\start.bat
```

3. **访问应用**
```
Local:   http://localhost:8000
Network: http://0.0.0.0:8000
```

### 方法二：手动启动

1. **安装依赖**
```bash
npm install
```

2. **配置环境**
```bash
cp .env.example .env
# 编辑 .env 文件配置API地址和密钥
```

3. **启动开发服务器**
```bash
npm run dev
```

### 方法三：Docker启动

1. **使用Docker Compose**
```bash
docker-compose up -d
```

2. **访问应用**
```
http://localhost:3000
```

## 📋 默认配置

- **API服务器**: http://***************:9380
- **API密钥**: ragflow-lhYmE2YTc2M2FiYjExZjA5YTdlMDAwYz
- **开发端口**: 8000 (监听 0.0.0.0)
- **生产端口**: 3000

## 🔑 默认登录

使用您在RAGFlow后端系统中注册的账号进行登录。

## 📚 主要功能

- ✅ 用户登录/注册
- ✅ 仪表板概览
- ✅ 知识库管理
- 🚧 文档管理 (开发中)
- 🚧 AI对话 (开发中)

## 🛠 开发命令

```bash
# 开发模式
npm run dev

# 生产构建
npm run build

# 代码检查
npm run lint

# 代码格式化
npm run prettier

# 测试
npm test
```

## 📖 更多文档

- [完整部署指南](DEPLOYMENT.md)
- [项目总结](PROJECT_SUMMARY.md)
- [API文档](README.md#api-integration)

## 🆘 常见问题

### Q: UmiJS配置错误 (fastRefresh)
A: 如果遇到 `fastRefresh` 配置错误，请确保使用的是UmiJS 4.x版本，并检查 `.umirc.ts` 配置文件。

### Q: 无法连接到API服务器
A: 检查 `.env` 文件中的 `API_BASE_URL` 配置，确保后端服务正在运行。

### Q: 登录失败
A: 确认API密钥正确，检查后端服务的认证配置。

### Q: 页面样式异常
A: 清除浏览器缓存，重新启动开发服务器。

### Q: 依赖安装失败
A: 尝试删除 `node_modules` 和 `package-lock.json`，然后重新安装：
```bash
rm -rf node_modules package-lock.json
npm install
```

## 📞 技术支持

如遇问题，请：
1. 查看控制台错误信息
2. 检查网络连接和API配置
3. 参考详细文档
4. 联系开发团队

---

🎉 **恭喜！您已成功启动RAGFlow前端应用！**
