#
#  Copyright 2024 The InfiniFlow Authors. All Rights Reserved.
#
#  Licensed under the Apache License, Version 2.0 (the "License");
#  you may not use this file except in compliance with the License.
#  You may obtain a copy of the License at
#
#      http://www.apache.org/licenses/LICENSE-2.0
#
#  Unless required by applicable law or agreed to in writing, software
#  distributed under the License is distributed on an "AS IS" BASIS,
#  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
#  See the License for the specific language governing permissions and
#  limitations under the License.
#

"""
内存管理器模块

负责管理聊天会话的上下文记忆，包括短期和长期记忆。
支持会话历史的存储、检索和清理。
"""

import json
import time
import logging
from typing import Dict, List, Optional, Any
from dataclasses import dataclass, asdict
from collections import deque


@dataclass
class ChatMessage:
    """聊天消息数据结构"""
    role: str  # "user" | "assistant" | "system"
    content: str
    timestamp: float
    message_id: str
    metadata: Optional[Dict[str, Any]] = None


@dataclass
class SessionContext:
    """会话上下文数据结构"""
    session_id: str
    user_id: str
    created_at: float
    last_active: float
    messages: List[ChatMessage]
    metadata: Optional[Dict[str, Any]] = None


class MemoryManager:
    """内存管理器
    
    管理聊天会话的上下文记忆，支持：
    - 短期记忆：当前会话的消息历史
    - 长期记忆：跨会话的用户偏好和重要信息
    - 自动清理：基于时间和大小的记忆清理
    """
    
    def __init__(
        self,
        max_messages_per_session: int = 50,
        max_session_age_hours: int = 24,
        max_total_sessions: int = 1000
    ):
        self.max_messages_per_session = max_messages_per_session
        self.max_session_age_hours = max_session_age_hours
        self.max_total_sessions = max_total_sessions
        
        # 内存存储 - 生产环境应使用Redis或数据库
        self._sessions: Dict[str, SessionContext] = {}
        self._user_sessions: Dict[str, List[str]] = {}  # user_id -> session_ids
        
        self.logger = logging.getLogger(__name__)
    
    def create_session(self, session_id: str, user_id: str) -> SessionContext:
        """创建新的会话上下文"""
        if not session_id or not user_id:
            raise ValueError("session_id and user_id are required")

        # 如果会话已存在，直接返回
        existing_session = self._sessions.get(session_id)
        if existing_session:
            # 验证用户ID是否匹配
            if existing_session.user_id != user_id:
                raise ValueError(f"Session {session_id} belongs to different user")
            self.logger.info(f"Session {session_id} already exists, returning existing session")
            return existing_session

        current_time = time.time()
        session = SessionContext(
            session_id=session_id,
            user_id=user_id,
            created_at=current_time,
            last_active=current_time,
            messages=[],
            metadata={}
        )

        self._sessions[session_id] = session

        # 更新用户会话列表
        if user_id not in self._user_sessions:
            self._user_sessions[user_id] = []

        # 避免重复添加session_id
        if session_id not in self._user_sessions[user_id]:
            self._user_sessions[user_id].append(session_id)

        # 清理过期会话
        self._cleanup_expired_sessions()

        self.logger.info(f"Created new session: {session_id} for user: {user_id}")
        return session
    
    def get_session(self, session_id: str) -> Optional[SessionContext]:
        """获取会话上下文"""
        if not session_id:
            return None
            
        session = self._sessions.get(session_id)
        if session:
            session.last_active = time.time()
        return session
    
    def add_message(
        self, 
        session_id: str, 
        role: str, 
        content: str, 
        message_id: str,
        metadata: Optional[Dict[str, Any]] = None
    ) -> bool:
        """向会话添加消息"""
        if not all([session_id, role, content, message_id]):
            self.logger.error("Missing required parameters for add_message")
            return False
        
        if role not in ["user", "assistant", "system"]:
            self.logger.error(f"Invalid role: {role}")
            return False
        
        session = self.get_session(session_id)
        if not session:
            self.logger.error(f"Session not found: {session_id}")
            return False
        
        message = ChatMessage(
            role=role,
            content=content,
            timestamp=time.time(),
            message_id=message_id,
            metadata=metadata or {}
        )
        
        session.messages.append(message)
        session.last_active = time.time()
        
        # 限制消息数量
        if len(session.messages) > self.max_messages_per_session:
            # 保留系统消息和最近的消息
            system_messages = [msg for msg in session.messages if msg.role == "system"]
            recent_messages = [msg for msg in session.messages if msg.role != "system"][-self.max_messages_per_session + len(system_messages):]
            session.messages = system_messages + recent_messages
        
        self.logger.debug(f"Added message to session {session_id}: {role}")
        return True
    
    def get_conversation_history(
        self, 
        session_id: str, 
        limit: Optional[int] = None,
        include_system: bool = True
    ) -> List[Dict[str, Any]]:
        """获取会话历史记录"""
        session = self.get_session(session_id)
        if not session:
            return []
        
        messages = session.messages
        if not include_system:
            messages = [msg for msg in messages if msg.role != "system"]
        
        if limit:
            messages = messages[-limit:]
        
        return [
            {
                "role": msg.role,
                "content": msg.content,
                "timestamp": msg.timestamp,
                "message_id": msg.message_id,
                "metadata": msg.metadata
            }
            for msg in messages
        ]
    
    def clear_session(self, session_id: str) -> bool:
        """清空会话"""
        if session_id not in self._sessions:
            return False
        
        session = self._sessions[session_id]
        user_id = session.user_id
        
        # 从用户会话列表中移除
        if user_id in self._user_sessions:
            self._user_sessions[user_id] = [
                sid for sid in self._user_sessions[user_id] if sid != session_id
            ]
            if not self._user_sessions[user_id]:
                del self._user_sessions[user_id]
        
        del self._sessions[session_id]
        self.logger.info(f"Cleared session: {session_id}")
        return True
    
    def get_user_sessions(self, user_id: str) -> List[str]:
        """获取用户的所有会话ID"""
        return self._user_sessions.get(user_id, [])
    
    def _cleanup_expired_sessions(self) -> None:
        """清理过期会话"""
        current_time = time.time()
        max_age_seconds = self.max_session_age_hours * 3600
        
        expired_sessions = []
        for session_id, session in self._sessions.items():
            if current_time - session.last_active > max_age_seconds:
                expired_sessions.append(session_id)
        
        for session_id in expired_sessions:
            self.clear_session(session_id)
        
        # 如果会话总数超过限制，清理最旧的会话
        if len(self._sessions) > self.max_total_sessions:
            sorted_sessions = sorted(
                self._sessions.items(),
                key=lambda x: x[1].last_active
            )
            sessions_to_remove = len(self._sessions) - self.max_total_sessions
            for session_id, _ in sorted_sessions[:sessions_to_remove]:
                self.clear_session(session_id)
        
        if expired_sessions:
            self.logger.info(f"Cleaned up {len(expired_sessions)} expired sessions")
    
    def get_session_stats(self) -> Dict[str, Any]:
        """获取内存管理器统计信息"""
        return {
            "total_sessions": len(self._sessions),
            "total_users": len(self._user_sessions),
            "max_messages_per_session": self.max_messages_per_session,
            "max_session_age_hours": self.max_session_age_hours,
            "max_total_sessions": self.max_total_sessions
        }
