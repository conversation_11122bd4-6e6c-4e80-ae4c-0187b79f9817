import { defineConfig } from 'umi';

export default defineConfig({
  // 完全禁用MFSU和esbuild
  mfsu: false,
  // 禁用esbuild相关功能
  esbuildMinifyIIFE: false,
  // 使用传统的压缩器
  jsMinifier: 'none',
  cssMinifier: 'none',
  // 禁用代码分割以避免esbuild问题
  codeSplitting: {
    jsStrategy: 'depPerChunk',
    jsStrategyOptions: {},
  },
  routes: [
    {
      path: '/login',
      component: '@/pages/login',
    },
    {
      path: '/',
      redirect: '/dashboard',
    },
    {
      path: '/dashboard',
      component: '@/pages/dashboard',
    },
    {
      path: '/ai-read',
      component: '@/pages/ai-read',
    },
    {
      path: '/text-to-image',
      component: '@/pages/text-to-image',
    },
    {
      path: '/knowledge',
      component: '@/pages/knowledge',
    },  
    {
      path: '/documents',
      component: '@/pages/documents',
    },
    {
      path: '/knowledge/:id',
      component: '@/pages/knowledge-detail',
      routes: [
        {
          path: '/knowledge/:id',
          redirect: '/knowledge/:id/dataset',
        },
        {
          path: '/knowledge/:id/dataset',
          component: '@/pages/knowledge-detail/dataset',
        },
        {
          path: '/knowledge/:id/setting',
          component: '@/pages/knowledge-detail/setting',
        },
        {
          path: '/knowledge/:id/retrieval-testing',
          component: '@/pages/knowledge-detail/retrieval-testing',
        },
        {
          path: '/knowledge/:id/dataset/:docId/chunks',
          component: '@/pages/knowledge-detail/chunks',
        },
      ],
    },
    {
      path: '/dialogs',
      routes: [
        {
          path: '/dialogs',
          component: '@/pages/dialogs',
          exact: true,
        },
        {
          path: '/dialogs/:id/view',
          component: '@/pages/dialogs/view',
        },
      ],
    },
    {
      path: '/chat',
      component: '@/pages/chat',
    },
    {
      path: '/conversations',
      component: '@/pages/conversations',
    },
    {
      path: '/ai-models',
      component: '@/pages/ai-models',
    },
    {
      path: '/settings',
      component: '@/pages/settings',
    },
    {
      path: '/chatbot',
      component: '@/pages/chatbot',
    },
  ],
  proxy: {
    '/v1': {
      target: process.env.API_BASE_URL || 'http://localhost:9380',
      changeOrigin: true,
      pathRewrite: { '^': '' },
    },
    '/api/v1/chatbot': {
      target: process.env.API_BASE_URL || 'http://localhost:9380',
      changeOrigin: true,
      pathRewrite: { '^': '' },
    },
    '/api/v1/ai-reading': {
      target: process.env.API_BASE_URL || 'http://localhost:9380',
      changeOrigin: true,
      pathRewrite: { '^/api': '' },
    },
    '/gradio': {
      target: 'http://*************:8090',
      changeOrigin: true,
      pathRewrite: { '^/gradio': '/gradio' },
      ws: true, // 支持WebSocket
      secure: false,
      // 确保代理正确处理端口号
      headers: {
        'Host': '*************:8090'
      }
    },
  },
  define: {
    'process.env.API_BASE_URL': process.env.API_BASE_URL || 'http://localhost:9380',
    'process.env.API_KEY': process.env.API_KEY || 'hbgk-lhYmE2YTc2M2FiYjExZjA5YTdlMDAwYz',
    // 确保包含完整的主机名和端口号
    'process.env.TEXT_TO_IMAGE_URL': process.env.TEXT_TO_IMAGE_URL || 'http://*************:8090/gradio',
    'process.env.MINERU_SERVER_URL': process.env.MINERU_SERVER_URL || 'http://*************:7860',
    'process.env.MINERU_API_KEY': process.env.MINERU_API_KEY || '',
    // Auto LLM Configuration for New Users
    'process.env.AUTO_LLM_CHAT_FACTORY': process.env.AUTO_LLM_CHAT_FACTORY || 'VLLM',
    'process.env.AUTO_LLM_CHAT_NAME': process.env.AUTO_LLM_CHAT_NAME || 'Qwen3-32B',
    'process.env.AUTO_LLM_CHAT_API_BASE': process.env.AUTO_LLM_CHAT_API_BASE || 'http://host.docker.internal:8000/v1',
    'process.env.AUTO_LLM_CHAT_MAX_TOKENS': process.env.AUTO_LLM_CHAT_MAX_TOKENS || '8192',
    'process.env.AUTO_LLM_EMBEDDING_FACTORY': process.env.AUTO_LLM_EMBEDDING_FACTORY || 'VLLM',
    'process.env.AUTO_LLM_EMBEDDING_NAME': process.env.AUTO_LLM_EMBEDDING_NAME || 'bge-m3',
    'process.env.AUTO_LLM_EMBEDDING_API_BASE': process.env.AUTO_LLM_EMBEDDING_API_BASE || 'http://host.docker.internal:18080/v1',
    'process.env.AUTO_LLM_EMBEDDING_MAX_TOKENS': process.env.AUTO_LLM_EMBEDDING_MAX_TOKENS || '8192',
    'process.env.AUTO_LLM_RERANK_FACTORY': process.env.AUTO_LLM_RERANK_FACTORY || 'VLLM',
    'process.env.AUTO_LLM_RERANK_NAME': process.env.AUTO_LLM_RERANK_NAME || 'bge-reranker-v2-m3',
    'process.env.AUTO_LLM_RERANK_API_BASE': process.env.AUTO_LLM_RERANK_API_BASE || 'http://host.docker.internal:18081/v1',
    'process.env.AUTO_LLM_RERANK_MAX_TOKENS': process.env.AUTO_LLM_RERANK_MAX_TOKENS || '8192',
  },
  title: '汉邦高科 Knowledge Base System',
  hash: true,
  outputPath: 'dist',
  publicPath: '/',
});
