#!/usr/bin/env python3
"""
测试AI Reading模块的配置加载
验证Mineru服务器配置是否正确从环境变量加载
"""

import os
import sys
from dotenv import load_dotenv

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, project_root)

# 加载环境变量
load_dotenv(os.path.join(project_root, 'sz', '.env'))

def test_config():
    """测试配置加载"""
    print("=== AI Reading 配置测试 ===")
    
    # 测试Mineru服务器配置
    mineru_server_url = os.getenv('MINERU_SERVER_URL', 'http://192.168.1.138:7860')
    mineru_api_key = os.getenv('MINERU_API_KEY', '')
    
    print(f"MINERU_SERVER_URL: {mineru_server_url}")
    print(f"MINERU_API_KEY: {'已设置' if mineru_api_key else '未设置'}")
    
    # 测试AI Reading服务初始化
    try:
        from ai_reading_service import AIReadingService
        service = AIReadingService()
        print(f"AI Reading服务初始化成功")
        print(f"服务器URL: {service.mineru_server_url}")
        print(f"API密钥: {'已设置' if service.mineru_api_key else '未设置'}")
        print(f"临时目录: {service.temp_dir}")
        
        # 检查临时目录是否存在
        if os.path.exists(service.temp_dir):
            print(f"临时目录存在: ✓")
        else:
            print(f"临时目录不存在: ✗")
            
    except Exception as e:
        print(f"AI Reading服务初始化失败: {e}")
    
    print("=== 配置测试完成 ===")

if __name__ == "__main__":
    test_config()
