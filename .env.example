# API Configuration
# 在Docker环境中，使用服务名进行容器间通信
# 在开发环境中，使用localhost或具体IP地址
API_BASE_URL=http://localhost:9380
API_KEY=hbgk-lhYmE2YTc2M2FiYjExZjA5YTdlMDAwYz

# Text to Image Service Configuration
TEXT_TO_IMAGE_URL=http://*************:8090/gradio

# AI Reading - Mineru Service Configuration
MINERU_SERVER_URL=http://*************:7860
MINERU_API_KEY=

# Application Configuration
NODE_ENV=development
PORT=8000
HOST=0.0.0.0

# Build Configuration
GENERATE_SOURCEMAP=false
DISABLE_ESLINT_PLUGIN=false

# Mock Data Configuration (for development/testing)
# Set to 'true' to use mock data when real API is not available
USE_MOCK_DATA=false

# Feature Flags
ENABLE_CONVERSATIONS=true
ENABLE_ADVANCED_SEARCH=true
ENABLE_BATCH_OPERATIONS=false

# Auto LLM Configuration for New Users
# 用户注册后自动添加的默认LLM配置
# Chat Model Configuration
AUTO_LLM_CHAT_FACTORY=VLLM
AUTO_LLM_CHAT_NAME=Qwen3-32B
AUTO_LLM_CHAT_API_BASE=http://host.docker.internal:8000/v1
AUTO_LLM_CHAT_MAX_TOKENS=8192

# Embedding Model Configuration
AUTO_LLM_EMBEDDING_FACTORY=VLLM
AUTO_LLM_EMBEDDING_NAME=bge-m3
AUTO_LLM_EMBEDDING_API_BASE=http://host.docker.internal:18080/v1
AUTO_LLM_EMBEDDING_MAX_TOKENS=8192

# Rerank Model Configuration
AUTO_LLM_RERANK_FACTORY=VLLM
AUTO_LLM_RERANK_NAME=bge-reranker-v2-m3
AUTO_LLM_RERANK_API_BASE=http://host.docker.internal:18081/v1
AUTO_LLM_RERANK_MAX_TOKENS=8192
