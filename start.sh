#!/bin/bash

echo "========================================"
echo "RAGFlow 知识库系统前端启动脚本"
echo "========================================"
echo

echo "检查 Node.js 环境..."
if ! command -v node &> /dev/null; then
    echo "错误: 未找到 Node.js，请先安装 Node.js 16+ 版本"
    echo "下载地址: https://nodejs.org/"
    exit 1
fi

echo "Node.js 版本:"
node --version
echo

echo "检查 npm 环境..."
if ! command -v npm &> /dev/null; then
    echo "错误: 未找到 npm"
    exit 1
fi

echo "npm 版本:"
npm --version
echo

echo "检查依赖是否已安装..."
if [ ! -d "node_modules" ]; then
    echo "正在安装依赖包..."
    npm install
    if [ $? -ne 0 ]; then
        echo "错误: 依赖安装失败"
        exit 1
    fi
    echo "依赖安装完成!"
    echo
else
    echo "依赖已安装，跳过安装步骤"
    echo
fi

echo "启动开发服务器..."
echo "应用将在 http://localhost:3000 启动"
echo "按 Ctrl+C 停止服务器"
echo

npm run dev
