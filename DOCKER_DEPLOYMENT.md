# 汉邦高科 Docker 部署指南

本目录包含汉邦高科系统的完整Docker部署配置文件。

## 目录结构

```
sz/
├── DOCKER_DEPLOYMENT.md           # 本文件
├── .env                           # 环境变量配置文件
├── docker-compose.production.yml  # 生产环境Docker Compose配置
├── docker-compose.simple.yml      # 简化部署配置
├── Dockerfile.backend             # 后端Docker镜像构建文件
├── nginx.conf                     # Nginx配置文件
├── build.sh                      # 镜像构建脚本
├── deploy.sh                     # 部署脚本
├── install-buildkit.sh           # BuildKit安装脚本
└── test-build.sh                 # 构建测试脚本
```

## 快速开始

### 1. 环境准备

确保系统已安装：
- Docker (版本 20.10+)
- Docker Compose (版本 2.0+)
- Docker BuildKit (可选，用于更快的构建)

### 2. 安装Docker BuildKit (推荐)

如果遇到BuildKit错误，可以安装BuildKit组件：

```bash
# 安装BuildKit
./install-buildkit.sh

# 或者手动安装
mkdir -p ~/.docker/cli-plugins
curl -L https://github.com/docker/buildx/releases/latest/download/buildx-v0.12.0.linux-amd64 -o ~/.docker/cli-plugins/docker-buildx
chmod +x ~/.docker/cli-plugins/docker-buildx
```

### 3. 配置环境变量

编辑 `.env` 文件，根据实际情况修改配置：

```bash
# 服务端口
FRONTEND_PORT=3000
BACKEND_PORT=9380

# 数据库配置
MYSQL_PASSWORD=your_secure_password
REDIS_PASSWORD=your_secure_password
```

### 4. 构建镜像

使用构建脚本：

```bash
# 构建所有镜像
./build.sh

# 仅构建后端镜像
./build.sh backend

# 仅构建前端镜像
./build.sh frontend

# 测试构建配置（不实际构建）
./test-build.sh
```

### 5. 部署服务

使用部署脚本：

```bash
# 完整部署（构建+启动）
./deploy.sh

# 仅启动服务（不构建）
./deploy.sh start

# 停止服务
./deploy.sh stop

# 查看服务状态
./deploy.sh status
```

或者直接使用Docker Compose：

```bash
# 启动所有服务
docker-compose -f docker-compose.production.yml up -d

# 查看服务状态
docker-compose -f docker-compose.production.yml ps

# 查看日志
docker-compose -f docker-compose.production.yml logs -f

# 停止服务
docker-compose -f docker-compose.production.yml down
```

## 服务访问

部署成功后，可以通过以下地址访问：

- **前端界面**: http://localhost:3000
- **后端API**: http://localhost:9380
- **API文档**: http://localhost:9380/docs

## 故障排除

### 常见问题

1. **BuildKit错误**
   ```bash
   ERROR: BuildKit is enabled but the buildx component is missing or broken.
   ```
   **解决方案**: 运行 `./install-buildkit.sh` 安装BuildKit组件

2. **权限问题**
   ```bash
   sudo chown -R $USER:$USER sz/
   chmod +x sz/*.sh
   ```

3. **端口冲突**
   - 修改 `.env` 文件中的端口配置
   - 确保端口未被其他服务占用

4. **内存不足**
   - 确保系统有足够内存（推荐8GB+）
   - 调整Docker内存限制

5. **构建失败**
   - 检查网络连接
   - 确保所有依赖文件存在
   - 查看构建日志排查具体错误

### 依赖文件检查

Backend构建需要以下文件：
- `../chromedriver-linux64.zip`
- `../chrome-linux64.zip`
- `../huggingface.co/`
- `../maidalun1020/`
- `../nltk_data/`
- `../tika-server-standard-3.0.0.jar`
- `../cl100k_base.tiktoken`
- `../libssl1.1_*.deb`

### 日志查看

```bash
# 查看所有服务日志
docker-compose -f docker-compose.production.yml logs

# 查看特定服务日志
docker-compose -f docker-compose.production.yml logs hbgk-backend

# 实时查看日志
docker-compose -f docker-compose.production.yml logs -f
```

## 文件说明

### Backend Dockerfile 特性

- 基于Ubuntu 22.04
- 包含Chrome和ChromeDriver支持
- 集成PyTorch CPU版本
- 包含HuggingFace模型文件
- 优化的Python环境配置
- 安全的非root用户运行
- 自动检测BuildKit可用性

### Frontend 配置

- 使用hbweb目录中的现有Dockerfile
- 多阶段构建优化镜像大小
- 基于Nginx Alpine
- 包含健康检查
- 静态资源缓存优化

### 构建脚本特性

- 自动检测BuildKit可用性
- 支持传统Docker构建方式
- 详细的错误提示和日志
- 依赖文件检查

## 生产环境建议

1. **安全配置**
   - 修改默认密码
   - 配置防火墙规则
   - 启用HTTPS

2. **性能优化**
   - 安装BuildKit以提高构建速度
   - 调整worker进程数
   - 配置资源限制

3. **监控和维护**
   - 定期备份数据
   - 监控系统资源使用
   - 定期更新镜像

## 支持

如有问题，请联系技术支持团队。
