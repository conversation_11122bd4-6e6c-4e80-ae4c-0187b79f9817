#
#  AI Reading App - AI阅读配套后台程序
#  提供文件上传、转换、问答等功能
#

import os
import uuid
import logging
import requests
import zipfile
import tempfile
import subprocess
import time
from datetime import datetime
from flask import request, jsonify
from flask_login import current_user, login_required
from werkzeug.utils import secure_filename

from api.db.db_models import AIReadingFile, AIReadingConversation
from api.db.services.user_service import TenantService
from api.utils.api_utils import construct_response, get_json_result, validate_request
from api.utils.file_utils import get_project_base_directory
from api.settings import RetCode

# 设置页面名称以匹配前端期望的URL
page_name = "ai-reading"



def await_document_qa(question: str, markdown_content: str, filename: str) -> str:
    """
    基于文档内容进行问答

    Args:
        question: 用户问题
        markdown_content: 文档的markdown内容
        filename: 文件名

    Returns:
        AI回答
    """
    try:
        # 简化版本的文档问答实现
        # TODO: 后续集成真正的LLM模型

        # 将问题转换为小写以便匹配
        question_lower = question.lower()
        content_lower = markdown_content.lower()

        # 提取文档的关键信息
        lines = markdown_content.split('\n')

        # 提取标题
        titles = []
        content_sections = []
        current_section = []

        for line in lines:
            line = line.strip()
            if not line:
                continue

            # 检测标题
            if line.startswith('#'):
                if current_section:
                    content_sections.append('\n'.join(current_section))
                    current_section = []
                title = line.lstrip('#').strip()
                titles.append(title)
                current_section.append(line)
            else:
                current_section.append(line)

        # 添加最后一个段落
        if current_section:
            content_sections.append('\n'.join(current_section))

        # 简单的关键词匹配和回答生成
        relevant_sections = []

        # 检查问题中的关键词，包括中文关键词
        question_keywords = []
        # 分割中文和英文关键词
        import re

        # 先提取完整的中文词组
        chinese_words = re.findall(r'[\u4e00-\u9fff]+', question_lower)
        english_words = re.findall(r'[a-zA-Z]+', question_lower)

        # 处理中文词组，分割成更小的词
        for word in chinese_words:
            if len(word) >= 2:
                question_keywords.append(word)
                # 分割长词为子词
                if len(word) > 2:
                    for i in range(len(word) - 1):
                        sub_word = word[i:i+2]
                        if sub_word not in question_keywords:
                            question_keywords.append(sub_word)

        # 处理英文词
        for word in english_words:
            if len(word) >= 2:
                question_keywords.append(word)

        # 添加一些常见的同义词和相关词
        keyword_expansions = {
            '人工智能': ['ai', '智能', '人工', '机器'],
            'ai': ['人工智能', '智能', '机器'],
            '机器学习': ['学习', '机器', 'ml'],
            '深度学习': ['深度', '神经网络', 'dl'],
            '什么': ['是什么', '定义', '概念'],
            '如何': ['怎么', '方法', '步骤'],
            '为什么': ['原因', '理由'],
        }

        # 扩展关键词
        expanded_keywords = set(question_keywords)
        for keyword in question_keywords:
            if keyword in keyword_expansions:
                expanded_keywords.update(keyword_expansions[keyword])

        question_keywords = list(expanded_keywords)

        for section in content_sections:
            section_lower = section.lower()
            # 计算匹配度
            matches = 0
            for keyword in question_keywords:
                if keyword in section_lower:
                    matches += section_lower.count(keyword)
            if matches > 0:
                relevant_sections.append((section, matches))

        # 按匹配度排序
        relevant_sections.sort(key=lambda x: x[1], reverse=True)

        # 生成回答
        if relevant_sections:
            # 取最相关的前3个段落
            top_sections = [section[0] for section in relevant_sections[:3]]

            answer = f"基于文档《{filename}》的内容，我找到了以下相关信息：\n\n"

            for i, section in enumerate(top_sections, 1):
                # 限制每个段落的长度
                section_text = section[:500] + "..." if len(section) > 500 else section
                answer += f"{i}. {section_text}\n\n"

            # 添加总结
            if len(relevant_sections) > 3:
                answer += f"（还有 {len(relevant_sections) - 3} 个相关段落未显示）\n\n"

            answer += "以上信息来源于您上传的文档内容。如需更详细的信息，请查看完整文档。"

        else:
            # 如果没有找到相关内容，提供文档概览
            if titles:
                answer = f"在文档《{filename}》中没有找到直接相关的内容。\n\n"
                answer += "文档主要包含以下章节：\n"
                for title in titles[:5]:  # 显示前5个标题
                    answer += f"• {title}\n"
                if len(titles) > 5:
                    answer += f"• 还有 {len(titles) - 5} 个其他章节\n"
                answer += "\n请尝试更具体的问题，或查看完整文档内容。"
            else:
                answer = f"抱歉，在文档《{filename}》中没有找到与您的问题相关的内容。请尝试重新表述您的问题或查看完整文档。"

        return answer

    except Exception as e:
        logging.error(f"文档问答失败: {str(e)}")
        return f"抱歉，处理您的问题时出现错误。请稍后重试。"

# 配置
UPLOAD_FOLDER = os.path.join(get_project_base_directory(), 'temp', 'ai_reading')
ALLOWED_EXTENSIONS = {'pdf', 'docx', 'pptx', 'doc', 'ppt'}
MAX_FILE_SIZE = 50 * 1024 * 1024  # 50MB
MAX_FILES_PER_USER = 25  # 最大文件数量
AUTO_DELETE_THRESHOLD = 20  # 达到此数量时自动删除最旧的文件

# 解析  API配置 - 从环境变量获取
MINERU_SERVER_URL = os.getenv('MINERU_SERVER_URL', 'http://192.168.1.138:7860')
MINERU_API_KEY = os.getenv('MINERU_API_KEY', '')

def allowed_file(filename):
    """检查文件扩展名是否允许"""
    return '.' in filename and \
           filename.rsplit('.', 1)[1].lower() in ALLOWED_EXTENSIONS

def ensure_upload_folder():
    """确保上传文件夹存在"""
    if not os.path.exists(UPLOAD_FOLDER):
        os.makedirs(UPLOAD_FOLDER, exist_ok=True)


def auto_delete_old_files(user_id: str, tenant_id: str):
    """
    自动删除最旧的文件以保持文件数量在限制范围内

    Args:
        user_id: 用户ID
        tenant_id: 租户ID
    """
    try:
        # 获取用户的所有文件，按创建时间排序
        user_files = AIReadingFile.select().where(
            (AIReadingFile.user_id == user_id) &
            (AIReadingFile.tenant_id == tenant_id) &
            (AIReadingFile.status == '1')
        ).order_by(AIReadingFile.create_time.asc())

        file_count = user_files.count()

        # 如果文件数量达到自动删除阈值，删除最旧的文件
        if file_count >= AUTO_DELETE_THRESHOLD:
            files_to_delete = file_count - AUTO_DELETE_THRESHOLD + 1

            for file_to_delete in user_files.limit(files_to_delete):
                try:
                    # 删除物理文件
                    files_to_remove = []
                    if file_to_delete.original_file_path and os.path.exists(file_to_delete.original_file_path):
                        files_to_remove.append(file_to_delete.original_file_path)

                    if file_to_delete.pdf_file_path and os.path.exists(file_to_delete.pdf_file_path):
                        files_to_remove.append(file_to_delete.pdf_file_path)

                    if file_to_delete.markdown_file_path and os.path.exists(file_to_delete.markdown_file_path):
                        files_to_remove.append(file_to_delete.markdown_file_path)

                    # 删除相关的对话记录
                    AIReadingConversation.delete().where(
                        AIReadingConversation.file_id == file_to_delete.id
                    ).execute()

                    # 删除数据库记录
                    file_to_delete.delete_instance()

                    # 删除物理文件
                    for file_path in files_to_remove:
                        try:
                            os.remove(file_path)
                        except Exception as e:
                            logging.warning(f"删除文件失败: {file_path}, 错误: {e}")

                except Exception as e:
                    logging.error(f"删除文件失败: {file_to_delete.id}, 错误: {e}")

    except Exception as e:
        logging.error(f"自动删除旧文件失败: {e}")


def check_file_limit(user_id: str, tenant_id: str) -> bool:
    """
    检查用户文件数量是否超过限制

    Args:
        user_id: 用户ID
        tenant_id: 租户ID

    Returns:
        True if within limit, False if exceeded
    """
    try:
        file_count = AIReadingFile.select().where(
            (AIReadingFile.user_id == user_id) &
            (AIReadingFile.tenant_id == tenant_id) &
            (AIReadingFile.status == '1')
        ).count()

        return file_count < MAX_FILES_PER_USER

    except Exception as e:
        logging.error(f"检查文件限制失败: {e}")
        return False

@manager.route('/upload', methods=['POST'])  # noqa: F821
@login_required
def upload_file():
    """
    文件上传接口
    """
    try:
        # 检查是否有文件
        if 'file' not in request.files:
            return get_json_result(
                code=RetCode.ARGUMENT_ERROR,
                message='没有文件被上传'
            )

        file = request.files['file']

        if file.filename == '':
            return get_json_result(
                code=RetCode.ARGUMENT_ERROR,
                message='没有选择文件'
            )

        if not allowed_file(file.filename):
            return get_json_result(
                code=RetCode.ARGUMENT_ERROR,
                message=f'不支持的文件类型，支持的类型: {", ".join(ALLOWED_EXTENSIONS)}'
            )

        # 检查文件大小
        file.seek(0, os.SEEK_END)
        file_size = file.tell()
        file.seek(0)

        if file_size > MAX_FILE_SIZE:
            return get_json_result(
                code=RetCode.ARGUMENT_ERROR,
                message=f'文件大小超过限制 ({MAX_FILE_SIZE // (1024*1024)}MB)'
            )

        # 确保上传目录存在
        ensure_upload_folder()

        # 生成唯一文件名
        file_id = str(uuid.uuid4()).replace('-', '')

        # 保留原始文件名（包含中文），只对存储文件名进行安全处理
        original_filename = file.filename
        safe_filename = secure_filename(file.filename)

        # 从原始文件名提取文件扩展名
        if '.' in original_filename:
            file_extension = original_filename.rsplit('.', 1)[1].lower()
        else:
            return get_json_result(
                code=RetCode.ARGUMENT_ERROR,
                message='文件名必须包含扩展名'
            )

        stored_filename = f"{file_id}.{file_extension}"
        file_path = os.path.join(UPLOAD_FOLDER, stored_filename)

        # 保存文件
        file.save(file_path)

        # 获取当前用户ID（直接使用current_user.id，与其他模块保持一致）
        user_id = current_user.id

        # 获取租户信息
        try:
            tenants = TenantService.get_info_by(user_id)
            if not tenants:
                # 删除刚保存的文件
                if os.path.exists(file_path):
                    os.remove(file_path)
                return get_json_result(
                    code=RetCode.AUTHENTICATION_ERROR,
                    message='租户信息不存在'
                )
            tenant_id = tenants[0].get('tenant_id')
        except Exception as e:
            logging.error(f"获取租户信息失败: {str(e)}")
            # 删除刚保存的文件
            if os.path.exists(file_path):
                os.remove(file_path)
            return get_json_result(
                code=RetCode.AUTHENTICATION_ERROR,
                message='获取租户信息失败'
            )

        # 检查文件数量限制
        if not check_file_limit(user_id, tenant_id):
            # 删除刚保存的文件
            if os.path.exists(file_path):
                os.remove(file_path)
            return get_json_result(
                code=RetCode.ARGUMENT_ERROR,
                message=f'文件数量超过限制 (最大{MAX_FILES_PER_USER}个文件)'
            )

        # 自动删除旧文件（如果达到阈值）
        auto_delete_old_files(user_id, tenant_id)

        # 创建数据库记录
        # 使用正确的方式创建数据库记录
        from api.db.db_models import DB
        from api import utils
        import time

        try:
            with DB.atomic() as transaction:
                # 获取当前时间戳
                current_timestamp = utils.current_timestamp()
                current_datetime = utils.timestamp_to_date(current_timestamp)

                # 使用insert方法而不是save方法，确保触发BaseModel的逻辑
                result = AIReadingFile.insert(
                    id=file_id,
                    tenant_id=tenant_id,
                    user_id=user_id,
                    original_filename=original_filename,
                    file_type=file_extension,
                    file_size=file_size,
                    original_file_path=file_path,
                    processing_status='uploaded',
                    processing_progress=0.0,
                    processing_message='文件上传成功，等待处理',
                    create_time=current_timestamp,
                    create_date=current_datetime,
                    update_time=current_timestamp,
                    update_date=current_datetime,
                    status='1'  # 确保状态字段正确
                ).execute()

                # 事务会在with块结束时自动提交

            # 等待一下确保事务完成
            time.sleep(0.2)

        except Exception as db_error:
            logging.exception(f"数据库操作失败: {str(db_error)}")
            # 删除已上传的文件
            if os.path.exists(file_path):
                os.remove(file_path)
            raise db_error

        return get_json_result(
            code=RetCode.SUCCESS,
            message='文件上传成功',
            data={
                'file_id': file_id,
                'original_filename': original_filename,
                'file_size': file_size,
                'processing_status': 'uploaded'
            }
        )

    except Exception as e:
        logging.exception(f"文件上传失败: {str(e)}")
        return get_json_result(
            code=RetCode.EXCEPTION_ERROR,
            message=f'文件上传失败: {str(e)}'
        )

@manager.route('/files', methods=['GET'])  # noqa: F821
@login_required
def get_files():
    """
    获取用户的文件列表
    """
    try:
        # 获取当前用户ID（直接使用current_user.id，与其他模块保持一致）
        user_id = current_user.id
        
        # 分页参数
        page = int(request.args.get('page', 1))
        page_size = int(request.args.get('page_size', 10))
        
        # 查询文件列表
        try:
            files = AIReadingFile.select().where(
                AIReadingFile.user_id == user_id,
                AIReadingFile.status == '1'
            ).order_by(AIReadingFile.create_time.desc()).paginate(page, page_size)
            logging.debug(f"get_files: 查询到 {len(files)} 个文件")
        except Exception as e:
            logging.error(f"get_files: 数据库查询失败: {str(e)}")
            logging.exception("详细错误信息:")
            return get_json_result(
                code=RetCode.DATA_ERROR,
                message='查询文件列表失败'
            )
        
        file_list = []
        for file in files:
            file_list.append({
                'file_id': file.id,
                'original_filename': file.original_filename,
                'file_type': file.file_type,
                'file_size': file.file_size,
                'processing_status': file.processing_status,
                'processing_progress': file.processing_progress,
                'processing_message': file.processing_message,
                'content_summary': file.content_summary,
                'create_time': file.create_time,
                'update_time': file.update_time
            })
        
        return get_json_result(
            code=RetCode.SUCCESS,
            message='获取文件列表成功',
            data={
                'files': file_list,
                'page': page,
                'page_size': page_size,
                'total': files.count()
            }
        )

    except Exception as e:
        logging.exception(f"获取文件列表失败: {str(e)}")
        return get_json_result(
            code=RetCode.EXCEPTION_ERROR,
            message=f'获取文件列表失败: {str(e)}'
        )

@manager.route('/files/<file_id>/status', methods=['GET'])  # noqa: F821
@login_required
def get_file_status(file_id):
    """
    获取文件处理状态
    """
    try:
        # 获取当前用户ID（直接使用current_user.id，与其他模块保持一致）
        user_id = current_user.id
        
        # 查询文件
        try:
            ai_file = AIReadingFile.get(
                AIReadingFile.id == file_id,
                AIReadingFile.user_id == user_id,
                AIReadingFile.status == '1'
            )
        except AIReadingFile.DoesNotExist:
            return get_json_result(
                code=RetCode.NOT_FOUND,
                message='文件不存在'
            )

        return get_json_result(
            code=RetCode.SUCCESS,
            message='获取文件状态成功',
            data={
                'file_id': ai_file.id,
                'processing_status': ai_file.processing_status,
                'processing_progress': ai_file.processing_progress,
                'processing_message': ai_file.processing_message,
                'mineru_state': ai_file.mineru_state,
                'content_summary': ai_file.content_summary
            }
        )

    except Exception as e:
        logging.exception(f"获取文件状态失败: {str(e)}")
        return get_json_result(
            code=RetCode.EXCEPTION_ERROR,
            message=f'获取文件状态失败: {str(e)}'
        )

@manager.route('/files/<file_id>/process', methods=['POST'])  # noqa: F821
@login_required
def process_file(file_id):
    """
    开始处理文件（转换为PDF并调用解析  API）
    """
    try:
        # 获取当前用户ID（直接使用current_user.id，与其他模块保持一致）
        user_id = current_user.id

        # 获取租户信息
        try:
            tenants = TenantService.get_info_by(user_id)
            if not tenants:
                return get_json_result(
                    code=RetCode.AUTHENTICATION_ERROR,
                    message='租户信息不存在'
                )
            tenant_id = tenants[0].get('tenant_id')
        except Exception as e:
            logging.error(f"获取租户信息失败: {str(e)}")
            return get_json_result(
                code=RetCode.AUTHENTICATION_ERROR,
                message='获取租户信息失败'
            )
        
        # 查询文件
        try:
            ai_file = AIReadingFile.get(
                AIReadingFile.id == file_id,
                AIReadingFile.user_id == user_id,
                AIReadingFile.status == '1'
            )
        except AIReadingFile.DoesNotExist:
            return get_json_result(
                code=RetCode.NOT_FOUND,
                message='文件不存在'
            )

        if ai_file.processing_status not in ['uploaded', 'failed']:
            return get_json_result(
                code=RetCode.OPERATING_ERROR,
                message='文件正在处理中或已完成'
            )
        
        # 更新状态为处理中
        ai_file.processing_status = 'processing'
        ai_file.processing_progress = 10.0
        ai_file.processing_message = '开始处理文件'
        ai_file.save()

        # 启动文件处理
        import sys
        import os
        sys.path.insert(0, os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(__file__))), 'ai_reading'))
        from service import AIReadingService
        ai_service = AIReadingService()

        # 在后台线程中处理文件，避免阻塞请求
        # 传递用户信息到后台线程，避免认证问题
        import threading
        def process_in_background():
            try:
                ai_service.process_file(file_id, user_id=user_id, tenant_id=tenant_id)
            except Exception as e:
                logging.exception(f"后台处理文件失败: {str(e)}")

        thread = threading.Thread(target=process_in_background)
        thread.daemon = True
        thread.start()
        
        return get_json_result(
            code=RetCode.SUCCESS,
            message='开始处理文件',
            data={
                'file_id': file_id,
                'processing_status': 'processing'
            }
        )

    except Exception as e:
        logging.exception(f"处理文件失败: {str(e)}")
        return get_json_result(
            code=RetCode.EXCEPTION_ERROR,
            message=f'处理文件失败: {str(e)}'
        )

@manager.route('/files/<file_id>/chat', methods=['POST'])  # noqa: F821
@login_required
def chat_with_document(file_id):
    """
    与文档进行问答对话
    """
    try:
        data = request.get_json()
        if not data or 'question' not in data:
            return get_json_result(
                code=RetCode.ARGUMENT_ERROR,
                message='缺少问题内容'
            )

        question = data['question'].strip()
        if not question:
            return get_json_result(
                code=RetCode.ARGUMENT_ERROR,
                message='问题不能为空'
            )

        # 获取当前用户ID（直接使用current_user.id，与其他模块保持一致）
        user_id = current_user.id

        # 检查文件是否存在且已完成处理
        try:
            ai_file = AIReadingFile.get(
                AIReadingFile.id == file_id,
                AIReadingFile.user_id == user_id,
                AIReadingFile.status == '1'
            )
        except AIReadingFile.DoesNotExist:
            return get_json_result(
                code=RetCode.NOT_FOUND,
                message='文件不存在'
            )

        if ai_file.processing_status != 'completed':
            return get_json_result(
                code=RetCode.OPERATING_ERROR,
                message='文件尚未处理完成，无法进行问答'
            )

        # 创建对话记录
        conversation_id = str(uuid.uuid4()).replace('-', '')
        session_id = data.get('session_id', str(uuid.uuid4()).replace('-', ''))

        # 获取当前时间戳
        current_time = int(time.time())

        conversation = AIReadingConversation(
            id=conversation_id,
            file_id=file_id,
            user_id=user_id,
            session_id=session_id,
            question=question,
            processing_status='pending',
            status='1',  # 明确设置状态为有效
            create_time=current_time,
            update_time=current_time
        )
        try:
            # 使用数据库连接上下文保存对话记录
            from api.db.db_models import DB
            with DB.connection_context():
                conversation.save(force_insert=True)
        except Exception as e:
            logging.error(f"对话记录创建失败: {str(e)}")
            logging.exception("详细错误信息:")
            return get_json_result(
                code=RetCode.EXCEPTION_ERROR,
                message=f'创建对话记录失败: {str(e)}'
            )

        # 检查markdown文件是否存在
        if not ai_file.markdown_file_path or not os.path.exists(ai_file.markdown_file_path):
            conversation.processing_status = 'failed'
            conversation.error_message = 'Markdown文件不存在，无法进行问答'
            conversation.save()
            return get_json_result(
                code=RetCode.OPERATING_ERROR,
                message='文档内容不可用，无法进行问答'
            )

        # 读取markdown内容
        try:
            with open(ai_file.markdown_file_path, 'r', encoding='utf-8') as f:
                markdown_content = f.read()
        except Exception as e:
            logging.error(f"读取markdown文件失败: {str(e)}")
            conversation.processing_status = 'failed'
            conversation.error_message = f'读取文档内容失败: {str(e)}'
            conversation.save()
            return get_json_result(
                code=RetCode.OPERATING_ERROR,
                message='读取文档内容失败'
            )

        # 调用AI模型进行问答
        try:
            answer = await_document_qa(question, markdown_content, ai_file.original_filename)
            confidence_score = 0.85  # 可以根据实际情况调整
        except Exception as e:
            logging.error(f"AI问答失败: {str(e)}")
            conversation.processing_status = 'failed'
            conversation.error_message = f'AI问答失败: {str(e)}'
            conversation.save()
            return get_json_result(
                code=RetCode.OPERATING_ERROR,
                message='AI问答失败，请稍后重试'
            )

        # 更新对话记录
        conversation.answer = answer
        conversation.processing_status = 'completed'
        conversation.confidence_score = confidence_score
        conversation.update_time = int(time.time())
        try:
            # 使用数据库连接上下文更新对话记录
            from api.db.db_models import DB
            with DB.connection_context():
                conversation.save()
        except Exception as e:
            logging.error(f"对话记录更新失败: {str(e)}")
            logging.exception("详细错误信息:")
            return get_json_result(
                code=RetCode.EXCEPTION_ERROR,
                message=f'更新对话记录失败: {str(e)}'
            )

        return get_json_result(
            code=RetCode.SUCCESS,
            message='问答成功',
            data={
                'conversation_id': conversation_id,
                'question': question,
                'answer': answer,
                'confidence_score': confidence_score,
                'session_id': session_id
            }
        )

    except Exception as e:
        logging.exception(f"文档问答失败: {str(e)}")
        return get_json_result(
            code=RetCode.EXCEPTION_ERROR,
            message=f'文档问答失败: {str(e)}'
        )

@manager.route('/files/<file_id>/conversations', methods=['GET'])  # noqa: F821
@login_required
def get_conversations(file_id):
    """
    获取文档的对话历史
    """
    try:
        # 获取当前用户ID（直接使用current_user.id，与其他模块保持一致）
        user_id = current_user.id

        # 检查文件是否存在
        try:
            ai_file = AIReadingFile.get(
                AIReadingFile.id == file_id,
                AIReadingFile.user_id == user_id,
                AIReadingFile.status == '1'
            )
        except AIReadingFile.DoesNotExist:
            return get_json_result(
                code=RetCode.NOT_FOUND,
                message='文件不存在'
            )

        # 分页参数
        page = int(request.args.get('page', 1))
        page_size = int(request.args.get('page_size', 20))
        session_id = request.args.get('session_id')

        # 构建查询条件
        query = AIReadingConversation.select().where(
            AIReadingConversation.file_id == file_id,
            AIReadingConversation.user_id == user_id,
            AIReadingConversation.status == '1'
        )

        if session_id:
            query = query.where(AIReadingConversation.session_id == session_id)

        # 先获取总数
        total_count = query.count()

        conversations = query.order_by(AIReadingConversation.create_time.asc()).paginate(page, page_size)

        conversation_list = []
        for conv in conversations:
            conversation_list.append({
                'conversation_id': conv.id,
                'question': conv.question,
                'answer': conv.answer,
                'confidence_score': conv.confidence_score,
                'session_id': conv.session_id,
                'processing_status': conv.processing_status,
                'create_time': conv.create_time,
                'update_time': conv.update_time
            })

        return get_json_result(
            code=RetCode.SUCCESS,
            message='获取对话历史成功',
            data={
                'conversations': conversation_list,
                'page': page,
                'page_size': page_size,
                'total': total_count,  # 使用之前计算的总数
                'file_info': {
                    'file_id': ai_file.id,
                    'original_filename': ai_file.original_filename,
                    'content_summary': ai_file.content_summary
                }
            }
        )

    except Exception as e:
        logging.exception(f"获取对话历史失败: {str(e)}")
        return get_json_result(
            code=RetCode.EXCEPTION_ERROR,
            message=f'获取对话历史失败: {str(e)}'
        )


@manager.route('/files/<file_id>/pdf', methods=['GET'])  # noqa: F821
@login_required
def get_pdf_file(file_id):
    """
    获取文件的PDF版本（用于预览）
    """
    try:
        # 获取当前用户ID（直接使用current_user.id，与其他模块保持一致）
        user_id = current_user.id

        # 检查文件是否存在
        try:
            ai_file = AIReadingFile.get(
                AIReadingFile.id == file_id,
                AIReadingFile.user_id == user_id,
                AIReadingFile.status == '1'
            )
        except AIReadingFile.DoesNotExist:
            return get_json_result(
                code=RetCode.NOT_FOUND,
                message='文件不存在'
            )

        # 检查PDF文件是否存在
        pdf_path = ai_file.pdf_file_path
        if not pdf_path or not os.path.exists(pdf_path):
            return get_json_result(
                code=RetCode.NOT_FOUND,
                message='PDF文件不存在，请等待文件处理完成'
            )

        # 返回PDF文件
        import flask
        from urllib.parse import quote

        with open(pdf_path, 'rb') as f:
            pdf_data = f.read()

        response = flask.make_response(pdf_data)
        response.headers.set('Content-Type', 'application/pdf')

        # 确保响应使用正确的字符编码
        response.charset = 'utf-8'

        # 对文件名进行URL编码以支持中文字符
        # 使用更安全的方法，提供fallback文件名
        try:
            safe_filename = quote(f"{ai_file.original_filename}.pdf", safe='')
            fallback_filename = "document.pdf"  # ASCII fallback

            # 使用RFC 5987格式，同时提供ASCII fallback
            disposition_value = f'inline; filename="{fallback_filename}"; filename*=UTF-8\'\'{safe_filename}'

            # 验证disposition_value可以编码为latin-1
            disposition_value.encode('latin-1')
            response.headers.set('Content-Disposition', disposition_value)
        except (UnicodeEncodeError, UnicodeDecodeError) as e:
            # 如果编码失败，使用简单的fallback
            logging.warning(f"文件名编码失败，使用fallback: {e}")
            response.headers.set('Content-Disposition', 'inline; filename="document.pdf"')

        return response

    except Exception as e:
        logging.exception(f"获取PDF文件失败: {str(e)}")
        return get_json_result(
            code=RetCode.EXCEPTION_ERROR,
            message=f'获取PDF文件失败: {str(e)}'
        )


@manager.route('/files/<file_id>', methods=['DELETE'])
@login_required
def delete_file(file_id):
    """删除AI阅读文件"""
    try:
        # 获取当前用户ID（直接使用current_user.id，与其他模块保持一致）
        user_id = current_user.id

        # 获取文件记录并验证所有权
        try:
            ai_file = AIReadingFile.get(
                AIReadingFile.id == file_id,
                AIReadingFile.user_id == user_id
            )
        except AIReadingFile.DoesNotExist:
            return get_json_result(
                code=RetCode.DATA_ERROR,
                message='文件不存在或无权限访问'
            )

        # 删除物理文件
        files_to_delete = []
        if ai_file.original_file_path and os.path.exists(ai_file.original_file_path):
            files_to_delete.append(ai_file.original_file_path)

        if ai_file.pdf_file_path and os.path.exists(ai_file.pdf_file_path):
            files_to_delete.append(ai_file.pdf_file_path)

        if ai_file.markdown_file_path and os.path.exists(ai_file.markdown_file_path):
            files_to_delete.append(ai_file.markdown_file_path)

        # 删除相关的对话记录
        AIReadingConversation.delete().where(
            AIReadingConversation.file_id == file_id
        ).execute()

        # 删除数据库记录
        ai_file.delete_instance()

        # 删除物理文件
        deleted_files = []
        for file_path in files_to_delete:
            try:
                os.remove(file_path)
                deleted_files.append(file_path)
            except Exception as e:
                logging.warning(f"删除文件失败: {file_path}, 错误: {e}")

        return get_json_result(
            code=RetCode.SUCCESS,
            message='文件删除成功',
            data={
                'file_id': file_id,
                'deleted_files': deleted_files
            }
        )

    except Exception as e:
        logging.exception(f"删除文件失败: {str(e)}")
        return get_json_result(
            code=RetCode.EXCEPTION_ERROR,
            message=f'删除文件失败: {str(e)}'
        )
