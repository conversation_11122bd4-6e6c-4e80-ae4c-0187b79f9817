# 汉邦高科前端部署指南

## 🚀 快速部署

### 方法一：Docker Compose 部署（推荐）

```bash
# 1. 进入项目目录
cd hbweb

# 2. 检查配置
./scripts/check-config.sh

# 3. 使用部署脚本
./deploy.sh
# 选择选项 4 (Docker Compose 部署)

# 4. 访问应用
# 前端: http://localhost:3000
# 后端API: http://localhost:9380
```

### 方法二：手动Docker Compose

```bash
# 1. 配置环境变量
cp .env.example .env.docker
# 根据需要修改 .env.docker

# 2. 启动服务
docker-compose --env-file .env.docker up --build -d

# 3. 查看状态
docker-compose ps
```

## 🔧 配置说明

### 环境配置文件

| 环境 | 配置文件 | API地址 | 用途 |
|------|----------|---------|------|
| 开发 | `.env` | `http://localhost:9380` | 本地开发 |
| Docker | `.env.docker` | `http://hbgk-api:9380` | 容器部署 |
| 生产 | `.env.production` | 根据实际配置 | 生产环境 |

### 主要配置项

```bash
# API配置
API_BASE_URL=http://localhost:9380    # 后端API地址
API_KEY=hbgk-lhYmE2YTc2M2FiYjExZjA5YTdlMDAwYz  # API密钥

# 服务端口
API_PORT=9380          # 后端API端口
FRONTEND_PORT=3000     # 前端服务端口

# 文生图服务
TEXT_TO_IMAGE_URL=http://localhost:8090/gradio
```

## 🌐 主机IP更改适配

### 自动适配（推荐）

**Docker部署**: 使用服务名通信，主机IP更改后无需修改配置

```yaml
# docker-compose.yml 中的配置
services:
  hbgk-frontend:
    environment:
      - API_BASE_URL=http://hbgk-api:9380  # 使用服务名
```

### 手动配置

**生产环境**: 主机IP更改后需要修改配置

```bash
# 1. 修改生产环境配置
vim .env.production

# 2. 更新API地址
API_BASE_URL=http://新IP地址:9380

# 3. 重新部署
docker-compose --env-file .env.production up --build -d
```

## 📋 部署检查清单

### 部署前检查

- [ ] 运行配置检查脚本: `./scripts/check-config.sh`
- [ ] 确认后端服务可访问
- [ ] 检查Docker和Docker Compose版本
- [ ] 确认端口未被占用 (3000, 9380)

### 部署后验证

```bash
# 1. 检查服务状态
docker-compose ps

# 2. 检查前端访问
curl http://localhost:3000

# 3. 检查API连接
curl http://localhost:9380/v1/user/info

# 4. 查看日志
docker-compose logs hbgk-frontend
docker-compose logs hbgk-api
```

## 🔍 故障排除

### 常见问题

#### 1. API连接失败
```bash
# 检查API地址配置
grep API_BASE_URL .env*

# 检查后端服务状态
curl http://localhost:9380/health

# 检查网络连通性
docker network ls
```

#### 2. 容器启动失败
```bash
# 查看容器日志
docker-compose logs

# 检查端口占用
netstat -tlnp | grep :3000
netstat -tlnp | grep :9380

# 重新构建镜像
docker-compose build --no-cache
```

#### 3. 环境变量未生效
```bash
# 检查环境变量文件
cat .env.docker

# 重新加载配置
docker-compose down
docker-compose --env-file .env.docker up -d
```

## 🚀 高级部署

### 生产环境部署

```bash
# 1. 准备生产配置
cp .env.example .env.production
vim .env.production  # 修改为生产环境配置

# 2. 构建生产镜像
docker build -t hbgk-frontend:prod .

# 3. 运行生产容器
docker run -d \
  --name hbgk-frontend-prod \
  -p 80:80 \
  --env-file .env.production \
  hbgk-frontend:prod
```

### 负载均衡部署

```bash
# 使用nginx进行负载均衡
# 配置多个前端实例
docker-compose scale hbgk-frontend=3
```

### SSL/HTTPS配置

```bash
# 1. 准备SSL证书
mkdir -p ssl/
# 复制证书文件到ssl目录

# 2. 修改nginx配置
# 在nginx.conf.template中添加SSL配置

# 3. 更新Docker配置
# 在docker-compose.yml中挂载SSL证书
```

## 📊 监控和维护

### 健康检查

```bash
# 检查服务健康状态
docker-compose ps
curl http://localhost:3000/health
curl http://localhost:9380/health
```

### 日志管理

```bash
# 查看实时日志
docker-compose logs -f

# 查看特定服务日志
docker-compose logs hbgk-frontend
docker-compose logs hbgk-api

# 日志轮转配置
# 在docker-compose.yml中配置logging选项
```

### 备份和恢复

```bash
# 备份配置文件
tar -czf config-backup.tar.gz .env* docker-compose.yml

# 备份数据卷
docker run --rm -v hbgk-data:/data -v $(pwd):/backup alpine tar czf /backup/data-backup.tar.gz /data
```

## 📞 技术支持

如遇到部署问题，请：

1. 运行配置检查脚本: `./scripts/check-config.sh`
2. 查看详细错误日志: `docker-compose logs`
3. 参考故障排除章节
4. 联系技术支持团队

---

**注意**: 本指南基于Docker部署方式，确保系统已安装Docker和Docker Compose。
