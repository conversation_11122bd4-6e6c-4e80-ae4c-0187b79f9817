#
#  Copyright 2024 The InfiniFlow Authors. All Rights Reserved.
#
#  Licensed under the Apache License, Version 2.0 (the "License");
#  you may not use this file except in compliance with the License.
#  You may obtain a copy of the License at
#
#      http://www.apache.org/licenses/LICENSE-2.0
#
#  Unless required by applicable law or agreed to in writing, software
#  distributed under the License is distributed on an "AS IS" BASIS,
#  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
#  See the License for the specific language governing permissions and
#  limitations under the License.
#

"""
消息处理器模块

负责处理用户输入和AI响应，包括输入验证、
输出格式化和长度限制。
"""

import re
import logging
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass


@dataclass
class ProcessedMessage:
    """处理后的消息"""
    content: str
    is_valid: bool
    error_message: Optional[str] = None
    metadata: Optional[Dict[str, Any]] = None


@dataclass
class ValidationResult:
    """验证结果"""
    is_valid: bool
    error_message: Optional[str] = None
    sanitized_content: Optional[str] = None


class MessageProcessor:
    """消息处理器
    
    负责：
    - 输入验证和清理
    - 输出格式化和长度限制
    - 敏感内容过滤
    - 消息元数据处理
    """
    
    def __init__(
        self,
        max_input_length: int = 4000,
        max_output_length: int = 8000,
        enable_content_filter: bool = True
    ):
        self.max_input_length = max_input_length
        self.max_output_length = max_output_length
        self.enable_content_filter = enable_content_filter
        
        self.logger = logging.getLogger(__name__)
        
        # 敏感词列表 - 生产环境应从配置文件加载
        self._sensitive_patterns = [
            r'\b(?:password|密码|pwd)\s*[:=]\s*\S+',
            r'\b(?:token|令牌)\s*[:=]\s*\S+',
            r'\b(?:api[_-]?key|接口密钥)\s*[:=]\s*\S+',
        ]
        
        # 编译正则表达式
        self._compiled_patterns = [
            re.compile(pattern, re.IGNORECASE) for pattern in self._sensitive_patterns
        ]
    
    def validate_input(self, content: str) -> ValidationResult:
        """验证用户输入"""
        if not content:
            return ValidationResult(
                is_valid=False,
                error_message="输入内容不能为空"
            )
        
        if not isinstance(content, str):
            return ValidationResult(
                is_valid=False,
                error_message="输入内容必须是字符串"
            )
        
        # 去除首尾空白字符
        content = content.strip()
        
        if not content:
            return ValidationResult(
                is_valid=False,
                error_message="输入内容不能只包含空白字符"
            )
        
        # 检查长度限制
        if len(content) > self.max_input_length:
            return ValidationResult(
                is_valid=False,
                error_message=f"输入内容长度不能超过{self.max_input_length}字符"
            )
        
        # 内容清理
        sanitized_content = self._sanitize_content(content)
        
        # 敏感内容检查
        if self.enable_content_filter and self._contains_sensitive_content(sanitized_content):
            return ValidationResult(
                is_valid=False,
                error_message="输入内容包含敏感信息，请重新输入"
            )
        
        return ValidationResult(
            is_valid=True,
            sanitized_content=sanitized_content
        )
    
    def process_user_input(self, content: str) -> ProcessedMessage:
        """处理用户输入"""
        validation_result = self.validate_input(content)
        
        if not validation_result.is_valid:
            return ProcessedMessage(
                content="",
                is_valid=False,
                error_message=validation_result.error_message
            )
        
        processed_content = validation_result.sanitized_content
        
        # 添加处理元数据
        metadata = {
            "original_length": len(content),
            "processed_length": len(processed_content),
            "processing_timestamp": self._get_current_timestamp()
        }
        
        return ProcessedMessage(
            content=processed_content,
            is_valid=True,
            metadata=metadata
        )
    
    def format_ai_response(self, content: str) -> ProcessedMessage:
        """格式化AI响应"""
        if not content:
            return ProcessedMessage(
                content="处理您的请求。",
                is_valid=True
            )
        
        if not isinstance(content, str):
            content = str(content)
        
        # 移除错误标记
        if content.startswith("**ERROR**"):
            error_content = content.replace("**ERROR**:", "").strip()
            self.logger.error(f"AI response error: {error_content}")
            return ProcessedMessage(
                content="抱歉，处理您的请求时出现了问题。请稍后重试。",
                is_valid=True,
                error_message=error_content
            )
        
        # 长度限制
        if len(content) > self.max_output_length:
            content = content[:self.max_output_length]
            # 尝试在句子边界截断
            content = self._truncate_at_sentence_boundary(content)
            content += "\n\n[回复已截断，如需完整内容请重新提问]"
        
        # 格式化处理
        formatted_content = self._format_response_content(content)
        
        metadata = {
            "original_length": len(content),
            "formatted_length": len(formatted_content),
            "was_truncated": len(content) > self.max_output_length,
            "processing_timestamp": self._get_current_timestamp()
        }
        
        return ProcessedMessage(
            content=formatted_content,
            is_valid=True,
            metadata=metadata
        )
    
    def _sanitize_content(self, content: str) -> str:
        """清理内容"""
        # 移除控制字符（保留换行和制表符）
        content = re.sub(r'[\x00-\x08\x0B\x0C\x0E-\x1F\x7F]', '', content)
        
        # 规范化空白字符
        content = re.sub(r'\s+', ' ', content)
        
        # 移除HTML标签（基础清理）
        content = re.sub(r'<[^>]+>', '', content)
        
        return content.strip()
    
    def _contains_sensitive_content(self, content: str) -> bool:
        """检查是否包含敏感内容"""
        for pattern in self._compiled_patterns:
            if pattern.search(content):
                return True
        return False
    
    def _truncate_at_sentence_boundary(self, content: str) -> str:
        """在句子边界截断内容"""
        # 中文句号、英文句号、问号、感叹号
        sentence_endings = r'[。.!?！？]'
        
        # 从后往前查找句子结束符
        matches = list(re.finditer(sentence_endings, content))
        if matches:
            last_match = matches[-1]
            return content[:last_match.end()]
        
        # 如果没有找到句子结束符，在最后一个空格处截断
        last_space = content.rfind(' ')
        if last_space > len(content) * 0.8:  # 只有在接近末尾时才在空格处截断
            return content[:last_space]
        
        return content
    
    def _format_response_content(self, content: str) -> str:
        """格式化响应内容"""
        # 移除多余的空行
        content = re.sub(r'\n\s*\n\s*\n', '\n\n', content)
        
        # 确保代码块格式正确
        content = self._fix_code_blocks(content)
        
        return content.strip()
    
    def _fix_code_blocks(self, content: str) -> str:
        """修复代码块格式"""
        # 简单的代码块修复
        lines = content.split('\n')
        in_code_block = False
        fixed_lines = []
        
        for line in lines:
            if line.strip().startswith('```'):
                in_code_block = not in_code_block
            fixed_lines.append(line)
        
        # 如果代码块没有正确关闭，添加结束标记
        if in_code_block:
            fixed_lines.append('```')
        
        return '\n'.join(fixed_lines)
    
    def _get_current_timestamp(self) -> float:
        """获取当前时间戳"""
        import time
        return time.time()
    
    def get_processor_stats(self) -> Dict[str, Any]:
        """获取处理器统计信息"""
        return {
            "max_input_length": self.max_input_length,
            "max_output_length": self.max_output_length,
            "enable_content_filter": self.enable_content_filter,
            "sensitive_patterns_count": len(self._sensitive_patterns)
        }
