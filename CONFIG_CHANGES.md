# AI Reading 模块配置更改说明

## 更改概述

本次更改将AI Reading模块的Mineru服务器配置从硬编码改为可配置的环境变量，并移除了所有使用互联网Mineru服务器的代码。

## 主要更改

### 1. 环境变量配置

在以下配置文件中添加了Mineru服务器配置：

- `sz/.env` - 主要环境配置文件
- `sz/.env.simple` - 简化部署配置文件
- `hbweb/.env` - 前端环境配置文件
- `hbweb/.env.example` - 前端配置示例文件
- `hbweb/.env.production` - 前端生产环境配置
- `hbweb/.env.docker` - 前端Docker配置

新增的环境变量：
```bash
# Mineru服务器地址 (用于PDF文档解析)
MINERU_SERVER_URL=http://192.168.1.138:7860
# Mineru API密钥 (如果需要)
MINERU_API_KEY=
```

### 2. 代码更改

#### ai_reading_service.py
- 添加了`python-dotenv`导入和环境变量加载
- 修改`__init__`方法从环境变量读取Mineru服务器配置
- 更新`call_mineru_api`函数使用配置的服务器地址
- 移除`upload_file_to_cdn`中的互联网CDN代码
- 简化`check_mineru_status`和`download_and_extract_result`函数
- 更新`process_file`函数适应本地处理方式

#### service.py
- 应用了与ai_reading_service.py相同的更改

#### api/apps/ai_reading_app.py
- 移除硬编码的互联网Mineru API URL
- 改为从环境变量读取配置

### 3. 前端配置更改

#### docker-entrypoint.sh
- 添加Mineru环境变量的默认值设置
- 在启动日志中显示Mineru服务器配置

#### .umirc.ts
- 在`define`配置中添加Mineru环境变量定义

### 4. 依赖更新

#### ai_reading/requirements.txt
- 添加`python-dotenv>=0.19.0`依赖

## 部署说明

### 1. 环境变量配置

在部署时，需要根据实际的Mineru服务器地址修改环境变量：

```bash
# 修改为实际的Mineru服务器地址
MINERU_SERVER_URL=http://your-mineru-server:7860

# 如果Mineru服务器需要API密钥，请设置
MINERU_API_KEY=your-api-key
```

### 2. 安装依赖

确保安装了新的依赖：

```bash
pip install python-dotenv>=0.19.0
```

### 3. 测试配置

可以使用提供的测试脚本验证配置：

```bash
cd ai_reading
python test_config.py
```

## 优势

1. **配置灵活性**: 可以通过环境变量轻松更改Mineru服务器地址
2. **部署友好**: 不同环境可以使用不同的配置文件
3. **安全性**: 移除了硬编码的API密钥
4. **本地化**: 完全使用本地Mineru服务器，不依赖互联网服务

## 注意事项

1. 确保Mineru服务器在指定地址可访问
2. 如果更改了Mineru服务器地址，需要更新相应的环境变量
3. 在Docker部署时，确保容器间网络配置正确
4. 测试时请确保Mineru服务器正常运行

## 兼容性

- 向后兼容：如果未设置环境变量，将使用默认的服务器地址
- 现有功能不受影响：所有AI Reading功能保持不变
- 数据库结构无变化：不需要数据库迁移
