version: '3.8'

services:
  # 前端服务
  hbgk-frontend:
    image: hbgk-frontend:${IMAGE_TAG:-latest}
    container_name: hbgk-frontend
    ports:
      - "${FRONTEND_PORT:-3000}:80"
    environment:
      - NODE_ENV=production
      - API_BASE_URL=http://hbgk-backend:9380
      - API_KEY=${API_KEY:-hbgk-lhYmE2YTc2M2FiYjExZjA5YTdlMDAwYz}
      - TEXT_TO_IMAGE_URL=${TEXT_TO_IMAGE_URL:-http://*************:8090/gradio}
    networks:
      - hbgk-network
    restart: on-failure
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost/"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # 后端服务
  hbgk-backend:
    image: hbgk-api:${IMAGE_TAG:-latest}
    container_name: hbgk-backend
    ports:
      - "${BACKEND_PORT:-9380}:9380"
    environment:
      - PYTHONPATH=/ragflow
      - HOST_IP=0.0.0.0
      - HTTP_PORT=9380
      # 数据库配置
      - MYSQL_HOST=${MYSQL_HOST:-ragflow-mysql}
      - MYSQL_USER=${MYSQL_USER:-root}
      - MYSQL_PASSWORD=${MYSQL_PASSWORD:-infini_rag_flow}
      - MYSQL_DBNAME=${MYSQL_DBNAME:-rag_flow}
      - MYSQL_PORT=5455
      # Redis配置
      - REDIS_HOST=${REDIS_HOST:-redis}
      - REDIS_PASSWORD=${REDIS_PASSWORD:-infini_rag_flow}
      # MinIO配置
      - MINIO_HOST=${MINIO_HOST:-minio}
      - MINIO_USER=${MINIO_USER:-rag_flow}
      - MINIO_PASSWORD=${MINIO_PASSWORD:-infini_rag_flow}
      # Elasticsearch配置
      - ES_HOST=${ES_HOST:-ragflow-es-01}
      - ES_USER=${ES_USER:-elastic}
      - ELASTIC_PASSWORD=${ELASTIC_PASSWORD:-infini_rag_flow}
    networks:
      - hbgk-network
    restart: on-failure
    depends_on:
#      mysql:
#        condition: service_healthy
      redis:
        condition: service_healthy
      minio:
        condition: service_healthy
    volumes:
      - hbgk-data:/ragflow/data
      - hbgk-logs:/ragflow/logs
      - hbgk-temp:/ragflow/temp

  # MySQL数据库
#  mysql:
    # mysql:5.7 linux/arm64 image is unavailable.
##    image: mysql:8.0.39
#    container_name: ragflow-mysql
#    environment:
#      - MYSQL_ROOT_PASSWORD=${MYSQL_PASSWORD:-infini_rag_flow}
#    #  - MYSQL_DATABASE=${MYSQL_DBNAME:-rag_flow}
#      - MYSQL_USER=${MYSQL_USER:-root}
#      - MYSQL_PASSWORD=${MYSQL_PASSWORD:-infini_rag_flow}
#    command:
#      --max_connections=1000
#      --character-set-server=utf8mb4
#      --collation-server=utf8mb4_unicode_ci
#      --default-authentication-plugin=mysql_native_password
#      --binlog_expire_logs_seconds=604800
#    ports:
#      - "${MYSQL_PORT:-3306}:3306"
#    volumes:
#      - mysql-data:/var/lib/mysql
#      - ./docker/init.sql:/docker-entrypoint-initdb.d/init.sql:ro
#    networks:
#      - hbgk-network
#    restart: unless-stopped
#    healthcheck:
#      test: ["CMD", "mysqladmin", "ping", "-h", "localhost"]
#      interval: 30s
 #     timeout: 10s
 #     retries: 5
#      start_period: 30s
#    healthcheck:
#      test: ["CMD", "mysqladmin" ,"ping", "-uroot", "-p${MYSQL_PASSWORD}"]
#      interval: 10s
#      timeout: 10s
#      retries: 3
#    restart: on-failure

  # Redis缓存
  redis:
    image: redis:7-alpine
    container_name: hbgk-redis
    command: redis-server --requirepass ${REDIS_PASSWORD:-infini_rag_flow}
    ports:
      - "${REDIS_PORT:-6379}:6379"
    volumes:
      - redis-data:/data
    networks:
      - hbgk-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "redis-cli", "--raw", "incr", "ping"]
      interval: 30s
      timeout: 10s
      retries: 5
      start_period: 30s

  # MinIO对象存储
  minio:
    image: minio/minio:latest
    container_name: hbgk-minio
    environment:
      - MINIO_ROOT_USER=${MINIO_USER:-rag_flow}
      - MINIO_ROOT_PASSWORD=${MINIO_PASSWORD:-infini_rag_flow}
    ports:
      - "${MINIO_PORT:-9000}:9000"
      - "${MINIO_CONSOLE_PORT:-9001}:9001"
    volumes:
      - minio-data:/data
    networks:
      - hbgk-network
    restart: unless-stopped
    command: server /data --console-address ":9001"
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:9000/minio/health/live"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 30s

  # Elasticsearch搜索引擎
  ragflow-es-01:
    image: docker.elastic.co/elasticsearch/elasticsearch:8.11.0
    container_name: ragflow-es-01
    environment:
      - discovery.type=single-node
      - ES_JAVA_OPTS=-Xms1g -Xmx1g
      - xpack.security.enabled=false
      - xpack.security.enrollment.enabled=false
    ports:
      - "${ES_PORT:-9200}:9200"
    volumes:
      - es-data:/usr/share/elasticsearch/data
    networks:
      - hbgk-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "curl -f http://localhost:9200/_cluster/health || exit 1"]
      interval: 30s
      timeout: 10s
      retries: 5
      start_period: 60s

networks:
  hbgk-network:
    driver: bridge
    name: hbgk-network

volumes:
  hbgk-data:
    driver: local
    name: hbgk-data
  hbgk-logs:
    driver: local
    name: hbgk-logs
  hbgk-temp:
    driver: local
    name: hbgk-temp
  mysql-data:
    driver: local
    name: hbgk-mysql-data
  redis-data:
    driver: local
    name: hbgk-redis-data
  minio-data:
    driver: local
    name: hbgk-minio-data
  es-data:
    driver: local
    name: hbgk-es-data
