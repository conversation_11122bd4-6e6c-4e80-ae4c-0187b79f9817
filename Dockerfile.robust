# Frontend Dockerfile for HanBangGaoKe - Robust Version
# Enhanced error handling and China mirror support

# Build stage
FROM node:18-alpine as builder

# Set working directory
WORKDIR /app

# Add China mirror for faster package installation
RUN sed -i 's/dl-cdn.alpinelinux.org/mirrors.aliyun.com/g' /etc/apk/repositories

# Install build dependencies with error handling
RUN echo "Installing build dependencies..." && \
    apk add --no-cache python3 make g++ && \
    echo "Build dependencies installed successfully"

# Copy package files first for better caching
COPY package*.json ./

# Install dependencies with error handling
RUN echo "Installing npm dependencies..." && \
    npm ci && \
    echo "npm dependencies installed successfully"

# Copy source code
COPY . .

# Build the application with error handling
RUN echo "Building application..." && \
    npm run build && \
    echo "Application built successfully" && \
    ls -la dist/

# Production stage
FROM nginx:alpine

# Add China mirror for faster package installation
RUN sed -i 's/dl-cdn.alpinelinux.org/mirrors.aliyun.com/g' /etc/apk/repositories

# Install required packages with error handling
RUN echo "Installing system packages..." && \
    apk add --no-cache \
        gettext \
        curl \
        tzdata && \
    echo "System packages installed successfully"

# Set timezone
ENV TZ=Asia/Shanghai
RUN ln -snf /usr/share/zoneinfo/$TZ /etc/localtime && echo $TZ > /etc/timezone

# Copy built assets from builder stage
COPY --from=builder /app/dist /usr/share/nginx/html

# Copy nginx configuration template
COPY nginx.conf.template /etc/nginx/templates/default.conf.template

# Copy startup script
COPY docker-entrypoint.sh /docker-entrypoint.sh
RUN chmod +x /docker-entrypoint.sh

# Check if nginx user exists and handle permissions properly
RUN echo "Setting up nginx user and permissions..." && \
    # Check if nginx user exists
    if id nginx >/dev/null 2>&1; then \
        echo "nginx user already exists"; \
    else \
        echo "Creating nginx user..."; \
        addgroup -g 101 -S nginx && \
        adduser -S -D -H -u 101 -h /var/cache/nginx -s /sbin/nologin -G nginx nginx; \
    fi && \
    # Create necessary directories if they don't exist
    mkdir -p /var/cache/nginx /var/log/nginx /etc/nginx/conf.d && \
    # Set proper permissions
    chown -R nginx:nginx /usr/share/nginx/html && \
    chown -R nginx:nginx /var/cache/nginx && \
    chown -R nginx:nginx /var/log/nginx && \
    chown -R nginx:nginx /etc/nginx/conf.d && \
    # Ensure proper permissions
    chmod -R 755 /usr/share/nginx/html && \
    chmod -R 755 /var/cache/nginx && \
    chmod -R 755 /var/log/nginx && \
    echo "Permissions set successfully"

# Verify setup
RUN echo "Verifying setup..." && \
    nginx -t && \
    echo "nginx configuration is valid" && \
    ls -la /usr/share/nginx/html/ && \
    echo "Setup verification completed"

# Expose port
EXPOSE 80

# Enhanced health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=30s --retries=3 \
    CMD curl -f http://localhost/ || exit 1

# Use custom entrypoint to handle environment variable substitution
ENTRYPOINT ["/docker-entrypoint.sh"]
CMD ["nginx", "-g", "daemon off;"]
