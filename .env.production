# 生产环境配置文件
# 用于生产环境部署时的环境变量配置

# API Configuration - 生产环境需要根据实际部署情况修改
# 请根据实际的后端API服务器地址进行配置
API_BASE_URL=http://localhost:9380
API_KEY=hbgk-lhYmE2YTc2M2FiYjExZjA5YTdlMDAwYz

# Text to Image Service Configuration
TEXT_TO_IMAGE_URL=http://*************:8090/gradio

# AI Reading - Mineru Service Configuration
MINERU_SERVER_URL=http://*************:7860
MINERU_API_KEY=

# Application Configuration
NODE_ENV=production
PORT=3000
HOST=0.0.0.0

# Build Configuration
GENERATE_SOURCEMAP=false
DISABLE_ESLINT_PLUGIN=true

# Feature Flags
ENABLE_CONVERSATIONS=true
ENABLE_ADVANCED_SEARCH=true
ENABLE_BATCH_OPERATIONS=false

# Auto LLM Configuration for New Users
# 用户注册后自动添加的默认LLM配置
# Chat Model Configuration
AUTO_LLM_CHAT_FACTORY=VLLM
AUTO_LLM_CHAT_NAME=Qwen3-32B
AUTO_LLM_CHAT_API_BASE=http://host.docker.internal:8000/v1
AUTO_LLM_CHAT_MAX_TOKENS=8192

# Embedding Model Configuration
AUTO_LLM_EMBEDDING_FACTORY=VLLM
AUTO_LLM_EMBEDDING_NAME=bge-m3
AUTO_LLM_EMBEDDING_API_BASE=http://host.docker.internal:18080/v1
AUTO_LLM_EMBEDDING_MAX_TOKENS=8192

# Rerank Model Configuration
AUTO_LLM_RERANK_FACTORY=VLLM
AUTO_LLM_RERANK_NAME=bge-reranker-v2-m3
AUTO_LLM_RERANK_API_BASE=http://host.docker.internal:18081/v1
AUTO_LLM_RERANK_MAX_TOKENS=8192

# Security Configuration
ENABLE_HTTPS=false
SSL_CERT_PATH=
SSL_KEY_PATH=
