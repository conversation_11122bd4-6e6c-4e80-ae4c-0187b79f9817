#
#  AI Reading Models - AI阅读数据模型
#  定义AI阅读相关的数据库模型
#

import os
import sys
from datetime import datetime

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, project_root)

try:
    from api.db.db_models import AIReadingFile, AIReadingConversation
except ImportError:
    # 如果无法导入，定义基本的模型结构
    class AIReadingFile:
        """AI阅读文件模型"""
        def __init__(self, **kwargs):
            for key, value in kwargs.items():
                setattr(self, key, value)
        
        def save(self):
            """保存到数据库"""
            pass
        
        @classmethod
        def get(cls, *args, **kwargs):
            """从数据库获取记录"""
            pass
        
        @classmethod
        def select(cls):
            """查询记录"""
            pass
    
    class AIReadingConversation:
        """AI阅读对话模型"""
        def __init__(self, **kwargs):
            for key, value in kwargs.items():
                setattr(self, key, value)
        
        def save(self):
            """保存到数据库"""
            pass

# 导出模型
__all__ = ['AIReadingFile', 'AIReadingConversation']
