#
#  Copyright 2024 The InfiniFlow Authors. All Rights Reserved.
#
#  Licensed under the Apache License, Version 2.0 (the "License");
#  you may not use this file except in compliance with the License.
#  You may obtain a copy of the License at
#
#      http://www.apache.org/licenses/LICENSE-2.0
#
#  Unless required by applicable law or agreed to in writing, software
#  distributed under the License is distributed on an "AS IS" BASIS,
#  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
#  See the License for the specific language governing permissions and
#  limitations under the License.
#

"""
聊天机器人配置模块

定义聊天机器人的各种配置参数和默认值。
"""

import os
from typing import Dict, Any


class ChatBotConfig:
    """聊天机器人配置类"""
    
    # 内存管理配置
    MAX_MESSAGES_PER_SESSION = int(os.getenv('CHATBOT_MAX_MESSAGES_PER_SESSION', 50))
    MAX_SESSION_AGE_HOURS = int(os.getenv('CHATBOT_MAX_SESSION_AGE_HOURS', 24))
    MAX_TOTAL_SESSIONS = int(os.getenv('CHATBOT_MAX_TOTAL_SESSIONS', 1000))
    
    # 消息处理配置
    MAX_INPUT_LENGTH = int(os.getenv('CHATBOT_MAX_INPUT_LENGTH', 4000))
    MAX_OUTPUT_LENGTH = int(os.getenv('CHATBOT_MAX_OUTPUT_LENGTH', 8000))
    ENABLE_CONTENT_FILTER = os.getenv('CHATBOT_ENABLE_CONTENT_FILTER', 'true').lower() == 'true'
    
    # LLM生成配置
    DEFAULT_TEMPERATURE = float(os.getenv('CHATBOT_DEFAULT_TEMPERATURE', 0.7))
    DEFAULT_TOP_P = float(os.getenv('CHATBOT_DEFAULT_TOP_P', 0.9))
    DEFAULT_MAX_TOKENS = int(os.getenv('CHATBOT_DEFAULT_MAX_TOKENS', 8000))
    DEFAULT_FREQUENCY_PENALTY = float(os.getenv('CHATBOT_DEFAULT_FREQUENCY_PENALTY', 0.0))
    DEFAULT_PRESENCE_PENALTY = float(os.getenv('CHATBOT_DEFAULT_PRESENCE_PENALTY', 0.0))
    
    # 系统提示配置
    DEFAULT_SYSTEM_PROMPT = os.getenv(
        'CHATBOT_DEFAULT_SYSTEM_PROMPT',
        "你是一个有用的AI助手。请用中文回答问题，保持友好和专业的语调。"
    )
    
    # API配置
    API_PREFIX = os.getenv('CHATBOT_API_PREFIX', '/api/v1/chatbot')
    ENABLE_CORS = os.getenv('CHATBOT_ENABLE_CORS', 'true').lower() == 'true'
    
    # 日志配置
    LOG_LEVEL = os.getenv('CHATBOT_LOG_LEVEL', 'INFO')
    LOG_FORMAT = os.getenv(
        'CHATBOT_LOG_FORMAT',
        '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    
    # 敏感词配置
    SENSITIVE_WORDS_FILE = os.getenv('CHATBOT_SENSITIVE_WORDS_FILE', '')
    
    @classmethod
    def get_memory_config(cls) -> Dict[str, Any]:
        """获取内存管理配置"""
        return {
            'max_messages_per_session': cls.MAX_MESSAGES_PER_SESSION,
            'max_session_age_hours': cls.MAX_SESSION_AGE_HOURS,
            'max_total_sessions': cls.MAX_TOTAL_SESSIONS
        }
    
    @classmethod
    def get_processor_config(cls) -> Dict[str, Any]:
        """获取消息处理器配置"""
        return {
            'max_input_length': cls.MAX_INPUT_LENGTH,
            'max_output_length': cls.MAX_OUTPUT_LENGTH,
            'enable_content_filter': cls.ENABLE_CONTENT_FILTER
        }
    
    @classmethod
    def get_llm_config(cls) -> Dict[str, Any]:
        """获取LLM生成配置"""
        return {
            'temperature': cls.DEFAULT_TEMPERATURE,
            'top_p': cls.DEFAULT_TOP_P,
            'max_tokens': cls.DEFAULT_MAX_TOKENS,
            'frequency_penalty': cls.DEFAULT_FREQUENCY_PENALTY,
            'presence_penalty': cls.DEFAULT_PRESENCE_PENALTY
        }
    
    @classmethod
    def get_all_config(cls) -> Dict[str, Any]:
        """获取所有配置"""
        return {
            'memory': cls.get_memory_config(),
            'processor': cls.get_processor_config(),
            'llm': cls.get_llm_config(),
            'system_prompt': cls.DEFAULT_SYSTEM_PROMPT,
            'api_prefix': cls.API_PREFIX,
            'enable_cors': cls.ENABLE_CORS,
            'log_level': cls.LOG_LEVEL
        }


# 预定义的系统提示模板
SYSTEM_PROMPT_TEMPLATES = {
    'default': "你是一个有用的AI助手。请用中文回答问题，保持友好和专业的语调。",
    'professional': "你是一个专业的AI助手，专门为企业用户提供服务。请保持正式和专业的语调，提供准确和有用的信息。",
    'casual': "你是一个友好的AI助手，喜欢用轻松愉快的方式与用户交流。请保持友好和幽默的语调。",
    'technical': "你是一个技术专家AI助手，专门回答技术相关问题。请提供详细和准确的技术信息。",
    'creative': "你是一个富有创意的AI助手，擅长帮助用户进行创意思考和问题解决。请发挥想象力，提供创新的想法。"
}


# 敏感词默认模式
DEFAULT_SENSITIVE_PATTERNS = [
    r'\b(?:password|密码|pwd)\s*[:=]\s*\S+',
    r'\b(?:token|令牌)\s*[:=]\s*\S+',
    r'\b(?:api[_-]?key|接口密钥)\s*[:=]\s*\S+',
    r'\b(?:secret|秘钥|机密)\s*[:=]\s*\S+',
    r'\b(?:credit[_-]?card|信用卡)\s*[:=]?\s*\d+',
    r'\b(?:phone|电话|手机)\s*[:=]?\s*\d{11}',
    r'\b(?:email|邮箱)\s*[:=]?\s*\S+@\S+\.\S+',
    r'\b(?:address|地址)\s*[:=]?\s*.{10,}',
]


def load_sensitive_words_from_file(file_path: str) -> list:
    """从文件加载敏感词列表"""
    if not file_path or not os.path.exists(file_path):
        return DEFAULT_SENSITIVE_PATTERNS
    
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            patterns = []
            for line in f:
                line = line.strip()
                if line and not line.startswith('#'):
                    patterns.append(line)
            return patterns if patterns else DEFAULT_SENSITIVE_PATTERNS
    except Exception:
        return DEFAULT_SENSITIVE_PATTERNS


def get_system_prompt_template(template_name: str) -> str:
    """获取系统提示模板"""
    return SYSTEM_PROMPT_TEMPLATES.get(template_name, SYSTEM_PROMPT_TEMPLATES['default'])


# 导出配置实例
config = ChatBotConfig()
