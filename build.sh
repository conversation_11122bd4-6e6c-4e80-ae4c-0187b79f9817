#!/bin/bash

# HanBangGaoKe Docker Build Script
# 汉邦高科Docker构建脚本

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if Docker is installed and running
check_docker() {
    log_info "检查Docker环境..."
    
    if ! command -v docker &> /dev/null; then
        log_error "Docker未安装，请先安装Docker"
        exit 1
    fi
    
    if ! docker info &> /dev/null; then
        log_error "Docker服务未运行，请启动Docker服务"
        exit 1
    fi
    
    log_success "Docker环境检查通过"
}

# Build backend image
build_backend() {
    local dockerfile_name="${DOCKERFILE_BACKEND:-Dockerfile.backend}"
    log_info "构建Backend镜像 (使用 $dockerfile_name)..."

    # Check if BuildKit is available, if not use legacy builder
    if docker buildx version >/dev/null 2>&1; then
        log_info "使用BuildKit构建..."
        export DOCKER_BUILDKIT=1
        docker build \
            --file "$dockerfile_name" \
            --tag hbgk-api:latest \
            --build-arg NEED_MIRROR=1 \
            --build-arg LIGHTEN=0 \
            --progress=plain \
            ../
    else
        log_warning "BuildKit不可用，使用传统构建方式..."
        unset DOCKER_BUILDKIT
        docker build \
            --file "$dockerfile_name" \
            --tag hbgk-api:latest \
            --build-arg NEED_MIRROR=1 \
            --build-arg LIGHTEN=0 \
            ../
    fi

    log_success "Backend镜像构建成功"
}

# Build backend image with robust Dockerfile (skips Microsoft ODBC)
build_backend_robust() {
    log_info "构建Backend镜像 (Robust版本，跳过Microsoft ODBC)..."
    DOCKERFILE_BACKEND="Dockerfile.backend.robust" build_backend
}

# Build frontend image
build_frontend() {
    log_info "构建Frontend镜像..."

    if [[ ! -d "../hbweb" ]]; then
        log_error "Frontend源码目录不存在: ../hbweb"
        return 1
    fi

    if [[ ! -f "../hbweb/Dockerfile" ]]; then
        log_error "Frontend Dockerfile不存在: ../hbweb/Dockerfile"
        return 1
    fi

    # Check if BuildKit is available, if not use legacy builder
    if docker buildx version >/dev/null 2>&1; then
        log_info "使用BuildKit构建..."
        export DOCKER_BUILDKIT=1
        docker build \
            --tag hbgk-web:latest \
            --progress=plain \
            ../hbweb/
    else
        log_warning "BuildKit不可用，使用传统构建方式..."
        unset DOCKER_BUILDKIT
        docker build \
            --tag hbgk-web:latest \
            ../hbweb/
    fi

    log_success "Frontend镜像构建成功"
}

# Show usage
show_usage() {
    echo "用法: $0 [选项]"
    echo "选项:"
    echo "  backend         仅构建Backend镜像"
    echo "  backend-robust  构建Backend镜像 (Robust版本，跳过Microsoft ODBC)"
    echo "  frontend        仅构建Frontend镜像"
    echo "  all             构建所有镜像 (默认)"
    echo "  --help, -h      显示此帮助信息"
    echo ""
    echo "环境变量:"
    echo "  DOCKERFILE_BACKEND  指定Backend Dockerfile文件名 (默认: Dockerfile.backend)"
}

# Main execution
main() {
    echo "========================================"
    echo "汉邦高科 Docker 镜像构建脚本"
    echo "========================================"
    echo
    
    check_docker
    
    case "${1:-all}" in
        "backend")
            build_backend
            ;;
        "backend-robust")
            build_backend_robust
            ;;
        "frontend")
            build_frontend
            ;;
        "all")
            build_backend
            build_frontend
            ;;
        "--help"|"-h")
            show_usage
            exit 0
            ;;
        *)
            log_error "未知选项: $1"
            show_usage
            exit 1
            ;;
    esac
    
    echo
    log_success "构建完成！"
    log_info "查看构建的镜像:"
    docker images | grep -E "hbgk-(api|web)"
}

# Run main function
main "$@"
