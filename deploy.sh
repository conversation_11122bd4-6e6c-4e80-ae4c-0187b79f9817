#!/bin/bash

# 汉邦高科 Docker 部署脚本
# 支持完整部署和简化部署两种模式

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 显示帮助信息
show_help() {
    echo "汉邦高科 Docker 部署脚本"
    echo ""
    echo "用法: $0 [选项]"
    echo ""
    echo "选项:"
    echo "  -m, --mode MODE     部署模式: full (完整部署) 或 simple (简化部署)"
    echo "  -h, --help          显示此帮助信息"
    echo "  --build             强制重新构建镜像"
    echo "  --stop              停止所有服务"
    echo "  --clean             清理所有容器和镜像"
    echo "  --debug             使用调试模式构建"
    echo "  --robust            使用稳健版本构建"
    echo "  --test-venv         测试Python venv环境"
    echo "  --test-pytorch      测试PyTorch和NLTK环境"
    echo ""
    echo "部署模式说明:"
    echo "  full    - 完整部署，包含前端、后端、MySQL、Redis、MinIO、Elasticsearch"
    echo "  simple  - 简化部署，仅包含前端和后端，需要外部数据库"
    echo ""
    echo "简化部署准备:"
    echo "  1. 设置外部服务: 参考 EXTERNAL_SERVICES_SETUP.md"
    echo "  2. 检查服务连接: ./check-external-services.sh"
    echo "  3. 配置环境变量: 使用 .env.simple 或修改 .env"
    echo ""
    echo "示例:"
    echo "  $0 -m full          # 完整部署"
    echo "  $0 -m simple        # 简化部署"
    echo "  $0 --build -m full  # 强制重新构建并完整部署"
    echo "  $0 --debug          # 调试模式构建"
    echo "  $0 --robust -m full # 使用稳健版本部署"
    echo "  $0 --stop           # 停止所有服务"
    echo "  $0 --clean          # 清理环境"
}

# 检查Docker和Docker Compose
check_requirements() {
    log_info "检查系统要求..."
    
    if ! command -v docker &> /dev/null; then
        log_error "Docker 未安装，请先安装 Docker"
        exit 1
    fi
    
    if ! command -v docker-compose &> /dev/null && ! docker compose version &> /dev/null; then
        log_error "Docker Compose 未安装，请先安装 Docker Compose"
        exit 1
    fi
    
    log_success "系统要求检查通过"
}

# 检查环境变量文件
check_env_file() {
    if [ ! -f .env ]; then
        log_warning ".env 文件不存在，从 .env.example 复制..."
        if [ -f .env.example ]; then
            cp .env.example .env
            log_info "请编辑 .env 文件配置相关参数"
        else
            log_error ".env.example 文件不存在"
            exit 1
        fi
    fi

    # 确保.env文件被Docker Compose读取
    log_info "验证.env文件配置..."
    if [ -f .env ]; then
        log_success ".env文件存在，将被Docker Compose自动读取"
        # 显示关键配置项
        log_info "关键配置项："
        grep -E "^(FRONTEND_PORT|BACKEND_PORT|MYSQL_HOST|REDIS_HOST)" .env | head -5 || true
    fi
}

# 停止服务
stop_services() {
    log_info "停止所有服务..."
    
    if [ -f docker-compose.production.yml ]; then
        docker-compose -f docker-compose.production.yml down
    fi
    
    if [ -f docker-compose.simple.yml ]; then
        docker-compose -f docker-compose.simple.yml down
    fi
    
    log_success "服务已停止"
}

# 清理环境
clean_environment() {
    log_info "清理Docker环境..."
    
    stop_services
    
    # 删除相关容器
    docker ps -a --filter "name=hbgk-" -q | xargs -r docker rm -f
    
    # 删除相关镜像
    docker images --filter "reference=*hbgk*" -q | xargs -r docker rmi -f
    
    # 清理未使用的资源
    docker system prune -f
    
    log_success "环境清理完成"
}

# 构建镜像
build_images() {
    local force_build=$1

    # 检查是否支持BuildKit
    if docker buildx version >/dev/null 2>&1; then
        log_info "使用BuildKit构建镜像..."
        export DOCKER_BUILDKIT=1
        export COMPOSE_DOCKER_CLI_BUILD=1
    else
        log_warning "BuildKit不可用，使用兼容模式..."
        # 使用兼容版本的Dockerfile
        if [ -f "Dockerfile.backend.compatible" ]; then
            sed -i 's|dockerfile: Dockerfile.backend|dockerfile: Dockerfile.backend.compatible|g' $COMPOSE_FILE
        fi
    fi

    if [ "$force_build" = "true" ]; then
        log_info "强制重新构建镜像..."
        docker-compose -f $COMPOSE_FILE build --no-cache
    else
        log_info "构建镜像..."
        docker-compose -f $COMPOSE_FILE build
    fi
}

# 调试构建
debug_build() {
    log_info "开始调试构建..."

    if docker buildx version >/dev/null 2>&1; then
        export DOCKER_BUILDKIT=1
        export COMPOSE_DOCKER_CLI_BUILD=1
    fi

    log_info "构建调试镜像..."
    docker build -f Dockerfile.debug -t hbgk-debug:latest .

    if [ $? -eq 0 ]; then
        log_success "调试镜像构建成功！"
        log_info "运行调试容器..."
        docker run --rm -it hbgk-debug:latest
    else
        log_error "调试镜像构建失败"
        exit 1
    fi
}

# venv测试构建
test_venv_build() {
    log_info "开始Python venv环境测试..."

    if docker buildx version >/dev/null 2>&1; then
        export DOCKER_BUILDKIT=1
        export COMPOSE_DOCKER_CLI_BUILD=1
    fi

    log_info "构建venv测试镜像..."
    docker build -f Dockerfile.venv-test -t hbgk-venv-test:latest .

    if [ $? -eq 0 ]; then
        log_success "venv测试镜像构建成功！"
        log_info "运行venv测试容器..."
        docker run --rm -it hbgk-venv-test:latest
    else
        log_error "venv测试镜像构建失败"
        exit 1
    fi
}

# PyTorch测试构建
test_pytorch_build() {
    log_info "开始PyTorch和NLTK环境测试..."

    if docker buildx version >/dev/null 2>&1; then
        export DOCKER_BUILDKIT=1
        export COMPOSE_DOCKER_CLI_BUILD=1
    fi

    log_info "构建PyTorch测试镜像..."
    docker build -f Dockerfile.pytorch-test -t hbgk-pytorch-test:latest .

    if [ $? -eq 0 ]; then
        log_success "PyTorch测试镜像构建成功！"
        log_info "运行PyTorch测试容器..."
        docker run --rm -it hbgk-pytorch-test:latest
    else
        log_error "PyTorch测试镜像构建失败"
        exit 1
    fi
}

# 部署服务
deploy_services() {
    local mode=$1
    local force_build=$2
    local dockerfile_variant=$3
    
    if [ "$mode" = "full" ]; then
        COMPOSE_FILE="docker-compose.production.yml"
        log_info "开始完整部署..."
    elif [ "$mode" = "simple" ]; then
        COMPOSE_FILE="docker-compose.simple.yml"
        log_info "开始简化部署..."
        log_warning "请确保外部数据库服务已正确配置"

        # 检查简化部署配置文件
        if [ -f ".env.simple" ]; then
            log_info "使用简化部署配置文件: .env.simple"
            export ENV_FILE=".env.simple"
        else
            log_warning "未找到.env.simple配置文件，使用默认.env"
            log_warning "建议使用.env.simple配置外部服务连接"
            export ENV_FILE=".env"
        fi
    else
        log_error "无效的部署模式: $mode"
        exit 1
    fi

    # 根据变体选择Dockerfile（使用环境变量方式）
    if [ "$dockerfile_variant" = "robust" ]; then
        log_info "使用稳健版本Dockerfile..."
        export BACKEND_DOCKERFILE="Dockerfile.backend.robust"
    elif [ "$dockerfile_variant" = "compatible" ]; then
        log_info "使用兼容版本Dockerfile..."
        export BACKEND_DOCKERFILE="Dockerfile.backend.compatible"
    else
        export BACKEND_DOCKERFILE="Dockerfile.backend"
    fi

    log_info "使用Dockerfile: $BACKEND_DOCKERFILE"
    
    if [ ! -f "$COMPOSE_FILE" ]; then
        log_error "Docker Compose 文件不存在: $COMPOSE_FILE"
        exit 1
    fi
    
    # 构建镜像
    build_images $force_build
    
    # 启动服务
    log_info "启动服务..."
    if [ "$mode" = "simple" ] && [ -n "$ENV_FILE" ]; then
        log_info "使用环境文件: $ENV_FILE"
        docker-compose -f $COMPOSE_FILE --env-file $ENV_FILE up -d
    else
        docker-compose -f $COMPOSE_FILE up -d
    fi
    
    # 等待服务启动
    log_info "等待服务启动..."
    sleep 10
    
    # 检查服务状态
    log_info "检查服务状态..."
    docker-compose -f $COMPOSE_FILE ps
    
    log_success "部署完成！"

    if [ "$mode" = "full" ]; then
        echo ""
        echo "服务访问地址:"
        echo "  前端: http://localhost:${FRONTEND_PORT:-3000}"
        echo "  后端API: http://localhost:${BACKEND_PORT:-9380}"
        echo "  MinIO控制台: http://localhost:${MINIO_CONSOLE_PORT:-9001}"
    else
        echo ""
        echo "服务访问地址:"
        echo "  前端: http://localhost:${FRONTEND_PORT:-3000}"
        echo "  后端API: http://localhost:${BACKEND_PORT:-9380}"
    fi
}

# 主函数
main() {
    local mode=""
    local force_build=false
    local action="deploy"
    local dockerfile_variant=""

    # 解析命令行参数
    while [[ $# -gt 0 ]]; do
        case $1 in
            -m|--mode)
                mode="$2"
                shift 2
                ;;
            --build)
                force_build=true
                shift
                ;;
            --stop)
                action="stop"
                shift
                ;;
            --clean)
                action="clean"
                shift
                ;;
            --debug)
                dockerfile_variant="debug"
                action="debug"
                shift
                ;;
            --robust)
                dockerfile_variant="robust"
                shift
                ;;
            --test-venv)
                action="test-venv"
                shift
                ;;
            --test-pytorch)
                action="test-pytorch"
                shift
                ;;
            -h|--help)
                show_help
                exit 0
                ;;
            *)
                log_error "未知参数: $1"
                show_help
                exit 1
                ;;
        esac
    done
    
    # 检查系统要求
    check_requirements
    
    case $action in
        "stop")
            stop_services
            ;;
        "clean")
            clean_environment
            ;;
        "debug")
            debug_build
            ;;
        "test-venv")
            test_venv_build
            ;;
        "test-pytorch")
            test_pytorch_build
            ;;
        "deploy")
            if [ -z "$mode" ]; then
                log_error "请指定部署模式 (-m full 或 -m simple)"
                show_help
                exit 1
            fi

            check_env_file
            deploy_services $mode $force_build $dockerfile_variant
            ;;
    esac
}

# 执行主函数
main "$@"
