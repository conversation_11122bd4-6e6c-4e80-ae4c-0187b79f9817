#!/bin/bash

# RAGFlow 前端部署脚本

set -e

echo "=========================================="
echo "RAGFlow 前端部署脚本"
echo "=========================================="
echo

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查命令是否存在
check_command() {
    if ! command -v $1 &> /dev/null; then
        log_error "$1 命令未找到，请先安装 $1"
        exit 1
    fi
}

# 检查必要的命令
log_info "检查必要的命令..."
check_command "node"
check_command "npm"
check_command "docker"

# 显示版本信息
log_info "环境信息:"
echo "Node.js: $(node --version)"
echo "npm: $(npm --version)"
echo "Docker: $(docker --version)"
echo

# 选择部署模式
echo "请选择部署模式:"
echo "1) 开发环境 (Development)"
echo "2) 生产环境 (Production)"
echo "3) Docker 部署"
echo "4) Docker Compose 部署"
read -p "请输入选项 (1-4): " deploy_mode

case $deploy_mode in
    1)
        log_info "启动开发环境..."
        
        # 安装依赖
        if [ ! -d "node_modules" ]; then
            log_info "安装依赖包..."
            npm install
        fi
        
        # 启动开发服务器
        log_info "启动开发服务器..."
        npm run dev
        ;;
        
    2)
        log_info "构建生产环境..."
        
        # 安装依赖
        log_info "安装依赖包..."
        npm ci --only=production
        
        # 构建项目
        log_info "构建项目..."
        npm run build
        
        # 预览构建结果
        log_info "预览构建结果..."
        npm run preview
        ;;
        
    3)
        log_info "Docker 部署..."
        
        # 构建 Docker 镜像
        log_info "构建 Docker 镜像..."
        docker build -t ragflow-frontend:latest .
        
        # 停止并删除旧容器
        log_info "停止旧容器..."
        docker stop ragflow-frontend 2>/dev/null || true
        docker rm ragflow-frontend 2>/dev/null || true
        
        # 运行新容器
        log_info "启动新容器..."
        docker run -d \
            --name ragflow-frontend \
            -p 3000:80 \
            --restart unless-stopped \
            ragflow-frontend:latest
        
        log_success "Docker 容器已启动，访问地址: http://localhost:3000"
        ;;
        
    4)
        log_info "Docker Compose 部署..."

        # 检查环境配置文件
        if [ ! -f ".env.docker" ]; then
            log_warning ".env.docker 文件不存在，使用默认配置"
            cp .env.example .env.docker
        fi

        # 停止旧服务
        log_info "停止旧服务..."
        docker-compose down 2>/dev/null || true

        # 使用Docker环境配置文件
        log_info "使用Docker环境配置..."
        export $(cat .env.docker | grep -v '^#' | xargs)

        # 构建并启动服务
        log_info "构建并启动服务..."
        docker-compose --env-file .env.docker up --build -d

        # 显示服务状态
        log_info "服务状态:"
        docker-compose ps

        log_success "Docker Compose 服务已启动，访问地址: http://localhost:${FRONTEND_PORT:-3000}"
        log_info "API服务地址: ${API_BASE_URL:-http://localhost:9380}"
        ;;
        
    *)
        log_error "无效的选项"
        exit 1
        ;;
esac

echo
log_success "部署完成!"

# 显示有用的命令
echo
log_info "有用的命令:"
case $deploy_mode in
    3)
        echo "查看容器日志: docker logs -f ragflow-frontend"
        echo "停止容器: docker stop ragflow-frontend"
        echo "重启容器: docker restart ragflow-frontend"
        ;;
    4)
        echo "查看服务日志: docker-compose logs -f"
        echo "停止服务: docker-compose down"
        echo "重启服务: docker-compose restart"
        ;;
esac

echo
log_info "如有问题，请查看日志或联系开发团队"
