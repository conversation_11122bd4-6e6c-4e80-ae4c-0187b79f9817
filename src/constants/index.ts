// API相关常量 - 支持环境变量动态配置
export const API_BASE_URL = process.env.API_BASE_URL || 'http://localhost:9380';
export const API_KEY = process.env.API_KEY || 'hbgk-lhYmE2YTc2M2FiYjExZjA5YTdlMDAwYz';

// 本地存储键名 - 与原项目保持一致
export const STORAGE_KEYS = {
  TOKEN: 'token',
  USER_INFO: 'userInfo',
  AUTHORIZATION: 'Authorization',
  LANGUAGE: 'lng',
  THEME: 'hbgk_theme',
} as const;

// API端点
export const API_ENDPOINTS = {
  // 用户相关
  USER_LOGIN: '/v1/user/login',
  USER_LOGOUT: '/v1/user/logout',
  USER_INFO: '/v1/user/info',
  
  // 知识库相关
  KB_LIST: '/v1/kb/list',
  KB_CREATE: '/v1/kb/create',
  KB_UPDATE: '/v1/kb/update',
  KB_DELETE: '/v1/kb/rm',
  KB_DETAIL: '/v1/kb/detail',
  
  // 文档相关
  DOC_LIST: '/v1/document/list',
  DOC_UPLOAD: '/v1/document/upload',
  DOC_DELETE: '/v1/document/rm',
  DOC_RENAME: '/v1/document/rename',
  DOC_RUN: '/v1/document/run',
  
  // 对话相关
  CHAT_LIST: '/v1/dialog/list',
  CHAT_CREATE: '/v1/dialog/set',
  CHAT_DELETE: '/v1/dialog/rm',
  CONVERSATION_ASK: '/v1/conversation/ask',
  CONVERSATION_LIST: '/v1/conversation/list',
  
  // 文件相关
  FILE_LIST: '/v1/file/list',
  FILE_UPLOAD: '/v1/file/upload',
  FILE_DELETE: '/v1/file/rm',
  FILE_RENAME: '/v1/file/rename',
  FILE_MOVE: '/v1/file/mv',
  FILE_CREATE_FOLDER: '/v1/file/create',
  
  // 系统相关
  SYSTEM_STATUS: '/v1/system/status',
  SYSTEM_VERSION: '/v1/system/version',
  
  // LLM相关
  LLM_LIST: '/v1/llm/list',
  LLM_FACTORIES: '/v1/llm/factories',
  LLM_ADD: '/v1/llm/add_llm',
  LLM_DELETE: '/v1/llm/delete_llm',
} as const;

// 文件类型
export const FILE_TYPES = {
  PDF: 'pdf',
  DOCX: 'docx',
  DOC: 'doc',
  TXT: 'txt',
  MD: 'md',
  XLSX: 'xlsx',
  XLS: 'xls',
  PPT: 'ppt',
  PPTX: 'pptx',
  HTML: 'html',
  JSON: 'json',
  CSV: 'csv',
} as const;

// 支持的文件类型
export const SUPPORTED_FILE_TYPES = [
  FILE_TYPES.PDF,
  FILE_TYPES.DOCX,
  FILE_TYPES.DOC,
  FILE_TYPES.TXT,
  FILE_TYPES.MD,
  FILE_TYPES.XLSX,
  FILE_TYPES.XLS,
  FILE_TYPES.HTML,
  FILE_TYPES.JSON,
  FILE_TYPES.CSV,
];

// 文件状态
export const FILE_STATUS = {
  UPLOADING: 'uploading',
  PROCESSING: 'processing',
  COMPLETED: 'completed',
  FAILED: 'failed',
} as const;

// 任务状态
export const TASK_STATUS = {
  PENDING: 'pending',
  RUNNING: 'running',
  SUCCESS: 'success',
  FAILURE: 'failure',
  CANCEL: 'cancel',
} as const;

// 响应状态码
export const RESPONSE_CODE = {
  SUCCESS: 0,
  AUTHENTICATION_ERROR: 101,
  AUTHORIZATION_ERROR: 102,
  ARGUMENT_ERROR: 103,
  SERVER_ERROR: 104,
  OPERATING_ERROR: 105,
  EXCEPTION_ERROR: 106,
} as const;

// 分页默认配置
export const PAGINATION_CONFIG = {
  DEFAULT_PAGE: 1,
  DEFAULT_PAGE_SIZE: 10,
  PAGE_SIZE_OPTIONS: ['10', '20', '50', '100'],
} as const;

// 主题配置
export const THEME_CONFIG = {
  PRIMARY_COLOR: '#1890ff',
  SUCCESS_COLOR: '#52c41a',
  WARNING_COLOR: '#faad14',
  ERROR_COLOR: '#ff4d4f',
  INFO_COLOR: '#1890ff',
} as const;

// 路由路径
export const ROUTES = {
  LOGIN: '/login',
  DASHBOARD: '/dashboard',
  KNOWLEDGE_BASE: '/knowledge-base',
  DOCUMENTS: '/documents',
  CHAT: '/chat',
  FILES: '/files',
  AGENTS: '/agents',
  SEARCH: '/search',
  SETTINGS: '/settings',
  API_MANAGEMENT: '/api-management',
} as const;

// 菜单配置
export const MENU_ITEMS = [
  {
    key: 'dashboard',
    path: ROUTES.DASHBOARD,
    icon: 'BarChartOutlined',
    label: 'menu.dashboard',
  },
  {
    key: 'knowledgeBase',
    path: ROUTES.KNOWLEDGE_BASE,
    icon: 'DatabaseOutlined',
    label: 'menu.knowledgeBase',
  },
  //{
  //  key: 'documents',
  //  path: ROUTES.DOCUMENTS,
  //  icon: 'FileTextOutlined',
  //  label: 'menu.documents',
  //},
  {
    key: 'chat',
    path: ROUTES.CHAT,
    icon: 'MessageOutlined',
    label: 'menu.chat',
  },
  {
    key: 'files',
    path: ROUTES.FILES,
    icon: 'FolderOutlined',
    label: 'menu.files',
  },
  {
    key: 'agents',
    path: ROUTES.AGENTS,
    icon: 'RobotOutlined',
    label: 'menu.agents',
  },
  {
    key: 'search',
    path: ROUTES.SEARCH,
    icon: 'SearchOutlined',
    label: 'menu.search',
  },
  {
    key: 'api',
    path: ROUTES.API_MANAGEMENT,
    icon: 'ApiOutlined',
    label: 'menu.api',
  },
  {
    key: 'settings',
    path: ROUTES.SETTINGS,
    icon: 'SettingOutlined',
    label: 'menu.settings',
  },
] as const;

// 默认配置
export const DEFAULT_CONFIG = {
  LANGUAGE: 'zh',
  THEME: 'light',
  PAGE_SIZE: 10,
  AUTO_SAVE_INTERVAL: 30000, // 30秒
  REQUEST_TIMEOUT: 30000, // 30秒
} as const;
