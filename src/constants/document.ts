export enum RunningStatus {
  UNSTART = '0', // need to run
  RUNNING = '1', // need to cancel
  CANCEL = '2', // need to refresh
  DONE = '3', // need to refresh
  FAIL = '4', // need to refresh
}

export const RunningStatusMap = {
  [RunningStatus.UNSTART]: {
    label: 'UNSTART',
    color: 'cyan',
    text: 'Not Started',
  },
  [RunningStatus.RUNNING]: {
    label: 'RUNNING',
    color: 'blue',
    text: 'Parsing',
  },
  [RunningStatus.CANCEL]: { 
    label: 'CANCEL', 
    color: 'orange',
    text: 'Cancelled',
  },
  [RunningStatus.DONE]: { 
    label: 'DONE', 
    color: 'green',
    text: 'Success',
  },
  [RunningStatus.FAIL]: { 
    label: 'FAIL', 
    color: 'red',
    text: 'Failed',
  },
};

export enum DocumentType {
  Virtual = 'virtual',
  Visual = 'visual',
}

export enum DocumentParserType {
  Naive = 'naive',
  Qa = 'qa',
  Resume = 'resume',
  Manual = 'manual',
  Table = 'table',
  Paper = 'paper',
  Book = 'book',
  Laws = 'laws',
  Presentation = 'presentation',
  Picture = 'picture',
  One = 'one',
  Audio = 'audio',
  Email = 'email',
  Tag = 'tag',
  KnowledgeGraph = 'knowledge_graph',
}

export const DocumentParserMap = {
  [DocumentParserType.Naive]: 'General',
  [DocumentParserType.Qa]: 'Q&A',
  [DocumentParserType.Resume]: 'Resume',
  [DocumentParserType.Manual]: 'Manual',
  [DocumentParserType.Table]: 'Table',
  [DocumentParserType.Paper]: 'Paper',
  [DocumentParserType.Book]: 'Book',
  [DocumentParserType.Laws]: 'Laws',
  [DocumentParserType.Presentation]: 'Presentation',
  [DocumentParserType.Picture]: 'Picture',
  [DocumentParserType.One]: 'One',
  [DocumentParserType.Audio]: 'Audio',
  [DocumentParserType.Email]: 'Email',
  [DocumentParserType.Tag]: 'Tag',
  [DocumentParserType.KnowledgeGraph]: 'Knowledge Graph',
};

export const FileTypeMap = {
  'pdf': 'PDF',
  'doc': 'Word',
  'docx': 'Word',
  'txt': 'Text',
  'md': 'Markdown',
  'html': 'HTML',
  'xlsx': 'Excel',
  'xls': 'Excel',
  'ppt': 'PowerPoint',
  'pptx': 'PowerPoint',
  'jpg': 'Image',
  'jpeg': 'Image',
  'png': 'Image',
  'gif': 'Image',
  'mp3': 'Audio',
  'wav': 'Audio',
  'mp4': 'Video',
  'avi': 'Video',
};
