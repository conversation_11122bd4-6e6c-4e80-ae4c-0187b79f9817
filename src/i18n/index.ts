import i18n from 'i18next';
import { initReactI18next } from 'react-i18next';

const resources = {
  zh: {
    translation: {
      // 通用
      common: {
        confirm: '确认',
        cancel: '取消',
        save: '保存',
        delete: '删除',
        edit: '编辑',
        view: '查看',
        search: '搜索',
        upload: '上传',
        download: '下载',
        loading: '加载中...',
        success: '操作成功',
        error: '操作失败',
        warning: '警告',
        info: '提示',
      },
      // 登录相关
      login: {
        title: '登录',
        email: '邮箱',
        password: '密码',
        emailPlaceholder: '请输入邮箱',
        passwordPlaceholder: '请输入密码',
        loginButton: '登录',
        loginSuccess: '登录成功',
        loginFailed: '登录失败',
        invalidCredentials: '邮箱或密码错误',
        welcome: '欢迎回来！',
      },
      // 导航菜单
      menu: {
        dashboard: '仪表板',
        knowledgeBase: '知识库管理',
        documents: '文档管理',
        chat: '对话聊天',
        files: '文件管理',
        agents: '智能体',
        search: '搜索',
        api: 'API管理',
        settings: '系统设置',
      },
      // 知识库
      knowledgeBase: {
        title: '知识库管理',
        create: '创建知识库',
        name: '知识库名称',
        description: '描述',
        createSuccess: '知识库创建成功',
        deleteConfirm: '确定要删除这个知识库吗？',
        deleteSuccess: '知识库删除成功',
      },
      // 文档
      documents: {
        title: '文档管理',
        upload: '上传文档',
        name: '文档名称',
        size: '文件大小',
        status: '状态',
        uploadTime: '上传时间',
        processing: '处理中',
        completed: '已完成',
        failed: '失败',
        uploadSuccess: '文档上传成功',
        deleteConfirm: '确定要删除这个文档吗？',
        deleteSuccess: '文档删除成功',
      },
      // 对话
      chat: {
        title: '对话聊天',
        inputPlaceholder: '请输入您的问题...',
        send: '发送',
        newChat: '新建对话',
        chatHistory: '对话历史',
        thinking: '思考中...',
      },
      // 文件管理
      files: {
        title: '文件管理',
        upload: '上传文件',
        createFolder: '新建文件夹',
        rename: '重命名',
        move: '移动',
        copy: '复制',
        folderName: '文件夹名称',
        fileName: '文件名称',
      },
      // 系统设置
      settings: {
        title: '系统设置',
        profile: '个人资料',
        model: '模型配置',
        api: 'API设置',
        system: '系统配置',
      },
    },
  },
  en: {
    translation: {
      // Common
      common: {
        confirm: 'Confirm',
        cancel: 'Cancel',
        save: 'Save',
        delete: 'Delete',
        edit: 'Edit',
        view: 'View',
        search: 'Search',
        upload: 'Upload',
        download: 'Download',
        loading: 'Loading...',
        success: 'Success',
        error: 'Error',
        warning: 'Warning',
        info: 'Info',
      },
      // Login
      login: {
        title: 'Login',
        email: 'Email',
        password: 'Password',
        emailPlaceholder: 'Please enter email',
        passwordPlaceholder: 'Please enter password',
        loginButton: 'Login',
        loginSuccess: 'Login successful',
        loginFailed: 'Login failed',
        invalidCredentials: 'Invalid email or password',
        welcome: 'Welcome back!',
      },
      // Menu
      menu: {
        dashboard: 'Dashboard',
        knowledgeBase: 'Knowledge Base',
        documents: 'Documents',
        chat: 'Chat',
        files: 'Files',
        agents: 'Agents',
        search: 'Search',
        api: 'API',
        settings: 'Settings',
      },
      // Knowledge Base
      knowledgeBase: {
        title: 'Knowledge Base Management',
        create: 'Create Knowledge Base',
        name: 'Knowledge Base Name',
        description: 'Description',
        createSuccess: 'Knowledge base created successfully',
        deleteConfirm: 'Are you sure to delete this knowledge base?',
        deleteSuccess: 'Knowledge base deleted successfully',
      },
      // Documents
      documents: {
        title: 'Document Management',
        upload: 'Upload Document',
        name: 'Document Name',
        size: 'File Size',
        status: 'Status',
        uploadTime: 'Upload Time',
        processing: 'Processing',
        completed: 'Completed',
        failed: 'Failed',
        uploadSuccess: 'Document uploaded successfully',
        deleteConfirm: 'Are you sure to delete this document?',
        deleteSuccess: 'Document deleted successfully',
      },
      // Chat
      chat: {
        title: 'Chat',
        inputPlaceholder: 'Please enter your question...',
        send: 'Send',
        newChat: 'New Chat',
        chatHistory: 'Chat History',
        thinking: 'Thinking...',
      },
      // Files
      files: {
        title: 'File Management',
        upload: 'Upload File',
        createFolder: 'Create Folder',
        rename: 'Rename',
        move: 'Move',
        copy: 'Copy',
        folderName: 'Folder Name',
        fileName: 'File Name',
      },
      // Settings
      settings: {
        title: 'Settings',
        profile: 'Profile',
        model: 'Model Configuration',
        api: 'API Settings',
        system: 'System Configuration',
      },
    },
  },
};

i18n
  .use(initReactI18next)
  .init({
    resources,
    lng: 'zh',
    fallbackLng: 'zh',
    interpolation: {
      escapeValue: false,
    },
  });

export default i18n;
