{"cwd": "/home/<USER>/ragflow/hbweb", "pkg": {"name": "汉邦高科-frontend", "version": "1.0.0", "description": "汉邦高科 Knowledge Base System Frontend", "main": "index.js", "scripts": {"dev": "cross-env HOST=0.0.0.0 PORT=8000 UMI_DEV_SERVER_COMPRESS=none umi dev", "build": "umi build", "start": "cross-env HOST=0.0.0.0 PORT=8000 UMI_DEV_SERVER_COMPRESS=none umi dev", "test": "jest", "lint": "eslint src --ext .ts,.tsx", "lint:fix": "eslint src --ext .ts,.tsx --fix", "prettier": "prettier --write src/**/*.{ts,tsx,js,jsx,json,css,less}"}, "dependencies": {"@ant-design/icons": "^5.2.6", "@tanstack/react-query": "^5.8.4", "ahooks": "^3.7.8", "antd": "^5.12.8", "dayjs": "^1.11.10", "i18next": "^23.7.6", "i18next-browser-languagedetector": "^7.2.0", "js-base64": "^3.7.5", "jsencrypt": "^3.3.2", "mammoth": "^1.9.1", "pdfjs-dist": "^5.3.93", "react": "^18.2.0", "react-dom": "^18.2.0", "react-i18next": "^13.5.0", "react-infinite-scroll-component": "^6.1.0", "react-markdown": "^10.1.0", "react-pdf": "^10.0.1", "react-string-replace": "^1.1.1", "rehype-raw": "^7.0.0", "remark-gfm": "^4.0.1", "umi": "^4.0.87", "umi-request": "^1.4.0"}, "devDependencies": {"@types/react": "^18.2.45", "@types/react-dom": "^18.2.18", "@typescript-eslint/eslint-plugin": "^6.14.0", "@typescript-eslint/parser": "^6.14.0", "cross-env": "^7.0.3", "eslint": "^8.55.0", "eslint-plugin-react": "^7.33.2", "eslint-plugin-react-hooks": "^4.6.0", "jest": "^29.7.0", "prettier": "^3.1.1", "typescript": "^5.3.3"}, "keywords": ["hanbangGaoKe", "knowledge-base", "react", "typescript", "antd"], "author": "HanBangGaoKe Team", "license": "Apache-2.0"}, "pkgPath": "/home/<USER>/ragflow/hbweb/package.json", "plugins": {"./node_modules/@umijs/core/dist/service/servicePlugin": {"config": {}, "time": {"hooks": {}, "register": 2}, "enableBy": "register", "type": "preset", "path": "/home/<USER>/ragflow/hbweb/node_modules/@umijs/core/dist/service/servicePlugin.js", "cwd": "/home/<USER>/ragflow/hbweb", "id": "./node_modules/@umijs/core/dist/service/servicePlugin", "key": "servicePlugin"}, "@umijs/preset-umi": {"config": {}, "time": {"hooks": {}, "register": 41}, "enableBy": "register", "type": "preset", "path": "/home/<USER>/ragflow/hbweb/node_modules/@umijs/preset-umi/dist/index.js", "cwd": "/home/<USER>/ragflow/hbweb", "id": "@umijs/preset-umi", "key": "umi"}, "./node_modules/@umijs/preset-umi/dist/registerMethods": {"config": {}, "time": {"hooks": {"onStart": [0]}, "register": 5}, "enableBy": "register", "type": "plugin", "path": "/home/<USER>/ragflow/hbweb/node_modules/@umijs/preset-umi/dist/registerMethods.js", "cwd": "/home/<USER>/ragflow/hbweb", "id": "./node_modules/@umijs/preset-umi/dist/registerMethods", "key": "registerMethods"}, "@umijs/did-you-know": {"config": {}, "time": {"hooks": {"onStart": [1]}, "register": 1}, "enableBy": "register", "type": "plugin", "path": "/home/<USER>/ragflow/hbweb/node_modules/@umijs/did-you-know/dist/plugin.js", "cwd": "/home/<USER>/ragflow/hbweb", "id": "@umijs/did-you-know", "key": "umijsDidYouKnow"}, "./node_modules/@umijs/preset-umi/dist/features/404/404": {"config": {}, "time": {"hooks": {"modifyRoutes": [0]}, "register": 0}, "enableBy": "register", "type": "plugin", "path": "/home/<USER>/ragflow/hbweb/node_modules/@umijs/preset-umi/dist/features/404/404.js", "cwd": "/home/<USER>/ragflow/hbweb", "id": "./node_modules/@umijs/preset-umi/dist/features/404/404", "key": "404"}, "./node_modules/@umijs/preset-umi/dist/features/appData/appData": {"config": {}, "time": {"hooks": {"modifyAppData": [45]}, "register": 44}, "enableBy": "register", "type": "plugin", "path": "/home/<USER>/ragflow/hbweb/node_modules/@umijs/preset-umi/dist/features/appData/appData.js", "cwd": "/home/<USER>/ragflow/hbweb", "id": "./node_modules/@umijs/preset-umi/dist/features/appData/appData", "key": "appData"}, "./node_modules/@umijs/preset-umi/dist/features/appData/umiInfo": {"config": {}, "time": {"hooks": {}, "register": 1}, "enableBy": "register", "type": "plugin", "path": "/home/<USER>/ragflow/hbweb/node_modules/@umijs/preset-umi/dist/features/appData/umiInfo.js", "cwd": "/home/<USER>/ragflow/hbweb", "id": "./node_modules/@umijs/preset-umi/dist/features/appData/umiInfo", "key": "umiInfo"}, "./node_modules/@umijs/preset-umi/dist/features/check/check": {"config": {}, "time": {"hooks": {"onCheckConfig": [1], "onCheck": [1]}, "register": 1}, "enableBy": "register", "type": "plugin", "path": "/home/<USER>/ragflow/hbweb/node_modules/@umijs/preset-umi/dist/features/check/check.js", "cwd": "/home/<USER>/ragflow/hbweb", "id": "./node_modules/@umijs/preset-umi/dist/features/check/check", "key": "check"}, "./node_modules/@umijs/preset-umi/dist/features/check/babel722": {"config": {}, "time": {"hooks": {"onCheck": [0]}, "register": 2}, "enableBy": "register", "type": "plugin", "path": "/home/<USER>/ragflow/hbweb/node_modules/@umijs/preset-umi/dist/features/check/babel722.js", "cwd": "/home/<USER>/ragflow/hbweb", "id": "./node_modules/@umijs/preset-umi/dist/features/check/babel722", "key": "babel722"}, "./node_modules/@umijs/preset-umi/dist/features/codeSplitting/codeSplitting": {"config": {}, "time": {"hooks": {}, "register": 1}, "enableBy": "config", "type": "plugin", "path": "/home/<USER>/ragflow/hbweb/node_modules/@umijs/preset-umi/dist/features/codeSplitting/codeSplitting.js", "cwd": "/home/<USER>/ragflow/hbweb", "id": "./node_modules/@umijs/preset-umi/dist/features/codeSplitting/codeSplitting", "key": "codeSplitting"}, "./node_modules/@umijs/preset-umi/dist/features/configPlugins/configPlugins": {"config": {}, "time": {"hooks": {"modifyConfig": [0]}, "register": 16}, "enableBy": "register", "type": "plugin", "path": "/home/<USER>/ragflow/hbweb/node_modules/@umijs/preset-umi/dist/features/configPlugins/configPlugins.js", "cwd": "/home/<USER>/ragflow/hbweb", "id": "./node_modules/@umijs/preset-umi/dist/features/configPlugins/configPlugins", "key": "configPlugins"}, "virtual: config-title": {"id": "virtual: config-title", "key": "title", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-styles": {"id": "virtual: config-styles", "key": "styles", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-scripts": {"id": "virtual: config-scripts", "key": "scripts", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-routes": {"id": "virtual: config-routes", "key": "routes", "config": {"onChange": "regenerateTmpFiles"}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-routeLoader": {"id": "virtual: config-routeLoader", "key": "routeLoader", "config": {"default": {"moduleType": "esm"}}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-reactRouter5Compat": {"id": "virtual: config-reactRouter5Compat", "key": "reactRouter5Compat", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-presets": {"id": "virtual: config-presets", "key": "presets", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-plugins": {"id": "virtual: config-plugins", "key": "plugins", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-npmClient": {"id": "virtual: config-npmClient", "key": "npmClient", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-mountElementId": {"id": "virtual: config-mountElementId", "key": "mountElementId", "config": {"default": "root"}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-metas": {"id": "virtual: config-metas", "key": "metas", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-links": {"id": "virtual: config-links", "key": "links", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-historyWithQuery": {"id": "virtual: config-historyWithQuery", "key": "historyWithQuery", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-history": {"id": "virtual: config-history", "key": "history", "config": {"default": {"type": "browser"}}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-headScripts": {"id": "virtual: config-headScripts", "key": "headScripts", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-esbuildMinifyIIFE": {"id": "virtual: config-esbuildMinifyIIFE", "key": "esbuildMinifyIIFE", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-conventionRoutes": {"id": "virtual: config-conventionRoutes", "key": "conventionRoutes", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-conventionLayout": {"id": "virtual: config-conventionLayout", "key": "conventionLayout", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-base": {"id": "virtual: config-base", "key": "base", "config": {"default": "/"}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-analyze": {"id": "virtual: config-analyze", "key": "analyze", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-writeToDisk": {"id": "virtual: config-writeToDisk", "key": "writeToDisk", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-transformRuntime": {"id": "virtual: config-transformRuntime", "key": "transformRuntime", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-theme": {"id": "virtual: config-theme", "key": "theme", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-targets": {"id": "virtual: config-targets", "key": "targets", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-svgr": {"id": "virtual: config-svgr", "key": "svgr", "config": {"default": {}}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-svgo": {"id": "virtual: config-svgo", "key": "svgo", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-stylusLoader": {"id": "virtual: config-stylusLoader", "key": "stylus<PERSON><PERSON><PERSON>", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-styleLoader": {"id": "virtual: config-style<PERSON>oader", "key": "<PERSON><PERSON><PERSON><PERSON>", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-srcTranspilerOptions": {"id": "virtual: config-srcTranspilerOptions", "key": "srcTranspilerOptions", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-srcTranspiler": {"id": "virtual: config-srcTranspiler", "key": "srcTranspiler", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-sassLoader": {"id": "virtual: config-sassLoader", "key": "sass<PERSON><PERSON>der", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-runtimePublicPath": {"id": "virtual: config-runtimePublicPath", "key": "runtimePublicPath", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-purgeCSS": {"id": "virtual: config-purgeCSS", "key": "purgeCSS", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-publicPath": {"id": "virtual: config-publicPath", "key": "publicPath", "config": {"default": "/"}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-proxy": {"id": "virtual: config-proxy", "key": "proxy", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-postcssLoader": {"id": "virtual: config-postcssLoader", "key": "postcss<PERSON><PERSON><PERSON>", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-outputPath": {"id": "virtual: config-outputPath", "key": "outputPath", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-normalCSSLoaderModules": {"id": "virtual: config-normalCSSLoaderModules", "key": "normalCSSLoaderModules", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-mfsu": {"id": "virtual: config-mfsu", "key": "mfsu", "config": {"default": {"strategy": "eager"}}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-mdx": {"id": "virtual: config-mdx", "key": "mdx", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-manifest": {"id": "virtual: config-manifest", "key": "manifest", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-lessLoader": {"id": "virtual: config-less<PERSON><PERSON>der", "key": "<PERSON><PERSON><PERSON><PERSON>", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-jsMinifierOptions": {"id": "virtual: config-jsMinifierOptions", "key": "jsMinifierOptions", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-jsMinifier": {"id": "virtual: config-jsMinifier", "key": "jsMinifier", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-inlineLimit": {"id": "virtual: config-inlineLimit", "key": "inlineLimit", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-ignoreMomentLocale": {"id": "virtual: config-ignoreMomentLocale", "key": "ignoreMomentLocale", "config": {"default": true}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-https": {"id": "virtual: config-https", "key": "https", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-hash": {"id": "virtual: config-hash", "key": "hash", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-forkTSChecker": {"id": "virtual: config-fork<PERSON><PERSON><PERSON><PERSON>", "key": "forkTSChecker", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-fastRefresh": {"id": "virtual: config-fastRefresh", "key": "fastRefresh", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-extraPostCSSPlugins": {"id": "virtual: config-extraPostCSSPlugins", "key": "extraPostCSSPlugins", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-extraBabelPresets": {"id": "virtual: config-extraBabelPresets", "key": "extraBabelPresets", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-extraBabelPlugins": {"id": "virtual: config-extraBabelPlugins", "key": "extraBabelPlugins", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-extraBabelIncludes": {"id": "virtual: config-extraBabelIncludes", "key": "extraBabelIncludes", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-externals": {"id": "virtual: config-externals", "key": "externals", "config": {"default": {}}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-esm": {"id": "virtual: config-esm", "key": "esm", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-devtool": {"id": "virtual: config-devtool", "key": "devtool", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-depTranspiler": {"id": "virtual: config-depTranspiler", "key": "depTranspiler", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-define": {"id": "virtual: config-define", "key": "define", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-deadCode": {"id": "virtual: config-deadCode", "key": "deadCode", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-cssPublicPath": {"id": "virtual: config-cssPublicPath", "key": "cssPublicPath", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-cssMinifierOptions": {"id": "virtual: config-cssMinifierOptions", "key": "cssMinifierOptions", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-cssMinifier": {"id": "virtual: config-cssMinifier", "key": "cssMinifier", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-cssLoaderModules": {"id": "virtual: config-cssLoaderModules", "key": "cssLoaderModules", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-cssLoader": {"id": "virtual: config-cssLoader", "key": "cssL<PERSON>der", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-copy": {"id": "virtual: config-copy", "key": "copy", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-checkDepCssModules": {"id": "virtual: config-checkDepCssModules", "key": "checkDepCssModules", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-chainWebpack": {"id": "virtual: config-chainWebpack", "key": "chainWebpack", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-cacheDirectoryPath": {"id": "virtual: config-cacheDirectoryPath", "key": "cacheDirectoryPath", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-babelLoaderCustomize": {"id": "virtual: config-babelLoaderCustomize", "key": "babelLoaderCustomize", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-autoprefixer": {"id": "virtual: config-autoprefixer", "key": "autoprefixer", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-autoCSSModules": {"id": "virtual: config-autoCSSModules", "key": "autoCSSModules", "config": {"default": true}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-alias": {"id": "virtual: config-alias", "key": "alias", "config": {"default": {"umi": "@@/exports", "react": "/home/<USER>/ragflow/hbweb/node_modules/react", "react-dom": "/home/<USER>/ragflow/hbweb/node_modules/react-dom", "react-router": "/home/<USER>/ragflow/hbweb/node_modules/react-router", "react-router-dom": "/home/<USER>/ragflow/hbweb/node_modules/react-router-dom"}}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "./node_modules/@umijs/preset-umi/dist/features/crossorigin/crossorigin": {"config": {}, "time": {"hooks": {}, "register": 1}, "enableBy": "config", "type": "plugin", "path": "/home/<USER>/ragflow/hbweb/node_modules/@umijs/preset-umi/dist/features/crossorigin/crossorigin.js", "cwd": "/home/<USER>/ragflow/hbweb", "id": "./node_modules/@umijs/preset-umi/dist/features/crossorigin/crossorigin", "key": "crossorigin"}, "./node_modules/@umijs/preset-umi/dist/features/depsOnDemand/depsOnDemand": {"config": {}, "time": {"hooks": {"onStart": [0]}, "register": 1}, "enableBy": "register", "type": "plugin", "path": "/home/<USER>/ragflow/hbweb/node_modules/@umijs/preset-umi/dist/features/depsOnDemand/depsOnDemand.js", "cwd": "/home/<USER>/ragflow/hbweb", "id": "./node_modules/@umijs/preset-umi/dist/features/depsOnDemand/depsOnDemand", "key": "deps<PERSON>n<PERSON><PERSON><PERSON>"}, "./node_modules/@umijs/preset-umi/dist/features/devTool/devTool": {"config": {}, "time": {"hooks": {}, "register": 1}, "enableBy": "register", "type": "plugin", "path": "/home/<USER>/ragflow/hbweb/node_modules/@umijs/preset-umi/dist/features/devTool/devTool.js", "cwd": "/home/<USER>/ragflow/hbweb", "id": "./node_modules/@umijs/preset-umi/dist/features/devTool/devTool", "key": "devTool"}, "./node_modules/@umijs/preset-umi/dist/features/esbuildHelperChecker/esbuildHelperChecker": {"config": {}, "time": {"hooks": {}, "register": 124}, "enableBy": "register", "type": "plugin", "path": "/home/<USER>/ragflow/hbweb/node_modules/@umijs/preset-umi/dist/features/esbuildHelperChecker/esbuildHelperChecker.js", "cwd": "/home/<USER>/ragflow/hbweb", "id": "./node_modules/@umijs/preset-umi/dist/features/esbuildHelperChecker/esbuildHelperChecker", "key": "esbuildHelperChecker"}, "./node_modules/@umijs/preset-umi/dist/features/esmi/esmi": {"config": {}, "time": {"hooks": {}, "register": 226}, "enableBy": "config", "type": "plugin", "path": "/home/<USER>/ragflow/hbweb/node_modules/@umijs/preset-umi/dist/features/esmi/esmi.js", "cwd": "/home/<USER>/ragflow/hbweb", "id": "./node_modules/@umijs/preset-umi/dist/features/esmi/esmi", "key": "esmi"}, "./node_modules/@umijs/preset-umi/dist/features/exportStatic/exportStatic": {"config": {}, "time": {"hooks": {}, "register": 44}, "enableBy": "config", "type": "plugin", "path": "/home/<USER>/ragflow/hbweb/node_modules/@umijs/preset-umi/dist/features/exportStatic/exportStatic.js", "cwd": "/home/<USER>/ragflow/hbweb", "id": "./node_modules/@umijs/preset-umi/dist/features/exportStatic/exportStatic", "key": "exportStatic"}, "./node_modules/@umijs/preset-umi/dist/features/favicons/favicons": {"config": {}, "time": {"hooks": {"modifyAppData": [1]}, "register": 7}, "enableBy": "register", "type": "plugin", "path": "/home/<USER>/ragflow/hbweb/node_modules/@umijs/preset-umi/dist/features/favicons/favicons.js", "cwd": "/home/<USER>/ragflow/hbweb", "id": "./node_modules/@umijs/preset-umi/dist/features/favicons/favicons", "key": "favicons"}, "./node_modules/@umijs/preset-umi/dist/features/helmet/helmet": {"config": {}, "time": {"hooks": {}, "register": 2}, "enableBy": "register", "type": "plugin", "path": "/home/<USER>/ragflow/hbweb/node_modules/@umijs/preset-umi/dist/features/helmet/helmet.js", "cwd": "/home/<USER>/ragflow/hbweb", "id": "./node_modules/@umijs/preset-umi/dist/features/helmet/helmet", "key": "helmet"}, "./node_modules/@umijs/preset-umi/dist/features/icons/icons": {"config": {}, "time": {"hooks": {}, "register": 2}, "enableBy": "config", "type": "plugin", "path": "/home/<USER>/ragflow/hbweb/node_modules/@umijs/preset-umi/dist/features/icons/icons.js", "cwd": "/home/<USER>/ragflow/hbweb", "id": "./node_modules/@umijs/preset-umi/dist/features/icons/icons", "key": "icons"}, "./node_modules/@umijs/preset-umi/dist/features/mock/mock": {"config": {}, "time": {"hooks": {"onStart": [1]}, "register": 54}, "type": "plugin", "path": "/home/<USER>/ragflow/hbweb/node_modules/@umijs/preset-umi/dist/features/mock/mock.js", "cwd": "/home/<USER>/ragflow/hbweb", "id": "./node_modules/@umijs/preset-umi/dist/features/mock/mock", "key": "mock"}, "./node_modules/@umijs/preset-umi/dist/features/mpa/mpa": {"config": {}, "time": {"hooks": {}, "register": 2}, "enableBy": "config", "type": "plugin", "path": "/home/<USER>/ragflow/hbweb/node_modules/@umijs/preset-umi/dist/features/mpa/mpa.js", "cwd": "/home/<USER>/ragflow/hbweb", "id": "./node_modules/@umijs/preset-umi/dist/features/mpa/mpa", "key": "mpa"}, "./node_modules/@umijs/preset-umi/dist/features/okam/okam": {"config": {}, "time": {"hooks": {}, "register": 2}, "type": "plugin", "path": "/home/<USER>/ragflow/hbweb/node_modules/@umijs/preset-umi/dist/features/okam/okam.js", "cwd": "/home/<USER>/ragflow/hbweb", "id": "./node_modules/@umijs/preset-umi/dist/features/okam/okam", "key": "okam"}, "./node_modules/@umijs/preset-umi/dist/features/overrides/overrides": {"config": {}, "time": {"hooks": {}, "register": 0}, "enableBy": "register", "type": "plugin", "path": "/home/<USER>/ragflow/hbweb/node_modules/@umijs/preset-umi/dist/features/overrides/overrides.js", "cwd": "/home/<USER>/ragflow/hbweb", "id": "./node_modules/@umijs/preset-umi/dist/features/overrides/overrides", "key": "overrides"}, "./node_modules/@umijs/preset-umi/dist/features/phantomDependency/phantomDependency": {"config": {}, "time": {"hooks": {}, "register": 6}, "enableBy": "config", "type": "plugin", "path": "/home/<USER>/ragflow/hbweb/node_modules/@umijs/preset-umi/dist/features/phantomDependency/phantomDependency.js", "cwd": "/home/<USER>/ragflow/hbweb", "id": "./node_modules/@umijs/preset-umi/dist/features/phantomDependency/phantomDependency", "key": "phantomDependency"}, "./node_modules/@umijs/preset-umi/dist/features/polyfill/polyfill": {"config": {}, "time": {"hooks": {"modifyConfig": [1]}, "register": 3}, "type": "plugin", "path": "/home/<USER>/ragflow/hbweb/node_modules/@umijs/preset-umi/dist/features/polyfill/polyfill.js", "cwd": "/home/<USER>/ragflow/hbweb", "id": "./node_modules/@umijs/preset-umi/dist/features/polyfill/polyfill", "key": "polyfill"}, "./node_modules/@umijs/preset-umi/dist/features/polyfill/publicPathPolyfill": {"config": {}, "time": {"hooks": {}, "register": 1}, "enableBy": "register", "type": "plugin", "path": "/home/<USER>/ragflow/hbweb/node_modules/@umijs/preset-umi/dist/features/polyfill/publicPathPolyfill.js", "cwd": "/home/<USER>/ragflow/hbweb", "id": "./node_modules/@umijs/preset-umi/dist/features/polyfill/publicPathPolyfill", "key": "publicPathPolyfill"}, "./node_modules/@umijs/preset-umi/dist/features/prepare/prepare": {"config": {}, "time": {"hooks": {}, "register": 1}, "enableBy": "register", "type": "plugin", "path": "/home/<USER>/ragflow/hbweb/node_modules/@umijs/preset-umi/dist/features/prepare/prepare.js", "cwd": "/home/<USER>/ragflow/hbweb", "id": "./node_modules/@umijs/preset-umi/dist/features/prepare/prepare", "key": "prepare"}, "./node_modules/@umijs/preset-umi/dist/features/routePrefetch/routePrefetch": {"config": {}, "time": {"hooks": {}, "register": 3}, "enableBy": "config", "type": "plugin", "path": "/home/<USER>/ragflow/hbweb/node_modules/@umijs/preset-umi/dist/features/routePrefetch/routePrefetch.js", "cwd": "/home/<USER>/ragflow/hbweb", "id": "./node_modules/@umijs/preset-umi/dist/features/routePrefetch/routePrefetch", "key": "routePrefetch"}, "./node_modules/@umijs/preset-umi/dist/features/terminal/terminal": {"config": {}, "time": {"hooks": {}, "register": 2}, "enableBy": "register", "type": "plugin", "path": "/home/<USER>/ragflow/hbweb/node_modules/@umijs/preset-umi/dist/features/terminal/terminal.js", "cwd": "/home/<USER>/ragflow/hbweb", "id": "./node_modules/@umijs/preset-umi/dist/features/terminal/terminal", "key": "terminal"}, "./node_modules/@umijs/preset-umi/dist/features/tmpFiles/tmpFiles": {"config": {}, "time": {"hooks": {}, "register": 12}, "enableBy": "register", "type": "plugin", "path": "/home/<USER>/ragflow/hbweb/node_modules/@umijs/preset-umi/dist/features/tmpFiles/tmpFiles.js", "cwd": "/home/<USER>/ragflow/hbweb", "id": "./node_modules/@umijs/preset-umi/dist/features/tmpFiles/tmpFiles", "key": "tmpFiles"}, "./node_modules/@umijs/preset-umi/dist/features/clientLoader/clientLoader": {"config": {}, "time": {"hooks": {}, "register": 4}, "enableBy": "config", "type": "plugin", "path": "/home/<USER>/ragflow/hbweb/node_modules/@umijs/preset-umi/dist/features/clientLoader/clientLoader.js", "cwd": "/home/<USER>/ragflow/hbweb", "id": "./node_modules/@umijs/preset-umi/dist/features/clientLoader/clientLoader", "key": "clientLoader"}, "./node_modules/@umijs/preset-umi/dist/features/routeProps/routeProps": {"config": {}, "time": {"hooks": {}, "register": 1}, "type": "plugin", "path": "/home/<USER>/ragflow/hbweb/node_modules/@umijs/preset-umi/dist/features/routeProps/routeProps.js", "cwd": "/home/<USER>/ragflow/hbweb", "id": "./node_modules/@umijs/preset-umi/dist/features/routeProps/routeProps", "key": "routeProps"}, "./node_modules/@umijs/preset-umi/dist/features/ssr/ssr": {"config": {}, "time": {"hooks": {}, "register": 6}, "enableBy": "config", "type": "plugin", "path": "/home/<USER>/ragflow/hbweb/node_modules/@umijs/preset-umi/dist/features/ssr/ssr.js", "cwd": "/home/<USER>/ragflow/hbweb", "id": "./node_modules/@umijs/preset-umi/dist/features/ssr/ssr", "key": "ssr"}, "./node_modules/@umijs/preset-umi/dist/features/tmpFiles/configTypes": {"config": {}, "time": {"hooks": {}, "register": 10}, "enableBy": "register", "type": "plugin", "path": "/home/<USER>/ragflow/hbweb/node_modules/@umijs/preset-umi/dist/features/tmpFiles/configTypes.js", "cwd": "/home/<USER>/ragflow/hbweb", "id": "./node_modules/@umijs/preset-umi/dist/features/tmpFiles/configTypes", "key": "configTypes"}, "./node_modules/@umijs/preset-umi/dist/features/transform/transform": {"config": {}, "time": {"hooks": {}, "register": 6}, "enableBy": "register", "type": "plugin", "path": "/home/<USER>/ragflow/hbweb/node_modules/@umijs/preset-umi/dist/features/transform/transform.js", "cwd": "/home/<USER>/ragflow/hbweb", "id": "./node_modules/@umijs/preset-umi/dist/features/transform/transform", "key": "transform"}, "./node_modules/@umijs/preset-umi/dist/features/lowImport/lowImport": {"config": {}, "time": {"hooks": {}, "register": 12}, "enableBy": "config", "type": "plugin", "path": "/home/<USER>/ragflow/hbweb/node_modules/@umijs/preset-umi/dist/features/lowImport/lowImport.js", "cwd": "/home/<USER>/ragflow/hbweb", "id": "./node_modules/@umijs/preset-umi/dist/features/lowImport/lowImport", "key": "lowImport"}, "./node_modules/@umijs/preset-umi/dist/features/vite/vite": {"config": {}, "time": {"hooks": {}, "register": 1}, "enableBy": "config", "type": "plugin", "path": "/home/<USER>/ragflow/hbweb/node_modules/@umijs/preset-umi/dist/features/vite/vite.js", "cwd": "/home/<USER>/ragflow/hbweb", "id": "./node_modules/@umijs/preset-umi/dist/features/vite/vite", "key": "vite"}, "./node_modules/@umijs/preset-umi/dist/features/apiRoute/apiRoute": {"config": {}, "time": {"hooks": {}, "register": 12}, "type": "plugin", "path": "/home/<USER>/ragflow/hbweb/node_modules/@umijs/preset-umi/dist/features/apiRoute/apiRoute.js", "cwd": "/home/<USER>/ragflow/hbweb", "id": "./node_modules/@umijs/preset-umi/dist/features/apiRoute/apiRoute", "key": "apiRoute"}, "./node_modules/@umijs/preset-umi/dist/features/monorepo/redirect": {"config": {}, "time": {"hooks": {}, "register": 32}, "enableBy": "config", "type": "plugin", "path": "/home/<USER>/ragflow/hbweb/node_modules/@umijs/preset-umi/dist/features/monorepo/redirect.js", "cwd": "/home/<USER>/ragflow/hbweb", "id": "./node_modules/@umijs/preset-umi/dist/features/monorepo/redirect", "key": "monorepoRedirect"}, "./node_modules/@umijs/preset-umi/dist/features/test/test": {"config": {}, "time": {"hooks": {}, "register": 2}, "type": "plugin", "path": "/home/<USER>/ragflow/hbweb/node_modules/@umijs/preset-umi/dist/features/test/test.js", "cwd": "/home/<USER>/ragflow/hbweb", "id": "./node_modules/@umijs/preset-umi/dist/features/test/test", "key": "test"}, "./node_modules/@umijs/preset-umi/dist/features/clickToComponent/clickToComponent": {"config": {}, "time": {"hooks": {}, "register": 1}, "enableBy": "config", "type": "plugin", "path": "/home/<USER>/ragflow/hbweb/node_modules/@umijs/preset-umi/dist/features/clickToComponent/clickToComponent.js", "cwd": "/home/<USER>/ragflow/hbweb", "id": "./node_modules/@umijs/preset-umi/dist/features/clickToComponent/clickToComponent", "key": "clickToComponent"}, "./node_modules/@umijs/preset-umi/dist/features/legacy/legacy": {"config": {}, "time": {"hooks": {}, "register": 2}, "enableBy": "config", "type": "plugin", "path": "/home/<USER>/ragflow/hbweb/node_modules/@umijs/preset-umi/dist/features/legacy/legacy.js", "cwd": "/home/<USER>/ragflow/hbweb", "id": "./node_modules/@umijs/preset-umi/dist/features/legacy/legacy", "key": "legacy"}, "./node_modules/@umijs/preset-umi/dist/features/classPropertiesLoose/classPropertiesLoose": {"config": {}, "time": {"hooks": {}, "register": 1}, "enableBy": "config", "type": "plugin", "path": "/home/<USER>/ragflow/hbweb/node_modules/@umijs/preset-umi/dist/features/classPropertiesLoose/classPropertiesLoose.js", "cwd": "/home/<USER>/ragflow/hbweb", "id": "./node_modules/@umijs/preset-umi/dist/features/classPropertiesLoose/classPropertiesLoose", "key": "classPropertiesLoose"}, "./node_modules/@umijs/preset-umi/dist/features/webpack/webpack": {"config": {}, "time": {"hooks": {}, "register": 2}, "type": "plugin", "path": "/home/<USER>/ragflow/hbweb/node_modules/@umijs/preset-umi/dist/features/webpack/webpack.js", "cwd": "/home/<USER>/ragflow/hbweb", "id": "./node_modules/@umijs/preset-umi/dist/features/webpack/webpack", "key": "preset-umi:webpack"}, "./node_modules/@umijs/preset-umi/dist/features/swc/swc": {"config": {}, "time": {"hooks": {"addOnDemandDeps": [0]}, "register": 1}, "enableBy": "register", "type": "plugin", "path": "/home/<USER>/ragflow/hbweb/node_modules/@umijs/preset-umi/dist/features/swc/swc.js", "cwd": "/home/<USER>/ragflow/hbweb", "id": "./node_modules/@umijs/preset-umi/dist/features/swc/swc", "key": "swc"}, "./node_modules/@umijs/preset-umi/dist/features/ui/ui": {"config": {}, "time": {"hooks": {}, "register": 12}, "type": "plugin", "path": "/home/<USER>/ragflow/hbweb/node_modules/@umijs/preset-umi/dist/features/ui/ui.js", "cwd": "/home/<USER>/ragflow/hbweb", "id": "./node_modules/@umijs/preset-umi/dist/features/ui/ui", "key": "ui"}, "./node_modules/@umijs/preset-umi/dist/features/mako/mako": {"config": {}, "time": {"hooks": {}, "register": 1}, "enableBy": "config", "type": "plugin", "path": "/home/<USER>/ragflow/hbweb/node_modules/@umijs/preset-umi/dist/features/mako/mako.js", "cwd": "/home/<USER>/ragflow/hbweb", "id": "./node_modules/@umijs/preset-umi/dist/features/mako/mako", "key": "mako"}, "./node_modules/@umijs/preset-umi/dist/features/hmrGuardian/hmrGuardian": {"config": {}, "time": {"hooks": {}, "register": 3}, "type": "plugin", "path": "/home/<USER>/ragflow/hbweb/node_modules/@umijs/preset-umi/dist/features/hmrGuardian/hmrGuardian.js", "cwd": "/home/<USER>/ragflow/hbweb", "id": "./node_modules/@umijs/preset-umi/dist/features/hmrGuardian/hmrGuardian", "key": "hm<PERSON><PERSON><PERSON><PERSON>"}, "./node_modules/@umijs/preset-umi/dist/features/routePreloadOnLoad/routePreloadOnLoad": {"config": {}, "time": {"hooks": {}, "register": 4}, "type": "plugin", "path": "/home/<USER>/ragflow/hbweb/node_modules/@umijs/preset-umi/dist/features/routePreloadOnLoad/routePreloadOnLoad.js", "cwd": "/home/<USER>/ragflow/hbweb", "id": "./node_modules/@umijs/preset-umi/dist/features/routePreloadOnLoad/routePreloadOnLoad", "key": "routePreloadOnLoad"}, "./node_modules/@umijs/preset-umi/dist/features/forget/forget": {"config": {}, "time": {"hooks": {}, "register": 3}, "enableBy": "config", "type": "plugin", "path": "/home/<USER>/ragflow/hbweb/node_modules/@umijs/preset-umi/dist/features/forget/forget.js", "cwd": "/home/<USER>/ragflow/hbweb", "id": "./node_modules/@umijs/preset-umi/dist/features/forget/forget", "key": "forget"}, "./node_modules/@umijs/preset-umi/dist/features/bundler/bundler": {"config": {}, "time": {"hooks": {}, "register": 2}, "enableBy": "register", "type": "plugin", "path": "/home/<USER>/ragflow/hbweb/node_modules/@umijs/preset-umi/dist/features/bundler/bundler.js", "cwd": "/home/<USER>/ragflow/hbweb", "id": "./node_modules/@umijs/preset-umi/dist/features/bundler/bundler", "key": "preset-umi:bundler"}, "./node_modules/@umijs/preset-umi/dist/commands/build": {"config": {}, "time": {"hooks": {}, "register": 9}, "enableBy": "register", "type": "plugin", "path": "/home/<USER>/ragflow/hbweb/node_modules/@umijs/preset-umi/dist/commands/build.js", "cwd": "/home/<USER>/ragflow/hbweb", "id": "./node_modules/@umijs/preset-umi/dist/commands/build", "key": "build"}, "./node_modules/@umijs/preset-umi/dist/commands/config/config": {"config": {}, "time": {"hooks": {}, "register": 60}, "enableBy": "register", "type": "plugin", "path": "/home/<USER>/ragflow/hbweb/node_modules/@umijs/preset-umi/dist/commands/config/config.js", "cwd": "/home/<USER>/ragflow/hbweb", "id": "./node_modules/@umijs/preset-umi/dist/commands/config/config", "key": "config"}, "./node_modules/@umijs/preset-umi/dist/commands/dev/dev": {"config": {}, "time": {"hooks": {"modifyAppData": [9], "onStart": [1]}, "register": 123}, "type": "plugin", "path": "/home/<USER>/ragflow/hbweb/node_modules/@umijs/preset-umi/dist/commands/dev/dev.js", "cwd": "/home/<USER>/ragflow/hbweb", "id": "./node_modules/@umijs/preset-umi/dist/commands/dev/dev", "key": "dev"}, "./node_modules/@umijs/preset-umi/dist/commands/help": {"config": {}, "time": {"hooks": {}, "register": 2}, "enableBy": "register", "type": "plugin", "path": "/home/<USER>/ragflow/hbweb/node_modules/@umijs/preset-umi/dist/commands/help.js", "cwd": "/home/<USER>/ragflow/hbweb", "id": "./node_modules/@umijs/preset-umi/dist/commands/help", "key": "help"}, "./node_modules/@umijs/preset-umi/dist/commands/lint": {"config": {}, "time": {"hooks": {}, "register": 1}, "enableBy": "register", "type": "plugin", "path": "/home/<USER>/ragflow/hbweb/node_modules/@umijs/preset-umi/dist/commands/lint.js", "cwd": "/home/<USER>/ragflow/hbweb", "id": "./node_modules/@umijs/preset-umi/dist/commands/lint", "key": "lint"}, "./node_modules/@umijs/preset-umi/dist/commands/setup": {"config": {}, "time": {"hooks": {}, "register": 2}, "enableBy": "register", "type": "plugin", "path": "/home/<USER>/ragflow/hbweb/node_modules/@umijs/preset-umi/dist/commands/setup.js", "cwd": "/home/<USER>/ragflow/hbweb", "id": "./node_modules/@umijs/preset-umi/dist/commands/setup", "key": "setup"}, "./node_modules/@umijs/preset-umi/dist/commands/deadcode": {"config": {}, "time": {"hooks": {}, "register": 2}, "enableBy": "register", "type": "plugin", "path": "/home/<USER>/ragflow/hbweb/node_modules/@umijs/preset-umi/dist/commands/deadcode.js", "cwd": "/home/<USER>/ragflow/hbweb", "id": "./node_modules/@umijs/preset-umi/dist/commands/deadcode", "key": "deadcode"}, "./node_modules/@umijs/preset-umi/dist/commands/version": {"config": {}, "time": {"hooks": {}, "register": 1}, "enableBy": "register", "type": "plugin", "path": "/home/<USER>/ragflow/hbweb/node_modules/@umijs/preset-umi/dist/commands/version.js", "cwd": "/home/<USER>/ragflow/hbweb", "id": "./node_modules/@umijs/preset-umi/dist/commands/version", "key": "version"}, "./node_modules/@umijs/preset-umi/dist/commands/generators/page": {"config": {}, "time": {"hooks": {}, "register": 3}, "enableBy": "register", "type": "plugin", "path": "/home/<USER>/ragflow/hbweb/node_modules/@umijs/preset-umi/dist/commands/generators/page.js", "cwd": "/home/<USER>/ragflow/hbweb", "id": "./node_modules/@umijs/preset-umi/dist/commands/generators/page", "key": "generator:page"}, "./node_modules/@umijs/preset-umi/dist/commands/generators/prettier": {"config": {}, "time": {"hooks": {}, "register": 1}, "enableBy": "register", "type": "plugin", "path": "/home/<USER>/ragflow/hbweb/node_modules/@umijs/preset-umi/dist/commands/generators/prettier.js", "cwd": "/home/<USER>/ragflow/hbweb", "id": "./node_modules/@umijs/preset-umi/dist/commands/generators/prettier", "key": "generator:prettier"}, "./node_modules/@umijs/preset-umi/dist/commands/generators/tsconfig": {"config": {}, "time": {"hooks": {}, "register": 1}, "enableBy": "register", "type": "plugin", "path": "/home/<USER>/ragflow/hbweb/node_modules/@umijs/preset-umi/dist/commands/generators/tsconfig.js", "cwd": "/home/<USER>/ragflow/hbweb", "id": "./node_modules/@umijs/preset-umi/dist/commands/generators/tsconfig", "key": "generator:tsconfig"}, "./node_modules/@umijs/preset-umi/dist/commands/generators/jest": {"config": {}, "time": {"hooks": {}, "register": 2}, "enableBy": "register", "type": "plugin", "path": "/home/<USER>/ragflow/hbweb/node_modules/@umijs/preset-umi/dist/commands/generators/jest.js", "cwd": "/home/<USER>/ragflow/hbweb", "id": "./node_modules/@umijs/preset-umi/dist/commands/generators/jest", "key": "generator:jest"}, "./node_modules/@umijs/preset-umi/dist/commands/generators/tailwindcss": {"config": {}, "time": {"hooks": {}, "register": 2}, "enableBy": "register", "type": "plugin", "path": "/home/<USER>/ragflow/hbweb/node_modules/@umijs/preset-umi/dist/commands/generators/tailwindcss.js", "cwd": "/home/<USER>/ragflow/hbweb", "id": "./node_modules/@umijs/preset-umi/dist/commands/generators/tailwindcss", "key": "generator:tailwindcss"}, "./node_modules/@umijs/preset-umi/dist/commands/generators/dva": {"config": {}, "time": {"hooks": {}, "register": 5}, "enableBy": "register", "type": "plugin", "path": "/home/<USER>/ragflow/hbweb/node_modules/@umijs/preset-umi/dist/commands/generators/dva.js", "cwd": "/home/<USER>/ragflow/hbweb", "id": "./node_modules/@umijs/preset-umi/dist/commands/generators/dva", "key": "generator:dva"}, "./node_modules/@umijs/preset-umi/dist/commands/generators/component": {"config": {}, "time": {"hooks": {}, "register": 2}, "enableBy": "register", "type": "plugin", "path": "/home/<USER>/ragflow/hbweb/node_modules/@umijs/preset-umi/dist/commands/generators/component.js", "cwd": "/home/<USER>/ragflow/hbweb", "id": "./node_modules/@umijs/preset-umi/dist/commands/generators/component", "key": "generator:component"}, "./node_modules/@umijs/preset-umi/dist/commands/generators/mock": {"config": {}, "time": {"hooks": {}, "register": 2}, "enableBy": "register", "type": "plugin", "path": "/home/<USER>/ragflow/hbweb/node_modules/@umijs/preset-umi/dist/commands/generators/mock.js", "cwd": "/home/<USER>/ragflow/hbweb", "id": "./node_modules/@umijs/preset-umi/dist/commands/generators/mock", "key": "generator:mock"}, "./node_modules/@umijs/preset-umi/dist/commands/generators/cypress": {"config": {}, "time": {"hooks": {}, "register": 10}, "enableBy": "register", "type": "plugin", "path": "/home/<USER>/ragflow/hbweb/node_modules/@umijs/preset-umi/dist/commands/generators/cypress.js", "cwd": "/home/<USER>/ragflow/hbweb", "id": "./node_modules/@umijs/preset-umi/dist/commands/generators/cypress", "key": "generator:cypress"}, "./node_modules/@umijs/preset-umi/dist/commands/generators/api": {"config": {}, "time": {"hooks": {}, "register": 1}, "enableBy": "register", "type": "plugin", "path": "/home/<USER>/ragflow/hbweb/node_modules/@umijs/preset-umi/dist/commands/generators/api.js", "cwd": "/home/<USER>/ragflow/hbweb", "id": "./node_modules/@umijs/preset-umi/dist/commands/generators/api", "key": "generator:api"}, "./node_modules/@umijs/preset-umi/dist/commands/generators/precommit": {"config": {}, "time": {"hooks": {}, "register": 2}, "enableBy": "register", "type": "plugin", "path": "/home/<USER>/ragflow/hbweb/node_modules/@umijs/preset-umi/dist/commands/generators/precommit.js", "cwd": "/home/<USER>/ragflow/hbweb", "id": "./node_modules/@umijs/preset-umi/dist/commands/generators/precommit", "key": "generator:precommit"}, "./node_modules/@umijs/preset-umi/dist/commands/plugin": {"config": {}, "time": {"hooks": {}, "register": 6}, "enableBy": "register", "type": "plugin", "path": "/home/<USER>/ragflow/hbweb/node_modules/@umijs/preset-umi/dist/commands/plugin.js", "cwd": "/home/<USER>/ragflow/hbweb", "id": "./node_modules/@umijs/preset-umi/dist/commands/plugin", "key": "command:plugin"}, "./node_modules/@umijs/preset-umi/dist/commands/verify-commit": {"config": {}, "time": {"hooks": {}, "register": 1}, "enableBy": "register", "type": "plugin", "path": "/home/<USER>/ragflow/hbweb/node_modules/@umijs/preset-umi/dist/commands/verify-commit.js", "cwd": "/home/<USER>/ragflow/hbweb", "id": "./node_modules/@umijs/preset-umi/dist/commands/verify-commit", "key": "verifyCommit"}, "./node_modules/@umijs/preset-umi/dist/commands/preview": {"config": {}, "time": {"hooks": {}, "register": 18}, "enableBy": "register", "type": "plugin", "path": "/home/<USER>/ragflow/hbweb/node_modules/@umijs/preset-umi/dist/commands/preview.js", "cwd": "/home/<USER>/ragflow/hbweb", "id": "./node_modules/@umijs/preset-umi/dist/commands/preview", "key": "preview"}, "./node_modules/@umijs/preset-umi/dist/commands/mfsu/mfsu": {"config": {}, "time": {"hooks": {}, "register": 1}, "enableBy": "register", "type": "plugin", "path": "/home/<USER>/ragflow/hbweb/node_modules/@umijs/preset-umi/dist/commands/mfsu/mfsu.js", "cwd": "/home/<USER>/ragflow/hbweb", "id": "./node_modules/@umijs/preset-umi/dist/commands/mfsu/mfsu", "key": "mfsu-cli"}, "@umijs/plugin-run": {"config": {}, "time": {"hooks": {}, "register": 10}, "enableBy": "register", "type": "plugin", "path": "/home/<USER>/ragflow/hbweb/node_modules/@umijs/plugin-run/dist/index.js", "cwd": "/home/<USER>/ragflow/hbweb", "id": "@umijs/plugin-run", "key": "run"}, "./node_modules/@umijs/core/dist/service/generatePlugin": {"config": {}, "time": {"hooks": {}, "register": 1}, "enableBy": "register", "type": "plugin", "path": "/home/<USER>/ragflow/hbweb/node_modules/@umijs/core/dist/service/generatePlugin.js", "cwd": "/home/<USER>/ragflow/hbweb", "id": "./node_modules/@umijs/core/dist/service/generatePlugin", "key": "generatePlugin"}}, "presets": [], "name": "dev", "args": {"_": []}, "userConfig": {"mfsu": false, "esbuildMinifyIIFE": false, "jsMinifier": "none", "cssMinifier": "none", "codeSplitting": {"jsStrategy": "depPerChunk", "jsStrategyOptions": {}}, "routes": [{"path": "/login", "component": "@/pages/login"}, {"path": "/", "redirect": "/dashboard"}, {"path": "/dashboard", "component": "@/pages/dashboard"}, {"path": "/ai-read", "component": "@/pages/ai-read"}, {"path": "/text-to-image", "component": "@/pages/text-to-image"}, {"path": "/knowledge", "component": "@/pages/knowledge"}, {"path": "/documents", "component": "@/pages/documents"}, {"path": "/knowledge/:id", "component": "@/pages/knowledge-detail", "routes": [{"path": "/knowledge/:id", "redirect": "/knowledge/:id/dataset"}, {"path": "/knowledge/:id/dataset", "component": "@/pages/knowledge-detail/dataset"}, {"path": "/knowledge/:id/setting", "component": "@/pages/knowledge-detail/setting"}, {"path": "/knowledge/:id/retrieval-testing", "component": "@/pages/knowledge-detail/retrieval-testing"}, {"path": "/knowledge/:id/dataset/:docId/chunks", "component": "@/pages/knowledge-detail/chunks"}]}, {"path": "/dialogs", "routes": [{"path": "/dialogs", "component": "@/pages/dialogs", "exact": true}, {"path": "/dialogs/:id/view", "component": "@/pages/dialogs/view"}]}, {"path": "/chat", "component": "@/pages/chat"}, {"path": "/conversations", "component": "@/pages/conversations"}, {"path": "/ai-models", "component": "@/pages/ai-models"}, {"path": "/settings", "component": "@/pages/settings"}, {"path": "/chatbot", "component": "@/pages/chatbot"}], "proxy": {"/v1": {"target": "http://***************:9380", "changeOrigin": true, "pathRewrite": {"^": ""}}, "/api/v1/chatbot": {"target": "http://***************:9380", "changeOrigin": true, "pathRewrite": {"^": ""}}, "/api/v1/ai-reading": {"target": "http://***************:9380", "changeOrigin": true, "pathRewrite": {"^/api": ""}}, "/gradio": {"target": "http://*************:8090", "changeOrigin": true, "pathRewrite": {"^/gradio": "/gradio"}, "ws": true, "secure": false, "headers": {"Host": "*************:8090"}}}, "define": {"process.env.API_BASE_URL": "http://***************:9380", "process.env.API_KEY": "hbgk-lhYmE2YTc2M2FiYjExZjA5YTdlMDAwYz", "process.env.TEXT_TO_IMAGE_URL": "http://*************:8090/gradio", "process.env.MINERU_SERVER_URL": "http://*************:7860", "process.env.MINERU_API_KEY": "", "process.env.AUTO_LLM_CHAT_FACTORY": "VLLM", "process.env.AUTO_LLM_CHAT_NAME": "Qwen3-32B", "process.env.AUTO_LLM_CHAT_API_BASE": "http://*************:8000/v1", "process.env.AUTO_LLM_CHAT_MAX_TOKENS": "8192", "process.env.AUTO_LLM_EMBEDDING_FACTORY": "VLLM", "process.env.AUTO_LLM_EMBEDDING_NAME": "bge-m3", "process.env.AUTO_LLM_EMBEDDING_API_BASE": "http://*************:18080/v1", "process.env.AUTO_LLM_EMBEDDING_MAX_TOKENS": "8192", "process.env.AUTO_LLM_RERANK_FACTORY": "VLLM", "process.env.AUTO_LLM_RERANK_NAME": "bge-reranker-v2-m3", "process.env.AUTO_LLM_RERANK_API_BASE": "http://*************:18081/v1", "process.env.AUTO_LLM_RERANK_MAX_TOKENS": "8192"}, "title": "汉邦高科 Knowledge Base System", "hash": true, "outputPath": "dist", "publicPath": "/"}, "mainConfigFile": "/home/<USER>/ragflow/hbweb/.umirc.ts", "config": {"routeLoader": {"moduleType": "esm"}, "mountElementId": "root", "history": {"type": "browser"}, "base": "/", "svgr": {}, "publicPath": "/", "mfsu": false, "ignoreMomentLocale": true, "externals": {}, "autoCSSModules": true, "alias": {"umi": "@@/exports", "react": "/home/<USER>/ragflow/hbweb/node_modules/react", "react-dom": "/home/<USER>/ragflow/hbweb/node_modules/react-dom", "react-router": "/home/<USER>/ragflow/hbweb/node_modules/react-router", "react-router-dom": "/home/<USER>/ragflow/hbweb/node_modules/react-router-dom", "@": "/home/<USER>/ragflow/hbweb/src", "@@": "/home/<USER>/ragflow/hbweb/src/.umi", "regenerator-runtime": "/home/<USER>/ragflow/hbweb/node_modules/regenerator-runtime"}, "esbuildMinifyIIFE": false, "jsMinifier": "none", "cssMinifier": "none", "codeSplitting": {"jsStrategy": "depPerChunk", "jsStrategyOptions": {}}, "routes": [{"path": "/login", "component": "@/pages/login"}, {"path": "/", "redirect": "/dashboard"}, {"path": "/dashboard", "component": "@/pages/dashboard"}, {"path": "/ai-read", "component": "@/pages/ai-read"}, {"path": "/text-to-image", "component": "@/pages/text-to-image"}, {"path": "/knowledge", "component": "@/pages/knowledge"}, {"path": "/documents", "component": "@/pages/documents"}, {"path": "/knowledge/:id", "component": "@/pages/knowledge-detail", "routes": [{"path": "/knowledge/:id", "redirect": "/knowledge/:id/dataset"}, {"path": "/knowledge/:id/dataset", "component": "@/pages/knowledge-detail/dataset"}, {"path": "/knowledge/:id/setting", "component": "@/pages/knowledge-detail/setting"}, {"path": "/knowledge/:id/retrieval-testing", "component": "@/pages/knowledge-detail/retrieval-testing"}, {"path": "/knowledge/:id/dataset/:docId/chunks", "component": "@/pages/knowledge-detail/chunks"}]}, {"path": "/dialogs", "routes": [{"path": "/dialogs", "component": "@/pages/dialogs", "exact": true}, {"path": "/dialogs/:id/view", "component": "@/pages/dialogs/view"}]}, {"path": "/chat", "component": "@/pages/chat"}, {"path": "/conversations", "component": "@/pages/conversations"}, {"path": "/ai-models", "component": "@/pages/ai-models"}, {"path": "/settings", "component": "@/pages/settings"}, {"path": "/chatbot", "component": "@/pages/chatbot"}], "proxy": {"/v1": {"target": "http://***************:9380", "changeOrigin": true, "pathRewrite": {"^": ""}}, "/api/v1/chatbot": {"target": "http://***************:9380", "changeOrigin": true, "pathRewrite": {"^": ""}}, "/api/v1/ai-reading": {"target": "http://***************:9380", "changeOrigin": true, "pathRewrite": {"^/api": ""}}, "/gradio": {"target": "http://*************:8090", "changeOrigin": true, "pathRewrite": {"^/gradio": "/gradio"}, "ws": true, "secure": false, "headers": {"Host": "*************:8090"}}}, "define": {"process.env.API_BASE_URL": "http://***************:9380", "process.env.API_KEY": "hbgk-lhYmE2YTc2M2FiYjExZjA5YTdlMDAwYz", "process.env.TEXT_TO_IMAGE_URL": "http://*************:8090/gradio", "process.env.MINERU_SERVER_URL": "http://*************:7860", "process.env.MINERU_API_KEY": "", "process.env.AUTO_LLM_CHAT_FACTORY": "VLLM", "process.env.AUTO_LLM_CHAT_NAME": "Qwen3-32B", "process.env.AUTO_LLM_CHAT_API_BASE": "http://*************:8000/v1", "process.env.AUTO_LLM_CHAT_MAX_TOKENS": "8192", "process.env.AUTO_LLM_EMBEDDING_FACTORY": "VLLM", "process.env.AUTO_LLM_EMBEDDING_NAME": "bge-m3", "process.env.AUTO_LLM_EMBEDDING_API_BASE": "http://*************:18080/v1", "process.env.AUTO_LLM_EMBEDDING_MAX_TOKENS": "8192", "process.env.AUTO_LLM_RERANK_FACTORY": "VLLM", "process.env.AUTO_LLM_RERANK_NAME": "bge-reranker-v2-m3", "process.env.AUTO_LLM_RERANK_API_BASE": "http://*************:18081/v1", "process.env.AUTO_LLM_RERANK_MAX_TOKENS": "8192"}, "title": "汉邦高科 Knowledge Base System", "hash": true, "outputPath": "dist", "targets": {"chrome": 80}}, "routes": {"1": {"path": "/login", "file": "@/pages/login/index.tsx", "id": "1", "absPath": "/login", "__content": "import {\n  useLogin,\n  useLoginChannels,\n  useLoginWithChannel,\n  useRegister,\n} from '@/hooks/login-hooks';\nimport AuthGuard from '@/components/AuthGuard';\nimport { rsaPsw } from '@/utils';\nimport { Button, Checkbox, Form, Input, Card, Typography, Space, Divider, Result, Progress } from 'antd';\nimport { useEffect, useState, useRef } from 'react';\n\nimport { useTranslate } from '@/hooks/use-i18n';\nimport LanguageSwitcher from '@/components/LanguageSwitcher';\nimport styles from './index.less';\n\nconst { Title, Text } = Typography;\n\ninterface LoginContentProps {\n  onRegisterCountdown?: (isCountdown: boolean) => void;\n}\n\nconst LoginContent: React.FC<LoginContentProps> = ({ onRegisterCountdown }) => {\n  const [title, setTitle] = useState('login');\n  const [form] = Form.useForm();\n  const [showCountdown, setShowCountdown] = useState(false);\n  const [countdown, setCountdown] = useState(30);\n  const countdownRef = useRef<NodeJS.Timeout | null>(null);\n  const t = useTranslate();\n  const { login, loading: signLoading } = useLogin();\n  const { register, loading: registerLoading } = useRegister();\n  const { channels, loading: channelsLoading } = useLoginChannels();\n  const { login: loginWithChannel, loading: loginWithChannelLoading } =\n    useLoginWithChannel();\n  const loading =\n    signLoading ||\n    registerLoading ||\n    channelsLoading ||\n    loginWithChannelLoading;\n\n  const handleLoginWithChannel = async (channel: string) => {\n    await loginWithChannel(channel);\n  };\n\n  const changeTitle = () => {\n    setTitle((title) => (title === 'login' ? 'register' : 'login'));\n    // 切换页面时清除倒计时\n    if (countdownRef.current) {\n      clearInterval(countdownRef.current);\n      countdownRef.current = null;\n    }\n    setShowCountdown(false);\n    setCountdown(30);\n    onRegisterCountdown?.(false); // 通知父组件倒计时结束\n  };\n\n  const startCountdown = () => {\n    setShowCountdown(true);\n    setCountdown(30);\n    onRegisterCountdown?.(true); // 通知父组件开始倒计时，阻止自动跳转\n\n    countdownRef.current = setInterval(() => {\n      setCountdown((prev) => {\n        if (prev <= 1) {\n          // 倒计时结束，切换到登录页面\n          if (countdownRef.current) {\n            clearInterval(countdownRef.current);\n            countdownRef.current = null;\n          }\n          setShowCountdown(false);\n          setTitle('login');\n          form.resetFields();\n          onRegisterCountdown?.(false); // 通知父组件倒计时结束，允许自动跳转\n\n          // 手动触发跳转到dashboard\n          setTimeout(() => {\n            window.location.href = '/dashboard';\n          }, 100);\n\n          return 30;\n        }\n        return prev - 1;\n      });\n    }, 1000);\n  };\n\n  const skipCountdown = () => {\n    if (countdownRef.current) {\n      clearInterval(countdownRef.current);\n      countdownRef.current = null;\n    }\n    setShowCountdown(false);\n    setTitle('login');\n    form.resetFields();\n    setCountdown(30);\n    onRegisterCountdown?.(false); // 通知父组件倒计时结束，允许自动跳转\n\n    // 手动触发跳转到dashboard\n    setTimeout(() => {\n      window.location.href = '/dashboard';\n    }, 100);\n  };\n\n  // 组件卸载时清理定时器\n  useEffect(() => {\n    return () => {\n      if (countdownRef.current) {\n        clearInterval(countdownRef.current);\n      }\n    };\n  }, []);\n\n  const onCheck = async () => {\n    try {\n      const params = await form.validateFields();\n\n      const rsaPassWord = rsaPsw(params.password) as string;\n\n      if (title === 'login') {\n        await login({\n          email: `${params.email}`.trim(),\n          password: rsaPassWord,\n        });\n        // 登录成功后，useAuth hook会自动处理跳转\n        // 不需要在这里手动跳转\n      } else {\n        const code = await register({\n          nickname: params.nickname,\n          email: params.email,\n          password: rsaPassWord,\n        });\n        if (code === 0) {\n          // 注册成功后开始倒计时\n          startCountdown();\n        }\n      }\n    } catch (errorInfo) {\n      console.log('Failed:', errorInfo);\n    }\n  };\n\n  return (\n    <div className={styles.loginPage}>\n      {/* 语言切换器 */}\n      <div className={styles.languageSwitcher}>\n        <LanguageSwitcher />\n      </div>\n\n      <div className={styles.loginContainer}>\n        <Card className={styles.loginCard}>\n          {showCountdown ? (\n            // 注册成功倒计时页面\n            <div className={styles.countdownContainer}>\n              <Result\n                status=\"success\"\n                title={t('auth.registerSuccess')}\n                subTitle={t('auth.registerSuccessDesc')}\n                extra={[\n                  <div key=\"countdown\" className={styles.countdownContent}>\n                    <div className={styles.initializingText}>\n                      <Text strong>{t('auth.initializingSystem')}</Text>\n                    </div>\n                    <div className={styles.progressContainer}>\n                      <Progress\n                        type=\"circle\"\n                        percent={((30 - countdown) / 30) * 100}\n                        format={() => countdown}\n                        size={120}\n                        strokeColor={{\n                          '0%': '#108ee9',\n                          '100%': '#87d068',\n                        }}\n                      />\n                    </div>\n                    <div className={styles.countdownText}>\n                      <Text type=\"secondary\">\n                        {t('auth.countdownMessage', { seconds: countdown })}\n                      </Text>\n                    </div>\n                    <Button\n                      type=\"link\"\n                      onClick={skipCountdown}\n                      className={styles.skipButton}\n                    >\n                      {t('auth.skipCountdown')}\n                    </Button>\n                  </div>\n                ]}\n              />\n            </div>\n          ) : (\n            // 原有的登录/注册表单\n            <>\n              <div className={styles.loginHeader}>\n                <Title level={2} className={styles.loginTitle}>\n                  {title === 'login' ? t('auth.signIn') : t('auth.signUp')}\n                </Title>\n                <Text type=\"secondary\">\n                  {title === 'login'\n                    ? t('auth.welcomeBack')\n                    : t('auth.createAccountDesc')}\n                </Text>\n              </div>\n\n          <Form\n            form={form}\n            layout=\"vertical\"\n            name=\"auth_form\"\n            className={styles.loginForm}\n          >\n            <Form.Item\n              name=\"email\"\n              label={t('auth.email')}\n              rules={[\n                { required: true, message: t('auth.emailRequired') },\n                { type: 'email', message: t('auth.emailInvalid') }\n              ]}\n            >\n              <Input size=\"large\" placeholder={t('auth.enterEmail')} />\n            </Form.Item>\n\n            {title === 'register' && (\n              <Form.Item\n                name=\"nickname\"\n                label={t('auth.nickname')}\n                rules={[{ required: true, message: t('auth.nicknameRequired') }]}\n              >\n                <Input size=\"large\" placeholder={t('auth.enterNickname')} />\n              </Form.Item>\n            )}\n\n            <Form.Item\n              name=\"password\"\n              label={t('auth.password')}\n              rules={[{ required: true, message: t('auth.passwordRequired') }]}\n            >\n              <Input.Password\n                size=\"large\"\n                placeholder={t('auth.enterPassword')}\n                onPressEnter={onCheck}\n              />\n            </Form.Item>\n            \n            {title === 'login' && (\n              <Form.Item name=\"remember\" valuePropName=\"checked\">\n                <Checkbox>{t('auth.rememberMe')}</Checkbox>\n              </Form.Item>\n            )}\n\n            <Form.Item>\n              <Button\n                type=\"primary\"\n                block\n                size=\"large\"\n                onClick={onCheck}\n                loading={loading}\n                className={styles.submitButton}\n              >\n                {title === 'login' ? t('auth.signIn') : t('auth.createAccount')}\n              </Button>\n            </Form.Item>\n\n            <div className={styles.switchMode}>\n              {title === 'login' ? (\n                <Text>\n                  {t('auth.dontHaveAccount')}{' '}\n                  <Button type=\"link\" onClick={changeTitle} className={styles.linkButton}>\n                    {t('auth.signUp')}\n                  </Button>\n                </Text>\n              ) : (\n                <Text>\n                  {t('auth.alreadyHaveAccount')}{' '}\n                  <Button type=\"link\" onClick={changeTitle} className={styles.linkButton}>\n                    {t('auth.signIn')}\n                  </Button>\n                </Text>\n              )}\n            </div>\n\n            {title === 'login' && channels && channels.length > 0 && (\n              <>\n                <Divider>{t('auth.orContinueWith', 'Or continue with')}</Divider>\n                <Space direction=\"vertical\" style={{ width: '100%' }}>\n                  {channels.map((item) => (\n                    <Button\n                      key={item.channel}\n                      block\n                      size=\"large\"\n                      onClick={() => handleLoginWithChannel(item.channel)}\n                      className={styles.oauthButton}\n                    >\n                      {t('auth.signInWith', 'Sign in with {{provider}}', { provider: item.display_name })}\n                    </Button>\n                  ))}\n                </Space>\n              </>\n            )}\n          </Form>\n            </>\n          )}\n        </Card>\n      </div>\n    </div>\n  );\n};\n\nconst Login = () => {\n  const [preventAutoRedirect, setPreventAutoRedirect] = useState(false);\n\n  return (\n    <AuthGuard requireAuth={false} preventAutoRedirect={preventAutoRedirect}>\n      <LoginContent onRegisterCountdown={setPreventAutoRedirect} />\n    </AuthGuard>\n  );\n};\n\nexport default Login;\n", "__isJSFile": true, "__absFile": "/home/<USER>/ragflow/hbweb/src/pages/login/index.tsx"}, "2": {"path": "/", "redirect": "/dashboard", "id": "2", "absPath": "/"}, "3": {"path": "/dashboard", "file": "@/pages/dashboard/index.tsx", "id": "3", "absPath": "/dashboard", "__content": "import { useAuth } from '@/hooks/auth-hooks';\nimport { useLogout } from '@/hooks/login-hooks';\nimport AppLayout from '@/components/AppLayout';\nimport { useNavigate } from 'umi';\nimport { useTranslate } from '@/hooks/use-i18n';\nimport {\n  BookOutlined,\n  DatabaseOutlined,\n  MessageOutlined,\n  SettingOutlined,\n  UserOutlined,\n  LogoutOutlined,\n  FileTextOutlined,\n  RobotOutlined,\n} from '@ant-design/icons';\nimport {\n  Avatar,\n  Dropdown,\n  Typography,\n  Card,\n  Row,\n  Col,\n  Statistic,\n  Button,\n  Space,\n  List,\n  Tag,\n  message,\n} from 'antd';\nimport { useState } from 'react';\nimport styles from './index.less';\n\nconst { Title, Text } = Typography;\n\nconst DashboardContent = () => {\n  const t = useTranslate();\n  const { userInfo } = useAuth();\n  const { logout } = useLogout();\n  const navigate = useNavigate();\n\n  const handleLogout = async () => {\n    await logout();\n  };\n\n  const userMenuItems = [\n    {\n      key: 'profile',\n      icon: <UserOutlined />,\n      label: t('settings.profile'),\n    },\n    {\n      key: 'settings',\n      icon: <SettingOutlined />,\n      label: t('navigation.settings'),\n    },\n    {\n      type: 'divider' as const,\n    },\n    {\n      key: 'logout',\n      icon: <LogoutOutlined />,\n      label: t('auth.signOut'),\n      onClick: handleLogout,\n    },\n  ];\n\n  const menuItems = [\n    {\n      key: 'dashboard',\n      icon: <DatabaseOutlined />,\n      label: t('navigation.dashboard'),\n    },\n    {\n      key: 'conversations',\n      icon: <MessageOutlined />,\n      label: t('navigation.conversations'),\n    },\n    {\n      key: 'knowledge',\n      icon: <BookOutlined />,\n      label: t('navigation.knowledgeBase'),\n      onClick: () => navigate('/knowledge'),\n    },\n    // {\n    //   key: 'documents',\n    //   icon: <FileTextOutlined />,\n    //   label: t('navigation.documents'),\n    // },\n    {\n      key: 'models',\n      icon: <RobotOutlined />,\n      label: t('navigation.aiModels'),\n    },\n    {\n      key: 'settings',\n      icon: <SettingOutlined />,\n      label: t('navigation.settings'),\n    },\n  ];\n\n  const quickActions = [\n    {\n      title: t('dashboard.createKnowledgeBase', 'Create Knowledge Base'),\n      description: t('dashboard.createKnowledgeBaseDesc', 'Start building your knowledge repository'),\n      icon: <BookOutlined />,\n      color: '#1890ff',\n      action: () => navigate('/knowledge'),\n    },\n    {\n      title: t('dashboard.manageDocuments', 'Manage Documents'),\n      description: t('dashboard.manageDocumentsDesc', 'Upload and organize your documents'),\n      icon: <FileTextOutlined />,\n      color: '#52c41a',\n      action: () => navigate('/knowledge'),\n    },\n    {\n      title: t('dashboard.startConversation', 'Start Conversation'),\n      description: t('dashboard.startConversationDesc', 'Chat with your AI assistant'),\n      icon: <MessageOutlined />,\n      color: '#722ed1',\n      action: () => {\n        message.info(t('common.comingSoon'));\n      },\n    },\n    {\n      title: t('dashboard.configureModels', 'Configure Models'),\n      description: t('dashboard.configureModelsDesc', 'Set up your AI models'),\n      icon: <RobotOutlined />,\n      color: '#fa8c16',\n      action: () => {\n        message.info(t('common.comingSoon'));\n      },\n    },\n  ];\n\n  const recentActivities = [\n    {\n      title: t('dashboard.knowledgeBaseCreated', 'Knowledge Base created'),\n      time: '2 hours ago',\n      type: 'create',\n    },\n    {\n      title: t('dashboard.documentUploaded', 'Document uploaded'),\n      time: '4 hours ago',\n      type: 'upload',\n    },\n    {\n      title: t('dashboard.conversationCompleted', 'Conversation with AI completed'),\n      time: '1 day ago',\n      type: 'chat',\n    },\n    {\n      title: t('dashboard.modelConfigUpdated', 'Model configuration updated'),\n      time: '2 days ago',\n      type: 'config',\n    },\n  ];\n\n  return (\n    <div className={styles.dashboardContent}>\n      <div className={styles.welcomeSection}>\n        <Typography.Title level={2}>{t('dashboard.welcomeBack', 'Welcome back, {{name}}!', { name: userInfo?.name || t('common.user') })}</Typography.Title>\n        <Typography.Text type=\"secondary\">\n          {t('dashboard.manageDescription', 'Manage your knowledge base and interact with AI-powered insights')}\n        </Typography.Text>\n      </div>\n{/*\n      <Row gutter={[24, 24]} className={styles.statsRow}>\n        <Col xs={24} sm={12} lg={6}>\n          <Card>\n            <Statistic\n              title={t('dashboard.knowledgeBases', 'Knowledge Bases')}\n              value={5}\n              prefix={<BookOutlined />}\n              valueStyle={{ color: '#1890ff' }}\n            />\n          </Card>\n        </Col>\n        <Col xs={24} sm={12} lg={6}>\n          <Card>\n            <Statistic\n              title={t('navigation.documents')}\n              value={128}\n              prefix={<FileTextOutlined />}\n              valueStyle={{ color: '#52c41a' }}\n            />\n          </Card>\n        </Col>\n        <Col xs={24} sm={12} lg={6}>\n          <Card>\n            <Statistic\n              title={t('navigation.conversations')}\n              value={42}\n              prefix={<MessageOutlined />}\n              valueStyle={{ color: '#722ed1' }}\n            />\n          </Card>\n        </Col>\n        <Col xs={24} sm={12} lg={6}>\n          <Card>\n            <Statistic\n              title={t('navigation.aiModels')}\n              value={3}\n              prefix={<RobotOutlined />}\n              valueStyle={{ color: '#fa8c16' }}\n            />\n          </Card>\n        </Col>\n      </Row>\n*/}\n      <Row gutter={[24, 24]}>\n        <Col xs={24} lg={24}>\n          <Card title={t('dashboard.quickActions', 'Quick Actions')} className={styles.quickActionsCard}>\n            <Row gutter={[16, 16]}>\n              {quickActions.map((action, index) => (\n                <Col xs={24} sm={12} key={index}>\n                  <Card\n                    hoverable\n                    className={styles.quickActionCard}\n                    onClick={action.action}\n                  >\n                    <div className={styles.actionContent}>\n                      <div\n                        className={styles.actionIcon}\n                        style={{ color: action.color }}\n                      >\n                        {action.icon}\n                      </div>\n                      <div>\n                        <Title level={5} style={{ margin: 0 }}>\n                          {action.title}\n                        </Title>\n                        <Text type=\"secondary\">{action.description}</Text>\n                      </div>\n                    </div>\n                  </Card>\n                </Col>\n              ))}\n            </Row>\n          </Card>\n        </Col>\n        {/*}\n        <Col xs={24} lg={8}>\n          <Card title={t('dashboard.recentActivities', 'Recent Activities')} className={styles.activitiesCard}>\n            <List\n              dataSource={recentActivities}\n              renderItem={(item) => (\n                <List.Item>\n                  <List.Item.Meta\n                    title={item.title}\n                    description={item.time}\n                  />\n                  <Tag color={\n                    item.type === 'create' ? 'blue' :\n                    item.type === 'upload' ? 'green' :\n                    item.type === 'chat' ? 'purple' : 'orange'\n                  }>\n                    {t(`dashboard.activityTypes.${item.type}`, item.type)}\n                  </Tag>\n                </List.Item>\n              )}\n            />\n          </Card>\n        </Col>\n        */}\n      </Row>\n    </div>\n  );\n};\n\nconst Dashboard = () => {\n  return (\n    <AppLayout showSearch={false}>\n      <DashboardContent />\n    </AppLayout>\n  );\n};\n\nexport default Dashboard;\n", "__isJSFile": true, "__absFile": "/home/<USER>/ragflow/hbweb/src/pages/dashboard/index.tsx"}, "4": {"path": "/ai-read", "file": "@/pages/ai-read/index.tsx", "id": "4", "absPath": "/ai-read", "__content": "import React, { useState, useCallback } from 'react';\nimport {\n  Layout,\n  Card,\n  Upload,\n  Tabs,\n  Typography,\n  Button,\n  Space,\n  Spin,\n  Alert,\n  message,\n  Row,\n  Col,\n  Divider,\n  Empty,\n  Progress,\n  Tag,\n  Modal,\n} from 'antd';\nimport {\n  UploadOutlined,\n  FileTextOutlined,\n  MessageOutlined,\n  RobotOutlined,\n  FileSearchOutlined,\n  QuestionCircleOutlined,\n  ReloadOutlined,\n  PlayCircleOutlined,\n  DeleteOutlined,\n} from '@ant-design/icons';\nimport { useTranslate } from '@/hooks/use-i18n';\nimport AppLayout from '@/components/AppLayout';\nimport MarkdownContent from '@/components/MarkdownContent';\nimport DocumentSummaryCard from './components/DocumentSummaryCard';\nimport DocumentChatCard from './components/DocumentChatCard';\nimport PendingCard from './components/PendingCard';\nimport DocumentPreview from '@/components/DocumentPreview';\nimport { useAIReading } from '@/hooks/use-ai-reading-hooks';\nimport styles from './index.less';\n\nconst { Content } = Layout;\nconst { Title, Text } = Typography;\nconst { Dragger } = Upload;\n\nconst AIReadPage: React.FC = () => {\n  const t = useTranslate();\n  const [activeTab, setActiveTab] = useState('preview');\n\n  // 使用AI阅读hook\n  const {\n    files,\n    currentFile,\n    conversations,\n    loading,\n    uploading,\n    processing,\n    uploadFile,\n    processFile,\n    deleteFile,\n    chatWithDocument,\n    fetchConversations,\n    selectFile,\n    refreshFileStatus,\n  } = useAIReading();\n\n  // 处理文档上传\n  const handleDocumentUpload = useCallback(async (fileList: File[]) => {\n    // 检查文件数量限制\n    if (files.length + fileList.length > 25) {\n      message.error(t('aiRead.maxFilesError'));\n      return;\n    }\n\n    try {\n      // 处理每个文件\n      for (const file of fileList) {\n        // 检查文件类型\n        const allowedTypes = [\n          'application/pdf',\n          'application/msword',\n          'application/vnd.openxmlformats-officedocument.wordprocessingml.document',\n          'application/vnd.openxmlformats-officedocument.presentationml.presentation',\n          'application/vnd.ms-powerpoint',\n          'text/plain',\n          'text/markdown',\n        ];\n\n        const allowedExtensions = /\\.(pdf|doc|docx|ppt|pptx|txt|md)$/i;\n\n        if (!allowedTypes.includes(file.type) && !allowedExtensions.test(file.name)) {\n          message.error(t('aiRead.unsupportedFormat', { filename: file.name }));\n          continue;\n        }\n\n        // 检查文件大小 (50MB限制)\n        const maxSize = 50 * 1024 * 1024; // 50MB\n        if (file.size > maxSize) {\n          message.error(t('aiRead.fileTooLarge', {\n            filename: file.name,\n            maxSize: (maxSize / 1024 / 1024).toFixed(0)\n          }));\n          continue;\n        }\n\n        // 上传文件\n        await uploadFile(file);\n      }\n    } catch (error) {\n      console.error(t('aiRead.uploadFailedError'), error);\n      message.error(t('aiRead.uploadFailed'));\n    }\n  }, [files.length, uploadFile, t]);\n\n  // 获取状态标签颜色\n  const getStatusColor = (status: string) => {\n    switch (status) {\n      case 'uploaded': return 'blue';\n      case 'converting_to_pdf': return 'orange';\n      case 'calling_parser': return 'purple';\n      case 'processing': return 'cyan';\n      case 'saving_results': return 'geekblue';\n      case 'generating_summary': return 'magenta';\n      case 'completed': return 'green';\n      case 'failed': return 'red';\n      default: return 'default';\n    }\n  };\n\n  // 获取状态文本\n  const getStatusText = (status: string) => {\n    const statusKey = `aiRead.status.${status}`;\n    const translatedStatus = t(statusKey);\n    // 如果翻译键不存在，返回原状态值\n    return translatedStatus !== statusKey ? translatedStatus : status;\n  };\n\n  // 上传配置\n  const uploadProps = {\n    name: 'file',\n    multiple: true,\n    beforeUpload: (file: File, fileList: File[]) => {\n      handleDocumentUpload(fileList);\n      return false; // 阻止默认上传\n    },\n    disabled: uploading,\n    accept: '.pdf,.doc,.docx,.ppt,.pptx,.txt,.md',\n    showUploadList: false, // 隐藏默认的上传列表\n  };\n\n  // Tab配置\n  const tabItems = [\n    {\n      key: 'preview',\n      label: (\n        <Space>\n          <FileTextOutlined />\n          {t('aiRead.preview')}\n        </Space>\n      ),\n      children: (\n        <DocumentPreview\n          document={currentFile ? {\n            id: currentFile.file_id,\n            name: currentFile.original_filename,\n            content: currentFile.content_summary || '',\n            type: currentFile.file_type,\n            size: currentFile.file_size,\n            uploadTime: currentFile.create_time,\n            // 对于Word文件，总是提供PDF预览URL；对于PDF文件，也提供URL\n            url: ['pdf', 'doc', 'docx', 'ppt', 'pptx'].includes(currentFile.file_type.toLowerCase())\n              ? `/api/v1/ai-reading/files/${currentFile.file_id}/pdf`\n              : undefined,\n          } : null}\n          loading={loading}\n        />\n      ),\n    },\n    {\n      key: 'summary',\n      label: (\n        <Space>\n          <FileSearchOutlined />\n          {t('aiRead.summary')}\n        </Space>\n      ),\n      children: (\n        <DocumentSummaryCard\n          file={currentFile}\n          loading={loading}\n          onRefresh={() => currentFile && refreshFileStatus(currentFile.file_id)}\n        />\n      ),\n    },\n    {\n      key: 'pending',\n      label: (\n        <Space>\n          <QuestionCircleOutlined />\n          {t('aiRead.pending')}\n        </Space>\n      ),\n      children: (\n        <PendingCard />\n      ),\n    },\n    {\n      key: 'chat',\n      label: (\n        <Space>\n          <MessageOutlined />\n          {t('aiRead.chat')}\n        </Space>\n      ),\n      children: (\n        <DocumentChatCard\n          file={currentFile}\n          conversations={conversations}\n          loading={loading}\n          onSendMessage={(question, sessionId) =>\n            currentFile && chatWithDocument(currentFile.file_id, question, sessionId)\n          }\n          onLoadConversations={(sessionId) =>\n            currentFile && fetchConversations(currentFile.file_id, sessionId)\n          }\n        />\n      ),\n    },\n  ];\n\n  return (\n    <AppLayout>\n      <Content className={styles.aiReadContent}>\n        <div className={styles.container}>\n          {/* 页面标题 */}\n          <Row gutter={[16, 16]}>\n              <Col xs={24} sm={16}>\n              \n                <div className={styles.header}>\n                  <Title level={2}>\n                    <RobotOutlined style={{ marginRight: 8 }} />\n                    {t('aiRead.title')}\n                  </Title>\n                  <Text type=\"secondary\">\n                    {t('aiRead.description')}\n                  </Text>\n                </div>\n              </Col>\n              <Col xs={24} sm={8}>\n                {/* 文件上传激励区域 */}\n                  <div className={styles.uploadButton}>\n                    <Dragger {...uploadProps} className={styles.compactUploadArea}>\n                      <div className=\"ant-upload-drag-container\">\n                        <div className=\"ant-upload-drag-icon\">\n                          <UploadOutlined />\n                        </div>\n                        <div className=\"upload-content\">\n                          <div className=\"ant-upload-text\">\n                            {t('aiRead.uploadArea')}\n                          </div>\n                          <div className=\"ant-upload-hint\">\n                            {t('aiRead.uploadHint')} ({t('aiRead.maxFiles')})\n                          </div>\n                        </div>\n                      </div>\n                    </Dragger>\n                  </div>\n                </Col>\n          </Row>\n\n          <div className={styles.fileUploadSection}>\n            {/* 文件列表 */}\n            {files.length > 0 && (\n              <div className={styles.fileList}>\n                <Title level={4}>{t('aiRead.uploadedFiles')} ({files.length}/25)</Title>\n                <div className={styles.fileItems}>\n                  {files.map((file) => (\n                    <div\n                      key={file.file_id}\n                      className={`${styles.fileItem} ${currentFile?.file_id === file.file_id ? styles.active : ''}`}\n                      onClick={() => selectFile(file)}\n                    >\n                      <div className={styles.fileInfo}>\n                        <div className={styles.fileName}>{file.original_filename}</div>\n                        <div className={styles.fileSize}>\n                          {(file.file_size / 1024).toFixed(2)} KB\n                        </div>\n                        <div className={styles.fileStatus}>\n                          <Tag color={getStatusColor(file.processing_status)}>\n                            {getStatusText(file.processing_status)}\n                          </Tag>\n                        </div>\n                        {['processing', 'converting_to_pdf', 'calling_parser', 'saving_results', 'generating_summary'].includes(file.processing_status) && (\n                          <Progress\n                            percent={file.processing_progress}\n                            size=\"small\"\n                            status=\"active\"\n                          />\n                        )}\n                      </div>\n                      <div className={styles.fileActions}>\n                        {processing[file.file_id] && (\n                          <Spin size=\"small\" />\n                        )}\n                        {file.processing_status === 'uploaded' && (\n                          <Button\n                            type=\"text\"\n                            size=\"small\"\n                            icon={<PlayCircleOutlined />}\n                            onClick={(e) => {\n                              e.stopPropagation();\n                              processFile(file.file_id);\n                            }}\n                            title={t('aiRead.startProcessing')}\n                          />\n                        )}\n                        <Button\n                          type=\"text\"\n                          size=\"small\"\n                          icon={<ReloadOutlined />}\n                          onClick={(e) => {\n                            e.stopPropagation();\n                            refreshFileStatus(file.file_id);\n                          }}\n                          title={t('aiRead.refreshStatus')}\n                        />\n                        <Button\n                          type=\"text\"\n                          size=\"small\"\n                          icon={<DeleteOutlined />}\n                          danger\n                          onClick={(e) => {\n                            e.stopPropagation();\n                            Modal.confirm({\n                              title: t('aiRead.confirmDelete'),\n                              content: t('aiRead.confirmDeleteMessage', { filename: file.original_filename }),\n                              okText: t('common.delete'),\n                              okType: 'danger',\n                              cancelText: t('common.cancel'),\n                              onOk: () => deleteFile(file.file_id),\n                            });\n                          }}\n                          title={t('aiRead.deleteFile')}\n                        />\n                      </div>\n                    </div>\n                  ))}\n                </div>\n              </div>\n            )}\n          </div>\n              \n          <Row gutter={24} className={styles.mainContent}>\n            {/* 左侧：文档上传和内容显示 */}\n            <Col xs={24} lg={12} className={styles.leftPanel}>\n              <Card \n                title={\n                  <Space>\n                    <FileTextOutlined />\n                    {t('aiRead.documentContent')}\n                  </Space>\n                }\n                className={styles.documentCard}\n              >\n                {currentFile ? (\n                  <div>\n                    <div className={styles.fileDetails}>\n                      <Title level={4}>{currentFile.original_filename}</Title>\n                      <Space direction=\"vertical\" size=\"small\" style={{ width: '100%' }}>\n                        <div>\n                          <Row gutter={[16, 16]}>\n                            <Col xs={24} sm={8}>\n                              <Text strong>{t('aiRead.fileSize')} </Text>\n                              <Text>{(currentFile.file_size / 1024).toFixed(2)} KB</Text>\n                            </Col>\n                            <Col xs={24} sm={8}>\n                                <Text strong>{t('aiRead.fileType')} </Text>\n                                <Text>{currentFile.file_type}</Text>\n                            </Col>\n                            <Col xs={24} sm={8}>\n                              <Text strong>{t('aiRead.processingStatus')} </Text>\n                              <Tag color={getStatusColor(currentFile.processing_status)}>\n                                {getStatusText(currentFile.processing_status)}\n                              </Tag>\n                            </Col>\n                          </Row>                        </div>\n                        {['processing', 'converting_to_pdf', 'calling_parser', 'saving_results', 'generating_summary'].includes(currentFile.processing_status) && (\n                          <div>\n                            <Text strong>{t('aiRead.processingProgress')} </Text>\n                            <Progress\n                              percent={currentFile.processing_progress}\n                              status=\"active\"\n                            />\n                            <Text type=\"secondary\">{currentFile.processing_message}</Text>\n                          </div>\n                        )}\n                        {currentFile.content_summary && (\n                          <div>\n                            <div className={styles.summaryHeader}>\n                              <Space>\n                                <RobotOutlined style={{ color: '#1890ff' }} />\n                                <Text strong>{t('aiRead.aiGeneratedSummary')}</Text>\n                              </Space>\n                            </div>\n                            <div className={styles.leftPanelSummary}>\n                              <MarkdownContent content={currentFile.content_summary} />\n                            </div>\n                          </div>\n                        )}\n                      </Space>\n                    </div>\n                  </div>\n                ) : (\n                  <Empty\n                    description={t('aiRead.selectFileToView')}\n                    image={Empty.PRESENTED_IMAGE_SIMPLE}\n                  />\n                )}\n              </Card>\n            </Col>\n\n            {/* 右侧：三张卡片 */}\n            <Col xs={24} lg={12} className={styles.rightPanel}>\n              <Card className={styles.tabsCard}>\n                <Tabs\n                  activeKey={activeTab}\n                  onChange={setActiveTab}\n                  items={tabItems}\n                  size=\"large\"\n                  tabPosition=\"top\"\n                />\n              </Card>\n            </Col>\n          </Row>\n        </div>\n      </Content>\n    </AppLayout>\n  );\n};\n\nexport default AIReadPage;\n", "__isJSFile": true, "__absFile": "/home/<USER>/ragflow/hbweb/src/pages/ai-read/index.tsx"}, "5": {"path": "/text-to-image", "file": "@/pages/text-to-image/index.tsx", "id": "5", "absPath": "/text-to-image", "__content": "import React, { useEffect, useState } from 'react';\nimport { Card, Spin, Alert, Typography } from 'antd';\nimport { PictureOutlined } from '@ant-design/icons';\nimport AppLayout from '@/components/AppLayout';\nimport { useTranslate } from '@/hooks/use-i18n';\nimport styles from './index.less';\n\nconst { Title } = Typography;\n\nconst TextToImage: React.FC = () => {\n  const t = useTranslate();\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState<string | null>(null);\n  \n  // 使用环境变量配置的Gradio URL\n  // 在Docker部署环境中，使用完整的URL确保端口号正确\n  const gradioUrl = process.env.TEXT_TO_IMAGE_URL || '/gradio';\n\n  useEffect(() => {\n    // 检查iframe是否加载成功\n    const timer = setTimeout(() => {\n      setLoading(false);\n    }, 3000); // 3秒后停止loading状态\n\n    return () => clearTimeout(timer);\n  }, []);\n\n  const handleIframeLoad = () => {\n    setLoading(false);\n    setError(null);\n  };\n\n  const handleIframeError = () => {\n    setLoading(false);\n    setError(t('textToImage.loadError'));\n  };\n\n  return (\n    <AppLayout>\n      <div className={styles.container}>\n        {/* 页面标题 */}\n        <div className={styles.pageHeader}>\n          <div className={styles.headerContent}>\n            <div className={styles.headerLeft}>\n              <Title level={2} className={styles.pageTitle}>\n                <PictureOutlined style={{ marginRight: 8 }} />\n                {t('navigation.textToImage')}\n              </Title>\n              <Typography.Text type=\"secondary\">\n                {t('textToImage.description')}\n              </Typography.Text>\n            </div>\n          </div>\n        </div>\n\n        {/* 内容区域 */}\n        <div className={styles.contentContainer}>\n          {loading && (\n            <div className={styles.loadingContainer}>\n              <Spin size=\"large\" />\n              <div className={styles.loadingText}>{t('textToImage.loading')}</div>\n            </div>\n          )}\n\n          {error && (\n            <div className={styles.errorContainer}>\n              <Alert\n                message={t('textToImage.serviceUnavailable')}\n                description={error}\n                type=\"error\"\n                showIcon\n                className={styles.errorAlert}\n              />\n            </div>\n          )}\n\n          <iframe\n            src={gradioUrl}\n            className={styles.gradioFrame}\n            title={t('textToImage.title')}\n            onLoad={handleIframeLoad}\n            onError={handleIframeError}\n            style={{ display: loading ? 'none' : 'block' }}\n          />\n        </div>\n      </div>\n    </AppLayout>\n  );\n};\n\nexport default TextToImage;\n", "__isJSFile": true, "__absFile": "/home/<USER>/ragflow/hbweb/src/pages/text-to-image/index.tsx"}, "6": {"path": "/knowledge", "file": "@/pages/knowledge/index.tsx", "id": "6", "absPath": "/knowledge", "__content": "import {\n  useInfiniteFetchKnowledgeList,\n  useCreateKnowledge,\n  useDeleteKnowledge,\n} from '@/hooks/knowledge-hooks';\nimport { useAuth } from '@/hooks/auth-hooks';\nimport AppLayout from '@/components/AppLayout';\nimport { formatDate } from '@/utils/date';\nimport { useTranslate } from '@/hooks/use-i18n';\nimport {\n  PlusOutlined,\n  SearchOutlined,\n  BookOutlined,\n  DeleteOutlined,\n  EditOutlined,\n  FileTextOutlined,\n  MoreOutlined,\n} from '@ant-design/icons';\nimport {\n  Button,\n  Card,\n  Empty,\n  Input,\n  Space,\n  Typography,\n  Row,\n  Col,\n  Modal,\n  Form,\n  message,\n  Dropdown,\n  Tag,\n  Statistic,\n  Select,\n} from 'antd';\nimport React, { useState, useMemo, useEffect } from 'react';\nimport InfiniteScroll from 'react-infinite-scroll-component';\nimport { useNavigate } from 'umi';\nimport styles from './index.less';\n\nconst { Title, Text } = Typography;\nconst { Search } = Input;\nconst { confirm } = Modal;\n\nconst KnowledgeListContent = () => {\n  const t = useTranslate();\n  const { userInfo } = useAuth();\n  const navigate = useNavigate();\n  const [createModalVisible, setCreateModalVisible] = useState(false);\n  const [createForm] = Form.useForm();\n\n  const {\n    fetchNextPage,\n    data,\n    hasNextPage,\n    searchString,\n    handleInputChange,\n    loading,\n  } = useInfiniteFetchKnowledgeList();\n\n  const { createKnowledge, loading: creatingLoading } = useCreateKnowledge();\n  const { deleteKnowledge, loading: deletingLoading } = useDeleteKnowledge();\n\n  const knowledgeList = useMemo(() => {\n    const list =\n      data?.pages?.flatMap((x) => (Array.isArray(x.kbs) ? x.kbs : [])) ?? [];\n    return list;\n  }, [data?.pages]);\n\n  const total = useMemo(() => {\n    return data?.pages.at(-1)?.total ?? 0;\n  }, [data?.pages]);\n\n  const handleCreateKnowledge = async () => {\n    try {\n      const values = await createForm.validateFields();\n      console.log('Creating knowledge base with values:', values);\n\n      const result = await createKnowledge({\n        name: values.name,\n        description: values.description || '',\n        language: values.language || 'Chinese',\n        permission: 'team',\n      });\n\n      console.log('Create knowledge base result:', result);\n\n      setCreateModalVisible(false);\n      createForm.resetFields();\n\n      // 创建成功后直接跳转到Knowledge Base Settings页面\n      // 检查不同可能的响应结构\n      const kbId = result?.kb_id || result?.data?.kb_id || result?.id;\n      console.log('Full result:', result);\n      console.log('Extracted kb_id:', kbId);\n\n      if (kbId) {\n        message.success(t('knowledge.createSuccess', 'Knowledge base created successfully!'));\n        console.log('Navigating to:', `/knowledge/${kbId}/setting`);\n        // 使用setTimeout确保状态更新完成后再跳转\n        setTimeout(() => {\n          navigate(`/knowledge/${kbId}/setting`);\n        }, 100);\n      } else {\n        console.warn('No kb_id found in response:', result);\n        message.success(t('knowledge.createSuccess', 'Knowledge base created successfully!'));\n        // 如果没有kb_id，刷新页面显示新创建的知识库\n        setTimeout(() => {\n          window.location.reload();\n        }, 1000);\n      }\n    } catch (error) {\n      console.error('Create knowledge base failed:', error);\n      message.error(t('knowledge.createFailed', 'Failed to create knowledge base'));\n    }\n  };\n\n  const handleDeleteKnowledge = (kb: any) => {\n    confirm({\n      title: t('knowledge.deleteTitle', 'Delete Knowledge Base'),\n      content: t('knowledge.deleteConfirm', 'Are you sure you want to delete \"{{name}}\"? This action cannot be undone.', { name: kb.name }),\n      okText: t('common.delete'),\n      okType: 'danger',\n      cancelText: t('common.cancel'),\n      onOk: async () => {\n        await deleteKnowledge(kb.id);\n      },\n    });\n  };\n\n  const getKnowledgeMenuItems = (kb: any) => [\n    {\n      key: 'view',\n      icon: <FileTextOutlined />,\n      label: t('knowledge.viewDocuments', 'View Documents'),\n      onClick: () => {\n        navigate(`/knowledge/${kb.id}/dataset`);\n      },\n    },\n    {\n      key: 'edit',\n      icon: <EditOutlined />,\n      label: t('common.edit'),\n      onClick: () => {\n        navigate(`/knowledge/${kb.id}/setting`);\n      },\n    },\n    {\n      key: 'delete',\n      icon: <DeleteOutlined />,\n      label: t('common.delete'),\n      danger: true,\n      onClick: () => handleDeleteKnowledge(kb),\n    },\n  ];\n\n  return (\n    <div className={styles.knowledgePage}>\n      <div className={styles.pageHeader}>\n        <div className={styles.headerContent}>\n          <div className={styles.headerLeft}>\n            <Title level={2} style={{ margin: 0 }}>\n              {t('knowledge.title')}\n            </Title>\n            <Text type=\"secondary\">\n              {t('knowledge.manageDescription', 'Manage your knowledge repositories and documents')}\n            </Text>\n          </div>\n          <div className={styles.headerRight}>\n            <Space>\n              <Search\n                placeholder={t('knowledge.searchPlaceholder', 'Search knowledge bases...')}\n                allowClear\n                style={{ width: 300 }}\n                value={searchString}\n                onChange={(e) => handleInputChange(e.target.value)}\n                loading={loading}\n              />\n              <Button\n                type=\"primary\"\n                icon={<PlusOutlined />}\n                onClick={() => setCreateModalVisible(true)}\n                size=\"large\"\n              >\n                {t('knowledge.createKnowledge')}\n              </Button>\n            </Space>\n          </div>\n        </div>\n      </div>\n      \n      <div className={styles.pageContent}>\n        {/*}\n        {total > 0 && (\n          <div className={styles.statsSection}>\n            <Row gutter={[16, 16]}>\n              <Col xs={24} sm={8}>\n                <Card>\n                  <Statistic\n                    title={t('knowledge.totalKnowledgeBases', 'Total Knowledge Bases')}\n                    value={total}\n                    prefix={<BookOutlined />}\n                    valueStyle={{ color: '#1890ff' }}\n                  />\n                </Card>\n              </Col>\n              <Col xs={24} sm={8}>\n                <Card>\n                  <Statistic\n                    title={t('knowledge.totalDocuments', 'Total Documents')}\n                    value={knowledgeList.reduce((sum, kb) => sum + (kb.doc_num || 0), 0)}\n                    prefix={<FileTextOutlined />}\n                    valueStyle={{ color: '#52c41a' }}\n                  />\n                </Card>\n              </Col>\n              <Col xs={24} sm={8}>\n                <Card>\n                  <Statistic\n                    title={t('knowledge.totalChunks', 'Total Chunks')}\n                    value={knowledgeList.reduce((sum, kb) => sum + (kb.chunk_num || 0), 0)}\n                    prefix={<SearchOutlined />}\n                    valueStyle={{ color: '#722ed1' }}\n                  />\n                </Card>\n              </Col>\n            </Row>\n          </div>\n        )}\n        */}\n        \n        <div className={styles.knowledgeGrid}>\n          {knowledgeList.length === 0 && !loading ? (\n            <Empty\n              image={Empty.PRESENTED_IMAGE_SIMPLE}\n              description={\n                <div>\n                  <Text type=\"secondary\">{t('knowledge.noKnowledgeBases', 'No knowledge bases found')}</Text>\n                  <br />\n                  <Button\n                    type=\"primary\"\n                    icon={<PlusOutlined />}\n                    onClick={() => setCreateModalVisible(true)}\n                    style={{ marginTop: 16 }}\n                  >\n                    {t('knowledge.createFirstKnowledge', 'Create Your First Knowledge Base')}\n                  </Button>\n                </div>\n              }\n            />\n          ) : (\n            <InfiniteScroll\n              dataLength={knowledgeList.length}\n              next={fetchNextPage}\n              hasMore={!!hasNextPage}\n              loader={<div className={styles.loading}>{t('common.loading')}</div>}\n              endMessage={\n                knowledgeList.length > 0 && (\n                  <div className={styles.endMessage}>\n                    <Text type=\"secondary\">{t('knowledge.noMoreKnowledgeBases', 'No more knowledge bases to load')}</Text>\n                  </div>\n                )\n              }\n            >\n              <Row gutter={[24, 24]}>\n                {knowledgeList.map((kb: any) => (\n                  <Col xs={24} sm={12} lg={8} xl={4} key={kb.id}>\n                    <Card\n                      hoverable\n                      className={styles.knowledgeCard}\n                      onClick={() => navigate(`/knowledge/${kb.id}/dataset`)}\n                      actions={[\n                        <Button\n                          type=\"text\"\n                          icon={<FileTextOutlined />}\n                          onClick={(e) => {\n                            e.stopPropagation();\n                            navigate(`/knowledge/${kb.id}/dataset`);\n                          }}\n                        >\n                          {t('knowledge.documents', 'Documents')}\n                        </Button>,\n                        <Dropdown\n                          menu={{ items: getKnowledgeMenuItems(kb) }}\n                          trigger={['click']}\n                        >\n                          <Button\n                            type=\"text\"\n                            icon={<MoreOutlined />}\n                            onClick={(e) => e.stopPropagation()}\n                          />\n                        </Dropdown>,\n                      ]}\n                    >\n                      <div className={styles.cardContent}>\n                        <div className={styles.cardHeader}>\n                          <div className={styles.cardIcon}>\n                            <BookOutlined />\n                          </div>\n                          <div className={styles.cardTitle}>\n                            <Title level={5} ellipsis={{ tooltip: kb.name }}>\n                              {kb.name}\n                            </Title>\n                            <Text type=\"secondary\" className={styles.cardTime}>\n                              {formatDate(kb.create_time, 'date')}\n                            </Text>\n                          </div>\n                        </div>\n                        \n                        <div className={styles.cardDescription}>\n                          <Text type=\"secondary\" ellipsis={{ rows: 2, tooltip: kb.description }}>\n                            {kb.description || t('knowledge.noDescription', 'No description provided')}\n                          </Text>\n                        </div>\n                        \n                        <div className={styles.cardStats}>\n                          <Space split={<span className={styles.statsDivider}>|</span>}>\n                            <span>\n                              <FileTextOutlined /> {kb.doc_num || 0} {t('knowledge.docsCount', 'docs')}\n                            </span>\n                            <span>\n                              <SearchOutlined /> {kb.chunk_num || 0} {t('knowledge.chunksCount', 'chunks')}\n                            </span>\n                          </Space>\n                        </div>\n                        \n                        <div className={styles.cardTags}>\n                          <Tag color=\"blue\">{kb.language || t('languages.chinese')}</Tag>\n                          {kb.permission === 'me' && <Tag color=\"green\">{t('knowledge.permissionPrivate')}</Tag>}\n                        </div>\n                      </div>\n                    </Card>\n                  </Col>\n                ))}\n              </Row>\n            </InfiniteScroll>\n          )}\n        </div>\n      </div>\n\n      <Modal\n        title={t('knowledge.createKnowledge')}\n        open={createModalVisible}\n        onOk={handleCreateKnowledge}\n        onCancel={() => {\n          setCreateModalVisible(false);\n          createForm.resetFields();\n        }}\n        confirmLoading={creatingLoading}\n        width={600}\n      >\n        <Form\n          form={createForm}\n          layout=\"vertical\"\n          requiredMark={false}\n        >\n          <Form.Item\n            name=\"name\"\n            label={t('knowledge.knowledgeName')}\n            rules={[\n              { required: true, message: t('knowledge.nameRequired') },\n              { max: 100, message: t('messages.validation.maxLength', { max: 100 }) },\n            ]}\n          >\n            <Input placeholder={t('knowledge.enterName')} />\n          </Form.Item>\n          \n          <Form.Item\n            name=\"description\"\n            label={t('knowledge.description')}\n            rules={[\n              { max: 500, message: t('messages.validation.maxLength', { max: 500 }) },\n            ]}\n          >\n            <Input.TextArea\n              rows={4}\n              placeholder={t('knowledge.enterDescription')}\n            />\n          </Form.Item>\n          \n          <Form.Item\n            name=\"language\"\n            label={t('common.language')}\n            initialValue=\"Chinese\"\n          >\n            <Select placeholder={t('knowledge.selectLanguage', 'Select language')}>\n              <Select.Option value=\"English\">{t('languages.english', 'English')}</Select.Option>\n              <Select.Option value=\"Chinese\">{t('languages.chinese', '中文')}</Select.Option>\n            </Select>\n          </Form.Item>\n        </Form>\n      </Modal>\n    </div>\n  );\n};\n\nconst KnowledgeList: React.FC = () => {\n  return (\n    <AppLayout showSearch={false}>\n      <KnowledgeListContent />\n    </AppLayout>\n  );\n};\n\nexport default KnowledgeList;\n", "__isJSFile": true, "__absFile": "/home/<USER>/ragflow/hbweb/src/pages/knowledge/index.tsx"}, "7": {"path": "/documents", "file": "@/pages/documents/index.tsx", "id": "7", "absPath": "/documents", "__content": "import React, { useState, useCallback } from 'react';\nimport {\n  Layout,\n  Table,\n  Button,\n  Space,\n  Input,\n  Upload,\n  Modal,\n  message,\n  Tooltip,\n  Tag,\n  Typography,\n  Card,\n  Statistic,\n  Row,\n  Col,\n  Dropdown,\n  Popconfirm,\n} from 'antd';\nimport {\n  UploadOutlined,\n  SearchOutlined,\n  EyeOutlined,\n  DownloadOutlined,\n  DeleteOutlined,\n  FileTextOutlined,\n  FilePdfOutlined,\n  FileWordOutlined,\n  FileOutlined,\n  ReloadOutlined,\n  MoreOutlined,\n} from '@ant-design/icons';\nimport { useTranslate } from '@/hooks/use-i18n';\nimport AppLayout from '@/components/AppLayout';\nimport DocumentPreview from '@/components/DocumentPreview';\nimport { useDocumentList, useUploadDocument, useDeleteDocument } from './hooks';\nimport documentService from '@/services/document-service';\nimport { RunningStatus, RunningStatusMap } from '@/constants/document';\nimport styles from './index.less';\n\nconst { Content } = Layout;\nconst { Title, Text } = Typography;\nconst { Search } = Input;\nconst { Dragger } = Upload;\n\ninterface DocumentItem {\n  id: string;\n  name: string;\n  type: string;\n  size: number;\n  uploadTime: string;\n  status: 'processing' | 'completed' | 'failed';\n  documentStatus: string; // 原始的document status字段 (1: 启用, 0: 禁用)\n  run: string; // 解析状态字段\n  progress?: number;\n}\n\nconst DocumentsPage: React.FC = () => {\n  const t = useTranslate();\n  const [searchString, setSearchString] = useState('');\n  const [uploadModalVisible, setUploadModalVisible] = useState(false);\n  const [previewModalVisible, setPreviewModalVisible] = useState(false);\n  const [currentDocument, setCurrentDocument] = useState<DocumentItem | null>(null);\n  const [selectedRowKeys, setSelectedRowKeys] = useState<string[]>([]);\n\n  // 使用hooks获取数据\n  const {\n    documents,\n    loading,\n    pagination,\n    setPagination,\n    refetch,\n    kbId,\n  } = useDocumentList(searchString);\n\n  const { uploadDocument, uploading } = useUploadDocument(kbId || undefined);\n  const { deleteDocument, deleting } = useDeleteDocument();\n\n  // 获取文件图标\n  const getFileIcon = (fileName: string, fileType: string) => {\n    const ext = fileName.toLowerCase().split('.').pop();\n    const type = fileType.toLowerCase();\n\n    if (type.includes('pdf') || ext === 'pdf') {\n      return <FilePdfOutlined style={{ color: '#ff4d4f' }} />;\n    }\n    if (type.includes('word') || ['doc', 'docx'].includes(ext || '')) {\n      return <FileWordOutlined style={{ color: '#1890ff' }} />;\n    }\n    if (type.includes('text') || ['txt', 'md'].includes(ext || '')) {\n      return <FileTextOutlined style={{ color: '#52c41a' }} />;\n    }\n    return <FileOutlined style={{ color: '#8c8c8c' }} />;\n  };\n\n  // 格式化文件大小\n  const formatFileSize = (bytes: number) => {\n    if (bytes === 0) return '0 B';\n    const k = 1024;\n    const sizes = ['B', 'KB', 'MB', 'GB'];\n    const i = Math.floor(Math.log(bytes) / Math.log(k));\n    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];\n  };\n\n  // 获取处理状态标签\n  const getStatusTag = (status: string) => {\n    switch (status) {\n      case 'processing':\n        return <Tag color=\"processing\">{t('documents.processing')}</Tag>;\n      case 'completed':\n        return <Tag color=\"success\">{t('documents.completed')}</Tag>;\n      case 'failed':\n        return <Tag color=\"error\">{t('documents.failed')}</Tag>;\n      default:\n        return <Tag color=\"default\">{t('documents.unknown')}</Tag>;\n    }\n  };\n\n  // 获取解析状态标签 (基于run字段)\n  const getParsingStatusTag = (runStatus: string) => {\n    const statusInfo = RunningStatusMap[runStatus as RunningStatus] || {\n      color: 'default',\n      text: runStatus || 'Unknown',\n      label: runStatus || 'Unknown'\n    };\n\n    return (\n      <Tag color={statusInfo.color}>\n        {statusInfo.text}\n      </Tag>\n    );\n  };\n\n  // 获取文档状态标签 (1: 启用, 0: 禁用)\n  const getDocumentStatusTag = (documentStatus: string) => {\n    return (\n      <Tag color={documentStatus === '1' ? 'green' : 'red'}>\n        {documentStatus === '1' ? '启用' : '禁用'}\n      </Tag>\n    );\n  };\n\n  // 处理文件上传\n  const handleUpload = useCallback(async (file: File) => {\n    try {\n      await uploadDocument(file);\n      message.success(t('documents.uploadSuccess'));\n      setUploadModalVisible(false);\n      refetch();\n    } catch (error) {\n      console.error('Upload failed:', error);\n      message.error(t('documents.uploadFailed'));\n    }\n  }, [uploadDocument, refetch]);\n\n  // 处理文件下载\n  const handleDownload = useCallback((record: DocumentItem) => {\n    try {\n      // 使用真实的下载URL\n      const downloadUrl = documentService.downloadDocument(record.id);\n      const link = document.createElement('a');\n      link.href = downloadUrl;\n      link.download = record.name;\n      document.body.appendChild(link);\n      link.click();\n      document.body.removeChild(link);\n      message.success(t('documents.downloadStarted'));\n    } catch (error) {\n      console.error('Download failed:', error);\n      message.error('Download failed');\n    }\n  }, [t]);\n\n  // 处理文件预览\n  const handlePreview = useCallback((record: DocumentItem) => {\n    setCurrentDocument(record);\n    setPreviewModalVisible(true);\n  }, []);\n\n  // 处理文件删除\n  const handleDelete = useCallback(async (record: DocumentItem) => {\n    try {\n      await deleteDocument(record.id);\n      message.success(t('documents.deleteSuccess'));\n      refetch();\n    } catch (error) {\n      console.error('Delete failed:', error);\n      message.error(t('documents.deleteFailed'));\n    }\n  }, [deleteDocument, refetch]);\n\n  // 批量删除\n  const handleBatchDelete = useCallback(async () => {\n    try {\n      await Promise.all(selectedRowKeys.map(id => deleteDocument(id)));\n      message.success(`${selectedRowKeys.length} ${t('documents.batchDeleteSuccess')}`);\n      setSelectedRowKeys([]);\n      refetch();\n    } catch (error) {\n      console.error('Batch delete failed:', error);\n      message.error(t('documents.batchDeleteFailed'));\n    }\n  }, [selectedRowKeys, deleteDocument, refetch]);\n\n  // 表格列定义\n  const columns = [\n    {\n      title: t('documents.documentName'),\n      dataIndex: 'name',\n      key: 'name',\n      fixed: 'left' as const,\n      width: 300,\n      render: (text: string, record: DocumentItem) => (\n        <Space>\n          {getFileIcon(record.name, record.type)}\n          <Tooltip title={text}>\n            <Button\n              type=\"link\"\n              onClick={() => handlePreview(record)}\n              style={{ padding: 0, height: 'auto', fontWeight: 500 }}\n            >\n              {text.length > 40 ? `${text.substring(0, 40)}...` : text}\n            </Button>\n          </Tooltip>\n        </Space>\n      ),\n    },\n    {\n      title: t('documents.type'),\n      dataIndex: 'type',\n      key: 'type',\n      width: 120,\n      render: (type: string) => (\n        <Tag color=\"blue\">{type || t('documents.unknown')}</Tag>\n      ),\n    },\n    {\n      title: t('documents.size'),\n      dataIndex: 'size',\n      key: 'size',\n      width: 100,\n      render: (size: number) => formatFileSize(size),\n    },\n    {\n      title: t('documents.status'),\n      dataIndex: 'status',\n      key: 'status',\n      width: 120,\n      render: (status: string) => getStatusTag(status),\n    },\n    {\n      title: '解析状态',\n      dataIndex: 'run',\n      key: 'parsing_status',\n      width: 120,\n      render: (runStatus: string) => getParsingStatusTag(runStatus),\n    },\n    {\n      title: '文档状态',\n      dataIndex: 'documentStatus',\n      key: 'documentStatus',\n      width: 100,\n      render: (documentStatus: string) => getDocumentStatusTag(documentStatus),\n    },\n    {\n      title: t('documents.uploadTime'),\n      dataIndex: 'uploadTime',\n      key: 'uploadTime',\n      width: 180,\n      render: (time: string) => new Date(time).toLocaleString(),\n      sorter: true,\n      defaultSortOrder: 'descend' as const,\n    },\n    {\n      title: t('documents.actions'),\n      key: 'actions',\n      fixed: 'right' as const,\n      width: 120,\n      render: (_: any, record: DocumentItem) => (\n        <Dropdown\n          menu={{\n            items: [\n              {\n                key: 'preview',\n                icon: <EyeOutlined />,\n                label: t('documents.preview'),\n                onClick: () => handlePreview(record),\n              },\n              {\n                key: 'download',\n                icon: <DownloadOutlined />,\n                label: t('documents.download'),\n                onClick: () => handleDownload(record),\n              },\n              {\n                type: 'divider',\n              },\n              {\n                key: 'delete',\n                icon: <DeleteOutlined />,\n                label: t('documents.delete'),\n                danger: true,\n                onClick: () => handleDelete(record),\n              },\n            ],\n          }}\n          trigger={['click']}\n        >\n          <Button type=\"text\" icon={<MoreOutlined />} />\n        </Dropdown>\n      ),\n    },\n  ];\n\n  // 行选择配置\n  const rowSelection = {\n    selectedRowKeys,\n    onChange: (keys: React.Key[]) => setSelectedRowKeys(keys as string[]),\n  };\n\n  return (\n    <AppLayout>\n      <Content className={styles.documentsContent}>\n        <div className={styles.container}>\n          {/* 页面标题 */}\n          <div className={styles.header}>\n            <Title level={2}>\n              <FileTextOutlined style={{ marginRight: 8 }} />\n              {t('documents.title')}\n            </Title>\n            <Text type=\"secondary\">\n              {t('documents.description')}\n            </Text>\n          </div>\n\n          {/* 统计卡片 */}\n          <Row gutter={16} className={styles.statsRow}>\n            <Col xs={24} sm={8}>\n              <Card>\n                <Statistic\n                  title={t('documents.totalDocuments')}\n                  value={documents?.length || 0}\n                  prefix={<FileTextOutlined />}\n                />\n              </Card>\n            </Col>\n            <Col xs={24} sm={8}>\n              <Card>\n                <Statistic\n                  title={t('documents.completed')}\n                  value={documents?.filter(d => d.status === 'completed').length || 0}\n                  prefix={<FileTextOutlined />}\n                  valueStyle={{ color: '#3f8600' }}\n                />\n              </Card>\n            </Col>\n            <Col xs={24} sm={8}>\n              <Card>\n                <Statistic\n                  title={t('documents.processing')}\n                  value={documents?.filter(d => d.status === 'processing').length || 0}\n                  prefix={<FileTextOutlined />}\n                  valueStyle={{ color: '#1890ff' }}\n                />\n              </Card>\n            </Col>\n          </Row>\n\n          {/* 操作栏 */}\n          <Card className={styles.actionBar}>\n            <Row justify=\"space-between\" align=\"middle\">\n              <Col>\n                <Space>\n                  <Search\n                    placeholder={t('documents.searchPlaceholder')}\n                    allowClear\n                    style={{ width: 300 }}\n                    onSearch={setSearchString}\n                    onChange={(e) => !e.target.value && setSearchString('')}\n                  />\n                  <Button\n                    icon={<ReloadOutlined />}\n                    onClick={() => refetch()}\n                    loading={loading}\n                  >\n                    {t('documents.refresh')}\n                  </Button>\n                </Space>\n              </Col>\n              <Col>\n                <Space>\n                  {selectedRowKeys.length > 0 && (\n                    <Popconfirm\n                      title={`${t('documents.batchDeleteConfirm')} (${selectedRowKeys.length})`}\n                      onConfirm={handleBatchDelete}\n                      okText={t('common.yes')}\n                      cancelText={t('common.no')}\n                    >\n                      <Button danger loading={deleting}>\n                        {t('documents.deleteSelected')} ({selectedRowKeys.length})\n                      </Button>\n                    </Popconfirm>\n                  )}\n                  <Button\n                    type=\"primary\"\n                    icon={<UploadOutlined />}\n                    onClick={() => setUploadModalVisible(true)}\n                  >\n                    {t('documents.uploadDocument')}\n                  </Button>\n                </Space>\n              </Col>\n            </Row>\n          </Card>\n\n          {/* 文档表格 */}\n          <Card className={styles.tableCard}>\n            <Table\n              columns={columns}\n              dataSource={documents}\n              rowKey=\"id\"\n              loading={loading}\n              pagination={{\n                ...pagination,\n                showSizeChanger: true,\n                showQuickJumper: true,\n                showTotal: (total, range) =>\n                  `${range[0]}-${range[1]} ${t('common.of')} ${total} ${t('documents.showTotal')}`,\n                onChange: (page, pageSize) => {\n                  setPagination(prev => ({ ...prev, current: page, pageSize }));\n                },\n              }}\n              rowSelection={rowSelection}\n              scroll={{ x: 1200 }}\n            />\n          </Card>\n        </div>\n\n        {/* 上传模态框 */}\n        <Modal\n          title={t('documents.uploadDocument')}\n          open={uploadModalVisible}\n          onCancel={() => setUploadModalVisible(false)}\n          footer={null}\n          width={600}\n        >\n          <Dragger\n            name=\"file\"\n            multiple={false}\n            beforeUpload={(file) => {\n              handleUpload(file);\n              return false;\n            }}\n            disabled={uploading}\n            accept=\".pdf,.doc,.docx,.txt,.md\"\n          >\n            <p className=\"ant-upload-drag-icon\">\n              <UploadOutlined />\n            </p>\n            <p className=\"ant-upload-text\">\n              {t('aiRead.uploadArea')}\n            </p>\n            <p className=\"ant-upload-hint\">\n              {t('documents.uploadHint')}\n            </p>\n          </Dragger>\n        </Modal>\n\n        {/* 预览模态框 */}\n        <Modal\n          title={`${t('documents.preview')}: ${currentDocument?.name}`}\n          open={previewModalVisible}\n          onCancel={() => {\n            setPreviewModalVisible(false);\n            setCurrentDocument(null);\n          }}\n          footer={null}\n          width={800}\n          style={{ top: 20 }}\n        >\n          {currentDocument && (\n            <DocumentPreview\n              document={{\n                id: currentDocument.id,\n                name: currentDocument.name,\n                content: t('documents.mockContent', `Mock content for ${currentDocument.name}. In a real implementation, this would be the actual document content.`, { name: currentDocument.name }),\n                type: currentDocument.type,\n                size: currentDocument.size,\n                uploadTime: currentDocument.uploadTime,\n                url: documentService.downloadDocument(currentDocument.id), // 添加下载URL用于预览\n              }}\n            />\n          )}\n        </Modal>\n      </Content>\n    </AppLayout>\n  );\n};\n\nexport default DocumentsPage;\n", "__isJSFile": true, "__absFile": "/home/<USER>/ragflow/hbweb/src/pages/documents/index.tsx"}, "8": {"path": "/knowledge/:id", "file": "@/pages/knowledge-detail/index.tsx", "id": "8", "absPath": "/knowledge/:id", "__content": "import AuthGuard from '@/components/AuthGuard';\nimport { Layout } from 'antd';\nimport { Outlet } from 'umi';\nimport KnowledgeSidebar from './components/KnowledgeSidebar';\nimport styles from './index.less';\n\nconst { Content, Sider } = Layout;\n\nconst KnowledgeDetailContent = () => {\n  return (\n    <Layout className={styles.knowledgeDetailLayout}>\n      <Sider width={280} className={styles.sidebar}>\n        <KnowledgeSidebar />\n      </Sider>\n      <Layout>\n        <Content className={styles.content}>\n          <Outlet />\n        </Content>\n      </Layout>\n    </Layout>\n  );\n};\n\nconst KnowledgeDetail = () => {\n  return (\n    <AuthGuard requireAuth={true}>\n      <KnowledgeDetailContent />\n    </AuthGuard>\n  );\n};\n\nexport default KnowledgeDetail;\n", "__isJSFile": true, "__absFile": "/home/<USER>/ragflow/hbweb/src/pages/knowledge-detail/index.tsx"}, "9": {"path": "/knowledge/:id", "redirect": "/knowledge/:id/dataset", "parentId": "8", "id": "9", "absPath": "/knowledge/:id"}, "10": {"path": "/knowledge/:id/dataset", "file": "@/pages/knowledge-detail/dataset/index.tsx", "parentId": "8", "id": "10", "absPath": "/knowledge/:id/dataset", "__content": "import {\n  UploadOutlined,\n  DeleteOutlined,\n  EditOutlined,\n  FileTextOutlined,\n  ReloadOutlined,\n  DownloadOutlined,\n  PlayCircleOutlined,\n  PauseCircleOutlined,\n  RedoOutlined,\n  ToolOutlined,\n  EyeOutlined,\n  GlobalOutlined,\n} from '@ant-design/icons';\nimport {\n  Button,\n  Table,\n  Input,\n  Space,\n  Tag,\n  Dropdown,\n  Modal,\n  Upload,\n  message,\n  Progress,\n  Typography,\n  Card,\n  Row,\n  Col,\n  Statistic,\n  Switch,\n  Popover,\n  Popconfirm,\n  Badge,\n  Tooltip,\n  Form,\n  Select,\n  Checkbox,\n  Alert,\n} from 'antd';\nimport { useState, useMemo } from 'react';\nimport { useParams, useNavigate } from 'umi';\nimport { useDocumentList, useUploadDocument, useDeleteDocument, useKnowledgeStats } from './hooks';\nimport userService from '@/services/user-service';\nimport { RunningStatus, RunningStatusMap } from '@/constants/document';\nimport documentService from '@/services/document-service';\nimport { useMutation, useQueryClient, useQuery } from '@tanstack/react-query';\nimport { formatDate } from '@/utils/date';\nimport { useTranslate } from '@/hooks/use-i18n';\nimport styles from './index.less';\n\nconst { Search, TextArea } = Input;\nconst { Title, Text } = Typography;\nconst { confirm } = Modal;\nconst { Option } = Select;\n\nconst DocumentManagement = () => {\n  const t = useTranslate();\n  const { id: knowledgeId } = useParams<{ id: string }>();\n  const navigate = useNavigate();\n  const queryClient = useQueryClient();\n  const [searchString, setSearchString] = useState('');\n  const [uploadModalVisible, setUploadModalVisible] = useState(false);\n  const [webCrawlModalVisible, setWebCrawlModalVisible] = useState(false);\n  const [renameModalVisible, setRenameModalVisible] = useState(false);\n  const [parserModalVisible, setParserModalVisible] = useState(false);\n  const [previewModalVisible, setPreviewModalVisible] = useState(false);\n  const [currentDocument, setCurrentDocument] = useState<any>(null);\n  const [selectedRowKeys, setSelectedRowKeys] = useState<string[]>([]);\n\n  // 表单\n  const [webCrawlForm] = Form.useForm();\n  const [renameForm] = Form.useForm();\n  const [parserForm] = Form.useForm();\n\n  console.log('DocumentManagement - knowledgeId:', knowledgeId); // Debug log\n  \n  const {\n    documents,\n    loading,\n    pagination,\n    setPagination,\n    refetch,\n    autoRefreshEnabled,\n    setAutoRefreshEnabled,\n  } = useDocumentList(knowledgeId || '', searchString);\n\n  const { uploadDocument, uploading } = useUploadDocument(knowledgeId || '');\n  const { deleteDocument, deleting } = useDeleteDocument();\n\n  // 获取知识库统计信息\n  const { stats, refetch: refetchStats } = useKnowledgeStats(knowledgeId || '');\n\n  // 获取系统状态\n  const { data: systemStatus } = useQuery({\n    queryKey: ['system-status'],\n    queryFn: async () => {\n      try {\n        const { data } = await userService.getSystemStatus();\n        return data;\n      } catch (error) {\n        console.warn('Failed to get system status:', error);\n        return null;\n      }\n    },\n    enabled: autoRefreshEnabled,\n    staleTime: 30000, // 30秒缓存\n    retry: 1,\n  });\n\n  // 网页爬取\n  const { mutateAsync: webCrawl, isPending: webCrawlLoading } = useMutation({\n    mutationFn: async (params: { name: string; url: string }) => {\n      if (!knowledgeId) throw new Error('Knowledge base ID is required');\n      const { data } = await documentService.webCrawl({\n        kb_id: knowledgeId,\n        ...params,\n      });\n      return data;\n    },\n    onSuccess: () => {\n      message.success(t('knowledge.webCrawlStarted'));\n      setWebCrawlModalVisible(false);\n      webCrawlForm.resetFields();\n      refetch();\n    },\n    onError: (error: any) => {\n      message.error(error?.message || t('knowledge.webCrawlFailed'));\n    },\n  });\n\n  // 更改文档状态\n  const { mutateAsync: changeStatus } = useMutation({\n    mutationFn: async (params: { docId: string; status: string }) => {\n      const { data } = await documentService.changeDocumentStatus({\n        doc_id: params.docId,\n        status: params.status,\n      });\n      return data;\n    },\n    onSuccess: () => {\n      message.success(t('knowledge.statusUpdated'));\n      refetch();\n    },\n    onError: (error: any) => {\n      message.error(error?.message || t('knowledge.statusUpdateFailed'));\n    },\n  });\n\n  // 运行/停止解析\n  const { mutateAsync: runDocument } = useMutation({\n    mutationFn: async (params: { docIds: string[]; run: string; delete?: boolean }) => {\n      const { data } = await documentService.runDocument({\n        doc_ids: params.docIds,\n        run: params.run,\n        delete: params.delete,\n      });\n      return data;\n    },\n    onSuccess: () => {\n      message.success(t('knowledge.parsingUpdated'));\n      refetch();\n    },\n    onError: (error: any) => {\n      message.error(error?.message || t('knowledge.parsingUpdateFailed'));\n    },\n  });\n\n  // 重命名文档\n  const { mutateAsync: renameDocument } = useMutation({\n    mutationFn: async (params: { docId: string; name: string }) => {\n      const { data } = await documentService.renameDocument({\n        doc_id: params.docId,\n        name: params.name,\n      });\n      return data;\n    },\n    onSuccess: () => {\n      message.success(t('knowledge.documentRenamed'));\n      setRenameModalVisible(false);\n      renameForm.resetFields();\n      setCurrentDocument(null);\n      refetch();\n    },\n    onError: (error: any) => {\n      message.error(error?.message || t('knowledge.renameFailed'));\n    },\n  });\n\n  // 更改解析器\n  const { mutateAsync: changeParser } = useMutation({\n    mutationFn: async (params: { docId: string; parserId: string; parserConfig?: any }) => {\n      const { data } = await documentService.changeParser({\n        doc_id: params.docId,\n        parser_id: params.parserId,\n        parser_config: params.parserConfig,\n      });\n      return data;\n    },\n    onSuccess: () => {\n      message.success(t('knowledge.parserChanged'));\n      setParserModalVisible(false);\n      parserForm.resetFields();\n      setCurrentDocument(null);\n      refetch();\n    },\n    onError: (error: any) => {\n      message.error(error?.message || t('knowledge.parserChangeFailed'));\n    },\n  });\n\n  const handleSearch = (value: string) => {\n    console.log('Search triggered:', value); // Debug log\n    setSearchString(value);\n    // Reset to first page when searching\n    setPagination(prev => ({ ...prev, current: 1 }));\n  };\n\n  const handleUpload = async (file: File) => {\n    try {\n      await uploadDocument(file);\n      setUploadModalVisible(false);\n      refetch();\n      refetchStats(); // 同时刷新统计数据\n      message.success(t('knowledge.uploadSuccess'));\n    } catch (error) {\n      message.error(t('knowledge.uploadFailed'));\n    }\n  };\n\n  const handleRefresh = () => {\n    refetch();\n    refetchStats(); // 同时刷新统计数据\n  };\n\n  const handleStatusChange = (docId: string, checked: boolean) => {\n    changeStatus({\n      docId,\n      status: checked ? '1' : '0',\n    });\n  };\n\n  const handleRunDocument = (docId: string, isRunning: boolean, shouldDelete: boolean = false) => {\n    const run = isRunning ? RunningStatus.CANCEL : RunningStatus.RUNNING;\n    runDocument({\n      docIds: [docId],\n      run,\n      delete: shouldDelete,\n    });\n  };\n\n  const handleWebCrawlSubmit = async () => {\n    try {\n      const values = await webCrawlForm.validateFields();\n      await webCrawl(values);\n    } catch (error) {\n      console.error('Web crawl validation failed:', error);\n    }\n  };\n\n  const handleBatchParse = () => {\n    if (selectedRowKeys.length === 0) {\n      message.warning(t('knowledge.selectDocumentsToParse'));\n      return;\n    }\n    runDocument({\n      docIds: selectedRowKeys,\n      run: RunningStatus.RUNNING,\n    });\n  };\n\n  const handleBatchDelete = () => {\n    if (selectedRowKeys.length === 0) {\n      message.warning(t('knowledge.selectDocumentsToDelete'));\n      return;\n    }\n    Modal.confirm({\n      title: t('knowledge.deleteDocuments'),\n      content: t('knowledge.deleteDocumentsConfirm', { count: selectedRowKeys.length }),\n      onOk: () => {\n        selectedRowKeys.forEach(docId => {\n          const doc = documents.find(d => d.id === docId);\n          if (doc) {\n            handleDelete(docId, doc.name);\n          }\n        });\n        setSelectedRowKeys([]);\n      },\n    });\n  };\n\n  const handleDownload = (docId: string, filename: string) => {\n    const url = documentService.downloadDocument(docId);\n    const link = document.createElement('a');\n    link.href = url;\n    link.download = filename;\n    document.body.appendChild(link);\n    link.click();\n    document.body.removeChild(link);\n  };\n\n  const showRenameModal = (document: any) => {\n    setCurrentDocument(document);\n    renameForm.setFieldsValue({ name: document.name });\n    setRenameModalVisible(true);\n  };\n\n  const showParserModal = (document: any) => {\n    setCurrentDocument(document);\n    parserForm.setFieldsValue({\n      parser_id: document.parser_id,\n      parser_config: document.parser_config,\n    });\n    setParserModalVisible(true);\n  };\n\n  const handleDelete = (documentId: string, documentName: string) => {\n    confirm({\n      title: t('knowledge.deleteDocuments'),\n      content: t('knowledge.deleteDocumentConfirm', { name: documentName }),\n      okText: t('common.delete'),\n      okType: 'danger',\n      cancelText: t('common.cancel'),\n      onOk: async () => {\n        try {\n          await deleteDocument(documentId);\n          refetch();\n          refetchStats(); // 同时刷新统计数据\n          message.success(t('knowledge.deleteDocumentSuccess'));\n        } catch (error) {\n          message.error(t('knowledge.deleteDocumentFailed'));\n        }\n      },\n    });\n  };\n\n  const getStatusTag = (status: string, record: any) => {\n    const statusInfo = RunningStatusMap[status as RunningStatus] || {\n      color: 'default',\n      text: status,\n      label: status\n    };\n    const isRunning = status === RunningStatus.RUNNING;\n\n    return (\n      <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>\n        <Popover\n          content={\n            <div>\n              <p><strong>{t('knowledge.processBegin')}:</strong> {formatDate(record.process_begin_at, 'datetime')}</p>\n              <p><strong>{t('knowledge.duration')}:</strong> {record.process_duation?.toFixed(2)}s</p>\n              <p><strong>{t('knowledge.progress')}:</strong> {(record.progress * 100).toFixed(2)}%</p>\n              {record.progress_msg && (\n                <p><strong>{t('knowledge.message')}:</strong> <span style={{ whiteSpace: 'pre-wrap', wordBreak: 'break-word' }}>{record.progress_msg}</span></p>\n              )}\n            </div>\n          }\n        >\n          <Tag color={statusInfo.color}>\n            {isRunning ? (\n              <Space>\n                <Badge color={statusInfo.color} />\n                {statusInfo.text}\n                <span>{(record.progress * 100).toFixed(2)}%</span>\n              </Space>\n            ) : (\n              statusInfo.text\n            )}\n          </Tag>\n        </Popover>\n\n        <Popconfirm\n          title={record.chunk_num > 0 ? t('knowledge.reparseWarning') : t('knowledge.startParsing')}\n          onConfirm={() => handleRunDocument(record.id, isRunning, record.chunk_num > 0)}\n          onCancel={() => handleRunDocument(record.id, isRunning, false)}\n          disabled={record.chunk_num === 0}\n          okText={t('common.yes')}\n          cancelText={t('common.no')}\n        >\n          <Button\n            type=\"text\"\n            size=\"small\"\n            icon={\n              isRunning ? <PauseCircleOutlined /> :\n              status === RunningStatus.DONE ? <RedoOutlined /> :\n              <PlayCircleOutlined />\n            }\n            onClick={record.chunk_num === 0 ? () => handleRunDocument(record.id, isRunning, false) : undefined}\n          />\n        </Popconfirm>\n      </div>\n    );\n  };\n\n  const getDocumentActions = (record: any) => {\n    const isRunning = record.run === RunningStatus.RUNNING;\n\n    return [\n      {\n        key: 'rename',\n        icon: <EditOutlined />,\n        label: t('knowledge.rename'),\n        disabled: isRunning,\n        onClick: () => showRenameModal(record),\n      },\n      {\n        key: 'parser',\n        icon: <ToolOutlined />,\n        label: t('knowledge.changeParser'),\n        disabled: isRunning,\n        onClick: () => showParserModal(record),\n      },\n      {\n        key: 'preview',\n        icon: <EyeOutlined />,\n        label: t('knowledge.preview'),\n        disabled: isRunning,\n        onClick: () => {\n          setCurrentDocument(record);\n          setPreviewModalVisible(true);\n        },\n      },\n      {\n        key: 'download',\n        icon: <DownloadOutlined />,\n        label: t('knowledge.download'),\n        disabled: isRunning,\n        onClick: () => handleDownload(record.id, record.name),\n      },\n      {\n        key: 'delete',\n        icon: <DeleteOutlined />,\n        label: t('common.delete'),\n        danger: true,\n        disabled: isRunning,\n        onClick: () => handleDelete(record.id, record.name),\n      },\n    ];\n  };\n\n  const columns = [\n    {\n      title: t('knowledge.documentName'),\n      dataIndex: 'name',\n      key: 'name',\n      fixed: 'left',\n      width: 300,\n      ellipsis: true,\n      render: (text: string, record: any) => (\n        <div style={{ display: 'flex', alignItems: 'center', gap: 8 }}>\n          <FileTextOutlined style={{ color: '#1890ff' }} />\n          <Tooltip title={text}>\n            <span\n              style={{\n                cursor: 'pointer',\n                color: '#1890ff',\n                textDecoration: 'underline',\n                fontWeight: 500,\n              }}\n              onClick={() => navigate(`/knowledge/${knowledgeId}/dataset/${record.id}/chunks`)}\n            >\n              {text.length > 40 ? `${text.substring(0, 40)}...` : text}\n            </span>\n          </Tooltip>\n        </div>\n      ),\n    },\n    {\n      title: t('knowledge.type'),\n      dataIndex: 'type',\n      key: 'type',\n      width: 100,\n      render: (type: string) => <Tag>{type.toUpperCase()}</Tag>,\n    },\n    {\n      title: t('knowledge.size'),\n      dataIndex: 'size',\n      key: 'size',\n      width: 100,\n      render: (size: number) => {\n        if (!size) return '-';\n        const units = ['B', 'KB', 'MB', 'GB'];\n        let unitIndex = 0;\n        let fileSize = size;\n        while (fileSize >= 1024 && unitIndex < units.length - 1) {\n          fileSize /= 1024;\n          unitIndex++;\n        }\n        return `${fileSize.toFixed(1)} ${units[unitIndex]}`;\n      },\n    },\n    {\n      title: t('knowledge.chunks'),\n      dataIndex: 'chunk_num',\n      key: 'chunk_num',\n      width: 100,\n      render: (value: number) => value || 0,\n    },\n    {\n      title: t('knowledge.tokens'),\n      dataIndex: 'token_num',\n      key: 'token_num',\n      width: 100,\n      render: (value: number) => value || 0,\n    },\n    {\n      title: t('knowledge.status'),\n      dataIndex: 'status',\n      key: 'status',\n      width: 100,\n      render: (status: string, record: any) => (\n        <Switch\n          checked={status === '1'}\n          onChange={(checked) => handleStatusChange(record.id, checked)}\n          disabled={record.run === RunningStatus.RUNNING}\n        />\n      ),\n    },\n    {\n      title: t('knowledge.parsingStatus'),\n      dataIndex: 'run',\n      key: 'parsing_status',\n      width: 200,\n      render: (status: string, record: any) => getStatusTag(status, record),\n    },\n    {\n      title: t('knowledge.progress'),\n      dataIndex: 'progress',\n      key: 'progress',\n      width: 120,\n      render: (progress: number, record: any) => {\n        if (record.run === 'processing') {\n          return <Progress percent={Math.round(progress * 100)} size=\"small\" />;\n        }\n        return '-';\n      },\n    },\n    {\n      title: t('knowledge.chunks'),\n      dataIndex: 'chunk_num',\n      key: 'chunk_num',\n      width: 80,\n      render: (chunks: number) => chunks || 0,\n    },\n    {\n      title: t('knowledge.created'),\n      dataIndex: 'create_time',\n      key: 'create_time',\n      width: 120,\n      render: (time: string) => formatDate(time, 'date'),\n    },\n    {\n      title: t('knowledge.actions'),\n      key: 'actions',\n      fixed: 'right',\n      width: 120,\n      render: (_: any, record: any) => {\n        const isRunning = record.run === RunningStatus.RUNNING;\n        return (\n          <Dropdown\n            menu={{ items: getDocumentActions(record) }}\n            trigger={['click']}\n            disabled={isRunning}\n          >\n            <Button\n              type=\"text\"\n              size=\"small\"\n              disabled={isRunning}\n              style={{\n                color: isRunning ? '#ccc' : '#1890ff',\n                cursor: isRunning ? 'not-allowed' : 'pointer'\n              }}\n            >\n              {t('knowledge.actions')}\n            </Button>\n          </Dropdown>\n        );\n      },\n    },\n  ];\n\n  // 使用全局统计数据，而不是当前页面的文档数据\n  const totalDocuments = stats.totalDocuments;\n  const completedDocuments = stats.completedDocuments;\n  const processingDocuments = stats.processingDocuments;\n  const totalChunks = stats.totalChunks;\n\n  return (\n    <div className={styles.documentManagement}>\n      {/* Header */}\n      <div className={styles.header}>\n        <div className={styles.headerLeft}>\n          <Title level={3} style={{ margin: 0 }}>\n            {t('knowledge.documents')}\n          </Title>\n          <Text type=\"secondary\">\n            {t('knowledge.manageDocuments')}\n          </Text>\n        </div>\n        <div className={styles.headerRight}>\n          <Space>\n            <Search\n              placeholder={t('knowledge.searchDocuments')}\n              value={searchString}\n              onChange={(e) => setSearchString(e.target.value)}\n              onSearch={handleSearch}\n              style={{ width: 200 }}\n            />\n\n            {selectedRowKeys.length > 0 && (\n              <>\n                <Button\n                  icon={<PlayCircleOutlined />}\n                  onClick={handleBatchParse}\n                >\n                  {t('knowledge.batchParse')} ({selectedRowKeys.length})\n                </Button>\n                <Button\n                  danger\n                  icon={<DeleteOutlined />}\n                  onClick={handleBatchDelete}\n                >\n                  {t('knowledge.batchDelete')} ({selectedRowKeys.length})\n                </Button>\n              </>\n            )}\n\n            <Button\n              icon={<ReloadOutlined />}\n              onClick={handleRefresh}\n              loading={loading}\n            >\n              {t('knowledge.refresh')}\n            </Button>\n            <Tooltip title={t('knowledge.autoRefreshTooltip', {\n              status: autoRefreshEnabled ? t('knowledge.disableAutoRefresh') : t('knowledge.enableAutoRefresh')\n            })}>\n              <Switch\n                checked={autoRefreshEnabled}\n                onChange={setAutoRefreshEnabled}\n                checkedChildren={t('common.auto')}\n                unCheckedChildren={t('common.manual')}\n                size=\"small\"\n              />\n            </Tooltip>\n            <Button\n              icon={<GlobalOutlined />}\n              onClick={() => setWebCrawlModalVisible(true)}\n            >\n              {t('knowledge.webCrawl')}\n            </Button>\n            <Button\n              type=\"primary\"\n              icon={<UploadOutlined />}\n              onClick={() => setUploadModalVisible(true)}\n            >\n              {t('knowledge.uploadDocument')}\n            </Button>\n          </Space>\n        </div>\n      </div>\n\n      {/* Statistics */}\n      <Row gutter={[16, 16]} className={styles.statistics}>\n        <Col xs={24} sm={6}>\n          <Card>\n            <Statistic\n              title={t('knowledge.totalDocuments')}\n              value={totalDocuments}\n              prefix={<FileTextOutlined />}\n              valueStyle={{ color: '#1890ff' }}\n            />\n          </Card>\n        </Col>\n        <Col xs={24} sm={6}>\n          <Card>\n            <Statistic\n              title={t('knowledge.completed')}\n              value={completedDocuments}\n              valueStyle={{ color: '#52c41a' }}\n            />\n          </Card>\n        </Col>\n        <Col xs={24} sm={6}>\n          <Card>\n            <Statistic\n              title={t('knowledge.processing')}\n              value={processingDocuments}\n              valueStyle={{ color: '#1890ff' }}\n            />\n          </Card>\n        </Col>\n        <Col xs={24} sm={6}>\n          <Card>\n            <Statistic\n              title={t('knowledge.totalChunks')}\n              value={totalChunks}\n              valueStyle={{ color: '#722ed1' }}\n            />\n          </Card>\n        </Col>\n      </Row>\n\n      {/* System Status Warning */}\n      {systemStatus && (\n        systemStatus.doc_engine?.status === 'red' ||\n        systemStatus.database?.status === 'red'\n      ) && (\n        <Alert\n          message={t('knowledge.systemHealthWarning')}\n          description={t('knowledge.systemHealthDescription')}\n          type=\"warning\"\n          showIcon\n          closable\n          style={{ marginBottom: 16 }}\n        />\n      )}\n\n\n\n      {/* Search and Filters */}\n      <div className={styles.toolbar}>\n        <Search\n          placeholder={t('knowledge.searchDocuments')}\n          allowClear\n          style={{ width: 300 }}\n          onSearch={handleSearch}\n          onChange={(e) => !e.target.value && handleSearch('')}\n        />\n      </div>\n\n      {/* Documents Table */}\n      <Card className={styles.tableCard}>\n        <Table\n          columns={columns}\n          dataSource={documents}\n          rowKey=\"id\"\n          loading={loading}\n          rowSelection={{\n            selectedRowKeys,\n            onChange: (newSelectedRowKeys: React.Key[]) => {\n              setSelectedRowKeys(newSelectedRowKeys as string[]);\n            },\n            getCheckboxProps: (record: any) => ({\n              disabled: record.run === RunningStatus.RUNNING,\n            }),\n          }}\n          pagination={{\n            ...pagination,\n            showSizeChanger: true,\n            showQuickJumper: true,\n            showTotal: (total, range) =>\n              t('knowledge.documentsRange', { start: range[0], end: range[1], total }),\n            onChange: (page, pageSize) => {\n              console.log('Pagination changed - User clicked page:', page, 'pageSize:', pageSize, 'current state:', pagination); // Debug log\n              setPagination(prev => {\n                const newPagination = {\n                  ...prev,\n                  current: page, // 保持1-based页码\n                  pageSize: pageSize || prev.pageSize\n                };\n                console.log('Setting new pagination (1-based):', newPagination, '→ Will call API with page:', page - 1); // Debug log\n                return newPagination;\n              });\n            },\n            onShowSizeChange: (current, size) => {\n              console.log('Page size changed:', { current, size }); // Debug log\n              setPagination(prev => {\n                const newPagination = {\n                  ...prev,\n                  current: 1, // Reset to first page when page size changes\n                  pageSize: size\n                };\n                console.log('Setting new pagination (size change):', newPagination); // Debug log\n                return newPagination;\n              });\n            },\n          }}\n          scroll={{ x: 1400 }}\n        />\n      </Card>\n\n      {/* Upload Modal */}\n      <Modal\n        title={t('knowledge.uploadDocument')}\n        open={uploadModalVisible}\n        onCancel={() => setUploadModalVisible(false)}\n        footer={null}\n        width={600}\n      >\n        <Upload.Dragger\n          name=\"file\"\n          multiple={false}\n          beforeUpload={(file) => {\n            handleUpload(file);\n            return false; // Prevent default upload\n          }}\n          disabled={uploading}\n        >\n          <p className=\"ant-upload-drag-icon\">\n            <UploadOutlined />\n          </p>\n          <p className=\"ant-upload-text\">\n            {t('knowledge.clickOrDragToUpload')}\n          </p>\n          <p className=\"ant-upload-hint\">\n            {t('knowledge.supportedFormats')}\n          </p>\n        </Upload.Dragger>\n      </Modal>\n\n      {/* Web Crawl Modal */}\n      <Modal\n        title={t('knowledge.webCrawl')}\n        open={webCrawlModalVisible}\n        onOk={handleWebCrawlSubmit}\n        onCancel={() => {\n          setWebCrawlModalVisible(false);\n          webCrawlForm.resetFields();\n        }}\n        confirmLoading={webCrawlLoading}\n      >\n        <Form form={webCrawlForm} layout=\"vertical\">\n          <Form.Item\n            name=\"name\"\n            label={t('knowledge.documentName')}\n            rules={[{ required: true, message: t('knowledge.pleaseInputDocumentName') }]}\n          >\n            <Input placeholder={t('knowledge.enterDocumentName')} />\n          </Form.Item>\n          <Form.Item\n            name=\"url\"\n            label={t('knowledge.url')}\n            rules={[\n              { required: true, message: t('knowledge.pleaseInputURL') },\n              { type: 'url', message: t('knowledge.pleaseInputValidURL') },\n            ]}\n          >\n            <Input placeholder=\"https://example.com\" />\n          </Form.Item>\n        </Form>\n      </Modal>\n\n      {/* Rename Modal */}\n      <Modal\n        title={t('knowledge.rename')}\n        open={renameModalVisible}\n        onOk={async () => {\n          try {\n            const values = await renameForm.validateFields();\n            if (currentDocument) {\n              await renameDocument({\n                docId: currentDocument.id,\n                name: values.name,\n              });\n            }\n          } catch (error) {\n            console.error('Rename validation failed:', error);\n          }\n        }}\n        onCancel={() => {\n          setRenameModalVisible(false);\n          renameForm.resetFields();\n          setCurrentDocument(null);\n        }}\n      >\n        <Form form={renameForm} layout=\"vertical\">\n          <Form.Item\n            name=\"name\"\n            label={t('knowledge.documentName')}\n            rules={[{ required: true, message: t('knowledge.pleaseInputDocumentName') }]}\n          >\n            <Input placeholder={t('knowledge.enterNewDocumentName')} />\n          </Form.Item>\n        </Form>\n      </Modal>\n\n      {/* Parser Modal */}\n      <Modal\n        title={t('knowledge.changeParser')}\n        open={parserModalVisible}\n        onOk={async () => {\n          try {\n            const values = await parserForm.validateFields();\n            if (currentDocument) {\n              await changeParser({\n                docId: currentDocument.id,\n                parserId: values.parser_id,\n                parserConfig: values.parser_config,\n              });\n            }\n          } catch (error) {\n            console.error('Parser validation failed:', error);\n          }\n        }}\n        onCancel={() => {\n          setParserModalVisible(false);\n          parserForm.resetFields();\n          setCurrentDocument(null);\n        }}\n        width={600}\n      >\n        <Form form={parserForm} layout=\"vertical\">\n          <Form.Item\n            name=\"parser_id\"\n            label={t('knowledge.parserType')}\n            rules={[{ required: true, message: t('knowledge.pleaseSelectParserType') }]}\n          >\n            <Select placeholder={t('knowledge.parserType')}>\n              <Option value=\"naive\">{t('knowledge.general')}</Option>\n              <Option value=\"qa\">{t('knowledge.qa')}</Option>\n              <Option value=\"resume\">{t('knowledge.resume')}</Option>\n              <Option value=\"manual\">{t('knowledge.manual')}</Option>\n              <Option value=\"table\">{t('knowledge.table')}</Option>\n              <Option value=\"paper\">{t('knowledge.paper')}</Option>\n              <Option value=\"book\">{t('knowledge.book')}</Option>\n              <Option value=\"laws\">{t('knowledge.laws')}</Option>\n              <Option value=\"presentation\">{t('knowledge.presentation')}</Option>\n              <Option value=\"picture\">{t('knowledge.picture')}</Option>\n              <Option value=\"one\">{t('knowledge.one')}</Option>\n              <Option value=\"audio\">{t('knowledge.audio')}</Option>\n              <Option value=\"email\">{t('knowledge.email')}</Option>\n              <Option value=\"tag\">{t('knowledge.tag')}</Option>\n              <Option value=\"knowledge_graph\">{t('knowledge.knowledgeGraph')}</Option>\n            </Select>\n          </Form.Item>\n          <Form.Item\n            name=\"parser_config\"\n            label={t('knowledge.parserConfiguration')}\n          >\n            <TextArea\n              rows={6}\n              placeholder={t('knowledge.enterParserConfig')}\n            />\n          </Form.Item>\n        </Form>\n      </Modal>\n\n      {/* Preview Modal */}\n      <Modal\n        title={t('knowledge.previewDocument', { name: currentDocument?.name })}\n        open={previewModalVisible}\n        onCancel={() => {\n          setPreviewModalVisible(false);\n          setCurrentDocument(null);\n        }}\n        footer={null}\n        width={800}\n      >\n        <div>\n          <p><strong>{t('knowledge.type')}:</strong> {currentDocument?.type}</p>\n          <p><strong>{t('knowledge.size')}:</strong> {currentDocument?.size} bytes</p>\n          <p><strong>{t('knowledge.parser')}:</strong> {currentDocument?.parser_id}</p>\n          <p><strong>{t('knowledge.chunks')}:</strong> {currentDocument?.chunk_num}</p>\n          <p><strong>{t('knowledge.tokens')}:</strong> {currentDocument?.token_num}</p>\n          <p><strong>{t('knowledge.created')}:</strong> {formatDate(currentDocument?.create_time, 'datetime')}</p>\n          {currentDocument?.thumbnail && (\n            <div>\n              <p><strong>{t('knowledge.thumbnail')}:</strong></p>\n              <img src={currentDocument.thumbnail} alt={t('knowledge.thumbnail')} style={{ maxWidth: '100%', maxHeight: 200 }} />\n            </div>\n          )}\n        </div>\n      </Modal>\n    </div>\n  );\n};\n\nexport default DocumentManagement;\n", "__isJSFile": true, "__absFile": "/home/<USER>/ragflow/hbweb/src/pages/knowledge-detail/dataset/index.tsx"}, "11": {"path": "/knowledge/:id/setting", "file": "@/pages/knowledge-detail/setting/index.tsx", "parentId": "8", "id": "11", "absPath": "/knowledge/:id/setting", "__content": "import { useFetchKnowledgeDetail, useUpdateKnowledge } from '@/hooks/knowledge-hooks';\nimport {\n  SaveOutlined,\n  DeleteOutlined,\n  ExclamationCircleOutlined,\n} from '@ant-design/icons';\nimport {\n  Form,\n  Input,\n  Select,\n  Button,\n  Card,\n  Typography,\n  Space,\n  Modal,\n  message,\n  Divider,\n  Row,\n  Col,\n  Switch,\n  InputNumber,\n  Tooltip,\n  Alert,\n} from 'antd';\nimport React, { useEffect } from 'react';\nimport { useParams, useNavigate } from 'umi';\nimport { useTranslate } from '@/hooks/use-i18n';\nimport { useDeleteKnowledge } from '@/hooks/knowledge-hooks';\nimport {\n  useEmbeddingModelOptions,\n  useRerankModelOptions,\n  useParserOptions,\n  useLanguageOptions\n} from '@/hooks/llm-hooks';\nimport dialogService from '@/services/dialog-service';\nimport chatService from '@/services/chat-service';\nimport styles from './index.less';\n\nconst { Title, Text } = Typography;\nconst { TextArea } = Input;\nconst { Option } = Select;\nconst { confirm } = Modal;\n\n// Parser类型描述 - 基于RAGFlow原版web目录的国际化文件\nconst getParserDescriptions = (t: any) => ({\n  naive: {\n    title: t('knowledge.parserDescriptions.naive.title', 'General'),\n    description: t('knowledge.parserDescriptions.naive.description', `Supported file formats are MD, MDX, DOCX, XLSX, XLS (Excel 97-2003), PPT, PDF, TXT, JPEG, JPG, PNG, TIF, GIF, CSV, JSON, EML, HTML.\n\nThis method chunks files using a 'naive' method:\n• Use vision detection model to split the texts into smaller segments.\n• Then, combine adjacent segments until the token count exceeds the threshold specified by 'Chunk token number for text', at which point a chunk is created.`),\n    type: 'info'\n  },\n  qa: {\n    title: t('knowledge.parserDescriptions.qa.title', 'Q&A'),\n    description: t('knowledge.parserDescriptions.qa.description', `This chunking method supports XLSX and CSV/TXT file formats.\n\n• If a file is in XLSX or XLS (Excel 97-2003) format, it should contain two columns without headers: one for questions and the other for answers, with the question column preceding the answer column. Multiple sheets are acceptable, provided the columns are properly structured.\n• If a file is in CSV/TXT format, it must be UTF-8 encoded with TAB as the delimiter to separate questions and answers.`),\n    type: 'info'\n  },\n  resume: {\n    title: t('knowledge.parserDescriptions.resume.title', 'Resume'),\n    description: t('knowledge.parserDescriptions.resume.description', `Only PDF is supported.\n\nWe assume that the resume has a hierarchical section structure, using the lowest section titles as basic unit for chunking documents. Therefore, figures and tables in the same section will not be separated, which may result in larger chunk sizes.`),\n    type: 'info'\n  },\n  manual: {\n    title: t('knowledge.parserDescriptions.manual.title', 'Manual'),\n    description: t('knowledge.parserDescriptions.manual.description', `Only PDF is supported.\n\nWe assume that the manual has a hierarchical section structure, using the lowest section titles as basic unit for chunking documents. Therefore, figures and tables in the same section will not be separated, which may result in larger chunk sizes.`),\n    type: 'info'\n  },\n  table: {\n    title: t('knowledge.parserDescriptions.table.title', 'Table'),\n    description: t('knowledge.parserDescriptions.table.description', `Supported file formats are XLSX, XLS (Excel 97-2003), CSV.\n\nThis method is specifically designed for tabular data. Each row in the table is treated as a separate chunk, preserving the structure and relationships within the data.`),\n    type: 'info'\n  },\n  paper: {\n    title: t('knowledge.paper'),\n    description: t('knowledge.parserDescriptions.paper.description'),\n    type: 'warning'\n  },\n  book: {\n    title: t('knowledge.book'),\n    description: t('knowledge.parserDescriptions.book.description'),\n    type: 'info'\n  },\n  laws: {\n    title: t('knowledge.laws'),\n    description: t('knowledge.parserDescriptions.laws.description'),\n    type: 'info'\n  },\n  presentation: {\n    title: t('knowledge.presentation'),\n    description: t('knowledge.parserDescriptions.presentation.description'),\n    type: 'info'\n  },\n  picture: {\n    title: t('knowledge.picture'),\n    description: t('knowledge.parserDescriptions.picture.description'),\n    type: 'info'\n  },\n  one: {\n    title: t('knowledge.one'),\n    description: t('knowledge.parserDescriptions.one.description'),\n    type: 'warning'\n  },\n  audio: {\n    title: t('knowledge.audio'),\n    description: t('knowledge.parserDescriptions.audio.description'),\n    type: 'info'\n  },\n  email: {\n    title: t('knowledge.email'),\n    description: t('knowledge.parserDescriptions.email.description'),\n    type: 'info'\n  },\n  tag: {\n    title: t('knowledge.tag'),\n    description: t('knowledge.parserDescriptions.tag.description'),\n    type: 'warning'\n  },\n  knowledge_graph: {\n    title: t('knowledge.parserDescriptions.knowledge_graph.title'),\n    description: t('knowledge.parserDescriptions.knowledge_graph.description'),\n    type: 'info'\n  }\n});\n\nconst KnowledgeSettings = () => {\n  const { id: knowledgeId } = useParams<{ id: string }>();\n  const navigate = useNavigate();\n  const [form] = Form.useForm();\n  const [selectedParser, setSelectedParser] = React.useState<string>('naive');\n  const t = useTranslate(); // Add the translation function\n\n  const { data: knowledgeDetail, loading } = useFetchKnowledgeDetail(knowledgeId || '');\n  const { updateKnowledge, loading: updating } = useUpdateKnowledge();\n  const { deleteKnowledge, loading: deleting } = useDeleteKnowledge();\n\n  // 获取模型选项\n  const { models: embeddingModels, isLoading: embeddingLoading } = useEmbeddingModelOptions();\n  const { models: rerankModels, isLoading: rerankLoading } = useRerankModelOptions();\n  const parserOptions = useParserOptions();\n  const languageOptions = useLanguageOptions();\n\n  // 获取解析器描述\n  const PARSER_DESCRIPTIONS = getParserDescriptions(t);\n\n  // 监听RAPTOR开关状态\n  const raptorEnabled = Form.useWatch('raptor', form);\n\n  useEffect(() => {\n    if (knowledgeDetail) {\n      const parserId = knowledgeDetail.parser_id || 'naive';\n      setSelectedParser(parserId);\n\n      // 正确解析RAPTOR配置 - 处理可能的嵌套结构\n      let raptorConfig = knowledgeDetail.parser_config?.raptor;\n      let useRaptor = false;\n      let raptorMaxCluster = 64;\n      let raptorThreshold = 0.1;\n      let raptorMaxToken = 256;\n      let raptorRandomSeed = 0;\n      let raptorPrompt = '';\n\n      if (raptorConfig) {\n        if (typeof raptorConfig === 'boolean') {\n          // 简单的布尔值\n          useRaptor = raptorConfig;\n        } else if (typeof raptorConfig === 'object') {\n          // 对象形式，可能有嵌套\n          if (raptorConfig.use_raptor !== undefined) {\n            useRaptor = raptorConfig.use_raptor;\n            raptorMaxCluster = raptorConfig.raptor_max_cluster || raptorConfig.max_cluster || 64;\n            raptorThreshold = raptorConfig.raptor_threshold || raptorConfig.threshold || 0.1;\n            raptorMaxToken = raptorConfig.raptor_max_token || raptorConfig.max_token || 256;\n            raptorRandomSeed = raptorConfig.raptor_random_seed || raptorConfig.random_seed || 0;\n            raptorPrompt = raptorConfig.raptor_prompt || raptorConfig.prompt || '';\n          } else {\n            // 可能是旧格式或者嵌套格式，尝试提取最内层的值\n            const extractValue = (obj: any, key: string, defaultValue: any) => {\n              if (obj && typeof obj === 'object') {\n                if (obj[key] !== undefined) {\n                  return obj[key];\n                }\n                // 递归查找嵌套值\n                for (const k in obj) {\n                  if (typeof obj[k] === 'object') {\n                    const result = extractValue(obj[k], key, null);\n                    if (result !== null) return result;\n                  }\n                }\n              }\n              return defaultValue;\n            };\n\n            useRaptor = extractValue(raptorConfig, 'use_raptor', false);\n            raptorMaxCluster = extractValue(raptorConfig, 'raptor_max_cluster', 64);\n            raptorThreshold = extractValue(raptorConfig, 'raptor_threshold', 0.1);\n            raptorMaxToken = extractValue(raptorConfig, 'raptor_max_token', 256);\n            raptorRandomSeed = extractValue(raptorConfig, 'raptor_random_seed', 0);\n            raptorPrompt = extractValue(raptorConfig, 'raptor_prompt', '') || extractValue(raptorConfig, 'prompt', '');\n          }\n        }\n      }\n\n      form.setFieldsValue({\n        name: knowledgeDetail.name || '',\n        description: knowledgeDetail.description || '',\n        language: knowledgeDetail.language || 'English',\n        permission: knowledgeDetail.permission || 'me',\n        parser_id: parserId,\n        embd_id: knowledgeDetail.embd_id || '',\n        // Parser config fields\n        chunk_token_num: knowledgeDetail.parser_config?.chunk_token_num || 512,\n        delimiter: knowledgeDetail.parser_config?.delimiter || '\\\\n!?;。？！',\n        auto_keywords: knowledgeDetail.parser_config?.auto_keywords || 0,\n        auto_questions: knowledgeDetail.parser_config?.auto_questions || 0,\n        html4excel: knowledgeDetail.parser_config?.html4excel || false,\n        layout_recognize: knowledgeDetail.parser_config?.layout_recognize || 'DeepDOC',\n        task_page_size: knowledgeDetail.parser_config?.task_page_size || 0,\n        // 正确设置RAPTOR参数\n        raptor: useRaptor,\n        raptor_max_cluster: raptorMaxCluster,\n        raptor_threshold: raptorThreshold,\n        raptor_max_token: raptorMaxToken,\n        raptor_random_seed: raptorRandomSeed,\n        raptor_prompt: raptorPrompt,\n        // Advanced settings (只包含后端支持的字段)\n        pagerank: knowledgeDetail.pagerank || 0,\n        // 注意：以下字段在RAGFlow知识库API中不被支持，但保留在表单中用于显示\n        similarity_threshold: 0.1,\n        vector_similarity_weight: 0.3,\n        top_n: 6,\n        rerank_id: '',\n      });\n    }\n  }, [knowledgeDetail, form]);\n\n  // Handle RAPTOR switch change\n  const handleRaptorChange = (checked: boolean) => {\n    if (!checked) {\n      // Reset RAPTOR related fields when disabled\n      form.setFieldsValue({\n        raptor_max_cluster: 64,\n        raptor_threshold: 0.1,\n      });\n    }\n  };\n\n  const handleSave = async () => {\n    try {\n      const values = await form.validateFields();\n\n      // 根据RAGFlow后端API要求构建更新参数\n      // 注意：kb_id作为路径参数传递，不包含在请求体中\n\n      // 正确构建RAPTOR配置，避免嵌套问题\n      const raptorConfig = values.raptor ? {\n        use_raptor: true,\n        max_cluster: values.raptor_max_cluster || 64,\n        threshold: values.raptor_threshold || 0.1,\n        max_token: values.raptor_max_token || 256,\n        random_seed: values.raptor_random_seed || 0,\n        prompt: values.raptor_prompt || '',\n      } : {\n        use_raptor: false\n      };\n\n      const updateParams = {\n        kb_id: knowledgeId!, // 用于API调用，但会在service中处理\n        name: values.name,\n        description: values.description || '', // 确保description不为undefined\n        language: values.language,\n        permission: values.permission,\n        parser_id: values.parser_id,\n        embd_id: values.embd_id,\n        parser_config: {\n          chunk_token_num: values.chunk_token_num,\n          delimiter: values.delimiter,\n          auto_keywords: values.auto_keywords,\n          auto_questions: values.auto_questions,\n          html4excel: values.html4excel,\n          layout_recognize: values.layout_recognize,\n          task_page_size: values.task_page_size || 0,\n          // 使用正确的RAPTOR配置格式，参考RAGFlow原版\n          raptor: raptorConfig,\n          graphrag: {\n            use_graphrag: values.graphrag || false,\n            ...(values.graphrag ? {\n              // 添加graphrag相关配置\n            } : {})\n          },\n        },\n        // pagerank作为顶级参数传递，参考RAGFlow原版\n        pagerank: values.pagerank || 0,\n        // 注意：以下字段在RAGFlow知识库更新API中不被支持，暂时注释\n        // similarity_threshold: values.similarity_threshold,\n        // vector_similarity_weight: values.vector_similarity_weight,\n        // top_n: values.top_n,\n        // rerank_id: values.rerank_id,\n      };\n\n      console.log('Updating knowledge base with params:', updateParams);\n      console.log('RAPTOR config:', updateParams.parser_config.raptor);\n      console.log('PageRank:', updateParams.pagerank);\n      console.log('Form values:', values);\n\n      const result = await updateKnowledge(updateParams);\n      console.log('Update result:', result);\n      message.success(t('knowledge.updateSuccess', 'Knowledge base updated successfully!'));\n    } catch (error: any) {\n      console.error('Update failed:', error);\n      message.error(error?.message || t('knowledge.updateFailed', 'Failed to update knowledge base'));\n    }\n  };\n\n  const handleDelete = () => {\n    confirm({\n      title: t('knowledge.deleteKnowledgeBase', 'Delete Knowledge Base'),\n      icon: <ExclamationCircleOutlined />,\n      content: (\n        <div>\n          <p>{t('knowledge.deleteConfirmMessage', 'Are you sure you want to delete this knowledge base?')}</p>\n          <p><strong>{t('knowledge.deleteCannotUndo', 'This action cannot be undone.')}</strong></p>\n          <p>{t('knowledge.deleteAllData', 'All documents and data will be permanently removed.')}</p>\n        </div>\n      ),\n      okText: t('common.delete', 'Delete'),\n      okType: 'danger',\n      cancelText: t('common.cancel', 'Cancel'),\n      onOk: async () => {\n        try {\n          // 首先获取与该知识库关联的所有dialog\n          const dialogsResponse = await dialogService.listDialog();\n\n          // 确保 dialogsResponse.data 是数组\n          const dialogsData = Array.isArray(dialogsResponse.data)\n            ? dialogsResponse.data\n            : (dialogsResponse.data && typeof dialogsResponse.data === 'object' && 'data' in dialogsResponse.data\n               ? (dialogsResponse.data as any).data || []\n               : []);\n\n          const relatedDialogs = dialogsData.filter((dialog: any) =>\n            dialog.kb_ids && dialog.kb_ids.includes(knowledgeId)\n          );\n\n          // 删除关联的conversations\n          for (const dialog of relatedDialogs) {\n            try {\n              const conversationsResponse = await chatService.listConversations({ dialog_id: dialog.id });\n              if (conversationsResponse.data && conversationsResponse.data.length > 0) {\n                // 逐个删除conversations，因为API只支持单个删除\n                for (const conv of conversationsResponse.data) {\n                  await chatService.deleteConversation({ conversation_id: conv.id });\n                }\n              }\n            } catch (error) {\n              console.warn('Failed to delete conversations for dialog:', dialog.id, error);\n            }\n          }\n\n          // 删除关联的dialogs\n          if (relatedDialogs.length > 0) {\n            const dialogIds = relatedDialogs.map((dialog: any) => dialog.id);\n            await dialogService.removeDialog({ dialogIds });\n          }\n\n          // 最后删除知识库\n          await deleteKnowledge(knowledgeId!);\n          message.success(t('knowledge.deleteSuccess', 'Knowledge base and related dialogs deleted successfully!'));\n          console.log('Knowledge base deleted, navigating to knowledge list');\n          // 使用setTimeout确保消息显示后再跳转\n          setTimeout(() => {\n            navigate('/knowledge');\n          }, 500);\n        } catch (error) {\n          console.error('Delete failed:', error);\n          message.error(t('knowledge.deleteFailed', 'Failed to delete knowledge base'));\n        }\n      },\n    });\n  };\n\n  if (loading) {\n    return (\n      <div className={styles.loading}>\n        <div>{t('knowledge.loadingSettings', 'Loading knowledge base settings...')}</div>\n      </div>\n    );\n  }\n\n  return (\n    <div className={styles.knowledgeSettings}>\n      {/* Header */}\n      <div className={styles.header}>\n        <div className={styles.headerLeft}>\n          <Title level={3} style={{ margin: 0 }}>\n            {t('knowledge.settingsTitle', 'Knowledge Base Settings')}\n          </Title>\n          <Text type=\"secondary\">\n            {t('knowledge.settingsDescription', 'Configure your knowledge base properties and processing settings')}\n          </Text>\n        </div>\n        <div className={styles.headerRight}>\n          <Space>\n            <Button\n              type=\"primary\"\n              icon={<SaveOutlined />}\n              onClick={handleSave}\n              loading={updating}\n            >\n              {t('common.saveChanges', 'Save Changes')}\n            </Button>\n          </Space>\n        </div>\n      </div>\n\n      <Form\n        form={form}\n        layout=\"vertical\"\n        className={styles.settingsForm}\n      >\n        {/* General Settings */}\n        <Card title={t('knowledge.generalSettings', 'General Settings')} className={styles.settingsCard}>\n          {/* 第一行：4个基本配置项 */}\n          <Row gutter={[24, 16]}>\n            <Col xs={24} sm={12} lg={6}>\n              <Form.Item\n                name=\"name\"\n                label={t('knowledge.knowledgeBaseName', 'Knowledge Base Name')}\n                rules={[\n                  { required: true, message: t('knowledge.nameRequired', 'Please enter knowledge base name') },\n                  { max: 100, message: t('knowledge.nameMaxLength', 'Name cannot exceed 100 characters') },\n                ]}\n              >\n                <Input placeholder={t('knowledge.namePlaceholder', 'Enter knowledge base name')} />\n              </Form.Item>\n            </Col>\n            <Col xs={24} sm={12} lg={6}>\n              <Form.Item\n                name=\"language\"\n                label={t('knowledge.language', 'Language')}\n                rules={[{ required: true, message: t('knowledge.languageRequired', 'Please select language') }]}\n              >\n                <Select placeholder={t('knowledge.languagePlaceholder', 'Select language')}>\n                  {languageOptions.map(option => (\n                    <Option key={option.value} value={option.value}>\n                      {option.label}\n                    </Option>\n                  ))}\n                </Select>\n              </Form.Item>\n            </Col>\n            <Col xs={24} sm={12} lg={6}>\n              <Form.Item\n                name=\"permission\"\n                label={t('knowledge.permission', 'Permission')}\n                rules={[{ required: true, message: t('knowledge.permissionRequired', 'Please select permission') }]}\n              >\n                <Select placeholder={t('knowledge.permissionPlaceholder', 'Select permission')}>\n                  <Option value=\"me\">{t('knowledge.permissionPrivate', 'Private (Only me)')}</Option>\n                  <Option value=\"team\">{t('knowledge.permissionTeam', 'Team')}</Option>\n                  <Option value=\"public\">{t('knowledge.permissionPublic', 'Public')}</Option>\n                </Select>\n              </Form.Item>\n            </Col>\n          </Row>\n\n          {/* 第二行：PDF解析器配置 */}\n          <Row gutter={[24, 16]}>\n            <Col xs={24} sm={12} lg={6}>\n              <Form.Item\n                name=\"layout_recognize\"\n                label={t('knowledge.pdfParser', 'PDF Parser')}\n                rules={[{ required: true, message: t('knowledge.pdfParserRequired', 'Please select PDF parser') }]}\n              >\n                <Select placeholder={t('knowledge.pdfParserPlaceholder', 'Select PDF parser')}>\n                  <Option value=\"DeepDOC\">DeepDOC</Option>\n                  <Option value=\"OCR\">OCR</Option>\n                  <Option value=\"Manual\">Manual</Option>\n                </Select>\n              </Form.Item>\n            </Col>\n            <Col xs={24} sm={12} lg={6}>\n              <Form.Item\n                name=\"parser_id\"\n                label={t('knowledge.parserType', 'Parser Type')}\n                rules={[{ required: true, message: t('knowledge.parserTypeRequired', 'Please select parser type') }]}\n              >\n                <Select\n                  placeholder={t('knowledge.parserTypePlaceholder', 'Select parser type')}\n                  onChange={(value) => setSelectedParser(value)}\n                >\n                  {parserOptions.map(option => (\n                    <Option key={option.value} value={option.value}>\n                      {option.label}\n                    </Option>\n                  ))}\n                </Select>\n              </Form.Item>\n            </Col>\n          </Row>\n\n          {/* 第二行：Description单独一行 */}\n          <Row gutter={[24, 16]}>\n            <Col span={24}>\n              <Form.Item\n                name=\"description\"\n                label={t('knowledge.description', 'Description')}\n                rules={[\n                  { max: 500, message: t('knowledge.descriptionMaxLength', 'Description cannot exceed 500 characters') },\n                ]}\n              >\n                <TextArea\n                  rows={4}\n                  placeholder={t('knowledge.descriptionPlaceholder', 'Enter description (optional)')}\n                  showCount\n                  maxLength={500}\n                />\n              </Form.Item>\n            </Col>\n          </Row>\n\n          {/* Parser Type Description */}\n          {selectedParser && PARSER_DESCRIPTIONS[selectedParser as keyof typeof PARSER_DESCRIPTIONS] && (\n            <Row gutter={[24, 0]} style={{ marginTop: 16 }} className={styles.parserDescription}>\n              <Col span={24}>\n                <Alert\n                  message={t('knowledge.parserMethodTitle', '{{title}} Parser Method', {\n                    title: PARSER_DESCRIPTIONS[selectedParser as keyof typeof PARSER_DESCRIPTIONS].title\n                  })}\n                  description={\n                    <div style={{ whiteSpace: 'pre-line' }}>\n                      {PARSER_DESCRIPTIONS[selectedParser as keyof typeof PARSER_DESCRIPTIONS].description}\n                    </div>\n                  }\n                  type={PARSER_DESCRIPTIONS[selectedParser as keyof typeof PARSER_DESCRIPTIONS].type as any}\n                  showIcon\n                  style={{ marginBottom: 0 }}\n                />\n              </Col>\n            </Row>\n          )}\n        </Card>\n\n        {/* Processing Settings */}\n        <Card title={t('knowledge.processingSettings', 'Processing Settings')} className={`${styles.settingsCard} ${styles.processingSettings}`}>\n          {/* 第一行：8个配置项 */}\n          <Row gutter={[12, 16]}>\n            <Col xs={24} sm={12} md={8} lg={3}>\n              <Form.Item\n                name=\"chunk_token_num\"\n                label={t('knowledge.chunkTokens')}\n                rules={[\n                  { required: true, message: t('knowledge.chunkTokensRequired') },\n                  { type: 'number', min: 1, max: 2048, message: t('knowledge.chunkTokensRange') },\n                ]}\n                tooltip={\n                  <div style={{ whiteSpace: 'pre-line', maxWidth: 300 }}>\n                    {t('knowledge.chunkTokensTooltip')}\n                  </div>\n                }\n              >\n                <InputNumber\n                  min={1}\n                  max={2048}\n                  placeholder=\"512\"\n                />\n              </Form.Item>\n            </Col>\n            <Col xs={24} sm={12} md={8} lg={3}>\n              <Form.Item\n                name=\"delimiter\"\n                label={t('knowledge.delimiter')}\n                rules={[{ required: true, message: t('knowledge.delimiterRequired') }]}\n                tooltip={\n                  <div style={{ whiteSpace: 'pre-line', maxWidth: 300 }}>\n                    {t('knowledge.delimiterTooltip')}\n                  </div>\n                }\n              >\n                <Input placeholder=\"\\\\n!?;。？！\" />\n              </Form.Item>\n            </Col>\n            <Col xs={24} sm={12} md={8} lg={3}>\n              <Form.Item\n                name=\"auto_keywords\"\n                label={t('knowledge.autoKeywords')}\n                rules={[\n                  { type: 'number', min: 0, max: 30, message: t('knowledge.autoKeywordsRange') },\n                ]}\n                tooltip={t('knowledge.autoKeywordsTooltip')}\n              >\n                <InputNumber\n                  min={0}\n                  max={10}\n                  placeholder=\"0\"\n                />\n              </Form.Item>\n            </Col>\n            <Col xs={24} sm={12} md={8} lg={3}>\n              <Form.Item\n                name=\"auto_questions\"\n                label={t('knowledge.autoQuestions')}\n                rules={[\n                  { type: 'number', min: 0, max: 10, message: t('knowledge.autoQuestionsRange') },\n                ]}\n                tooltip={t('knowledge.autoQuestionsTooltip')}\n              >\n                <InputNumber\n                  min={0}\n                  max={10}\n                  placeholder=\"0\"\n                />\n              </Form.Item>\n            </Col>\n            <Col xs={24} sm={12} md={8} lg={3}>\n              <Form.Item\n                name=\"html4excel\"\n                label={t('knowledge.html4excel')}\n                valuePropName=\"checked\"\n                tooltip={t('knowledge.html4excelTooltip')}\n              >\n                <Switch />\n              </Form.Item>\n            </Col>\n\n            <Col xs={24} sm={12} md={8} lg={3}>\n              <Form.Item\n                name=\"pagerank\"\n                label={t('knowledge.pageRank')}\n                rules={[\n                  { type: 'number', min: 0, max: 100, message: t('knowledge.pageRankRange') },\n                ]}\n                tooltip={t('knowledge.pageRankTooltip')}\n              >\n                <InputNumber\n                  min={0}\n                  max={100}\n                  placeholder=\"0\"\n                />\n              </Form.Item>\n            </Col>\n            <Col xs={24} sm={12} md={8} lg={3}>\n              <Form.Item\n                name=\"embd_id\"\n                label={t('knowledge.embedModel', 'Embed Model')}\n                tooltip={\n                  <div style={{ whiteSpace: 'pre-line', maxWidth: 300 }}>\n                    {t('knowledge.embedModelTooltip')}\n                  </div>\n                }\n              >\n                <Select\n                  placeholder={t('knowledge.selectModel', 'Select model')}\n                  loading={embeddingLoading}\n                  disabled={true}\n                  notFoundContent={embeddingLoading ? t('common.loading', 'Loading...') : t('knowledge.noModelsAvailable', 'No models available')}\n                >\n                  {embeddingModels.map(model => (\n                    <Option key={model.value} value={model.value}>\n                      {model.label}\n                    </Option>\n                  ))}\n                </Select>\n              </Form.Item>\n            </Col>\n          </Row>\n\n          {/* 第二行：RAPTOR配置（6个配置项） */}\n          <Row gutter={[12, 16]}>\n            <Col xs={24} sm={12} md={8} lg={3}>\n              <Form.Item\n                name=\"raptor\"\n                label={t('knowledge.raptor')}\n                valuePropName=\"checked\"\n                tooltip={t('knowledge.raptorTooltip')}\n              >\n                <Switch />\n              </Form.Item>\n            </Col>\n            <Col xs={24} sm={12} md={8} lg={3}>\n              <Form.Item\n                name=\"raptor_max_cluster\"\n                label={t('knowledge.maxClusters')}\n                rules={[\n                  { type: 'number', min: 1, max: 256, message: t('knowledge.maxClustersRange') },\n                ]}\n                tooltip={t('knowledge.maxClustersTooltip')}\n              >\n                <InputNumber\n                  min={1}\n                  max={256}\n                  placeholder=\"64\"\n                  disabled={!raptorEnabled}\n                />\n              </Form.Item>\n            </Col>\n            <Col xs={24} sm={12} md={8} lg={3}>\n              <Form.Item\n                name=\"raptor_threshold\"\n                label={t('knowledge.threshold')}\n                rules={[\n                  { type: 'number', min: 0, max: 1, message: t('knowledge.thresholdRange') },\n                ]}\n                tooltip={t('knowledge.thresholdTooltip')}\n              >\n                <InputNumber\n                  min={0}\n                  max={1}\n                  step={0.1}\n                  placeholder=\"0.1\"\n                  disabled={!raptorEnabled}\n                />\n              </Form.Item>\n            </Col>\n            <Col xs={24} sm={12} md={8} lg={3}>\n              <Form.Item\n                name=\"raptor_max_token\"\n                label={t('knowledge.maxTokens')}\n                rules={[\n                  { type: 'number', min: 0, max: 2048, message: t('knowledge.maxTokensRange') },\n                ]}\n                tooltip={t('knowledge.maxTokensTooltip')}\n              >\n                <InputNumber\n                  min={0}\n                  max={2048}\n                  placeholder=\"256\"\n                  disabled={!raptorEnabled}\n                />\n              </Form.Item>\n            </Col>\n            <Col xs={24} sm={12} md={8} lg={3}>\n              <Form.Item\n                name=\"raptor_random_seed\"\n                label={t('knowledge.randomSeed')}\n                rules={[\n                  { type: 'number', min: 0, max: 999999, message: t('knowledge.randomSeedRange') },\n                ]}\n                tooltip={t('knowledge.randomSeedTooltip')}\n              >\n                <InputNumber\n                  min={0}\n                  max={999999}\n                  placeholder=\"0\"\n                  disabled={!raptorEnabled}\n                />\n              </Form.Item>\n            </Col>\n          </Row>\n\n          {/* RAPTOR提示词配置 - 当RAPTOR启用时显示 */}\n          {raptorEnabled && (\n            <Row gutter={[12, 16]} style={{ marginTop: 16 }}>\n              <Col span={24}>\n                <Form.Item\n                  name=\"raptor_prompt\"\n                  label={t('knowledge.raptorPrompt')}\n                  tooltip={t('knowledge.raptorPromptTooltip')}\n                >\n                  <Input.TextArea\n                    rows={4}\n                    placeholder={t('knowledge.raptorPromptPlaceholder')}\n                    disabled={!raptorEnabled}\n                  />\n                </Form.Item>\n              </Col>\n            </Row>\n          )}\n        </Card>\n\n\n\n        {/* Danger Zone */}\n        <Card title={t('knowledge.dangerZone', 'Danger Zone')} className={styles.dangerCard}>\n          <div className={styles.dangerContent}>\n            <div className={styles.dangerText}>\n              <Title level={5} style={{ color: '#ff4d4f', margin: 0 }}>\n                {t('knowledge.deleteKnowledgeBase', 'Delete Knowledge Base')}\n              </Title>\n              <Text type=\"secondary\">\n                {t('knowledge.deleteWarning', 'Once you delete a knowledge base, there is no going back. Please be certain.')}\n              </Text>\n            </div>\n            <Button\n              danger\n              icon={<DeleteOutlined />}\n              onClick={handleDelete}\n              loading={deleting}\n            >\n              {t('knowledge.deleteKnowledgeBase', 'Delete Knowledge Base')}\n            </Button>\n          </div>\n        </Card>\n      </Form>\n    </div>\n  );\n};\n\nexport default KnowledgeSettings;\n", "__isJSFile": true, "__absFile": "/home/<USER>/ragflow/hbweb/src/pages/knowledge-detail/setting/index.tsx"}, "12": {"path": "/knowledge/:id/retrieval-testing", "file": "@/pages/knowledge-detail/retrieval-testing/index.tsx", "parentId": "8", "id": "12", "absPath": "/knowledge/:id/retrieval-testing", "__content": "import React, { useState } from 'react';\nimport { useParams } from 'umi';\nimport {\n  Card,\n  Input,\n  Button,\n  List,\n  Typography,\n  Space,\n  Tag,\n  Divider,\n  Row,\n  Col,\n  InputNumber,\n  Switch,\n  Form,\n  Spin,\n  Empty,\n  message,\n  Select,\n} from 'antd';\nimport { SearchOutlined, SettingOutlined } from '@ant-design/icons';\nimport { useRetrievalTest } from '@/hooks/retrieval-hooks';\nimport { useRerankModelOptions } from '@/hooks/llm-hooks';\nimport { useTranslate } from '@/hooks/use-i18n';\nimport styles from './index.less';\n\nconst { TextArea } = Input;\nconst { Title, Text, Paragraph } = Typography;\nconst { Option } = Select;\n\ninterface RetrievalResult {\n  chunk_id: string;\n  content_with_weight: string;\n  doc_id: string;\n  docnm_kwd: string;\n  similarity: number;\n  important_kwd: string[];\n  question_kwd: string[];\n  image_id?: string;\n  available_int?: number;\n}\n\nconst RetrievalTesting: React.FC = () => {\n  const t = useTranslate();\n  const { id: knowledgeId } = useParams<{ id: string }>();\n  const [form] = Form.useForm();\n  const [question, setQuestion] = useState('');\n  const [showSettings, setShowSettings] = useState(true); // 默认显示设置\n  const [results, setResults] = useState<RetrievalResult[]>([]);\n  \n  const { testRetrieval, loading } = useRetrievalTest();\n  const { models: rerankModels = [], isLoading: rerankLoading } = useRerankModelOptions();\n\n  // 处理高亮内容的函数\n  const processHighlightContent = (content: string, question: string) => {\n    if (!content) return content;\n\n    // 如果内容已经包含HTML标签，直接返回\n    if (content.includes('<mark>') || content.includes('<em>') || content.includes('<strong>')) {\n      return content;\n    }\n\n    // 如果没有高亮标签，尝试手动高亮关键词\n    if (question && question.trim()) {\n      const keywords = question.trim().split(/\\s+/).filter(word => word.length > 2);\n      let highlightedContent = content;\n\n      keywords.forEach(keyword => {\n        const regex = new RegExp(`(${keyword})`, 'gi');\n        highlightedContent = highlightedContent.replace(regex, '<mark>$1</mark>');\n      });\n\n      return highlightedContent;\n    }\n\n    return content;\n  };\n\n  const handleTest = async () => {\n    if (!question.trim()) {\n      message.warning(t('retrieval.enterQuestion'));\n      return;\n    }\n\n    try {\n      const settings = await form.validateFields();\n      const response = await testRetrieval({\n        kb_id: knowledgeId!,\n        question: question.trim(),\n        similarity_threshold: settings.similarity_threshold || 0.0,\n        vector_similarity_weight: settings.vector_similarity_weight || 0.3,\n        top_k: settings.top_k || 10,\n        use_kg: settings.use_kg || false,\n        highlight: settings.highlight !== false,\n        rerank_id: settings.rerank_id || '',\n        page: 1,\n        size: settings.top_k || 10,\n      });\n      \n      console.log('Retrieval response:', response);\n      console.log('First chunk content:', response.chunks?.[0]?.content_with_weight);\n      setResults(response.chunks || []);\n      message.success(t('retrieval.foundChunks', {\n        count: response.chunks?.length || 0,\n        total: response.total || 0\n      }));\n    } catch (error) {\n      console.error('Retrieval test failed:', error);\n      message.error(t('retrieval.testFailed'));\n    }\n  };\n\n  const renderResult = (item: RetrievalResult, index: number) => (\n    <List.Item key={item.chunk_id}>\n      <Card \n        size=\"small\" \n        className={styles.resultCard}\n        title={\n          <Space>\n            <Text strong>#{index + 1}</Text>\n            <Text type=\"secondary\">{item.docnm_kwd}</Text>\n            <Tag color=\"blue\">{t('retrieval.score')}: {((item.similarity || 0) * 100).toFixed(2)}%</Tag>\n          </Space>\n        }\n      >\n        <Paragraph\n          ellipsis={{ rows: 3, expandable: true, symbol: t('common.more') }}\n          className={styles.content}\n        >\n          <div dangerouslySetInnerHTML={{\n            __html: processHighlightContent(item.content_with_weight, question)\n          }} />\n        </Paragraph>\n        \n        {item.important_kwd && item.important_kwd.length > 0 && (\n          <div className={styles.keywords}>\n            <Text type=\"secondary\" style={{ fontSize: '12px' }}>{t('retrieval.keywords')}: </Text>\n            {item.important_kwd.map((keyword, idx) => (\n              <Tag key={idx} size=\"small\" color=\"orange\">\n                {keyword}\n              </Tag>\n            ))}\n          </div>\n        )}\n\n        {item.question_kwd && item.question_kwd.length > 0 && (\n          <div className={styles.questions}>\n            <Text type=\"secondary\" style={{ fontSize: '12px' }}>{t('retrieval.questions')}: </Text>\n            {item.question_kwd.map((q, idx) => (\n              <Tag key={idx} size=\"small\" color=\"green\">\n                {q}\n              </Tag>\n            ))}\n          </div>\n        )}\n      </Card>\n    </List.Item>\n  );\n\n  return (\n    <div className={styles.container}>\n      <Card title={t('knowledge.retrievalTesting')} className={styles.mainCard}>\n        <Row gutter={[24, 24]}>\n          <Col xs={24} lg={showSettings ? 16 : 24}>\n            <Space direction=\"vertical\" style={{ width: '100%' }} size=\"large\">\n              <div>\n                <Space style={{ marginBottom: 16 }}>\n                  <Title level={5} style={{ margin: 0 }}>{t('retrieval.testQuestion')}</Title>\n                  <Button\n                    type=\"text\"\n                    icon={<SettingOutlined />}\n                    onClick={() => setShowSettings(!showSettings)}\n                  >\n                    {showSettings ? t('common.hide') : t('common.show')} {t('retrieval.retrievalSettings')}\n                  </Button>\n                </Space>\n                <TextArea\n                  value={question}\n                  onChange={(e) => setQuestion(e.target.value)}\n                  placeholder={t('retrieval.questionPlaceholder')}\n                  rows={3}\n                  style={{ marginBottom: 16 }}\n                />\n                <Button\n                  type=\"primary\"\n                  icon={<SearchOutlined />}\n                  onClick={handleTest}\n                  loading={loading}\n                  size=\"large\"\n                >\n                  {t('retrieval.testRetrieval')}\n                </Button>\n              </div>\n\n              <Divider />\n\n              <div>\n                <Space>\n                  <Title level={5} style={{ margin: 0 }}>{t('retrieval.results')} ({results.length})</Title>\n                  {form.getFieldValue('highlight') !== false && (\n                    <Tag color=\"green\" size=\"small\">{t('retrieval.highlightOn')}</Tag>\n                  )}\n                </Space>\n                {loading ? (\n                  <div style={{ textAlign: 'center', padding: '40px' }}>\n                    <Spin size=\"large\" />\n                  </div>\n                ) : results.length > 0 ? (\n                  <List\n                    dataSource={results}\n                    renderItem={renderResult}\n                    pagination={{\n                      pageSize: 5,\n                      showSizeChanger: true,\n                      showQuickJumper: true,\n                      showTotal: (total) => t('retrieval.totalResults', { total }),\n                    }}\n                  />\n                ) : (\n                  <Empty description={t('retrieval.noResults')} />\n                )}\n              </div>\n            </Space>\n          </Col>\n\n          {showSettings && (\n            <Col xs={24} lg={8}>\n              <Card\n                title={t('retrieval.retrievalSettings')}\n                size=\"small\"\n                extra={\n                  <Text type=\"secondary\" style={{ fontSize: '12px' }}>\n                    {t('retrieval.configureParameters')}\n                  </Text>\n                }\n              >\n                <div style={{ marginBottom: 16 }}>\n                  <Text type=\"secondary\" style={{ fontSize: '13px' }}>\n                    {t('retrieval.adjustParameters')}\n                  </Text>\n                </div>\n                <Form\n                  form={form}\n                  layout=\"vertical\"\n                  initialValues={{\n                    similarity_threshold: 0.0,\n                    vector_similarity_weight: 0.3,\n                    top_k: 10,\n                    use_kg: false,\n                    highlight: true,\n                    rerank_id: '',\n                  }}\n                >\n                  <Form.Item\n                    name=\"similarity_threshold\"\n                    label={t('retrieval.similarityThreshold')}\n                    tooltip={t('retrieval.similarityThresholdTooltip')}\n                  >\n                    <InputNumber\n                      min={0}\n                      max={1}\n                      step={0.1}\n                      style={{ width: '100%' }}\n                      placeholder=\"0.0\"\n                    />\n                  </Form.Item>\n\n                  <Form.Item\n                    name=\"vector_similarity_weight\"\n                    label={t('retrieval.vectorSimilarityWeight')}\n                    tooltip={t('retrieval.vectorSimilarityWeightTooltip')}\n                  >\n                    <InputNumber\n                      min={0}\n                      max={1}\n                      step={0.1}\n                      style={{ width: '100%' }}\n                      placeholder=\"0.3\"\n                    />\n                  </Form.Item>\n\n                  <Form.Item\n                    name=\"top_k\"\n                    label={t('retrieval.topKResults')}\n                    tooltip={t('retrieval.topKResultsTooltip')}\n                  >\n                    <InputNumber\n                      min={1}\n                      max={100}\n                      style={{ width: '100%' }}\n                      placeholder=\"10\"\n                    />\n                  </Form.Item>\n\n                  <Form.Item\n                    name=\"rerank_id\"\n                    label={t('retrieval.rerankModel')}\n                    tooltip={t('retrieval.rerankModelTooltip')}\n                  >\n                    <Select\n                      placeholder={t('retrieval.selectRerankModel')}\n                      allowClear\n                      loading={rerankLoading}\n                      notFoundContent={rerankLoading ? t('common.loading') : t('retrieval.noModelsAvailable')}\n                    >\n                      {rerankModels.map(model => (\n                        <Option key={model.value} value={model.value}>\n                          {model.label}\n                        </Option>\n                      ))}\n                    </Select>\n                  </Form.Item>\n\n                  <Form.Item\n                    name=\"use_kg\"\n                    label={t('retrieval.useKnowledgeGraph')}\n                    valuePropName=\"checked\"\n                    tooltip={t('retrieval.useKnowledgeGraphTooltip')}\n                  >\n                    <Switch />\n                  </Form.Item>\n\n                  <Form.Item\n                    name=\"highlight\"\n                    label={t('retrieval.highlightKeywords')}\n                    valuePropName=\"checked\"\n                    tooltip={t('retrieval.highlightKeywordsTooltip')}\n                  >\n                    <Switch\n                      checkedChildren={t('common.on')}\n                      unCheckedChildren={t('common.off')}\n                    />\n                  </Form.Item>\n                </Form>\n              </Card>\n            </Col>\n          )}\n        </Row>\n      </Card>\n    </div>\n  );\n};\n\nexport default RetrievalTesting;\n", "__isJSFile": true, "__absFile": "/home/<USER>/ragflow/hbweb/src/pages/knowledge-detail/retrieval-testing/index.tsx"}, "13": {"path": "/knowledge/:id/dataset/:docId/chunks", "file": "@/pages/knowledge-detail/chunks/index.tsx", "parentId": "8", "id": "13", "absPath": "/knowledge/:id/dataset/:docId/chunks", "__content": "import React, { useState } from 'react';\nimport {\n  Table,\n  Button,\n  Space,\n  Input,\n  Card,\n  Typography,\n  Breadcrumb,\n  Tag,\n  Tooltip,\n  Switch,\n  message,\n} from 'antd';\nimport {\n  ArrowLeftOutlined,\n  SearchOutlined,\n  FileTextOutlined,\n  HomeOutlined,\n  BookOutlined,\n} from '@ant-design/icons';\nimport { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';\nimport { useParams, useNavigate } from 'umi';\nimport { useTranslate } from '@/hooks/use-i18n';\nimport documentService from '@/services/document-service';\nimport { formatDate } from '@/utils/date';\nimport type { IDocumentChunk } from '@/interfaces/document';\nimport type { ColumnsType } from 'antd/es/table';\nimport request from '@/utils/request';\nimport api from '@/utils/api';\n\nconst { Text, Paragraph } = Typography;\nconst { Search } = Input;\n\ninterface ChunksPageProps {}\n\nconst ChunksPage: React.FC<ChunksPageProps> = () => {\n  const t = useTranslate();\n  const { id: kbId, docId } = useParams<{ id: string; docId: string }>();\n  const navigate = useNavigate();\n  const queryClient = useQueryClient();\n\n  // 状态管理\n  const [searchKeywords, setSearchKeywords] = useState('');\n  const [currentPage, setCurrentPage] = useState(1);\n  const [pageSize, setPageSize] = useState(20);\n  const [loadingChunks, setLoadingChunks] = useState<Set<string>>(new Set());\n  // 本地状态管理，用于乐观更新\n  const [localChunkStates, setLocalChunkStates] = useState<Map<string, number>>(new Map());\n\n  // 获取文档chunks\n  const { data: chunksData, isLoading: chunksLoading } = useQuery({\n    queryKey: ['document-chunks', docId, searchKeywords, currentPage, pageSize],\n    queryFn: async () => {\n      if (!docId) return { total: 0, chunks: [] };\n      const { data } = await documentService.getDocumentChunks({\n        doc_id: docId,\n        keywords: searchKeywords,\n        page: currentPage,\n        size: pageSize,\n      });\n      return data?.data || { total: 0, chunks: [] };\n    },\n    enabled: !!docId,\n  });\n\n  const chunks = chunksData?.chunks || [];\n  const total = chunksData?.total || 0;\n\n  // 切换单个chunk状态的mutation - 使用专门的switch接口\n  const switchChunkMutation = useMutation({\n    mutationFn: async ({ chunk_id, available_int, doc_id }: {\n      chunk_id: string,\n      available_int: number,\n      doc_id: string\n    }) => {\n      // 确保chunk_id不为空\n      if (!chunk_id) {\n        throw new Error(t('chunks.chunkIdRequired'));\n      }\n\n      const requestData = {\n        chunk_ids: [chunk_id], // 确保将chunk_id正确放入数组\n        available_int,\n        doc_id\n      };\n\n      const { data } = await request.post(api.switch_chunk, {\n        data: requestData\n      });\n      return { data, chunk_id, available_int };\n    },\n    onSuccess: ({ chunk_id, available_int }) => {\n      // 移除loading状态\n      setLoadingChunks(prev => {\n        const newSet = new Set(prev);\n        newSet.delete(chunk_id);\n        return newSet;\n      });\n\n      // 确认本地状态与服务器状态一致\n      setLocalChunkStates(prev => new Map(prev).set(chunk_id, available_int));\n\n      // 使用精确更新，只更新被修改的chunk的状态，而不是重新获取整个列表\n      queryClient.setQueryData(\n        ['document-chunks', docId, searchKeywords, currentPage, pageSize],\n        (oldData: any) => {\n          if (!oldData) return oldData;\n\n          return {\n            ...oldData,\n            chunks: oldData.chunks.map((chunk: IDocumentChunk) =>\n              chunk.chunk_id === chunk_id\n                ? { ...chunk, available_int }\n                : chunk\n            )\n          };\n        }\n      );\n\n      message.success(t('chunks.statusUpdated'));\n    },\n    onError: (error: any, variables) => {\n      // 移除loading状态\n      setLoadingChunks(prev => {\n        const newSet = new Set(prev);\n        newSet.delete(variables.chunk_id);\n        return newSet;\n      });\n\n      // 回滚本地状态\n      setLocalChunkStates(prev => {\n        const newMap = new Map(prev);\n        newMap.delete(variables.chunk_id);\n        return newMap;\n      });\n\n      message.error(error?.message || t('chunks.statusUpdateFailed'));\n    },\n  });\n\n  // 处理状态切换 - 针对单个chunk执行\n  const handleStatusChange = (chunkId: string, checked: boolean) => {\n    if (!docId) return;\n\n    // 验证参数\n    if (!chunkId) {\n      message.error(t('chunks.chunkIdMissing'));\n      return;\n    }\n\n    // 防止重复点击\n    if (loadingChunks.has(chunkId)) return;\n\n    const newStatus = checked ? 1 : 0;\n\n    // 乐观更新：立即更新本地状态\n    setLocalChunkStates(prev => new Map(prev).set(chunkId, newStatus));\n\n    // 设置loading状态 - 只针对当前被点击的chunk\n    setLoadingChunks(prev => new Set(prev).add(chunkId));\n\n    // 使用switch接口更新单个chunk的状态\n    switchChunkMutation.mutate({\n      chunk_id: chunkId,\n      available_int: newStatus,\n      doc_id: docId,\n    });\n  };\n\n  // 表格列定义\n  const columns: ColumnsType<IDocumentChunk> = [\n    {\n      title: t('chunks.content'),\n      dataIndex: 'content_with_weight',\n      key: 'content_with_weight',\n      width: '60%',\n      render: (text: string) => (\n        <div>\n          <Paragraph\n            ellipsis={{\n              rows: 3,\n              expandable: true,\n              symbol: t('common.more'),\n            }}\n            style={{ marginBottom: 0 }}\n          >\n            {text}\n          </Paragraph>\n        </div>\n      ),\n    },\n\n    {\n      title: t('chunks.keywords'),\n      dataIndex: 'important_kwd',\n      key: 'important_kwd',\n      width: 200,\n      render: (keywords: string[]) => (\n        <div>\n          {keywords?.slice(0, 3).map((keyword, index) => (\n            <Tag key={index} color=\"blue\" style={{ marginBottom: 4 }}>\n              {keyword}\n            </Tag>\n          ))}\n          {keywords?.length > 3 && (\n            <Tooltip title={keywords.slice(3).join(', ')}>\n              <Tag color=\"default\">+{keywords.length - 3}</Tag>\n            </Tooltip>\n          )}\n        </div>\n      ),\n    },\n    {\n      title: t('chunks.status'),\n      dataIndex: 'available_int',\n      key: 'available_int',\n      width: 100,\n      render: (status: number, record: IDocumentChunk) => {\n        // 使用正确的字段名：chunk_id\n        const chunkId = record.chunk_id;\n\n        // 检查chunk_id是否存在\n        if (!chunkId) {\n          console.error('Chunk ID is missing!', record);\n          return <span>{t('chunks.idMissing')}</span>;\n        }\n\n        // 优先使用本地状态，如果没有则使用服务器状态\n        const currentStatus = localChunkStates.has(chunkId)\n          ? localChunkStates.get(chunkId)!\n          : status;\n\n        return (\n          <Switch\n            checked={currentStatus === 1}\n            onChange={(checked) => handleStatusChange(chunkId, checked)}\n            loading={loadingChunks.has(chunkId)}\n            disabled={loadingChunks.has(chunkId)}\n            checkedChildren={t('chunks.enabled')}\n            unCheckedChildren={t('chunks.disabled')}\n          />\n        );\n      },\n    },\n  ];\n\n  const handleSearch = (value: string) => {\n    setSearchKeywords(value);\n    setCurrentPage(1);\n  };\n\n  const handleTableChange = (pagination: any) => {\n    setCurrentPage(pagination.current);\n    setPageSize(pagination.pageSize);\n  };\n\n  if (!kbId || !docId) {\n    return (\n      <div style={{ padding: 24, textAlign: 'center' }}>\n        <h2>{t('chunks.idsRequired')}</h2>\n      </div>\n    );\n  }\n\n  return (\n    <div style={{ padding: 24 }}>\n      {/* Breadcrumb */}\n      <Breadcrumb style={{ marginBottom: 16 }}>\n        <Breadcrumb.Item>\n          <HomeOutlined />\n        </Breadcrumb.Item>\n        <Breadcrumb.Item>\n          <BookOutlined />\n          <span\n            style={{ cursor: 'pointer', marginLeft: 8 }}\n            onClick={() => navigate('/knowledge')}\n          >\n            {t('knowledge.title')}\n          </span>\n        </Breadcrumb.Item>\n        <Breadcrumb.Item>\n          <FileTextOutlined />\n          <span\n            style={{ cursor: 'pointer', marginLeft: 8 }}\n            onClick={() => navigate(`/knowledge/${kbId}`)}\n          >\n            {t('knowledge.documents')}\n          </span>\n        </Breadcrumb.Item>\n        <Breadcrumb.Item>{t('knowledge.chunks')}</Breadcrumb.Item>\n      </Breadcrumb>\n\n      {/* Header */}\n      <div style={{ marginBottom: 24 }}>\n        <Space>\n          <Button\n            icon={<ArrowLeftOutlined />}\n            onClick={() => navigate(`/knowledge/${kbId}`)}\n          >\n            {t('chunks.backToDocuments')}\n          </Button>\n        </Space>\n        <h2 style={{ marginTop: 16, marginBottom: 8 }}>{t('chunks.documentChunks')}</h2>\n        <Text type=\"secondary\">\n          {t('chunks.viewAndManage')}\n        </Text>\n      </div>\n\n      {/* Toolbar */}\n      <Card style={{ marginBottom: 16 }}>\n        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>\n          <div>\n            <Text strong>{t('chunks.totalChunks')}: {total}</Text>\n          </div>\n          <div>\n            <Search\n              placeholder={t('chunks.searchPlaceholder')}\n              allowClear\n              enterButton={<SearchOutlined />}\n              size=\"middle\"\n              style={{ width: 300 }}\n              onSearch={handleSearch}\n            />\n          </div>\n        </div>\n      </Card>\n\n      {/* Chunks Table */}\n      <Card>\n        <Table\n          columns={columns}\n          dataSource={chunks}\n          rowKey=\"id\"\n          loading={chunksLoading}\n          pagination={{\n            current: currentPage,\n            pageSize: pageSize,\n            total: total,\n            showSizeChanger: true,\n            showQuickJumper: true,\n            showTotal: (total, range) =>\n              t('chunks.chunksRange', { start: range[0], end: range[1], total }),\n            pageSizeOptions: ['10', '20', '50', '100'],\n          }}\n          onChange={handleTableChange}\n          scroll={{ x: 1000 }}\n        />\n      </Card>\n    </div>\n  );\n};\n\nexport default ChunksPage;\n", "__isJSFile": true, "__absFile": "/home/<USER>/ragflow/hbweb/src/pages/knowledge-detail/chunks/index.tsx"}, "14": {"path": "/dialogs", "id": "14", "absPath": "/dialogs"}, "15": {"path": "/dialogs", "exact": true, "file": "@/pages/dialogs/index.tsx", "parentId": "14", "id": "15", "absPath": "/dialogs", "__content": "import React, { useState } from 'react';\nimport {\n  Card,\n  Button,\n  Table,\n  Space,\n  Input,\n  Modal,\n  Form,\n  message,\n  Popconfirm,\n  Tag,\n  Typography,\n  Tooltip,\n} from 'antd';\nimport {\n  PlusOutlined,\n  EditOutlined,\n  DeleteOutlined,\n  EyeOutlined,\n  MessageOutlined,\n} from '@ant-design/icons';\nimport { useNavigate } from 'react-router-dom';\nimport type { ColumnsType } from 'antd/es/table';\nimport {\n  useFetchDialogList,\n  useSetDialog,\n  useDeleteDialog,\n  useBatchDeleteDialogs,\n} from '@/hooks/use-dialog-hooks';\nimport { IDialog, IDialogParams } from '@/interfaces/dialog';\nimport AppLayout from '@/components/AppLayout';\nimport StandardDialogForm from '@/components/DialogForm/standard-dialog-form';\nimport { useTranslate } from '@/hooks/use-i18n';\nimport styles from './index.less';\n\nconst { Title, Text } = Typography;\nconst { Search } = Input;\n\nconst DialogsContent: React.FC = () => {\n  const t = useTranslate();\n  const navigate = useNavigate();\n  const [form] = Form.useForm();\n  const [searchKeywords, setSearchKeywords] = useState('');\n  const [modalVisible, setModalVisible] = useState(false);\n  const [editingDialog, setEditingDialog] = useState<IDialog | null>(null);\n  const [selectedRowKeys, setSelectedRowKeys] = useState<React.Key[]>([]);\n\n  // 获取Dialog列表\n  const { data: dialogs = [], isLoading: loading, refetch } = useFetchDialogList({\n    keywords: searchKeywords,\n    page_size: 100,\n  });\n\n  // 创建/更新Dialog\n  const { mutateAsync: setDialog, isPending: saving } = useSetDialog();\n\n  // 删除Dialog\n  const { mutateAsync: deleteDialog } = useDeleteDialog();\n\n  // 批量删除Dialog\n  const { mutateAsync: batchDeleteDialogs } = useBatchDeleteDialogs();\n\n  const handleCreateDialog = () => {\n    setEditingDialog(null);\n    form.resetFields();\n    setModalVisible(true);\n  };\n\n  const handleEditDialog = (dialog: IDialog) => {\n    setEditingDialog(dialog);\n    setModalVisible(true);\n  };\n\n  const handleSaveDialog = async (params: IDialog) => {\n    try {\n      await setDialog(params);\n      setModalVisible(false);\n      form.resetFields();\n      setEditingDialog(null);\n    } catch (error) {\n      console.error('Save dialog failed:', error);\n    }\n  };\n\n  const handleDeleteDialog = async (dialogId: string) => {\n    await deleteDialog(dialogId);\n  };\n\n  const handleBatchDelete = async () => {\n    if (selectedRowKeys.length === 0) {\n      message.warning(t('dialog.selectDialogsToDelete', 'Please select dialogs to delete'));\n      return;\n    }\n\n    Modal.confirm({\n      title: t('dialog.deleteDialogs', 'Delete Dialogs'),\n      content: t('dialog.batchDeleteConfirm', `Are you sure you want to delete ${selectedRowKeys.length} dialog(s)?`, { count: selectedRowKeys.length }),\n      okText: t('common.delete', 'Delete'),\n      okType: 'danger',\n      onOk: async () => {\n        await batchDeleteDialogs(selectedRowKeys as string[]);\n        setSelectedRowKeys([]);\n      },\n    });\n  };\n\n  const handleViewDialog = (dialog: IDialog) => {\n    console.log('🚀 handleViewDialog - dialog:', dialog);\n    console.log('🚀 handleViewDialog - dialog.id:', dialog.id);\n    console.log('🚀 handleViewDialog - dialog.dialog_id:', dialog.dialog_id);\n    navigate(`/dialogs/${dialog.id}/view`);\n  };\n\n  const handleChatDialog = (dialog: IDialog) => {\n    console.log('🚀 handleChatDialog - dialog:', dialog);\n    console.log('🚀 handleChatDialog - dialog.id:', dialog.id);\n    console.log('🚀 handleChatDialog - dialog.dialog_id:', dialog.dialog_id);\n    navigate(`/chat?dialog_id=${dialog.id}`);\n  };\n\n  const columns: ColumnsType<IDialog> = [\n    {\n      title: t('dialog.dialogName'),\n      dataIndex: 'name',\n      key: 'name',\n      render: (text: string, record: IDialog) => (\n        <div>\n          <Text strong>{text}</Text>\n          {record.description && (\n            <div>\n              <Text type=\"secondary\" style={{ fontSize: '12px' }}>\n                {record.description}\n              </Text>\n            </div>\n          )}\n        </div>\n      ),\n    },\n    {\n      title: t('dialog.language'),\n      dataIndex: 'language',\n      key: 'language',\n      width: 100,\n      render: (language: string) => (\n        <Tag color=\"blue\">{language?.toUpperCase() || 'EN'}</Tag>\n      ),\n    },\n    {\n      title: t('dialog.knowledgeBases', 'Knowledge Bases'),\n      dataIndex: 'kb_names',\n      key: 'kb_names',\n      width: 200,\n      render: (kbNames: string[]) => (\n        <div>\n          {kbNames?.slice(0, 2).map((name, index) => (\n            <Tag key={index} style={{ marginBottom: 4 }}>\n              {name}\n            </Tag>\n          ))}\n          {kbNames?.length > 2 && (\n            <Tooltip title={kbNames.slice(2).join(', ')}>\n              <Tag>+{kbNames.length - 2} {t('common.more', 'more')}</Tag>\n            </Tooltip>\n          )}\n        </div>\n      ),\n    },\n    {\n      title: t('dialog.llmModel', 'LLM Model'),\n      dataIndex: 'llm_id',\n      key: 'llm_id',\n      width: 150,\n      render: (llmId: string) => (\n        <Text code style={{ fontSize: '12px' }}>\n          {llmId || t('dialog.notSet', 'Not set')}\n        </Text>\n      ),\n    },\n    // 状态列已隐藏 - 根据用户要求不显示\n    // {\n    //   title: t('common.status', 'Status'),\n    //   dataIndex: 'status',\n    //   key: 'status',\n    //   width: 100,\n    //   render: (status: string) => (\n    //     <Tag color={status === 'active' ? 'green' : 'default'}>\n    //       {status || 'active'}\n    //     </Tag>\n    //   ),\n    // },\n    {\n      title: t('common.created', 'Created'),\n      dataIndex: 'create_date',\n      key: 'create_date',\n      width: 120,\n      render: (date: string) => (\n        <Text style={{ fontSize: '12px' }}>\n          {new Date(date).toLocaleDateString()}\n        </Text>\n      ),\n    },\n    {\n      title: t('common.actions', 'Actions'),\n      key: 'actions',\n      width: 200,\n      render: (_, record: IDialog) => (\n        <Space size=\"small\">\n          <Tooltip title={t('common.view')}>\n            <Button\n              type=\"text\"\n              icon={<EyeOutlined />}\n              onClick={() => handleViewDialog(record)}\n            />\n          </Tooltip>\n          <Tooltip title={t('dialog.startChat', 'Start Chat')}>\n            <Button\n              type=\"text\"\n              icon={<MessageOutlined />}\n              onClick={() => handleChatDialog(record)}\n            />\n          </Tooltip>\n          <Tooltip title={t('common.edit')}>\n            <Button\n              type=\"text\"\n              icon={<EditOutlined />}\n              onClick={() => handleEditDialog(record)}\n            />\n          </Tooltip>\n          <Popconfirm\n            title={t('dialog.deleteConfirm')}\n            description={t('dialog.deleteConfirm')}\n            onConfirm={() => handleDeleteDialog(record.id)}\n            okText={t('common.delete')}\n            okType=\"danger\"\n          >\n            <Tooltip title={t('common.delete')}>\n              <Button type=\"text\" danger icon={<DeleteOutlined />} />\n            </Tooltip>\n          </Popconfirm>\n        </Space>\n      ),\n    },\n  ];\n\n  const rowSelection = {\n    selectedRowKeys,\n    onChange: (newSelectedRowKeys: React.Key[]) => {\n      setSelectedRowKeys(newSelectedRowKeys);\n    },\n  };\n\n  return (\n    <div className={styles.dialogsPage}>\n      <div className={styles.pageHeader}>\n        <div className={styles.headerContent}>\n          <div className={styles.headerLeft}>\n            <Title level={2} style={{ margin: 0 }}>\n              {t('dialog.title')}\n            </Title>\n            <Text type=\"secondary\">\n              {t('dialog.manageDescription', 'Manage your chat dialogs and configurations')}\n            </Text>\n          </div>\n          <div className={styles.headerRight}>\n            <Space>\n              <Search\n                placeholder={t('dialog.searchPlaceholder', 'Search dialogs...')}\n                allowClear\n                style={{ width: 300 }}\n                value={searchKeywords}\n                onChange={(e) => setSearchKeywords(e.target.value)}\n                loading={loading}\n              />\n              <Button\n                type=\"primary\"\n                icon={<PlusOutlined />}\n                onClick={handleCreateDialog}\n                size=\"large\"\n              >\n                {t('dialog.createDialog')}\n              </Button>\n            </Space>\n          </div>\n        </div>\n      </div>\n\n      <div className={styles.dialogsContent}>\n        <Card>\n          <div className={styles.tableHeader}>\n            <Space>\n              {selectedRowKeys.length > 0 && (\n                <Button\n                  danger\n                  onClick={handleBatchDelete}\n                  icon={<DeleteOutlined />}\n                >\n                  {t('dialog.deleteSelected', 'Delete Selected')} ({selectedRowKeys.length})\n                </Button>\n              )}\n            </Space>\n          </div>\n\n          <Table\n            rowSelection={rowSelection}\n            columns={columns}\n            dataSource={dialogs}\n            rowKey=\"id\"\n            loading={loading}\n            pagination={{\n              total: dialogs.length,\n              pageSize: 20,\n              showSizeChanger: true,\n              showQuickJumper: true,\n              showTotal: (total, range) =>\n                t('dialog.dialogsRange', '{{start}}-{{end}} of {{total}} dialogs', {\n                  start: range[0],\n                  end: range[1],\n                  total\n                }),\n            }}\n          />\n        </Card>\n      </div>\n\n      {/* Create/Edit Dialog Modal */}\n      <Modal\n        title={editingDialog ? t('dialog.editDialog', 'Edit Dialog') : t('dialog.createDialog')}\n        open={modalVisible}\n        onCancel={() => {\n          setModalVisible(false);\n          form.resetFields();\n          setEditingDialog(null);\n        }}\n        footer={[\n          <Button\n            key=\"cancel\"\n            onClick={() => {\n              setModalVisible(false);\n              form.resetFields();\n              setEditingDialog(null);\n            }}\n          >\n            {t('common.cancel', 'Cancel')}\n          </Button>,\n          <Button\n            key=\"submit\"\n            type=\"primary\"\n            loading={saving}\n            onClick={() => form.submit()}\n          >\n            {editingDialog ? t('common.update', 'Update') : t('common.create', 'Create')}\n          </Button>,\n        ]}\n        width={900}\n        style={{ top: 20 }}\n        bodyStyle={{ maxHeight: '70vh', overflowY: 'auto' }}\n      >\n        <StandardDialogForm\n          form={form}\n          editingDialog={editingDialog}\n          onFinish={handleSaveDialog}\n        />\n      </Modal>\n    </div>\n  );\n};\n\nconst Dialogs = () => {\n  return (\n    <AppLayout>\n      <DialogsContent />\n    </AppLayout>\n  );\n};\n\nexport default Dialogs;\n", "__isJSFile": true, "__absFile": "/home/<USER>/ragflow/hbweb/src/pages/dialogs/index.tsx"}, "16": {"path": "/dialogs/:id/view", "file": "@/pages/dialogs/view.tsx", "parentId": "14", "id": "16", "absPath": "/dialogs/:id/view", "__content": "import React, { useEffect, useState } from 'react';\nimport { \n  Card, \n  Typography, \n  Spin, \n  Alert, \n  Button, \n  Space, \n  Descriptions, \n  Tag, \n  Divider,\n  Row,\n  Col,\n  Switch,\n  Slider,\n  Table,\n  Empty\n} from 'antd';\nimport {\n  ArrowLeftOutlined,\n  EditOutlined,\n  MessageOutlined,\n  SettingOutlined,\n  RobotOutlined,\n  DatabaseOutlined\n} from '@ant-design/icons';\nimport { useNavigate, useParams } from 'umi';\nimport AppLayout from '@/components/AppLayout';\nimport { useTranslate } from '@/hooks/use-i18n';\nimport { useFetchDialogList } from '@/hooks/use-dialog-hooks';\nimport { useKnowledgeBaseOptions } from '@/hooks/knowledge-base-hooks';\nimport { useLLMOptions } from '@/hooks/llm-hooks';\nimport { IDialog } from '@/interfaces/dialog';\n\nconst { Title, Text, Paragraph } = Typography;\n\nconst DialogViewPage: React.FC = () => {\n  const t = useTranslate();\n  const navigate = useNavigate();\n  const { id } = useParams<{ id: string }>();\n  \n  const [currentDialog, setCurrentDialog] = useState<IDialog | null>(null);\n  \n  // 获取Dialog列表来找到当前Dialog\n  const { data: dialogs = [], isLoading } = useFetchDialogList({});\n  \n  // 获取知识库和LLM选项用于显示名称\n  const { data: knowledgeBases = [] } = useKnowledgeBaseOptions();\n  const { models: llmModels = [] } = useLLMOptions();\n\n  useEffect(() => {\n    console.log('🚀 Dialog View - ID from URL:', id);\n    console.log('🚀 Dialog View - available dialogs:', dialogs);\n    \n    if (id && dialogs.length > 0) {\n      const dialog = dialogs.find((d: IDialog) => d.id === id);\n      console.log('🚀 Dialog View - found dialog:', dialog);\n      setCurrentDialog(dialog || null);\n    }\n  }, [id, dialogs]);\n\n  const handleBackToDialogs = () => {\n    navigate('/dialogs');\n  };\n\n  const handleEditDialog = () => {\n    if (currentDialog) {\n      navigate(`/dialogs?edit=${currentDialog.id}`);\n    }\n  };\n\n  const handleStartChat = () => {\n    if (currentDialog) {\n      navigate(`/chat?dialog_id=${currentDialog.id}`);\n    }\n  };\n\n  // 获取知识库名称\n  const getKnowledgeBaseNames = (kbIds: string[]) => {\n    if (!kbIds || kbIds.length === 0) return [];\n    return kbIds.map(id => {\n      const kb = knowledgeBases.find((kb: any) => kb.id === id);\n      return kb ? kb.name : id;\n    });\n  };\n\n  // 获取LLM模型名称\n  const getLLMModelName = (llmId: string) => {\n    const model = llmModels.find(m => m.value === llmId);\n    return model ? model.label : llmId;\n  };\n\n  if (isLoading) {\n    return (\n      <AppLayout>\n        <div style={{ \n          display: 'flex', \n          justifyContent: 'center', \n          alignItems: 'center', \n          height: '50vh' \n        }}>\n          <Spin size=\"large\" />\n        </div>\n      </AppLayout>\n    );\n  }\n\n  if (!id) {\n    return (\n      <AppLayout>\n        <Card>\n          <Alert\n            message={t('dialog.invalidDialogId', 'Invalid Dialog ID')}\n            description={t('dialog.noDialogIdProvided', 'No dialog ID provided in the URL.')}\n            type=\"warning\"\n            showIcon\n            action={\n              <Button type=\"primary\" onClick={handleBackToDialogs}>\n                {t('dialog.goToDialogs', 'Go to Dialogs')}\n              </Button>\n            }\n          />\n        </Card>\n      </AppLayout>\n    );\n  }\n\n  if (!currentDialog) {\n    return (\n      <AppLayout>\n        <Card>\n          <Alert\n            message={t('dialog.dialogNotFound', 'Dialog Not Found')}\n            description={\n              <div>\n                <p>Dialog with ID \"{id}\" was not found.</p>\n                <p>Available dialogs: {dialogs.length}</p>\n                {dialogs.length > 0 && (\n                  <details>\n                    <summary>Debug Info</summary>\n                    <pre>{JSON.stringify(dialogs.map(d => ({ \n                      id: d.id, \n                      dialog_id: d.dialog_id, \n                      name: d.name \n                    })), null, 2)}</pre>\n                  </details>\n                )}\n              </div>\n            }\n            type=\"error\"\n            showIcon\n            action={\n              <Button type=\"primary\" onClick={handleBackToDialogs}>\n                Go to Dialogs\n              </Button>\n            }\n          />\n        </Card>\n      </AppLayout>\n    );\n  }\n\n  // 参数变量表格列\n  const parameterColumns = [\n    {\n      title: 'Key',\n      dataIndex: 'key',\n      key: 'key',\n    },\n    {\n      title: 'Optional',\n      dataIndex: 'optional',\n      key: 'optional',\n      render: (optional: boolean) => (\n        <Tag color={optional ? 'blue' : 'red'}>\n          {optional ? 'Optional' : 'Required'}\n        </Tag>\n      ),\n    },\n  ];\n\n  return (\n    <AppLayout>\n      <div style={{ padding: '24px' }}>\n        {/* Header */}\n        <div style={{ marginBottom: '24px' }}>\n          <Space>\n            <Button \n              icon={<ArrowLeftOutlined />} \n              onClick={handleBackToDialogs}\n              type=\"text\"\n            >\n              {t('dialog.backToDialogs', 'Back to Dialogs')}\n            </Button>\n          </Space>\n          \n          <div style={{ marginTop: '16px', display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start' }}>\n            <div>\n              <Title level={2} style={{ margin: 0, display: 'flex', alignItems: 'center' }}>\n                <MessageOutlined style={{ marginRight: '8px' }} />\n                {currentDialog.name}\n              </Title>\n              {currentDialog.description && (\n                <Text type=\"secondary\" style={{ fontSize: '16px' }}>\n                  {currentDialog.description}\n                </Text>\n              )}\n            </div>\n            \n            <Space>\n              <Button \n                type=\"default\" \n                icon={<EditOutlined />}\n                onClick={handleEditDialog}\n              >\n                {t('dialog.editDialog', 'Edit Dialog')}\n              </Button>\n              <Button \n                type=\"primary\" \n                icon={<MessageOutlined />}\n                onClick={handleStartChat}\n              >\n                {t('dialog.startChat', 'Start Chat')}\n              </Button>\n            </Space>\n          </div>\n        </div>\n\n        {/* Basic Information */}\n        <Card title={<><SettingOutlined style={{ marginRight: '8px' }} />{t('dialog.basicInformation', 'Basic Information')}</>} style={{ marginBottom: '24px' }}>\n          <Descriptions column={2} bordered>\n            <Descriptions.Item label={t('dialog.assistantName', 'Assistant Name')}>{currentDialog.name}</Descriptions.Item>\n            <Descriptions.Item label={t('dialog.language', 'Language')}>{currentDialog.language || t('languages.english', 'English')}</Descriptions.Item>\n            <Descriptions.Item label={t('dialog.description', 'Description')} span={2}>\n              {currentDialog.description || <Text type=\"secondary\">{t('common.noDescription', 'No description')}</Text>}\n            </Descriptions.Item>\n            <Descriptions.Item label={t('common.status', 'Status')}>\n              <Tag color=\"green\">{t('common.active', 'Active')}</Tag>\n            </Descriptions.Item>\n            <Descriptions.Item label={t('common.created', 'Created')}>\n              {currentDialog.create_time ? new Date(currentDialog.create_time).toLocaleString() : t('common.unknown', 'Unknown')}\n            </Descriptions.Item>\n          </Descriptions>\n        </Card>\n\n        {/* Model Configuration */}\n        <Card title={<><RobotOutlined style={{ marginRight: '8px' }} />Model Configuration</>} style={{ marginBottom: '24px' }}>\n          <Descriptions column={2} bordered>\n            <Descriptions.Item label=\"LLM Model\" span={2}>\n              {currentDialog.llm_id ? getLLMModelName(currentDialog.llm_id) : <Text type=\"secondary\">Not configured</Text>}\n            </Descriptions.Item>\n            {currentDialog.llm_setting && (\n              <>\n                <Descriptions.Item label=\"Temperature\">\n                  {currentDialog.llm_setting.temperature ?? 'Not set'}\n                </Descriptions.Item>\n                <Descriptions.Item label=\"Top P\">\n                  {currentDialog.llm_setting.top_p ?? 'Not set'}\n                </Descriptions.Item>\n                <Descriptions.Item label=\"Presence Penalty\">\n                  {currentDialog.llm_setting.presence_penalty ?? 'Not set'}\n                </Descriptions.Item>\n                <Descriptions.Item label=\"Frequency Penalty\">\n                  {currentDialog.llm_setting.frequency_penalty ?? 'Not set'}\n                </Descriptions.Item>\n                <Descriptions.Item label=\"Max Tokens\" span={2}>\n                  {currentDialog.llm_setting.max_tokens ?? 'Not set'}\n                </Descriptions.Item>\n              </>\n            )}\n          </Descriptions>\n        </Card>\n\n        {/* Knowledge Base Configuration */}\n        <Card title={<><DatabaseOutlined style={{ marginRight: '8px' }} />Knowledge Base Configuration</>} style={{ marginBottom: '24px' }}>\n          <Descriptions column={2} bordered>\n            <Descriptions.Item label=\"Knowledge Bases\" span={2}>\n              {currentDialog.kb_ids && currentDialog.kb_ids.length > 0 ? (\n                <Space wrap>\n                  {getKnowledgeBaseNames(currentDialog.kb_ids).map((name, index) => (\n                    <Tag key={index} color=\"blue\">{name}</Tag>\n                  ))}\n                </Space>\n              ) : (\n                <Text type=\"secondary\">No knowledge bases selected</Text>\n              )}\n            </Descriptions.Item>\n            <Descriptions.Item label=\"Similarity Threshold\">\n              {currentDialog.similarity_threshold ?? 'Not set'}\n            </Descriptions.Item>\n            <Descriptions.Item label=\"Vector Similarity Weight\">\n              {currentDialog.vector_similarity_weight ?? 'Not set'}\n            </Descriptions.Item>\n            <Descriptions.Item label=\"Top N\">\n              {currentDialog.top_n ?? 'Not set'}\n            </Descriptions.Item>\n            <Descriptions.Item label=\"Rerank Model\">\n              {currentDialog.rerank_id || <Text type=\"secondary\">Not configured</Text>}\n            </Descriptions.Item>\n            {currentDialog.rerank_id && (\n              <Descriptions.Item label=\"Top K\">\n                {currentDialog.top_k ?? 'Not set'}\n              </Descriptions.Item>\n            )}\n          </Descriptions>\n        </Card>\n\n        {/* Prompt Configuration */}\n        <Card title=\"Prompt Configuration\" style={{ marginBottom: '24px' }}>\n          {currentDialog.prompt_config ? (\n            <>\n              <Descriptions column={2} bordered style={{ marginBottom: '16px' }}>\n                <Descriptions.Item label=\"Quote\">\n                  <Switch checked={currentDialog.prompt_config.quote} disabled />\n                </Descriptions.Item>\n                <Descriptions.Item label=\"Keyword\">\n                  <Switch checked={currentDialog.prompt_config.keyword} disabled />\n                </Descriptions.Item>\n                <Descriptions.Item label=\"TTS\">\n                  <Switch checked={currentDialog.prompt_config.tts} disabled />\n                </Descriptions.Item>\n                <Descriptions.Item label=\"Multi Turn\">\n                  <Switch checked={currentDialog.prompt_config.refine_multiturn} disabled />\n                </Descriptions.Item>\n                <Descriptions.Item label=\"Reasoning\">\n                  <Switch checked={currentDialog.prompt_config.reasoning} disabled />\n                </Descriptions.Item>\n                <Descriptions.Item label=\"Empty Response\">\n                  {currentDialog.prompt_config.empty_response || <Text type=\"secondary\">Not set</Text>}\n                </Descriptions.Item>\n              </Descriptions>\n\n              <Divider>System Prompt</Divider>\n              <Paragraph>\n                <pre style={{ \n                  whiteSpace: 'pre-wrap', \n                  background: '#f5f5f5', \n                  padding: '12px', \n                  borderRadius: '4px',\n                  fontSize: '14px'\n                }}>\n                  {currentDialog.prompt_config.system || 'No system prompt configured'}\n                </pre>\n              </Paragraph>\n\n              {currentDialog.prompt_config.prologue && (\n                <>\n                  <Divider>{t('dialog.prologue', 'Prologue')}</Divider>\n                  <Paragraph>\n                    <pre style={{ \n                      whiteSpace: 'pre-wrap', \n                      background: '#f5f5f5', \n                      padding: '12px', \n                      borderRadius: '4px',\n                      fontSize: '14px'\n                    }}>\n                      {currentDialog.prompt_config.prologue}\n                    </pre>\n                  </Paragraph>\n                </>\n              )}\n\n              {currentDialog.prompt_config.parameters && currentDialog.prompt_config.parameters.length > 0 && (\n                <>\n                  <Divider>{t('dialog.parameters', 'Parameters')}</Divider>\n                  <Table\n                    dataSource={currentDialog.prompt_config.parameters}\n                    columns={parameterColumns}\n                    rowKey=\"key\"\n                    pagination={false}\n                    size=\"small\"\n                  />\n                </>\n              )}\n            </>\n          ) : (\n            <Empty description={t('dialog.noPromptConfiguration', 'No prompt configuration')} />\n          )}\n        </Card>\n      </div>\n    </AppLayout>\n  );\n};\n\nexport default DialogViewPage;\n", "__isJSFile": true, "__absFile": "/home/<USER>/ragflow/hbweb/src/pages/dialogs/view.tsx"}, "17": {"path": "/chat", "file": "@/pages/chat/index.tsx", "id": "17", "absPath": "/chat", "__content": "import React, { useEffect, useState } from 'react';\nimport {\n  Card,\n  Typography,\n  Spin,\n  Alert,\n  Button,\n  Space,\n  Layout,\n  message,\n  Divider\n} from 'antd';\nimport {\n  ArrowLeftOutlined,\n  MessageOutlined,\n  SettingOutlined,\n  RobotOutlined\n} from '@ant-design/icons';\nimport { useNavigate, useSearchParams } from 'umi';\nimport AppLayout from '@/components/AppLayout';\nimport MessageList from '@/components/MessageList';\nimport MessageInput from '@/components/MessageInput';\nimport { useTranslate } from '@/hooks/use-i18n';\nimport { useFetchDialogList } from '@/hooks/use-dialog-hooks';\nimport {\n  useChatState,\n  useCreateConversation,\n  useThumbup,\n  useDeleteMessage,\n  useConversation,\n  useGetChatSearchParams,\n  useFetchNextConversation,\n  useSelectNextMessages\n} from '@/hooks/use-chat-hooks';\nimport { IDialog } from '@/interfaces/dialog';\nimport { IMessage } from '@/interfaces/message';\nimport { buildMessageUuid } from '@/utils/chat';\n\nconst { Title, Text } = Typography;\nconst { Content, Sider } = Layout;\n\nconst ChatPage: React.FC = () => {\n  const t = useTranslate();\n  const navigate = useNavigate();\n\n  // 使用新的URL参数获取hook（参考原始web）\n  const { dialogId, conversationId, isNew } = useGetChatSearchParams();\n\n  const [currentDialog, setCurrentDialog] = useState<IDialog | null>(null);\n\n  // 获取Dialog列表来找到当前Dialog\n  const { data: dialogs = [], isLoading } = useFetchDialogList({});\n\n  // 聊天状态管理\n  const chatState = useChatState(dialogId || '');\n  const { updateConversation } = chatState;\n\n  // 使用新的conversation获取hook（参考原始web）\n  const { data: conversation } = useFetchNextConversation();\n\n  // 使用派生消息管理（参考原始web）\n  const {\n    derivedMessages,\n    loading: messagesLoading,\n    addNewestAnswer,\n    addNewestQuestion,\n    removeLatestMessage,\n    removeMessageById,\n  } = useSelectNextMessages();\n\n  // 创建对话\n  const createConversation = useCreateConversation();\n\n  // 点赞功能\n  const thumbup = useThumbup();\n\n  // 删除消息功能\n  const deleteMessage = useDeleteMessage();\n\n  useEffect(() => {\n    if (dialogId && dialogs.length > 0) {\n      // 使用id字段而不是dialog_id字段进行查找\n      const dialog = dialogs.find((d: IDialog) => d.id === dialogId);\n      setCurrentDialog(dialog || null);\n    }\n  }, [dialogId, conversationId, dialogs]);\n\n\n\n  // 移除自动创建对话的逻辑，改为在发送第一条消息时创建\n\n  const handleBackToConversations = () => {\n    navigate('/conversations');\n  };\n\n  const handleSendMessage = async (content: string, files?: any[]) => {\n    if (!currentDialog) return;\n\n    // 创建用户消息\n    const userMessage: IMessage = {\n      id: buildMessageUuid({ role: 'user' }),\n      content: content.trim(),\n      role: 'user',\n      doc_ids: files || [],\n      create_time: new Date().toISOString(),\n    };\n\n    // 使用addNewestQuestion添加用户消息和空的助手消息\n    addNewestQuestion(userMessage, '');\n\n    // 然后调用chatState的发送逻辑，但不让它添加消息到界面\n    // 在Continue Chat时，传递当前的derivedMessages作为历史消息\n    // 创建一个包装函数来处理引用数据\n    const handleStreamUpdate = (content: string, reference?: any) => {\n      addNewestAnswer(content, reference);\n    };\n\n    try {\n      // 确保使用正确的conversation id：\n      // 1. 如果URL中有conversation_id且不是新对话，使用URL中的\n      // 2. 如果是新对话或URL中没有conversation_id，使用state中的（可能为空，会创建新的）\n      // 3. 一旦创建了新对话，立即更新URL以确保后续消息使用同一个conversation_id\n      const effectiveConversationId = (isNew !== 'true' && conversationId) ? conversationId : chatState.currentConversation?.id;\n\n      await chatState.sendMessageWithoutUI(content, files, handleStreamUpdate, derivedMessages, effectiveConversationId);\n\n      // 如果是新对话，发送成功后立即更新URL包含conversation_id，确保后续消息使用同一个conversation\n      if (isNew === 'true' && chatState.currentConversation?.id) {\n        const newUrl = `/chat?dialog_id=${dialogId}&conversation_id=${chatState.currentConversation.id}`;\n        navigate(newUrl, { replace: true });\n      }\n      // 如果原来URL中没有conversation_id，但现在有了，也要更新URL\n      else if (!conversationId && chatState.currentConversation?.id) {\n        const newUrl = `/chat?dialog_id=${dialogId}&conversation_id=${chatState.currentConversation.id}`;\n        navigate(newUrl, { replace: true });\n      }\n    } catch (error) {\n      console.error('Send message failed:', error);\n      // 如果发送失败，移除最后添加的消息\n      removeLatestMessage();\n    }\n  };\n\n  const handleStopSending = () => {\n    chatState.stopSending();\n  };\n\n  const handleThumbup = (messageId: string, thumbupValue: boolean) => {\n    thumbup.mutate({ messageId, thumbup: thumbupValue });\n  };\n\n  const handleCopy = (content: string) => {\n    message.success('Message copied to clipboard');\n  };\n\n  const handleTTS = (content: string) => {\n    // TTS功能已在MessageList组件中实现\n    // 这里可以添加额外的处理逻辑，比如统计等\n    console.log('TTS播放:', content.substring(0, 50) + '...');\n  };\n\n  // 按照原版ragflow的删除机制：删除消息后立即保存到后端\n  const handleDeleteMessage = async (messageId: string) => {\n    // 找到要删除的消息\n    const messageToDelete = derivedMessages.find(msg => msg.id === messageId);\n    if (!messageToDelete) {\n      message.error('删除失败：消息不存在');\n      return;\n    }\n\n    // 检查是否是欢迎消息，欢迎消息不允许删除\n    const isWelcomeMessage = messageToDelete.content?.includes(\"Hi! I'm your assistant\") ||\n                            messageToDelete.content?.includes(\"您好！我是您的助手\") ||\n                            messageToDelete.content?.includes(\"how can I help you\") ||\n                            messageToDelete.content?.includes(\"有什么可以帮助您的吗\") ||\n                            messageToDelete.content?.includes(\"How can I help you\") ||\n                            messageToDelete.content?.includes(\"我是您的助手\") ||\n                            messageToDelete.content?.includes(\"我可以帮助您\");\n\n    if (isWelcomeMessage) {\n      message.warning('欢迎消息不能删除');\n      return;\n    }\n\n    // 如果是新对话，只做前端删除\n    if (isNew === 'true') {\n      removeMessageById(messageId);\n      return;\n    }\n\n    // 对于已保存的对话，需要删除后立即保存到后端\n    if (!conversationId) {\n      message.error('删除失败：缺少对话ID');\n      return;\n    }\n\n    try {\n      // 按照原版ragflow的逻辑：只删除指定的消息和其直接配对的消息\n      const messageIndex = derivedMessages.findIndex(msg => msg.id === messageId);\n      if (messageIndex === -1) return;\n\n      const messagesToDelete = [messageId];\n\n      // 根据原版ragflow的逻辑，删除消息对\n      if (messageToDelete.role === 'user') {\n        // 如果删除用户消息，检查下一条是否是对应的助手回复\n        if (messageIndex + 1 < derivedMessages.length) {\n          const nextMessage = derivedMessages[messageIndex + 1];\n          if (nextMessage.role === 'assistant') {\n            messagesToDelete.push(nextMessage.id);\n          }\n        }\n      } else if (messageToDelete.role === 'assistant') {\n        // 如果删除助手消息，检查上一条是否是对应的用户消息\n        if (messageIndex > 0) {\n          const prevMessage = derivedMessages[messageIndex - 1];\n          if (prevMessage.role === 'user') {\n            messagesToDelete.push(prevMessage.id);\n          }\n        }\n      }\n\n      // 计算删除后的消息列表\n      const updatedMessages = derivedMessages.filter(msg => !messagesToDelete.includes(msg.id));\n\n      // 先更新前端状态（乐观更新）\n      messagesToDelete.forEach(id => removeMessageById(id));\n\n      // 立即保存到后端\n      await updateConversation.mutateAsync({\n        conversation_id: conversationId,\n        message: updatedMessages,\n        is_new: false,\n      });\n\n      message.success('消息删除成功');\n    } catch (error) {\n      console.error('Delete message failed:', error);\n      message.error('删除失败，正在刷新页面...');\n      // 如果保存失败，刷新页面恢复状态\n      setTimeout(() => {\n        window.location.reload();\n      }, 1000);\n    }\n  };\n\n  const handleResend = (content: string) => {\n    // 重新发送消息\n    handleSendMessage(content, []);\n  };\n\n  if (isLoading) {\n    return (\n      <AppLayout>\n        <div style={{ \n          display: 'flex', \n          justifyContent: 'center', \n          alignItems: 'center', \n          height: '50vh' \n        }}>\n          <Spin size=\"large\" />\n        </div>\n      </AppLayout>\n    );\n  }\n\n  if (!dialogId) {\n    return (\n      <AppLayout>\n        <Card>\n          <Alert\n            message={t('chat.noDialogSelected', 'No Dialog Selected')}\n            description={t('chat.selectDialogDesc', 'Please select a dialog from the dialogs page to start chatting.')}\n            type=\"warning\"\n            showIcon\n            action={\n              <Button type=\"primary\" onClick={handleBackToConversations}>\n                {t('chat.goToDialogs', 'Go to Dialogs')}\n              </Button>\n            }\n          />\n        </Card>\n      </AppLayout>\n    );\n  }\n\n  if (!currentDialog) {\n    return (\n      <AppLayout>\n        <Card>\n          <Alert\n            message={t('chat.dialogNotFound', 'Dialog Not Found')}\n            description={\n              <div>\n                <p>Dialog with ID \"{dialogId}\" was not found.</p>\n                <p>Available dialogs: {dialogs.length}</p>\n\n              </div>\n            }\n            type=\"error\"\n            showIcon\n            action={\n              <Button type=\"primary\" onClick={handleBackToConversations}>\n                {t('chat.backToConversations', 'Go to Conversations')}\n              </Button>\n            }\n          />\n        </Card>\n      </AppLayout>\n    );\n  }\n\n  return (\n    <AppLayout>\n      <div style={{ height: '100vh', display: 'flex', flexDirection: 'column' }}>\n        {/* Header */}\n        <div style={{\n          padding: '16px 24px',\n          borderBottom: '1px solid #f0f0f0',\n          background: '#fff'\n        }}>\n          <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>\n            <Space>\n              <Button\n                icon={<ArrowLeftOutlined />}\n                onClick={handleBackToConversations}\n                type=\"text\"\n              >\n                {t('chat.backToConversations', 'Back to Conversations')}\n              </Button>\n              <Divider type=\"vertical\" />\n              <div style={{ display: 'flex', alignItems: 'center' }}>\n                <RobotOutlined style={{ marginRight: '8px', color: '#1890ff' }} />\n                <div>\n                  <Title level={4} style={{ margin: 0 }}>\n                    {currentDialog.name}\n                  </Title>\n                  {currentDialog.description && (\n                    <Text type=\"secondary\" style={{ fontSize: '12px' }}>\n                      {currentDialog.description}\n                    </Text>\n                  )}\n                </div>\n              </div>\n            </Space>\n\n            <Space>\n              <Button\n                icon={<SettingOutlined />}\n                onClick={() => navigate(`/dialogs/${currentDialog.id}/view`)}\n                type=\"text\"\n              >\n                {t('navigation.settings')}\n              </Button>\n            </Space>\n          </div>\n        </div>\n\n        {/* Chat Content */}\n        <div style={{ flex: 1, display: 'flex', flexDirection: 'column', overflow: 'hidden' }}>\n          {/* Error Display */}\n          {chatState.error && (\n            <Alert\n              message={t('common.error', 'Error')}\n              description={chatState.error}\n              type=\"error\"\n              closable\n              onClose={() => {\n                // 清除错误状态\n                chatState.setCurrentConversation(chatState.currentConversation);\n              }}\n              style={{ margin: '8px 16px' }}\n            />\n          )}\n\n          {/* Messages Area */}\n          <div style={{ flex: 1, overflow: 'hidden' }}>\n            <MessageList\n              messages={derivedMessages}\n              loading={chatState.sending || messagesLoading}\n              onThumbup={handleThumbup}\n              onDelete={handleDeleteMessage}\n              onCopy={handleCopy}\n              onTTS={handleTTS}\n              onResend={handleResend}\n              showEmptyState={false}\n              useKnowledgeMarkdown={true}\n              isStreaming={chatState.sending}\n            />\n          </div>\n\n          {/* Input Area */}\n          <MessageInput\n            onSend={handleSendMessage}\n            onStop={handleStopSending}\n            loading={chatState.sending}\n            disabled={!currentDialog?.llm_id}\n            placeholder={\n              currentDialog?.llm_id\n                ? t('chat.chatWithDialog', 'Chat with {{name}}...', { name: currentDialog.name })\n                : t('chat.configureLLMFirst', 'Please configure LLM model first')\n            }\n          />\n        </div>\n\n        {/* Welcome Message */}\n        {(!derivedMessages || derivedMessages.length === 0) && (\n          <div style={{\n            position: 'absolute',\n            top: '50%',\n            left: '50%',\n            transform: 'translate(-50%, -50%)',\n            textAlign: 'center',\n            zIndex: 1\n          }}>\n            <RobotOutlined style={{ fontSize: '64px', color: '#1890ff', marginBottom: '16px' }} />\n            <Title level={3}>{t('chat.welcomeTo', 'Welcome to {{name}}', { name: currentDialog.name })}</Title>\n            <Text type=\"secondary\">\n              {currentDialog.prompt_config?.prologue || t('chat.defaultWelcome')}\n            </Text>\n            <div style={{ marginTop: '16px' }}>\n              <Space direction=\"vertical\" size=\"small\">\n                <Text type=\"secondary\" style={{ fontSize: '12px' }}>\n                  <strong>{t('chat.model', 'Model')}:</strong> {currentDialog.llm_id || t('chat.notConfigured', 'Not configured')}\n                </Text>\n                {currentDialog.kb_ids && currentDialog.kb_ids.length > 0 && (\n                  <Text type=\"secondary\" style={{ fontSize: '12px' }}>\n                    <strong>{t('chat.knowledgeBases', 'Knowledge Bases')}:</strong> {t('chat.connected', '{{count}} connected', { count: currentDialog.kb_ids.length })}\n                  </Text>\n                )}\n              </Space>\n            </div>\n          </div>\n        )}\n      </div>\n    </AppLayout>\n  );\n};\n\nexport default ChatPage;\n", "__isJSFile": true, "__absFile": "/home/<USER>/ragflow/hbweb/src/pages/chat/index.tsx"}, "18": {"path": "/conversations", "file": "@/pages/conversations/index.tsx", "id": "18", "absPath": "/conversations", "__content": "import React, { useState } from 'react';\nimport {\n  Card,\n  Button,\n  Input,\n  Space,\n  Typography,\n  Empty,\n  Spin,\n  Row,\n  Col,\n  Statistic,\n  Collapse,\n  Badge,\n} from 'antd';\nimport {\n  PlusOutlined,\n  MessageOutlined,\n  UserOutlined,\n  RobotOutlined,\n  ClockCircleOutlined,\n} from '@ant-design/icons';\nimport { useNavigate } from 'umi';\nimport AppLayout from '@/components/AppLayout';\nimport ConversationCard from './components/ConversationCard';\nimport DialogSelector from './components/DialogSelector';\nimport { useConversationsList, useDeleteConversation } from '@/hooks/use-conversations-hooks';\nimport { useFetchDialogList } from '@/hooks/use-dialog-hooks';\nimport { IDialog } from '@/interfaces/dialog';\nimport { useTranslate } from '@/hooks/use-i18n';\nimport styles from './index.less';\n\nconst { Title, Text } = Typography;\nconst { Search } = Input;\nconst { Panel } = Collapse;\n\nconst ConversationsContent: React.FC = () => {\n  const t = useTranslate();\n  const navigate = useNavigate();\n  const [searchKeywords, setSearchKeywords] = useState('');\n  const [dialogSelectorVisible, setDialogSelectorVisible] = useState(false);\n\n  // 获取conversations列表\n  const { data: conversations = [], isLoading: loading } = useConversationsList({\n    keywords: searchKeywords,\n    page_size: 100,\n  });\n\n  // 删除conversation\n  const deleteConversation = useDeleteConversation();\n\n  // 获取dialogs列表用于快速创建对话\n  const { data: dialogs = [] } = useFetchDialogList({});\n\n  const handleCreateConversation = () => {\n    if (dialogs.length === 0) {\n      // 如果没有dialogs，跳转到dialogs页面创建新的dialog\n      navigate('/dialogs');\n    } else if (dialogs.length === 1) {\n      // 如果只有一个dialog，直接使用它\n      navigate(`/chat?dialog_id=${dialogs[0].id}`);\n    } else {\n      // 如果有多个dialogs，显示选择器\n      setDialogSelectorVisible(true);\n    }\n  };\n\n  const handleDialogSelect = (dialog: IDialog) => {\n    setDialogSelectorVisible(false);\n    navigate(`/chat?dialog_id=${dialog.id}`);\n  };\n\n  const handleDialogSelectorCancel = () => {\n    setDialogSelectorVisible(false);\n  };\n\n  const handleSearch = (value: string) => {\n    setSearchKeywords(value);\n  };\n\n  const handleDeleteConversation = (conversationId: string) => {\n    deleteConversation.mutate(conversationId);\n  };\n\n  // 按Dialog分组conversations\n  const groupedConversations = React.useMemo(() => {\n    const groups: Record<string, {\n      dialog: any;\n      conversations: (typeof conversations)[0][];\n    }> = {};\n\n    conversations.forEach(conversation => {\n      const dialogId = conversation.dialog_id;\n      if (!groups[dialogId]) {\n        const dialog = dialogs.find(d => d.id === dialogId);\n        groups[dialogId] = {\n          dialog: dialog || { id: dialogId, name: 'Unknown Dialog' },\n          conversations: [],\n        };\n      }\n      groups[dialogId].conversations.push(conversation);\n    });\n\n    // 按dialog名称排序\n    return Object.values(groups).sort((a, b) =>\n      a.dialog.name.localeCompare(b.dialog.name)\n    );\n  }, [conversations, dialogs]);\n\n  return (\n    <div className={styles.conversationsPage}>\n      {/* 页面头部 */}\n      <div className={styles.pageHeader}>\n        <div className={styles.headerContent}>\n          <div className={styles.headerLeft}>\n            <Title level={2} className={styles.title}>\n              <MessageOutlined className={styles.icon} />\n              {t('conversations.title')}\n            </Title>\n            <Text type=\"secondary\" className={styles.description}>\n              {t('conversations.manageDescription', 'Manage your chat conversations and history')}\n            </Text>\n          </div>\n          <div className={styles.headerRight}>\n            <Search\n              placeholder={t('conversations.searchConversations')}\n              allowClear\n              className={styles.searchInput}\n              value={searchKeywords}\n              onChange={(e) => setSearchKeywords(e.target.value)}\n              onSearch={handleSearch}\n              loading={loading}\n            />\n            <Button\n              type=\"primary\"\n              icon={<PlusOutlined />}\n              onClick={handleCreateConversation}\n              className={styles.createButton}\n            >\n              {dialogs.length === 0\n                ? t('dialog.createDialog')\n                : dialogs.length === 1\n                  ? t('conversations.newConversation', 'New Conversation')\n                  : t('conversations.newConversation', 'New Conversation')\n              }\n            </Button>\n          </div>\n        </div>\n      </div>\n\n      {/* 统计信息 */}\n      {/*\n      {!loading && conversations.length > 0 && (\n        <Row gutter={16} style={{ marginBottom: '24px' }}>\n          <Col span={6}>\n            <Card>\n              <Statistic\n                title={t('conversations.totalConversations', 'Total Conversations')}\n                value={conversations.length}\n                prefix={<MessageOutlined />}\n              />\n            </Card>\n          </Col>\n          <Col span={6}>\n            <Card>\n              <Statistic\n                title={t('conversations.withMessages', 'With Messages')}\n                value={conversations.filter(c => c.message && c.message.length > 0).length}\n                prefix={<UserOutlined />}\n              />\n            </Card>\n          </Col>\n          <Col span={6}>\n            <Card>\n              <Statistic\n                title={t('conversations.totalMessages', 'Total Messages')}\n                value={conversations.reduce((sum, c) => sum + (c.message?.length || 0), 0)}\n                prefix={<RobotOutlined />}\n              />\n            </Card>\n          </Col>\n          <Col span={6}>\n            <Card>\n              <Statistic\n                title={t('conversations.recentlyActive', 'Recently Active')}\n                value={conversations.filter(c => {\n                  const updateTime = new Date(c.update_time);\n                  const oneDayAgo = new Date(Date.now() - 24 * 60 * 60 * 1000);\n                  return updateTime > oneDayAgo;\n                }).length}\n                prefix={<ClockCircleOutlined />}\n              />\n            </Card>\n          </Col>\n        </Row>\n      )}\n      */}\n      {/* 内容区域 */}\n      <Card className={styles.contentCard}>\n        {loading ? (\n          <div className={styles.loadingContainer}>\n            <Spin size=\"large\" />\n            <div className={styles.loadingText}>\n              <Text type=\"secondary\">{t('conversations.loading', 'Loading conversations...')}</Text>\n            </div>\n          </div>\n        ) : conversations.length === 0 ? (\n          <div className={styles.emptyContainer}>\n            <Empty\n              image={Empty.PRESENTED_IMAGE_SIMPLE}\n              description={\n                searchKeywords ?\n                  t('conversations.noConversationsFound', 'No conversations found for \"{{keywords}}\"', { keywords: searchKeywords }) :\n                  t('conversations.noConversations')\n              }\n            >\n              {!searchKeywords && (\n                <Button\n                  type=\"primary\"\n                  icon={<PlusOutlined />}\n                  onClick={handleCreateConversation}\n                >\n                  {dialogs.length === 0\n                    ? t('dialog.createFirstDialog', 'Create Your First Dialog')\n                    : dialogs.length === 1\n                      ? t('conversations.startNewConversation', 'Start New Conversation')\n                      : t('conversations.chooseDialogAndStart', 'Choose Dialog & Start Chat')\n                  }\n                </Button>\n              )}\n            </Empty>\n          </div>\n        ) : (\n          <Collapse\n            defaultActiveKey={groupedConversations.map((_, index) => index.toString())}\n            className={styles.dialogGroups}\n          >\n            {groupedConversations.map((group, index) => (\n              <Panel\n                header={\n                  <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>\n                    <span style={{ fontWeight: 'bold', fontSize: '16px' }}>\n                      {group.dialog.name}\n                    </span>\n                    <Badge\n                      count={group.conversations.length}\n                      style={{ backgroundColor: '#1890ff' }}\n                    />\n                  </div>\n                }\n                key={index.toString()}\n              >\n                <Row gutter={[12, 12]} className={styles.conversationsGrid}>\n                  {group.conversations.map((conversation) => (\n                    <Col xs={24} sm={12} md={8} lg={6} xl={4} xxl={3} key={conversation.id} className={styles.conversationCol}>\n                      <ConversationCard\n                        conversation={conversation}\n                        onDelete={handleDeleteConversation}\n                      />\n                    </Col>\n                  ))}\n                </Row>\n              </Panel>\n            ))}\n          </Collapse>\n        )}\n      </Card>\n\n      {/* Dialog选择器 */}\n      <DialogSelector\n        visible={dialogSelectorVisible}\n        dialogs={dialogs}\n        onSelect={handleDialogSelect}\n        onCancel={handleDialogSelectorCancel}\n        loading={loading}\n      />\n    </div>\n  );\n};\n\nconst ConversationsPage: React.FC = () => {\n  return (\n    <AppLayout\n      showSearch={false}\n    >\n      <ConversationsContent />\n    </AppLayout>\n  );\n};\n\nexport default ConversationsPage;\n", "__isJSFile": true, "__absFile": "/home/<USER>/ragflow/hbweb/src/pages/conversations/index.tsx"}, "19": {"path": "/ai-models", "file": "@/pages/ai-models/index.tsx", "id": "19", "absPath": "/ai-models", "__content": "import React, { useState } from 'react';\nimport {\n  Card,\n  Row,\n  Col,\n  Button,\n  Modal,\n  Form,\n  Input,\n  Select,\n  message,\n  Spin,\n  Typography,\n  Space,\n  Tag,\n  Divider,\n  List,\n  Tooltip,\n  Popconfirm,\n  Table,\n  InputNumber,\n  Switch,\n  Collapse,\n} from 'antd';\nimport {\n  PlusOutlined,\n  SettingOutlined,\n  DeleteOutlined,\n  ApiOutlined,\n  EyeOutlined,\n  EyeInvisibleOutlined,\n  RobotOutlined,\n  CloudOutlined,\n} from '@ant-design/icons';\nimport { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';\nimport llmService, { ILLMFactory, IMyLLMFactory, ISetApiKeyParams, IAddLLMParams } from '@/services/llm-service';\nimport userService from '@/services/user-service';\nimport AppLayout from '@/components/AppLayout';\nimport { useTranslate } from '@/hooks/use-i18n';\nimport styles from './index.less';\n\nconst { Title, Text } = Typography;\nconst { Option } = Select;\nconst { Panel } = Collapse;\n\ninterface ILLMModel {\n  type: string;\n  name: string;\n  used_token: number;\n}\n\nconst AIModels: React.FC = () => {\n  const t = useTranslate();\n  const [apiKeyModalVisible, setApiKeyModalVisible] = useState(false);\n  const [addModelModalVisible, setAddModelModalVisible] = useState(false);\n  const [defaultModelsModalVisible, setDefaultModelsModalVisible] = useState(false);\n  const [selectedFactory, setSelectedFactory] = useState<string>('');\n  const [selectedModel, setSelectedModel] = useState<any>(null);\n  const [apiKeyForm] = Form.useForm();\n  const [addModelForm] = Form.useForm();\n  const [defaultModelsForm] = Form.useForm();\n  const [showApiKey, setShowApiKey] = useState<{ [key: string]: boolean }>({});\n  const queryClient = useQueryClient();\n\n  // 获取LLM工厂列表\n  const { data: factories, isLoading: factoriesLoading, error: factoriesError } = useQuery({\n    queryKey: ['llm-factories'],\n    queryFn: async () => {\n      console.log('Fetching LLM factories...');\n      const { data } = await userService.factories_list();\n      console.log('LLM factories response:', data);\n      return data?.data || [];\n    },\n  });\n\n  // 获取我的LLM列表\n  const { data: myLLMs, isLoading: myLLMsLoading, refetch: refetchMyLLMs, error: myLLMsError } = useQuery({\n    queryKey: ['my-llms'],\n    queryFn: async () => {\n      console.log('Fetching my LLMs...');\n      const { data } = await userService.my_llm();\n      console.log('My LLMs response:', data);\n      console.log('My LLMs data structure:', data?.data);\n      return data?.data || {};\n    },\n  });\n\n  // 获取可用模型列表\n  const { data: availableModels, isLoading: modelsLoading } = useQuery({\n    queryKey: ['available-models'],\n    queryFn: async () => {\n      const { data } = await userService.llm_list();\n      return data?.data || {};\n    },\n  });\n\n  // 设置API Key\n  const { mutateAsync: setApiKey, isPending: settingApiKey } = useMutation({\n    mutationFn: async (params: ISetApiKeyParams) => {\n      const { data } = await userService.set_api_key(params);\n      return data;\n    },\n    onSuccess: () => {\n      message.success(t('aiModels.configureSuccess'));\n      setApiKeyModalVisible(false);\n      apiKeyForm.resetFields();\n      refetchMyLLMs();\n    },\n    onError: (error: any) => {\n      message.error(error?.message || t('aiModels.configureFailed', 'Failed to configure API Key'));\n    },\n  });\n\n  // 添加LLM模型\n  const { mutateAsync: addLLM, isPending: addingLLM } = useMutation({\n    mutationFn: async (params: IAddLLMParams) => {\n      const { data } = await userService.add_llm(params);\n      return data;\n    },\n    onSuccess: () => {\n      message.success('Model added successfully!');\n      setAddModelModalVisible(false);\n      addModelForm.resetFields();\n      refetchMyLLMs();\n    },\n    onError: (error: any) => {\n      message.error(error?.message || t('aiModels.addFailed', 'Failed to add model'));\n    },\n  });\n\n  // 删除模型\n  const { mutateAsync: deleteModel } = useMutation({\n    mutationFn: async (params: { llm_factory: string; llm_name: string }) => {\n      const { data } = await userService.delete_llm(params);\n      return data;\n    },\n    onSuccess: () => {\n      message.success('Model deleted successfully!');\n      refetchMyLLMs();\n    },\n    onError: (error: any) => {\n      message.error(error?.message || 'Failed to delete model');\n    },\n  });\n\n  const handleSetApiKey = (factoryName: string) => {\n    setSelectedFactory(factoryName);\n    setApiKeyModalVisible(true);\n  };\n\n  const handleAddModel = (factoryName: string) => {\n    setSelectedFactory(factoryName);\n    setAddModelModalVisible(true);\n  };\n\n  const handleApiKeySubmit = async () => {\n    try {\n      const values = await apiKeyForm.validateFields();\n      await setApiKey({\n        llm_factory: selectedFactory,\n        api_key: values.api_key,\n        api_base: values.base_url,\n      });\n    } catch (error) {\n      console.error('API Key form validation failed:', error);\n    }\n  };\n\n  const handleAddModelSubmit = async () => {\n    try {\n      const values = await addModelForm.validateFields();\n      await addLLM({\n        llm_factory: values.llm_factory,\n        llm_name: values.llm_name,\n        model_type: values.model_type,\n        api_key: values.api_key,\n        api_base: values.base_url,\n        max_tokens: values.max_tokens,\n      });\n    } catch (error) {\n      console.error('Add model form validation failed:', error);\n    }\n  };\n\n  const handleDeleteModel = async (factoryName: string, modelName: string) => {\n    await deleteModel({\n      llm_factory: factoryName,\n      llm_name: modelName,\n    });\n  };\n\n  const toggleApiKeyVisibility = (factoryName: string) => {\n    setShowApiKey(prev => ({\n      ...prev,\n      [factoryName]: !prev[factoryName]\n    }));\n  };\n\n  // 获取租户信息（包含默认模型设置）\n  const { data: tenantInfo } = useQuery({\n    queryKey: ['tenant-info'],\n    queryFn: async () => {\n      const { data } = await userService.get_tenant_info();\n      return data?.data;\n    },\n  });\n\n  // 设置默认模型\n  const { mutateAsync: setDefaultModels, isPending: settingDefaultModels } = useMutation({\n    mutationFn: async (params: any) => {\n      console.log('Setting default models with params:', params);\n      console.log('TenantInfo:', tenantInfo);\n\n      // 按照原版web的格式，包含tenant_id和name\n      const payload = {\n        tenant_id: tenantInfo?.tenant_id || '',\n        name: tenantInfo?.name || '',\n        ...params,\n      };\n\n      console.log('Final payload:', payload);\n      const { data } = await userService.set_tenant_info(payload);\n      return data;\n    },\n    onSuccess: (data) => {\n      console.log('Set default models success:', data);\n      if (data?.code === 0) {\n        message.success(t('aiModels.setDefaultSuccess', 'Default models set successfully!'));\n        setDefaultModelsModalVisible(false);\n        defaultModelsForm.resetFields();\n        queryClient.invalidateQueries({ queryKey: ['tenant-info'] });\n      } else {\n        message.error(data?.message || t('aiModels.setDefaultFailed', 'Failed to set default models'));\n      }\n    },\n    onError: (error: any) => {\n      console.error('Failed to set default models:', error);\n      message.error(error?.message || t('aiModels.setDefaultFailed', 'Failed to set default models'));\n    },\n  });\n\n  const handleSetDefaultModels = () => {\n    setDefaultModelsModalVisible(true);\n    // 设置当前默认值\n    if (tenantInfo) {\n      defaultModelsForm.setFieldsValue({\n        llm_id: tenantInfo.llm_id || '',\n        embd_id: tenantInfo.embd_id || '',\n        asr_id: tenantInfo.asr_id || '',\n        img2txt_id: tenantInfo.img2txt_id || '',\n        rerank_id: tenantInfo.rerank_id || '',\n        tts_id: tenantInfo.tts_id || '',\n      });\n    }\n  };\n\n  const handleDefaultModelsSubmit = async () => {\n    try {\n      const values = await defaultModelsForm.validateFields();\n      await setDefaultModels(values);\n    } catch (error) {\n      console.error('Validation failed:', error);\n    }\n  };\n\n  const getModelTypeColor = (type: string) => {\n    const colors: Record<string, string> = {\n      'chat': 'blue',\n      'embedding': 'green',\n      'rerank': 'orange',\n      'image2text': 'purple',\n      'speech2text': 'cyan',\n      'tts': 'magenta',\n    };\n    return colors[type] || 'default';\n  };\n\n  const getModelTypeIcon = (type: string) => {\n    const icons: Record<string, React.ReactNode> = {\n      'chat': <RobotOutlined />,\n      'embedding': <CloudOutlined />,\n      'rerank': <ApiOutlined />,\n    };\n    return icons[type] || <SettingOutlined />;\n  };\n\n  if (factoriesLoading || myLLMsLoading) {\n    return (\n      <AppLayout>\n        <div className={styles.loadingContainer}>\n          <Spin size=\"large\" />\n        </div>\n      </AppLayout>\n    );\n  }\n\n  return (\n    <AppLayout>\n      <div className={styles.aiModelsContainer}>\n      <div className={styles.header}>\n        <Title level={2}>{t('aiModels.title')}</Title>\n        <Text type=\"secondary\">\n          {t('aiModels.manageDescription', 'Manage your AI model providers and configurations')}\n        </Text>\n      </div>\n\n      <div className={styles.actions}>\n        <Space>\n          <Button\n            icon={<SettingOutlined />}\n            onClick={handleSetDefaultModels}\n          >\n            {t('aiModels.setDefaultModels', 'Set Default Models')}\n          </Button>\n          <Button\n            type=\"primary\"\n            icon={<PlusOutlined />}\n            onClick={() => setAddModelModalVisible(true)}\n          >\n            {t('aiModels.addModel')}\n          </Button>\n        </Space>\n      </div>\n\n      <Row gutter={[24, 24]}>\n        {/* Available Factories */}\n        <Col span={12}>\n          <Card title={t('aiModels.availableProviders', 'Available Providers')} className={styles.factoryCard}>\n            {factoriesLoading ? (\n              <div style={{ textAlign: 'center', padding: '40px' }}>\n                <Spin size=\"large\" />\n                <div style={{ marginTop: '16px' }}>\n                  <Text type=\"secondary\">{t('aiModels.loadingProviders', 'Loading providers...')}</Text>\n                </div>\n              </div>\n            ) : factoriesError ? (\n              <div style={{ textAlign: 'center', padding: '40px' }}>\n                <Text type=\"danger\">{t('aiModels.loadProvidersFailed', 'Failed to load providers: {{error}}', { error: factoriesError.message })}</Text>\n                <div style={{ marginTop: '16px' }}>\n                  <Button onClick={() => window.location.reload()}>{t('common.retry', 'Retry')}</Button>\n                </div>\n              </div>\n            ) : !factories || factories.length === 0 ? (\n              <div style={{ textAlign: 'center', padding: '40px' }}>\n                <Text type=\"secondary\">{t('aiModels.noProviders', 'No providers available')}</Text>\n              </div>\n            ) : (\n              <List\n                dataSource={factories}\n                renderItem={(factory: ILLMFactory) => (\n                <List.Item\n                  actions={[\n                    <Button\n                      type=\"link\"\n                      icon={<SettingOutlined />}\n                      onClick={() => handleSetApiKey(factory.name)}\n                    >\n                      {t('aiModels.configure')}\n                    </Button>,\n                  ]}\n                >\n                  <List.Item.Meta\n                    avatar={<div className={styles.factoryLogo}>{factory.name[0]}</div>}\n                    title={factory.name}\n                    description={\n                      <Space direction=\"vertical\" size=\"small\">\n                        <Text type=\"secondary\">{factory.tags}</Text>\n                        <Space wrap>\n                          {factory.model_types?.map((type) => (\n                            <Tag\n                              key={type}\n                              color={getModelTypeColor(type)}\n                              icon={getModelTypeIcon(type)}\n                            >\n                              {type}\n                            </Tag>\n                          ))}\n                        </Space>\n                      </Space>\n                    }\n                  />\n                </List.Item>\n              )}\n            />\n            )}\n          </Card>\n        </Col>\n\n        {/* My Models */}\n        <Col span={12}>\n          <Card title={t('aiModels.myModels', 'My Models')} className={styles.myModelsCard}>\n            {myLLMsLoading ? (\n              <div style={{ textAlign: 'center', padding: '40px' }}>\n                <Spin size=\"large\" />\n                <div style={{ marginTop: '16px' }}>\n                  <Text type=\"secondary\">{t('aiModels.loadingModels', 'Loading models...')}</Text>\n                </div>\n              </div>\n            ) : myLLMsError ? (\n              <div style={{ textAlign: 'center', padding: '40px' }}>\n                <Text type=\"danger\">Failed to load models: {myLLMsError.message}</Text>\n                <div style={{ marginTop: '16px' }}>\n                  <Button onClick={() => refetchMyLLMs()}>{t('common.retry', 'Retry')}</Button>\n                </div>\n              </div>\n            ) : !myLLMs || Object.keys(myLLMs).length === 0 ? (\n              <div className={styles.emptyState}>\n                <Text type=\"secondary\">{t('aiModels.noModels', 'No models configured yet')}</Text>\n              </div>\n            ) : (\n              <Collapse>\n                {myLLMs && Object.entries(myLLMs).map(([factoryName, factoryData]: [string, IMyLLMFactory]) => (\n                  <Panel\n                    key={factoryName}\n                    header={\n                      <Space>\n                        <Text strong>{factoryName}</Text>\n                        <Tag>{factoryData.llm?.length || 0} models</Tag>\n                      </Space>\n                    }\n                  >\n                    <List\n                      dataSource={factoryData.llm || []}\n                      renderItem={(model) => (\n                        <List.Item\n                          actions={[\n                            <Tooltip title=\"Delete Model\">\n                              <Popconfirm\n                                title=\"Are you sure to delete this model?\"\n                                onConfirm={() => handleDeleteModel(factoryName, model.name)}\n                                okText=\"Yes\"\n                                cancelText=\"No\"\n                              >\n                                <Button\n                                  type=\"link\"\n                                  danger\n                                  icon={<DeleteOutlined />}\n                                />\n                              </Popconfirm>\n                            </Tooltip>,\n                          ]}\n                        >\n                          <List.Item.Meta\n                            title={model.name}\n                            description={\n                              <Space>\n                                <Tag color={getModelTypeColor(model.type)}>\n                                  {model.type}\n                                </Tag>\n                                <Text type=\"secondary\">\n                                  Used tokens: {model.used_token?.toLocaleString() || 0}\n                                </Text>\n                              </Space>\n                            }\n                          />\n                        </List.Item>\n                      )}\n                    />\n                  </Panel>\n                ))}\n              </Collapse>\n            )}\n          </Card>\n        </Col>\n      </Row>\n\n      {/* API Key Modal */}\n      <Modal\n        title={t('aiModels.configureFactory', 'Configure {{factory}}', { factory: selectedFactory })}\n        open={apiKeyModalVisible}\n        onOk={handleApiKeySubmit}\n        onCancel={() => {\n          setApiKeyModalVisible(false);\n          apiKeyForm.resetFields();\n        }}\n        confirmLoading={settingApiKey}\n        width={600}\n      >\n        <Form form={apiKeyForm} layout=\"vertical\">\n          {/* API Key - 对于本地部署的模型（Ollama, VLLM, Xinference）不是必需的 */}\n          <Form.Item\n            name=\"api_key\"\n            label={t('aiModels.apiKey')}\n            tooltip={t('aiModels.apiKeyTooltip')}\n            rules={[\n              {\n                required: !['Ollama', 'VLLM', 'Xinference'].includes(selectedFactory),\n                message: t('aiModels.enterApiKey')\n              }\n            ]}\n          >\n            <Input.Password placeholder={t('aiModels.apiKeyPlaceholder', 'Enter API key (optional for local models)')} />\n          </Form.Item>\n\n          {/* Base URL - 对于本地部署的模型是必需的 */}\n          <Form.Item\n            name=\"base_url\"\n            label={t('aiModels.baseUrl', 'Base URL')}\n            tooltip={t('aiModels.baseUrlTooltip', 'The base URL for the model API endpoint')}\n            rules={[\n              {\n                required: ['Ollama', 'VLLM', 'Xinference'].includes(selectedFactory),\n                message: t('aiModels.baseUrlRequired', 'Please input Base URL!')\n              }\n            ]}\n          >\n            <Input\n              placeholder={\n                selectedFactory === 'Ollama' ? 'http://localhost:11434' :\n                selectedFactory === 'VLLM' ? 'http://localhost:8000' :\n                selectedFactory === 'Xinference' ? 'http://localhost:9997' :\n                t('aiModels.enterBaseUrl', 'Enter base URL')\n              }\n            />\n          </Form.Item>\n        </Form>\n      </Modal>\n\n      {/* Add Model Modal */}\n      <Modal\n        title={t('aiModels.addModel')}\n        open={addModelModalVisible}\n        onOk={handleAddModelSubmit}\n        onCancel={() => {\n          setAddModelModalVisible(false);\n          addModelForm.resetFields();\n        }}\n        confirmLoading={addingLLM}\n        width={600}\n      >\n        <Form form={addModelForm} layout=\"vertical\">\n          <Form.Item\n            name=\"llm_factory\"\n            label=\"Provider\"\n            rules={[{ required: true, message: 'Please select a provider!' }]}\n          >\n            <Select placeholder=\"Select provider\">\n              {factories?.map((factory: ILLMFactory) => (\n                <Option key={factory.name} value={factory.name}>\n                  {factory.name}\n                </Option>\n              ))}\n            </Select>\n          </Form.Item>\n\n          <Form.Item\n            name=\"model_type\"\n            label=\"Model Type\"\n            rules={[{ required: true, message: 'Please select model type!' }]}\n            initialValue=\"chat\"\n          >\n            <Select placeholder=\"Select model type\">\n              <Option value=\"chat\">Chat</Option>\n              <Option value=\"embedding\">Embedding</Option>\n              <Option value=\"rerank\">Rerank</Option>\n              <Option value=\"image2text\">Image to Text</Option>\n              <Option value=\"speech2text\">Speech to Text</Option>\n              <Option value=\"tts\">Text to Speech</Option>\n            </Select>\n          </Form.Item>\n\n          <Form.Item noStyle dependencies={['llm_factory']}>\n            {({ getFieldValue }) => {\n              const selectedProvider = getFieldValue('llm_factory');\n              return (\n                <Form.Item\n                  name=\"llm_name\"\n                  label={selectedProvider === 'Xinference' ? t('aiModels.modelUID', 'Model UID') : t('aiModels.modelName', 'Model Name')}\n                  tooltip={\n                    selectedProvider === 'Xinference'\n                      ? 'The unique identifier of the model in Xinference'\n                      : 'The name of the model'\n                  }\n                  rules={[{ required: true, message: t('aiModels.modelNameRequired', 'Please input model name!') }]}\n                >\n                  <Input\n                    placeholder={\n                      selectedProvider === 'Ollama' ? 'e.g., llama2, mistral' :\n                      selectedProvider === 'VLLM' ? 'e.g., meta-llama/Llama-2-7b-chat-hf' :\n                      selectedProvider === 'Xinference' ? 'e.g., model-uid-from-xinference' :\n                      t('aiModels.enterModelName', 'Enter model name')\n                    }\n                  />\n                </Form.Item>\n              );\n            }}\n          </Form.Item>\n\n          <Form.Item noStyle dependencies={['llm_factory']}>\n            {({ getFieldValue }) => {\n              const selectedProvider = getFieldValue('llm_factory');\n              const isLocalProvider = ['Ollama', 'VLLM', 'Xinference'].includes(selectedProvider);\n              return (\n                <Form.Item\n                  name=\"base_url\"\n                  label={t('aiModels.apiBaseUrl', 'API Base URL')}\n                  tooltip=\"The base URL for the model API endpoint\"\n                  rules={[\n                    {\n                      required: isLocalProvider,\n                      message: t('aiModels.apiBaseUrlRequired', 'Please input API Base URL!')\n                    }\n                  ]}\n                >\n                  <Input\n                    placeholder={\n                      selectedProvider === 'Ollama' ? 'http://localhost:11434' :\n                      selectedProvider === 'VLLM' ? 'http://localhost:8000' :\n                      selectedProvider === 'Xinference' ? 'http://localhost:9997' :\n                      t('aiModels.enterApiBaseUrl', 'Enter API base URL')\n                    }\n                  />\n                </Form.Item>\n              );\n            }}\n          </Form.Item>\n\n          <Form.Item noStyle dependencies={['llm_factory']}>\n            {({ getFieldValue }) => {\n              const selectedProvider = getFieldValue('llm_factory');\n              const isLocalProvider = ['Ollama', 'VLLM', 'Xinference'].includes(selectedProvider);\n              return (\n                <Form.Item\n                  name=\"api_key\"\n                  label=\"API Key\"\n                  tooltip=\"API key for authentication (optional for local deployments)\"\n                  rules={[\n                    {\n                      required: !isLocalProvider,\n                      message: 'Please input API Key!'\n                    }\n                  ]}\n                >\n                  <Input.Password placeholder={isLocalProvider ? \"Optional for local models\" : \"Enter API key\"} />\n                </Form.Item>\n              );\n            }}\n          </Form.Item>\n\n          <Form.Item\n            name=\"max_tokens\"\n            label=\"Max Tokens\"\n            tooltip=\"Maximum number of tokens the model can process\"\n            rules={[\n              { required: true, message: 'Please input max tokens!' },\n              { type: 'number', min: 1, message: 'Max tokens must be greater than 0!' }\n            ]}\n            initialValue={8192}\n          >\n            <InputNumber\n              placeholder=\"e.g., 8192\"\n              style={{ width: '100%' }}\n              min={1}\n            />\n          </Form.Item>\n\n          {/* Vision Support - 仅对chat模型显示 */}\n          <Form.Item noStyle dependencies={['model_type']}>\n            {({ getFieldValue }) =>\n              getFieldValue('model_type') === 'chat' && (\n                <Form.Item\n                  name=\"vision\"\n                  label=\"Vision Support\"\n                  tooltip=\"Enable if this model supports image understanding\"\n                  valuePropName=\"checked\"\n                >\n                  <Switch />\n                </Form.Item>\n              )\n            }\n          </Form.Item>\n        </Form>\n      </Modal>\n\n      {/* Set Default Models Modal */}\n      <Modal\n        title={t('aiModels.setDefaultModels', 'Set Default Models')}\n        open={defaultModelsModalVisible}\n        onOk={handleDefaultModelsSubmit}\n        onCancel={() => {\n          setDefaultModelsModalVisible(false);\n          defaultModelsForm.resetFields();\n        }}\n        confirmLoading={settingDefaultModels}\n        width={600}\n      >\n        <Form form={defaultModelsForm} layout=\"vertical\">\n          <Form.Item\n            name=\"llm_id\"\n            label=\"Chat Model\"\n            tooltip=\"Default model for chat conversations\"\n          >\n            <Select placeholder=\"Select chat model\" allowClear showSearch>\n              {myLLMs && Object.entries(myLLMs).map(([factoryName, factoryData]: [string, IMyLLMFactory]) =>\n                factoryData.llm?.filter(model => model.type === 'chat').map(model => (\n                  <Option key={`${model.name}@${factoryName}`} value={`${model.name}@${factoryName}`}>\n                    {model.name} ({factoryName})\n                  </Option>\n                ))\n              )}\n            </Select>\n          </Form.Item>\n\n          <Form.Item\n            name=\"embd_id\"\n            label=\"Embedding Model\"\n            tooltip=\"Default model for text embeddings\"\n          >\n            <Select placeholder=\"Select embedding model\" allowClear showSearch>\n              {myLLMs && Object.entries(myLLMs).map(([factoryName, factoryData]: [string, IMyLLMFactory]) =>\n                factoryData.llm?.filter(model => model.type === 'embedding').map(model => (\n                  <Option key={`${model.name}@${factoryName}`} value={`${model.name}@${factoryName}`}>\n                    {model.name} ({factoryName})\n                  </Option>\n                ))\n              )}\n            </Select>\n          </Form.Item>\n\n          <Form.Item\n            name=\"rerank_id\"\n            label=\"Rerank Model\"\n            tooltip=\"Default model for reranking search results\"\n          >\n            <Select placeholder=\"Select rerank model\" allowClear showSearch>\n              {myLLMs && Object.entries(myLLMs).map(([factoryName, factoryData]: [string, IMyLLMFactory]) =>\n                factoryData.llm?.filter(model => model.type === 'rerank').map(model => (\n                  <Option key={`${model.name}@${factoryName}`} value={`${model.name}@${factoryName}`}>\n                    {model.name} ({factoryName})\n                  </Option>\n                ))\n              )}\n            </Select>\n          </Form.Item>\n\n          <Form.Item\n            name=\"img2txt_id\"\n            label=\"Image to Text Model\"\n            tooltip=\"Default model for image to text conversion\"\n          >\n            <Select placeholder=\"Select image to text model\" allowClear showSearch>\n              {myLLMs && Object.entries(myLLMs).map(([factoryName, factoryData]: [string, IMyLLMFactory]) =>\n                factoryData.llm?.filter(model => model.type === 'image2text').map(model => (\n                  <Option key={`${model.name}@${factoryName}`} value={`${model.name}@${factoryName}`}>\n                    {model.name} ({factoryName})\n                  </Option>\n                ))\n              )}\n            </Select>\n          </Form.Item>\n\n          <Form.Item\n            name=\"asr_id\"\n            label=\"Speech to Text Model\"\n            tooltip=\"Default model for speech recognition\"\n          >\n            <Select placeholder=\"Select speech to text model\" allowClear showSearch>\n              {myLLMs && Object.entries(myLLMs).map(([factoryName, factoryData]: [string, IMyLLMFactory]) =>\n                factoryData.llm?.filter(model => model.type === 'speech2text').map(model => (\n                  <Option key={`${model.name}@${factoryName}`} value={`${model.name}@${factoryName}`}>\n                    {model.name} ({factoryName})\n                  </Option>\n                ))\n              )}\n            </Select>\n          </Form.Item>\n\n          <Form.Item\n            name=\"tts_id\"\n            label=\"Text to Speech Model\"\n            tooltip=\"Default model for text to speech conversion\"\n          >\n            <Select placeholder=\"Select text to speech model\" allowClear showSearch>\n              {myLLMs && Object.entries(myLLMs).map(([factoryName, factoryData]: [string, IMyLLMFactory]) =>\n                factoryData.llm?.filter(model => model.type === 'tts').map(model => (\n                  <Option key={`${model.name}@${factoryName}`} value={`${model.name}@${factoryName}`}>\n                    {model.name} ({factoryName})\n                  </Option>\n                ))\n              )}\n            </Select>\n          </Form.Item>\n        </Form>\n      </Modal>\n      </div>\n    </AppLayout>\n  );\n};\n\nexport default AIModels;\n", "__isJSFile": true, "__absFile": "/home/<USER>/ragflow/hbweb/src/pages/ai-models/index.tsx"}, "20": {"path": "/settings", "file": "@/pages/settings/index.tsx", "id": "20", "absPath": "/settings", "__content": "import React, { useState } from 'react';\nimport {\n  Card,\n  Row,\n  Col,\n  Form,\n  Input,\n  Button,\n  message,\n  Spin,\n  Typography,\n  Divider,\n  Space,\n  Avatar,\n  Upload,\n  Badge,\n  Descriptions,\n  Alert,\n} from 'antd';\nimport {\n  UserOutlined,\n  MailOutlined,\n  LockOutlined,\n  SaveOutlined,\n  UploadOutlined,\n  CheckCircleOutlined,\n  ExclamationCircleOutlined,\n  CloseCircleOutlined,\n  InfoCircleOutlined,\n} from '@ant-design/icons';\nimport { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';\nimport userService, { IUserInfo, IUserSettingParams, ISystemStatus } from '@/services/user-service';\nimport AppLayout from '@/components/AppLayout';\nimport { useTranslate } from '@/hooks/use-i18n';\nimport styles from './index.less';\n\nconst { Title, Text } = Typography;\n\nconst Settings: React.FC = () => {\n  const t = useTranslate();\n  const [profileForm] = Form.useForm();\n  const [passwordForm] = Form.useForm();\n  const queryClient = useQueryClient();\n\n  // 获取用户信息\n  const { data: userInfo, isLoading: userInfoLoading } = useQuery({\n    queryKey: ['user-info'],\n    queryFn: async () => {\n      const { data } = await userService.getUserInfo();\n      return data?.data;\n    },\n  });\n\n  // 当用户信息加载完成后，设置表单默认值\n  React.useEffect(() => {\n    if (userInfo) {\n      profileForm.setFieldsValue({\n        nickname: userInfo.nickname || '',\n        email: userInfo.email || '',\n      });\n    }\n  }, [userInfo, profileForm]);\n\n  // 获取系统状态\n  const { data: systemStatus, isLoading: systemStatusLoading } = useQuery({\n    queryKey: ['system-status'],\n    queryFn: async () => {\n      const { data } = await userService.getSystemStatus();\n      return data?.data;\n    },\n  });\n\n  // 获取系统版本\n  const { data: systemVersion } = useQuery({\n    queryKey: ['system-version'],\n    queryFn: async () => {\n      const { data } = await userService.getSystemVersion();\n      return data?.data;\n    },\n  });\n\n  // 更新用户设置\n  const { mutateAsync: updateUserSetting, isPending: updatingProfile } = useMutation({\n    mutationFn: async (params: IUserSettingParams) => {\n      const { data } = await userService.updateUserSetting(params);\n      return data;\n    },\n    onSuccess: () => {\n      message.success(t('settings.updateSuccess'));\n      queryClient.invalidateQueries({ queryKey: ['user-info'] });\n    },\n    onError: (error: any) => {\n      message.error(error?.message || t('settings.updateFailed', 'Failed to update profile'));\n    },\n  });\n\n  // 更新密码\n  const { mutateAsync: updatePassword, isPending: updatingPassword } = useMutation({\n    mutationFn: async (params: IUserSettingParams) => {\n      const { data } = await userService.updateUserSetting(params);\n      return data;\n    },\n    onSuccess: () => {\n      message.success('Password updated successfully!');\n      passwordForm.resetFields();\n    },\n    onError: (error: any) => {\n      message.error(error?.message || t('settings.passwordUpdateFailed', 'Failed to update password'));\n    },\n  });\n\n  const handleProfileSubmit = async () => {\n    try {\n      const values = await profileForm.validateFields();\n      await updateUserSetting(values);\n    } catch (error) {\n      console.error('Validation failed:', error);\n    }\n  };\n\n  const handlePasswordSubmit = async () => {\n    try {\n      const values = await passwordForm.validateFields();\n      await updatePassword(values);\n    } catch (error) {\n      console.error('Validation failed:', error);\n    }\n  };\n\n  const getStatusIcon = (status: string) => {\n    switch (status) {\n      case 'green':\n        return <CheckCircleOutlined style={{ color: '#52c41a' }} />;\n      case 'yellow':\n        return <ExclamationCircleOutlined style={{ color: '#faad14' }} />;\n      case 'red':\n        return <CloseCircleOutlined style={{ color: '#ff4d4f' }} />;\n      default:\n        return <InfoCircleOutlined style={{ color: '#1890ff' }} />;\n    }\n  };\n\n  const getStatusColor = (status: string) => {\n    switch (status) {\n      case 'green':\n        return 'success';\n      case 'yellow':\n        return 'warning';\n      case 'red':\n        return 'error';\n      default:\n        return 'default';\n    }\n  };\n\n  if (userInfoLoading) {\n    return (\n      <AppLayout>\n        <div className={styles.loadingContainer}>\n          <Spin size=\"large\" />\n        </div>\n      </AppLayout>\n    );\n  }\n\n  return (\n    <AppLayout>\n      <div className={styles.settingsContainer}>\n      <div className={styles.header}>\n        <Title level={2}>{t('settings.title')}</Title>\n        <Text type=\"secondary\">\n          {t('settings.manageDescription', 'Manage your account settings and system information')}\n        </Text>\n      </div>\n\n      <Row gutter={[24, 24]}>\n        {/* Profile Settings */}\n        <Col span={12}>\n          <Card title={t('settings.profileSettings', 'Profile Settings')} className={styles.profileCard}>\n            <div className={styles.avatarSection}>\n              <Avatar\n                size={80}\n                icon={<UserOutlined />}\n                src={userInfo?.avatar}\n                className={styles.avatar}\n              />\n              <Upload\n                showUploadList={false}\n                beforeUpload={() => false}\n              >\n                <Button icon={<UploadOutlined />} size=\"small\">\n                  {t('settings.changeAvatar', 'Change Avatar')}\n                </Button>\n              </Upload>\n            </div>\n\n            <Divider />\n\n            <Form\n              form={profileForm}\n              layout=\"vertical\"\n              onFinish={handleProfileSubmit}\n            >\n              <Form.Item\n                name=\"nickname\"\n                label={t('auth.nickname')}\n                rules={[{ required: true, message: t('auth.nicknameRequired') }]}\n              >\n                <Input\n                  prefix={<UserOutlined />}\n                  placeholder={t('settings.nicknamePlaceholder', 'Enter your nickname')}\n                />\n              </Form.Item>\n\n              <Form.Item\n                name=\"email\"\n                label={t('auth.email', 'Email')}\n                rules={[\n                  { required: true, message: t('settings.emailRequired', 'Please input your email!') },\n                  { type: 'email', message: t('settings.emailInvalid', 'Please enter a valid email!') },\n                ]}\n              >\n                <Input\n                  prefix={<MailOutlined />}\n                  placeholder={t('settings.emailPlaceholder', 'Enter your email')}\n                />\n              </Form.Item>\n\n              <Form.Item>\n                <Button\n                  type=\"primary\"\n                  htmlType=\"submit\"\n                  icon={<SaveOutlined />}\n                  loading={updatingProfile}\n                  block\n                >\n                  {t('settings.updateProfile')}\n                </Button>\n              </Form.Item>\n            </Form>\n          </Card>\n        </Col>\n\n        {/* Password Settings */}\n        <Col span={12}>\n          <Card title={t('settings.passwordSettings', 'Password Settings')} className={styles.passwordCard}>\n            <Form\n              form={passwordForm}\n              layout=\"vertical\"\n              onFinish={handlePasswordSubmit}\n            >\n              <Form.Item\n                name=\"password\"\n                label={t('settings.currentPassword', 'Current Password')}\n                rules={[{ required: true, message: t('settings.currentPasswordRequired', 'Please input your current password!') }]}\n              >\n                <Input.Password\n                  prefix={<LockOutlined />}\n                  placeholder={t('settings.currentPasswordPlaceholder', 'Enter current password')}\n                />\n              </Form.Item>\n\n              <Form.Item\n                name=\"new_password\"\n                label={t('settings.newPassword', 'New Password')}\n                rules={[\n                  { required: true, message: t('settings.newPasswordRequired', 'Please input your new password!') },\n                  { min: 6, message: t('settings.passwordMinLength', 'Password must be at least 6 characters!') },\n                ]}\n              >\n                <Input.Password\n                  prefix={<LockOutlined />}\n                  placeholder={t('settings.newPasswordPlaceholder', 'Enter new password')}\n                />\n              </Form.Item>\n\n              <Form.Item\n                name=\"confirm_password\"\n                label={t('settings.confirmPassword', 'Confirm New Password')}\n                dependencies={['new_password']}\n                rules={[\n                  { required: true, message: t('settings.confirmPasswordRequired', 'Please confirm your new password!') },\n                  ({ getFieldValue }) => ({\n                    validator(_, value) {\n                      if (!value || getFieldValue('new_password') === value) {\n                        return Promise.resolve();\n                      }\n                      return Promise.reject(new Error(t('settings.passwordMismatch', 'Passwords do not match!')));\n                    },\n                  }),\n                ]}\n              >\n                <Input.Password\n                  prefix={<LockOutlined />}\n                  placeholder={t('settings.confirmPasswordPlaceholder', 'Confirm new password')}\n                />\n              </Form.Item>\n\n              <Form.Item>\n                <Button\n                  type=\"primary\"\n                  htmlType=\"submit\"\n                  icon={<SaveOutlined />}\n                  loading={updatingPassword}\n                  block\n                >\n                  {t('settings.updatePassword', 'Update Password')}\n                </Button>\n              </Form.Item>\n            </Form>\n          </Card>\n        </Col>\n\n        {/* System Information */}\n        <Col span={24}>\n          <Card title={t('settings.systemInformation', 'System Information')} className={styles.systemCard}>\n            <Row gutter={[24, 24]}>\n              <Col span={12}>\n                <Descriptions title={t('settings.versionInformation', 'Version Information')} bordered size=\"small\">\n                  <Descriptions.Item label={t('settings.version', 'Version')} span={3}>\n                    {systemVersion?.version || t('common.unknown', 'Unknown')}\n                  </Descriptions.Item>\n                  <Descriptions.Item label={t('settings.userId', 'User ID')} span={3}>\n                    {userInfo?.id}\n                  </Descriptions.Item>\n                  <Descriptions.Item label={t('settings.created', 'Created')} span={3}>\n                    {userInfo?.create_time}\n                  </Descriptions.Item>\n                  <Descriptions.Item label={t('settings.lastUpdated', 'Last Updated')} span={3}>\n                    {userInfo?.update_time}\n                  </Descriptions.Item>\n                </Descriptions>\n              </Col>\n\n              <Col span={12}>\n                <div className={styles.systemStatus}>\n                  <Title level={4}>{t('settings.systemStatus', 'System Status')}</Title>\n                  {systemStatusLoading ? (\n                    <Spin />\n                  ) : (\n                    <Space direction=\"vertical\" style={{ width: '100%' }}>\n                      {systemStatus && Object.entries(systemStatus).map(([key, status]: [string, any]) => (\n                        <div key={key} className={styles.statusItem}>\n                          <Space>\n                            <Badge\n                              status={getStatusColor(status.status) as any}\n                              text={\n                                <Space>\n                                  {getStatusIcon(status.status)}\n                                  <Text strong>{key.replace('_', ' ').toUpperCase()}</Text>\n                                  <Text type=\"secondary\">({status.elapsed}ms)</Text>\n                                </Space>\n                              }\n                            />\n                          </Space>\n                          {status.error && (\n                            <Alert\n                              message={status.error}\n                              type=\"error\"\n                              size=\"small\"\n                              style={{ marginTop: 8 }}\n                            />\n                          )}\n                        </div>\n                      ))}\n                    </Space>\n                  )}\n                </div>\n              </Col>\n            </Row>\n          </Card>\n        </Col>\n      </Row>\n      </div>\n    </AppLayout>\n  );\n};\n\nexport default Settings;\n", "__isJSFile": true, "__absFile": "/home/<USER>/ragflow/hbweb/src/pages/settings/index.tsx"}, "21": {"path": "/chatbot", "file": "@/pages/chatbot/index.tsx", "id": "21", "absPath": "/chatbot", "__content": "import React, { useState, useCallback, useRef } from 'react';\nimport { Card, Row, Col, Typography, Space, Divider, message, Tabs } from 'antd';\nimport { RobotOutlined, MessageOutlined, SettingOutlined } from '@ant-design/icons';\nimport AppLayout from '@/components/AppLayout';\nimport ChatBot from '@/components/ChatBot';\nimport ChatHistoryList, { ChatHistoryListRef } from '@/components/ChatBot/ChatHistoryList';\nimport { useTranslate } from '@/hooks/use-i18n';\nimport { useChatBotStats, useChatBotHistory } from '@/hooks/use-chatbot-hooks';\nimport { ASSISTANTS, getDefaultAssistant, AssistantType } from '@/constants/assistants';\nimport styles from './index.less';\n\nconst { Title, Text, Paragraph } = Typography;\n\nconst ChatBotPage: React.FC = () => {\n  const t = useTranslate();\n  const { stats, isLoading, fetchStats } = useChatBotStats();\n  const { getSessionDetails } = useChatBotHistory();\n\n  // 状态管理\n  const [selectedSessionId, setSelectedSessionId] = useState<string>('');\n  const [chatBotKey, setChatBotKey] = useState<number>(0); // 用于强制重新渲染ChatBot组件\n  const [currentAssistant, setCurrentAssistant] = useState<AssistantType>(getDefaultAssistant());\n  const chatHistoryListRef = useRef<ChatHistoryListRef>(null);\n\n  React.useEffect(() => {\n    fetchStats();\n  }, [fetchStats]);\n\n  // 处理会话选择\n  const handleSelectSession = useCallback((sessionId: string) => {\n    if (!sessionId) {\n      // 只有当前有选中会话时，才需要重置ChatBot组件\n      if (selectedSessionId) {\n        setSelectedSessionId('');\n        setChatBotKey(prev => prev + 1); // 重置ChatBot组件\n      }\n      return;\n    }\n\n    // 如果选择的是不同的会话，才需要重新渲染\n    if (sessionId !== selectedSessionId) {\n      setSelectedSessionId(sessionId);\n      setChatBotKey(prev => prev + 1); // 强制重新渲染ChatBot组件\n    }\n  }, [selectedSessionId]);\n\n  // 刷新对话记录列表\n  const handleRefreshSessions = useCallback(() => {\n    if (chatHistoryListRef.current) {\n      chatHistoryListRef.current.refreshSessions();\n    }\n  }, []);\n\n  // 处理助手切换\n  const handleAssistantChange = useCallback((assistantId: string) => {\n    const assistant = ASSISTANTS.find(a => a.id === assistantId);\n    if (assistant && assistant.id !== currentAssistant.id) {\n      // 切换助手时初始化对话页面\n      setSelectedSessionId(''); // 清空当前选中的会话\n      setCurrentAssistant(assistant);\n      setChatBotKey(prev => prev + 1); // 强制重新渲染ChatBot组件，这会重置所有状态\n      message.success(t('chatbot.success.assistantSwitched'));\n    }\n  }, [currentAssistant.id, t]);\n\n  return (\n    <AppLayout>\n      <div className={styles.chatBotPage}>\n        <div className={styles.pageHeader}>\n          <Space size=\"large\">\n            <RobotOutlined className={styles.headerIcon} />\n            <div>\n              <Title level={2} style={{ margin: 0 }}>\n                {t('chatbot.title')}\n              </Title>\n              <Text type=\"secondary\">{t('chatbot.description')}</Text>\n            </div>\n          </Space>\n        </div>\n\n        <Row gutter={[24, 24]} className={styles.content}>\n          {/* 聊天机器人主界面 */}\n          <Col xs={24} lg={16}>\n            <Card\n              title={\n                <Space>\n                  <MessageOutlined />\n                  {t('chatbot.subtitle')}\n                </Space>\n              }\n              className={styles.chatCard}\n              tabList={ASSISTANTS.map(assistant => ({\n                key: assistant.id,\n                tab: (\n                  <Space>\n                    <span style={{ fontSize: '16px' }}>{assistant.icon}</span>\n                    <span>{assistant.name}</span>\n                  </Space>\n                )\n              }))}\n              activeTabKey={currentAssistant.id}\n              onTabChange={handleAssistantChange}\n              tabProps={{\n                size: 'small',\n                type: 'card'\n              }}\n            >\n              <ChatBot\n                key={chatBotKey}\n                height={600}\n                enableStream={true}\n                systemPrompt={currentAssistant.systemPrompt}\n                selectedSessionId={selectedSessionId}\n                onRefreshSessions={handleRefreshSessions}\n              />\n            </Card>\n          </Col>\n\n          {/* 对话记录列表 */}\n          <Col xs={24} lg={8}>\n            <ChatHistoryList\n              ref={chatHistoryListRef}\n              onSelectSession={handleSelectSession}\n              selectedSessionId={selectedSessionId}\n              style={{ height: 600 }}\n            />\n          </Col>\n        </Row>\n      </div>\n    </AppLayout>\n  );\n};\n\nexport default ChatBotPage;\n", "__isJSFile": true, "__absFile": "/home/<USER>/ragflow/hbweb/src/pages/chatbot/index.tsx"}}, "apiRoutes": {}, "hasSrcDir": true, "npmClient": "cnpm", "umi": {"version": "4.4.11", "name": "<PERSON><PERSON>", "importSource": "umi", "cliName": "umi"}, "bundleStatus": {"done": false}, "react": {"version": "18.3.1", "path": "/home/<USER>/ragflow/hbweb/node_modules/react"}, "react-dom": {"version": "18.3.1", "path": "/home/<USER>/ragflow/hbweb/node_modules/react-dom"}, "appJS": {"path": "/home/<USER>/ragflow/hbweb/src/app.tsx", "exports": ["rootContainer"]}, "locale": "C", "globalCSS": ["/home/<USER>/ragflow/hbweb/src/global.less"], "globalJS": [], "overridesCSS": [], "bundler": "webpack", "git": {"originUrl": "https://github.com/adax-xu/hbweb.git"}, "framework": "react", "typescript": {"tsVersion": "5.8.3", "tslibVersion": "2.8.1"}, "faviconFiles": [], "port": 8001, "host": "0.0.0.0", "ip": "***************"}