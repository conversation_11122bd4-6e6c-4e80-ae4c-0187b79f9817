// @ts-nocheck
// This file is generated by <PERSON><PERSON> automatically
// DO NOT CHANGE IT MANUALLY!
import React from 'react';

export async function getRoutes() {
  const routes = {"1":{"path":"/login","id":"1"},"2":{"path":"/","redirect":"/dashboard","id":"2"},"3":{"path":"/dashboard","id":"3"},"4":{"path":"/ai-read","id":"4"},"5":{"path":"/text-to-image","id":"5"},"6":{"path":"/knowledge","id":"6"},"7":{"path":"/documents","id":"7"},"8":{"path":"/knowledge/:id","id":"8"},"9":{"path":"/knowledge/:id","redirect":"/knowledge/:id/dataset","parentId":"8","id":"9"},"10":{"path":"/knowledge/:id/dataset","parentId":"8","id":"10"},"11":{"path":"/knowledge/:id/setting","parentId":"8","id":"11"},"12":{"path":"/knowledge/:id/retrieval-testing","parentId":"8","id":"12"},"13":{"path":"/knowledge/:id/dataset/:docId/chunks","parentId":"8","id":"13"},"14":{"path":"/dialogs","id":"14"},"15":{"path":"/dialogs","exact":true,"parentId":"14","id":"15"},"16":{"path":"/dialogs/:id/view","parentId":"14","id":"16"},"17":{"path":"/chat","id":"17"},"18":{"path":"/conversations","id":"18"},"19":{"path":"/ai-models","id":"19"},"20":{"path":"/settings","id":"20"},"21":{"path":"/chatbot","id":"21"}} as const;
  return {
    routes,
    routeComponents: {
'1': React.lazy(() => import(/* webpackChunkName: "p__login__index" */'@/pages/login/index.tsx')),
'2': React.lazy(() => import('./EmptyRoute')),
'3': React.lazy(() => import(/* webpackChunkName: "p__dashboard__index" */'@/pages/dashboard/index.tsx')),
'4': React.lazy(() => import(/* webpackChunkName: "p__ai-read__index" */'@/pages/ai-read/index.tsx')),
'5': React.lazy(() => import(/* webpackChunkName: "p__text-to-image__index" */'@/pages/text-to-image/index.tsx')),
'6': React.lazy(() => import(/* webpackChunkName: "p__knowledge__index" */'@/pages/knowledge/index.tsx')),
'7': React.lazy(() => import(/* webpackChunkName: "p__documents__index" */'@/pages/documents/index.tsx')),
'8': React.lazy(() => import(/* webpackChunkName: "p__knowledge-detail__index" */'@/pages/knowledge-detail/index.tsx')),
'9': React.lazy(() => import('./EmptyRoute')),
'10': React.lazy(() => import(/* webpackChunkName: "p__knowledge-detail__dataset__index" */'@/pages/knowledge-detail/dataset/index.tsx')),
'11': React.lazy(() => import(/* webpackChunkName: "p__knowledge-detail__setting__index" */'@/pages/knowledge-detail/setting/index.tsx')),
'12': React.lazy(() => import(/* webpackChunkName: "p__knowledge-detail__retrieval-testing__index" */'@/pages/knowledge-detail/retrieval-testing/index.tsx')),
'13': React.lazy(() => import(/* webpackChunkName: "p__knowledge-detail__chunks__index" */'@/pages/knowledge-detail/chunks/index.tsx')),
'14': React.lazy(() => import('./EmptyRoute')),
'15': React.lazy(() => import(/* webpackChunkName: "p__dialogs__index" */'@/pages/dialogs/index.tsx')),
'16': React.lazy(() => import(/* webpackChunkName: "p__dialogs__view" */'@/pages/dialogs/view.tsx')),
'17': React.lazy(() => import(/* webpackChunkName: "p__chat__index" */'@/pages/chat/index.tsx')),
'18': React.lazy(() => import(/* webpackChunkName: "p__conversations__index" */'@/pages/conversations/index.tsx')),
'19': React.lazy(() => import(/* webpackChunkName: "p__ai-models__index" */'@/pages/ai-models/index.tsx')),
'20': React.lazy(() => import(/* webpackChunkName: "p__settings__index" */'@/pages/settings/index.tsx')),
'21': React.lazy(() => import(/* webpackChunkName: "p__chatbot__index" */'@/pages/chatbot/index.tsx')),
},
  };
}
