import knowledgeService, {
  IKnowledgeListParams,
  IKnowledgeListBody,
  ICreateKnowledgeParams,
  IUpdateKnowledgeParams,
} from '@/services/knowledge-service';
import { useInfiniteQuery, useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { message } from 'antd';
import { useState } from 'react';
import { useDebounce } from 'ahooks';

export const useInfiniteFetchKnowledgeList = () => {
  const [searchString, setSearchString] = useState('');
  const debouncedSearchString = useDebounce(searchString, { wait: 300 });

  const {
    data,
    fetchNextPage,
    hasNextPage,
    isFetching: loading,
  } = useInfiniteQuery({
    queryKey: ['knowledgeList', debouncedSearchString],
    queryFn: async ({ pageParam = 1 }) => {
      const params: IKnowledgeListParams = {
        page: pageParam - 1, // API uses 0-based pagination
        page_size: 12,
        orderby: 'create_time',
        desc: true,
        keywords: debouncedSearchString || undefined,
      };
      const body: IKnowledgeListBody = {};
      
      const { data: res } = await knowledgeService.getKnowledgeList(params, body);
      return {
        kbs: res?.data?.kbs || [],
        total: res?.data?.total || 0,
        page: pageParam,
      };
    },
    getNextPageParam: (lastPage, pages) => {
      const currentTotal = pages.reduce((sum, page) => sum + page.kbs.length, 0);
      return currentTotal < lastPage.total ? pages.length + 1 : undefined;
    },
    initialPageParam: 1,
  });

  const handleInputChange = (value: string) => {
    setSearchString(value);
  };

  return {
    data,
    fetchNextPage,
    hasNextPage,
    searchString,
    handleInputChange,
    loading,
  };
};

export const useCreateKnowledge = () => {
  const queryClient = useQueryClient();
  
  const { mutateAsync, isPending: loading } = useMutation({
    mutationFn: async (params: ICreateKnowledgeParams) => {
      const { data } = await knowledgeService.createKnowledge(params);
      return data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['knowledgeList'] });
    },
    onError: (error: any) => {
      message.error(error?.message || 'Failed to create knowledge base');
    },
  });

  return { createKnowledge: mutateAsync, loading };
};

export const useUpdateKnowledge = () => {
  const queryClient = useQueryClient();
  
  const { mutateAsync, isPending: loading } = useMutation({
    mutationFn: async (params: IUpdateKnowledgeParams) => {
      const { data } = await knowledgeService.updateKnowledge(params);
      return data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['knowledgeList'] });
      queryClient.invalidateQueries({ queryKey: ['knowledgeDetail'] });
    },
    onError: (error: any) => {
      message.error(error?.message || 'Failed to update knowledge base');
    },
  });

  return { updateKnowledge: mutateAsync, loading };
};

export const useDeleteKnowledge = () => {
  const queryClient = useQueryClient();
  
  const { mutateAsync, isPending: loading } = useMutation({
    mutationFn: async (kb_id: string) => {
      const { data } = await knowledgeService.deleteKnowledge(kb_id);
      return data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['knowledgeList'] });
    },
    onError: (error: any) => {
      message.error(error?.message || 'Failed to delete knowledge base');
    },
  });

  return { deleteKnowledge: mutateAsync, loading };
};

export const useFetchKnowledgeDetail = (kb_id: string) => {
  const { data, isLoading: loading } = useQuery({
    queryKey: ['knowledgeDetail', kb_id],
    queryFn: async () => {
      if (!kb_id) return null;
      const { data } = await knowledgeService.getKnowledgeDetail(kb_id);
      return data?.data || null;
    },
    enabled: !!kb_id,
  });

  return { data, loading };
};
