import { useTranslation } from 'react-i18next';
import userService from '@/services/user-service';

export const useI18n = () => {
  const { t, i18n } = useTranslation();

  const changeLanguage = async (lng: string) => {
    // 先切换前端语言
    i18n.changeLanguage(lng);

    // 调用后端接口保存用户语言设置
    try {
      // 将前端语言代码转换为后端期望的格式
      const languageMap: { [key: string]: string } = {
        'zh': 'Chinese',
        'en': 'English',
      };

      const backendLanguage = languageMap[lng] || 'English';

      await userService.updateUserSetting({
        language: backendLanguage,
      });

      console.log('Language setting saved to backend:', backendLanguage);
    } catch (error) {
      console.error('Failed to save language setting to backend:', error);
      // 即使后端保存失败，前端语言切换仍然生效
    }
  };

  const getCurrentLanguage = () => {
    return i18n.language;
  };

  const isLanguage = (lng: string) => {
    return i18n.language === lng || i18n.language.startsWith(lng);
  };

  return {
    t,
    i18n,
    changeLanguage,
    getCurrentLanguage,
    isLanguage,
  };
};

// 便捷的翻译函数
export const useTranslate = () => {
  const { t } = useTranslation();
  return t;
};
