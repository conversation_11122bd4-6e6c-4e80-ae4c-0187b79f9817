import { useState, useCallback, useRef } from 'react';
import { flushSync } from 'react-dom';
import { message } from 'antd';
import chatBotService, {
  IChatBotMessage,
  ICreateSessionParams,
  ISendMessageParams,
} from '@/services/chatbot-service';
import { buildMessageUuid } from '@/utils/chat';

// 聊天机器人状态接口
export interface IChatBotState {
  sessionId: string | null;
  messages: IChatBotMessage[];
  isLoading: boolean;
  isStreaming: boolean;
  error: string | null;
  renderTrigger: number; // 添加一个渲染触发器
}

// 聊天机器人hooks
export const useChatBot = () => {
  const [state, setState] = useState<IChatBotState>({
    sessionId: null,
    messages: [],
    isLoading: false,
    isStreaming: false,
    error: null,
    renderTrigger: 0,
  });

  const streamingMessageRef = useRef<string>('');
  const currentMessageIdRef = useRef<string>('');
  const abortControllerRef = useRef<AbortController | null>(null);
  const updateQueueRef = useRef<string[]>([]);
  const isProcessingQueueRef = useRef<boolean>(false);

  // 更新状态的辅助函数
  const updateState = useCallback((updates: Partial<IChatBotState>) => {
    setState(prev => ({ ...prev, ...updates }));
  }, []);

  // 处理更新队列的函数
  const processUpdateQueue = useCallback(() => {
    if (isProcessingQueueRef.current || updateQueueRef.current.length === 0) {
      return;
    }

    isProcessingQueueRef.current = true;
    const content = updateQueueRef.current.shift()!;



    // 更新状态
    setState(prevState => {
      const currentMessages = [...prevState.messages];
      if (currentMessages.length > 0) {
        currentMessages[currentMessages.length - 1] = {
          ...currentMessages[currentMessages.length - 1],
          content: content,
          timestamp: Date.now(),
        };
      }
      return {
        ...prevState,
        messages: currentMessages,
        renderTrigger: prevState.renderTrigger + 1,
      };
    });

    // 延迟处理下一个更新，确保当前更新有时间被渲染
    setTimeout(() => {
      isProcessingQueueRef.current = false;
      processUpdateQueue(); // 递归处理下一个
    }, 50); // 50ms延迟，确保每个更新都能被看到
  }, []);

  // 创建新会话
  const createSession = useCallback(async (params: ICreateSessionParams = {}) => {
    try {
      updateState({ isLoading: true, error: null });

      const response = await chatBotService.createSession(params);

      if (response.code === 0) {
        updateState({
          sessionId: response.data.session_id,
          messages: [],
          isLoading: false,
        });
        // 移除成功消息，避免用户困惑
        return response.data.session_id;
      } else {
        throw new Error(response.message || '创建会话失败');
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : '创建会话失败';
      updateState({ error: errorMessage, isLoading: false });
      message.error(errorMessage);
      throw error;
    }
  }, [updateState]);

  // 发送消息（非流式）
  const sendMessage = useCallback(async (
    messageText: string,
    params: Omit<ISendMessageParams, 'message'> = {}
  ) => {
    if (!state.sessionId) {
      message.error('请先创建会话');
      return;
    }

    try {
      updateState({ isLoading: true, error: null });

      // 添加用户消息到界面
      const userMessage: IChatBotMessage = {
        role: 'user',
        content: messageText,
        timestamp: Date.now(),
        message_id: buildMessageUuid({ role: 'user' }),
      };

      updateState({
        messages: [...state.messages, userMessage],
      });

      // 发送消息到服务器
      const response = await chatBotService.sendMessage(state.sessionId, {
        message: messageText,
        ...params,
      });

      if (response.code === 0) {
        // 添加AI回复到界面
        const aiMessage: IChatBotMessage = {
          role: 'assistant',
          content: response.data.content,
          timestamp: Date.now(),
          message_id: response.data.message_id,
          metadata: response.data.metadata,
        };

        updateState({
          messages: [...state.messages, userMessage, aiMessage],
          isLoading: false,
        });
      } else {
        throw new Error(response.message || '发送消息失败');
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : '发送消息失败';
      updateState({ error: errorMessage, isLoading: false });
      message.error(errorMessage);
    }
  }, [state.sessionId, state.messages, updateState]);

  // 发送消息（流式）
  const sendMessageStream = useCallback(async (
    messageText: string,
    params: Omit<ISendMessageParams, 'message'> = {}
  ) => {
    // 获取当前的sessionId，确保使用最新的值
    const currentSessionId = state.sessionId;

    if (!currentSessionId) {
      message.error('请先创建会话');
      return;
    }

    try {
      // 创建新的AbortController
      abortControllerRef.current = new AbortController();

      updateState({ isStreaming: true, error: null });

      // 添加用户消息到界面
      const userMessage: IChatBotMessage = {
        role: 'user',
        content: messageText,
        timestamp: Date.now(),
        message_id: buildMessageUuid({ role: 'user' }),
      };

      // 创建临时AI消息用于流式更新
      const tempAiMessage: IChatBotMessage = {
        role: 'assistant',
        content: '',
        timestamp: Date.now(),
        message_id: 'streaming',
      };

      // 先添加用户消息和空的AI消息
      const initialMessages = [...state.messages, userMessage, tempAiMessage];
      updateState({
        messages: initialMessages,
      });

      streamingMessageRef.current = '';
      currentMessageIdRef.current = '';

      // 发送流式消息，使用当前的sessionId
      await chatBotService.sendMessageStream(
        currentSessionId,
        { message: messageText, ...params },
        // onMessage callback - 简单直接的状态更新，让StreamingText组件处理显示
        (content: string) => {
          streamingMessageRef.current = content;

          // 简单的状态更新，让StreamingText组件处理实时显示
          setState(prevState => {
            const currentMessages = [...prevState.messages];
            if (currentMessages.length > 0) {
              currentMessages[currentMessages.length - 1] = {
                ...currentMessages[currentMessages.length - 1],
                content: content, // 直接传递完整内容给StreamingText组件
                timestamp: Date.now(),
              };
            }
            return {
              ...prevState,
              messages: currentMessages,
              renderTrigger: prevState.renderTrigger + 1,
            };
          });
        },
        // onComplete callback - 接收到最后一个SSE消息后，移除Think内容，只显示最终回答
        (messageId: string) => {
          currentMessageIdRef.current = messageId;

          // 移除思考过程标签的工具函数
          const removeThinkingProcess = (text: string): string => {
            if (!text) return text;
            const thinkRegex = /<think>[\s\S]*?<\/think>/gi;
            let cleanText = text.replace(thinkRegex, '');
            cleanText = cleanText.replace(/<\/?think>/gi, '');
            return cleanText.trim();
          };

          // 在完成时清理thinking标签，只显示结果内容
          const finalContent = removeThinkingProcess(streamingMessageRef.current);



          // 使用函数式更新来获取最新的state，确保最终消息正确显示
          setState(prevState => {
            const currentMessages = [...prevState.messages];
            // 更新最后一条消息（AI回复）的最终内容
            if (currentMessages.length > 0) {
              currentMessages[currentMessages.length - 1] = {
                ...currentMessages[currentMessages.length - 1],
                content: finalContent, // 最终显示时移除think内容，只显示最终回答
                message_id: messageId,
              };
              console.log('🏁 最终消息更新完成:', {
                messageIndex: currentMessages.length - 1,
                finalMessageId: messageId,
                finalContentLength: finalContent.length
              });
            }
            return {
              ...prevState,
              messages: currentMessages,
              isStreaming: false,
            };
          });
          // 清除AbortController
          abortControllerRef.current = null;
        },
        // onError callback
        (error: string) => {
          updateState({
            error: error,
            isStreaming: false,
          });
          message.error(error);
          // 清除AbortController
          abortControllerRef.current = null;
        },
        // AbortController
        abortControllerRef.current
      );
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : '发送消息失败';
      updateState({ error: errorMessage, isStreaming: false });
      message.error(errorMessage);
    }
  }, [state.sessionId, state.messages, updateState]);

  // 加载会话历史
  const loadHistory = useCallback(async (sessionId?: string, limit?: number) => {
    const targetSessionId = sessionId || state.sessionId;
    if (!targetSessionId) {
      message.error('会话ID不存在');
      return;
    }

    try {
      updateState({ isLoading: true, error: null });
      
      const response = await chatBotService.getHistory(targetSessionId, limit);
      
      if (response.code === 0) {
        updateState({
          messages: response.data.messages,
          isLoading: false,
        });
      } else {
        throw new Error(response.message || '加载历史失败');
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : '加载历史失败';
      updateState({ error: errorMessage, isLoading: false });
      message.error(errorMessage);
    }
  }, [state.sessionId, updateState]);

  // 清空会话
  const clearSession = useCallback(async () => {
    if (!state.sessionId) {
      message.error('会话ID不存在');
      return;
    }

    try {
      updateState({ isLoading: true, error: null });
      
      const response = await chatBotService.clearSession(state.sessionId);
      
      if (response.code === 0) {
        updateState({
          messages: [],
          isLoading: false,
        });
        message.success('会话已清空');
      } else {
        throw new Error(response.message || '清空会话失败');
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : '清空会话失败';
      updateState({ error: errorMessage, isLoading: false });
      message.error(errorMessage);
    }
  }, [state.sessionId, updateState]);

  // 停止流式响应
  const stopStreaming = useCallback(() => {
    if (abortControllerRef.current) {
      abortControllerRef.current.abort();
      abortControllerRef.current = null;
    }
    updateState({ isStreaming: false });

    // 如果有正在流式传输的消息，将其添加到消息列表
    if (streamingMessageRef.current && currentMessageIdRef.current) {
      setState(prevState => {
        const currentMessages = [...prevState.messages];
        // 更新最后一条消息的内容
        if (currentMessages.length > 0) {
          currentMessages[currentMessages.length - 1] = {
            ...currentMessages[currentMessages.length - 1],
            content: streamingMessageRef.current,
          };
        }
        return {
          ...prevState,
          messages: currentMessages,
          isStreaming: false,
        };
      });
    }

    streamingMessageRef.current = '';
    currentMessageIdRef.current = '';
  }, []);

  // 设置消息列表
  const setMessages = useCallback((messages: IChatBotMessage[], sessionId?: string) => {
    const updates: Partial<IChatBotState> = { messages };
    if (sessionId) {
      updates.sessionId = sessionId;
    }
    updateState(updates);
  }, [updateState]);

  // 重置状态
  const resetState = useCallback(() => {
    // 先停止任何正在进行的流式响应
    if (abortControllerRef.current) {
      abortControllerRef.current.abort();
      abortControllerRef.current = null;
    }

    setState({
      sessionId: null,
      messages: [],
      isLoading: false,
      isStreaming: false,
      error: null,
      renderTrigger: 0,
    });
    streamingMessageRef.current = '';
    currentMessageIdRef.current = '';
  }, []);

  return {
    // 状态
    sessionId: state.sessionId,
    messages: state.messages,
    isLoading: state.isLoading,
    isStreaming: state.isStreaming,
    error: state.error,
    renderTrigger: state.renderTrigger,

    // 操作方法
    createSession,
    sendMessage,
    sendMessageStream,
    stopStreaming,
    loadHistory,
    clearSession,
    resetState,
    setMessages,
  };
};

// 聊天机器人服务状态hooks
export const useChatBotStats = () => {
  const [stats, setStats] = useState<any>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const fetchStats = useCallback(async () => {
    try {
      setIsLoading(true);
      setError(null);
      
      const response = await chatBotService.getStats();
      
      if (response.code === 0) {
        setStats(response.data);
      } else {
        throw new Error(response.message || '获取统计信息失败');
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : '获取统计信息失败';
      setError(errorMessage);
    } finally {
      setIsLoading(false);
    }
  }, []);

  return {
    stats,
    isLoading,
    error,
    fetchStats,
  };
};

// 聊天机器人历史记录hooks
export const useChatBotHistory = () => {
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // 获取用户会话列表
  const getUserSessions = useCallback(async (params: { limit?: number; offset?: number } = {}) => {
    try {
      setIsLoading(true);
      setError(null);

      const response = await chatBotService.getUserSessions(params);
      return response;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : '获取会话列表失败';
      setError(errorMessage);
      throw error;
    } finally {
      setIsLoading(false);
    }
  }, []);

  // 获取会话详情
  const getSessionDetails = useCallback(async (sessionId: string, limit?: number) => {
    try {
      setIsLoading(true);
      setError(null);

      const response = await chatBotService.getSessionDetails(sessionId, limit);
      return response;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : '获取会话详情失败';
      setError(errorMessage);
      throw error;
    } finally {
      setIsLoading(false);
    }
  }, []);

  // 删除会话
  const deleteSession = useCallback(async (sessionId: string) => {
    try {
      setIsLoading(true);
      setError(null);

      const response = await chatBotService.deleteSession(sessionId);
      return response;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : '删除会话失败';
      setError(errorMessage);
      throw error;
    } finally {
      setIsLoading(false);
    }
  }, []);

  return {
    isLoading,
    error,
    getUserSessions,
    getSessionDetails,
    deleteSession,
  };
};

export default useChatBot;
