import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { message } from 'antd';
import dialogService from '@/services/dialog-service';
import {
  IDialog,
  IDialogParams,
  IDialogListParams,
  IDialogGetParams,
} from '@/interfaces/dialog';

// 获取Dialog列表
export const useFetchDialogList = (params: IDialogListParams = {}) => {
  return useQuery({
    queryKey: ['fetchDialogList', params],
    queryFn: async () => {
      const { data } = await dialogService.listDialog(params);
      return data?.data || [];
    },
    refetchOnWindowFocus: false,
    staleTime: 5 * 60 * 1000, // 5分钟
  });
};

// 获取Dialog详情
export const useFetchDialog = (dialogId?: string) => {
  return useQuery({
    queryKey: ['fetchDialog', dialogId],
    queryFn: async () => {
      if (!dialogId) return null;
      const { data } = await dialogService.getDialog({ dialogId });
      return data?.data || null;
    },
    enabled: !!dialogId,
    refetchOnWindowFocus: false,
    staleTime: 5 * 60 * 1000,
  });
};

// 手动获取Dialog详情
export const useFetchManualDialog = () => {
  return useMutation({
    mutationKey: ['fetchManualDialog'],
    mutationFn: async (dialogId: string) => {
      const { data } = await dialogService.getDialog({ dialogId });
      return data;
    },
  });
};

// 创建/更新Dialog
export const useSetDialog = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationKey: ['setDialog'],
    mutationFn: async (params: IDialog) => {
      const { data } = await dialogService.setDialog(params);
      return data;
    },
    onSuccess: (data, variables) => {
      // 刷新Dialog列表
      queryClient.invalidateQueries({
        queryKey: ['fetchDialogList'],
      });

      // 刷新Dialog详情
      if (variables.dialog_id) {
        queryClient.invalidateQueries({
          queryKey: ['fetchDialog', variables.dialog_id],
        });
      }

      // 显示成功消息
      const isUpdate = !!variables.dialog_id;
      message.success(isUpdate ? 'Dialog updated successfully!' : 'Dialog created successfully!');
    },
    onError: (error: any) => {
      message.error(error?.message || 'Failed to save dialog');
    },
  });
};

// 创建Dialog
export const useCreateDialog = () => {
  const setDialogMutation = useSetDialog();

  return useMutation({
    mutationKey: ['createDialog'],
    mutationFn: async (params: Omit<IDialogParams, 'dialog_id'>) => {
      return setDialogMutation.mutateAsync(params);
    },
  });
};

// 更新Dialog
export const useUpdateDialog = () => {
  const setDialogMutation = useSetDialog();

  return useMutation({
    mutationKey: ['updateDialog'],
    mutationFn: async (params: IDialogParams) => {
      if (!params.dialog_id) {
        throw new Error('dialog_id is required for update');
      }
      return setDialogMutation.mutateAsync(params);
    },
  });
};

// 删除Dialog
export const useRemoveDialog = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationKey: ['removeDialog'],
    mutationFn: async (dialogIds: string[]) => {
      const { data } = await dialogService.removeDialog({ dialogIds });
      return data;
    },
    onSuccess: () => {
      // 刷新Dialog列表
      queryClient.invalidateQueries({
        queryKey: ['fetchDialogList'],
      });

      message.success('Dialog(s) deleted successfully!');
    },
    onError: (error: any) => {
      message.error(error?.message || 'Failed to delete dialog(s)');
    },
  });
};

// 删除单个Dialog
export const useDeleteDialog = () => {
  const removeDialogMutation = useRemoveDialog();

  return useMutation({
    mutationKey: ['deleteDialog'],
    mutationFn: async (dialogId: string) => {
      return removeDialogMutation.mutateAsync([dialogId]);
    },
  });
};

// 批量删除Dialog
export const useBatchDeleteDialogs = () => {
  const removeDialogMutation = useRemoveDialog();

  return useMutation({
    mutationKey: ['batchDeleteDialogs'],
    mutationFn: async (dialogIds: string[]) => {
      return removeDialogMutation.mutateAsync(dialogIds);
    },
  });
};

// 搜索Dialog
export const useSearchDialogs = () => {
  return useMutation({
    mutationKey: ['searchDialogs'],
    mutationFn: async (params: IDialogListParams) => {
      const { data } = await dialogService.listDialog(params);
      return data?.data || [];
    },
  });
};

// 获取Dialog统计信息
export const useDialogStats = () => {
  return useQuery({
    queryKey: ['dialogStats'],
    queryFn: async () => {
      const { data } = await dialogService.listDialog({ page_size: 1000 });
      const dialogs = data?.data || [];
      
      return {
        total: dialogs.length,
        active: dialogs.filter(d => d.status === 'active').length,
        inactive: dialogs.filter(d => d.status === 'inactive').length,
      };
    },
    refetchOnWindowFocus: false,
    staleTime: 10 * 60 * 1000, // 10分钟
  });
};
