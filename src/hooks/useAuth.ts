import { useState, useEffect } from 'react';
import { useMutation, useQuery } from '@tanstack/react-query';
import { message } from 'antd';
import { useTranslation } from 'react-i18next';
import { userApi } from '@/services/api';
import authService, { type UserInfo } from '@/utils/auth';
import { rsaPsw } from '@/utils';
import type { LoginParams } from '@/services/api';

// 登录Hook
export const useLogin = () => {
  const { t } = useTranslation();

  return useMutation({
    mutationFn: async (params: LoginParams) => {
      // 加密密码
      const encryptedPassword = rsaPsw(params.password);
      if (!encryptedPassword) {
        throw new Error('密码加密失败');
      }

      // 调用登录API，与原项目保持一致
      const { data: res = {}, response } = await userApi.login({
        email: params.email,
        password: encryptedPassword,
      });

      if (res.code === 0) {
        const { data } = res;
        message.success(t('login.loginSuccess'));

        // 获取Authorization头和token，与原项目保持一致
        const authorization = response.headers.get('Authorization');
        const token = data.access_token;

        // 构建用户信息对象，与原项目保持一致
        const userInfo = {
          avatar: data.avatar,
          name: data.nickname,  // 使用nickname作为name
          email: data.email,
        };

        // 使用原项目的存储方式
        authService.setItems({
          Authorization: authorization,
          userInfo: JSON.stringify(userInfo),
          token: token,
        });

        // 跳转到知识库页面，与原项目保持一致
        window.location.href = '/knowledge';
      }

      return res.code;
    },
    onError: (error: any) => {
      console.error('Login failed:', error);
      message.error(error.message || t('login.loginFailed'));
    },
  });
};

// 登出Hook
export const useLogout = () => {
  const { t } = useTranslation();

  return useMutation({
    mutationFn: async () => {
      try {
        await userApi.logout();
      } catch (error) {
        console.warn('Logout API failed:', error);
      }
    },
    onSuccess: () => {
      authService.logout();
      message.success('登出成功');
      authService.redirectToLogin();
    },
    onError: (error: any) => {
      console.error('Logout failed:', error);
      authService.logout();
      authService.redirectToLogin();
    },
  });
};

// 认证状态Hook
export const useAuthState = () => {
  const [isAuthenticated, setIsAuthenticated] = useState<boolean>(false);
  const [userInfo, setUserInfo] = useState<UserInfo | null>(null);
  const [loading, setLoading] = useState<boolean>(true);

  useEffect(() => {
    const checkAuth = () => {
      const authenticated = authService.isAuthenticated();
      const user = authService.getUserInfo();
      
      setIsAuthenticated(authenticated);
      setUserInfo(user);
      setLoading(false);
    };

    checkAuth();

    const handleStorageChange = (e: StorageEvent) => {
      if (e.key === 'Authorization' || e.key === 'userInfo') {
        checkAuth();
      }
    };

    window.addEventListener('storage', handleStorageChange);
    return () => window.removeEventListener('storage', handleStorageChange);
  }, []);

  return {
    isAuthenticated,
    userInfo,
    loading,
    login: useLogin(),
    logout: useLogout(),
  };
};
