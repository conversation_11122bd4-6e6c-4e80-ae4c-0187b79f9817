import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { message } from 'antd';
import { useTranslation } from 'react-i18next';
import { documentApi } from '@/services/api';
import type { Document } from '@/services/api';

// 获取文档列表Hook
export const useDocumentList = (params: {
  kb_id: string;
  page?: number;
  page_size?: number;
  orderby?: string;
  desc?: boolean;
  keywords?: string;
  run_status?: string[];
  types?: string[];
}) => {
  return useQuery({
    queryKey: ['documentList', params],
    queryFn: async () => {
      const response = await documentApi.getList(params);
      return response.data.data;
    },
    enabled: !!params.kb_id,
    staleTime: 30000, // 30秒
  });
};

// 上传文档Hook
export const useUploadDocument = () => {
  const { t } = useTranslation();
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (formData: FormData) => {
      const response = await documentApi.upload(formData);
      return response.data.data;
    },
    onSuccess: (data) => {
      message.success(t('documents.uploadSuccess'));
      // 刷新文档列表
      queryClient.invalidateQueries({ queryKey: ['documentList'] });
    },
    onError: (error: any) => {
      console.error('Upload document failed:', error);
      message.error(error.message || '文档上传失败');
    },
  });
};

// 删除文档Hook
export const useDeleteDocument = () => {
  const { t } = useTranslation();
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (doc_ids: string[]) => {
      const response = await documentApi.delete({ doc_ids });
      return response.data;
    },
    onSuccess: () => {
      message.success(t('documents.deleteSuccess'));
      // 刷新文档列表
      queryClient.invalidateQueries({ queryKey: ['documentList'] });
    },
    onError: (error: any) => {
      console.error('Delete document failed:', error);
      message.error(error.message || '删除文档失败');
    },
  });
};

// 重命名文档Hook
export const useRenameDocument = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (params: { doc_id: string; name: string }) => {
      const response = await documentApi.rename(params);
      return response.data;
    },
    onSuccess: () => {
      message.success('重命名成功');
      // 刷新文档列表
      queryClient.invalidateQueries({ queryKey: ['documentList'] });
    },
    onError: (error: any) => {
      console.error('Rename document failed:', error);
      message.error(error.message || '重命名失败');
    },
  });
};

// 运行文档解析Hook
export const useRunDocument = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (params: { doc_ids: string[]; run: number }) => {
      const response = await documentApi.run(params);
      return response.data;
    },
    onSuccess: () => {
      message.success('文档解析任务已启动');
      // 刷新文档列表
      queryClient.invalidateQueries({ queryKey: ['documentList'] });
    },
    onError: (error: any) => {
      console.error('Run document failed:', error);
      message.error(error.message || '启动解析失败');
    },
  });
};

// 文档统计Hook
export const useDocumentStats = (kb_id?: string) => {
  return useQuery({
    queryKey: ['documentStats', kb_id],
    queryFn: async () => {
      if (!kb_id) return null;
      
      // 获取文档列表来计算统计信息
      const response = await documentApi.getList({
        kb_id,
        page_size: 1000,
      });
      const docs = response.data.data.docs;
      
      return {
        total: docs.length,
        processing: docs.filter(doc => doc.status === 'processing').length,
        completed: docs.filter(doc => doc.status === 'completed').length,
        failed: docs.filter(doc => doc.status === 'failed').length,
        totalSize: docs.reduce((sum, doc) => sum + doc.size, 0),
        totalChunks: docs.reduce((sum, doc) => sum + doc.chunk_num, 0),
        recentDocs: docs.slice(0, 5), // 最近的5个文档
      };
    },
    enabled: !!kb_id,
    staleTime: 60000, // 1分钟
  });
};
