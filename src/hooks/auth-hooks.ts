import authorizationUtil from '@/utils/authorization-util';
import { useEffect, useState } from 'react';

export const useAuth = () => {
  const [isLogin, setIsLogin] = useState<boolean | null>(null); // null表示初始化中
  const [userInfo, setUserInfo] = useState<any>(null);

  useEffect(() => {
    const checkAuth = () => {
      const token = authorizationUtil.getToken();
      const authorization = authorizationUtil.getAuthorization();
      const userInfoStr = authorizationUtil.getUserInfo();

      if (token && authorization && userInfoStr) {
        setIsLogin(true);
        try {
          setUserInfo(JSON.parse(userInfoStr));
        } catch (error) {
          console.error('Failed to parse user info:', error);
          setUserInfo(null);
          setIsLogin(false);
        }
      } else {
        setIsLogin(false);
        setUserInfo(null);
      }
    };

    checkAuth();

    // Listen for storage changes
    const handleStorageChange = (e: StorageEvent) => {
      if (e.key === 'Token' || e.key === 'Authorization' || e.key === 'userInfo') {
        checkAuth();
      }
    };

    window.addEventListener('storage', handleStorageChange);

    return () => {
      window.removeEventListener('storage', handleStorageChange);
    };
  }, []);

  return { isLogin, userInfo };
};
