import { useQuery } from '@tanstack/react-query';
import knowledgeService from '@/services/knowledge-service';

// 获取知识库选项用于Dialog配置
export const useKnowledgeBaseOptions = () => {
  return useQuery({
    queryKey: ['knowledgeBaseOptions'],
    queryFn: async () => {
      const { data } = await knowledgeService.getKnowledgeList({
        page: 0, // API使用0-based分页
        page_size: 1000, // 获取所有知识库
      });
      return data?.data?.kbs || [];
    },
    refetchOnWindowFocus: false,
    staleTime: 5 * 60 * 1000, // 5分钟
  });
};

// 获取知识库详情
export const useKnowledgeBaseDetail = (kbId?: string) => {
  return useQuery({
    queryKey: ['knowledgeBaseDetail', kbId],
    queryFn: async () => {
      if (!kbId) return null;
      const { data } = await knowledgeService.getKnowledgeDetail(kbId);
      return data?.data || null;
    },
    enabled: !!kbId,
    refetchOnWindowFocus: false,
    staleTime: 5 * 60 * 1000,
  });
};
