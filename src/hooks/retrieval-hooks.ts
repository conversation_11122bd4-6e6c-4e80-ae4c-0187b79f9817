import { useMutation } from '@tanstack/react-query';
import { message } from 'antd';
import retrievalService, { IRetrievalTestParams, IRetrievalTestResponse } from '@/services/retrieval-service';

export const useRetrievalTest = () => {
  const { mutateAsync, isPending: loading } = useMutation({
    mutationFn: async (params: IRetrievalTestParams) => {
      const { data } = await retrievalService.testRetrieval(params);
      return data;
    },
    onError: (error: any) => {
      message.error(error?.message || 'Retrieval test failed');
    },
  });

  return { testRetrieval: mutateAsync, loading };
};
