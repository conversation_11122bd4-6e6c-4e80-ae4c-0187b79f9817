import { useState, useCallback, useRef, useEffect, useMemo } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { message } from 'antd';
import { useSearchParams } from 'umi';
import chatService from '@/services/chat-service';
import { getConversationId, buildMessageListWithUuid, isConversationIdExist, buildMessageUuid } from '@/utils/chat';
import { getAuthorization } from '@/utils/authorization-util';
import api from '@/utils/api';
import {
  IConversation,
  IMessage,
  IConversationListParams,
  ISendMessageParams,
  ICreateConversationParams,
  IDeleteConversationParams,
  IDeleteMessageParams,
  IChatState,
  ISSEData,
} from '@/interfaces/chat';

// 注意：按照原版RAGFlow的设计，reference处理在渲染时进行，不在useSelectNextMessages中处理

// 注意：按照原版RAGFlow的设计，document/thumbnails API调用应该在MarkdownContent组件中进行
// 这里不需要复杂的API调用逻辑，只需要简单处理引用数据结构

// 获取聊天页面URL参数（参考原始web实现）
export const useGetChatSearchParams = () => {
  const [searchParams] = useSearchParams();

  return {
    dialogId: searchParams.get('dialog_id') || '',
    conversationId: searchParams.get('conversation_id') || '',
    isNew: searchParams.get('isNew') || '',
  };
};

// 获取对话列表
export const useConversationList = (params: IConversationListParams) => {
  return useQuery({
    queryKey: ['conversations', params.dialog_id],
    queryFn: () => chatService.listConversations(params),
    enabled: !!params.dialog_id,
  });
};

// 获取单个对话（参考原始web实现）
export const useConversation = (conversationId: string) => {
  return useQuery({
    queryKey: ['conversation', conversationId],
    queryFn: async () => {
      if (isConversationIdExist(conversationId)) {
        const { data } = await chatService.getConversation({ conversationId });
        const conversation = data?.data ?? {};

        // 使用buildMessageListWithUuid处理消息列表
        const messageList = buildMessageListWithUuid(conversation?.message);

        return { ...conversation, message: messageList };
      }
      return { message: [] };
    },
    enabled: !!conversationId && isConversationIdExist(conversationId),
    refetchOnWindowFocus: false,
    staleTime: 2 * 60 * 1000, // 2分钟缓存
    gcTime: 0, // 参考原始web设置
  });
};

// 获取当前对话（自动从URL参数获取，参考原始web的useFetchNextConversation）
export const useFetchNextConversation = () => {
  const { isNew, conversationId } = useGetChatSearchParams();

  return useQuery({
    queryKey: ['fetchConversation', conversationId],
    queryFn: async () => {
      if (isNew !== 'true' && isConversationIdExist(conversationId)) {
        const { data } = await chatService.getConversation({ conversationId });
        const conversation = data?.data ?? {};

        // 按照原版RAGFlow的方式：不在这里处理reference，在渲染时处理

        // 使用buildMessageListWithUuid处理消息列表
        const messageList = buildMessageListWithUuid(conversation?.message);

        return { ...conversation, message: messageList };
      }
      return { message: [] };
    },
    enabled: !!conversationId || isNew === 'true',
    refetchOnWindowFocus: false,
    staleTime: 2 * 60 * 1000,
    gcTime: 0,
  });
};

// 派生消息管理（参考原始web的useSelectDerivedMessages）
export const useSelectDerivedMessages = () => {
  const [derivedMessages, setDerivedMessages] = useState<IMessage[]>([]);

  // 按照原版ragflow的addNewestQuestion实现
  const addNewestQuestion = useCallback((message: IMessage, answer: string = '') => {
    setDerivedMessages((pre) => {
      return [
        ...pre,
        {
          ...message,
          id: buildMessageUuid(message),
          // The message id is generated on the front end,
          // and the message id returned by the back end is the same as the question id,
          // so that the pair of messages can be deleted together when deleting the message
        },
        {
          role: 'assistant',
          content: answer,
          id: buildMessageUuid({ ...message, role: 'assistant' }),
        } as IMessage,
      ];
    });
  }, []);

  const addNewestAnswer = useCallback((content: string, reference?: any) => {
    setDerivedMessages((pre) => {
      if (pre.length === 0) return pre;
      const newMessages = [...pre];
      const lastMessage = newMessages[newMessages.length - 1];
      if (lastMessage.role === 'assistant') {
        const updatedMessage = {
          ...lastMessage,
          content,
          // 修复引用信息初始化问题：
          // - 如果SSE响应提供了引用数据（包括空数组），使用新的引用数据
          // - 如果SSE响应没有提供引用数据（undefined），保持初始的undefined状态
          // - 这确保每次新提问时都从干净的状态开始，不会显示旧的引用信息
          reference: reference !== undefined ? reference : undefined,
        };
        newMessages[newMessages.length - 1] = updatedMessage;
      }
      return newMessages;
    });
  }, []);

  const removeLatestMessage = useCallback(() => {
    setDerivedMessages((pre) => pre.slice(0, -1));
  }, []);

  const removeMessageById = useCallback((messageId: string) => {
    setDerivedMessages((pre) => pre.filter(msg => msg.id !== messageId));
  }, []);

  return {
    derivedMessages,
    setDerivedMessages,
    addNewestQuestion,
    addNewestAnswer,
    removeLatestMessage,
    removeMessageById,
  };
};

// 在useSelectNextMessages中加载文档资源
const loadDocumentAssetsForMessages = async (messages: IMessage[]): Promise<IMessage[]> => {
  try {
    const documentIds = new Set<string>();
    const chunkIds = new Set<string>();

    // 收集所有引用的document_id和chunk.id
    messages.forEach((message, index) => {

      // 处理conversation中的reference多维数组结构
      // 检查是否是多维数组格式（历史消息可能包含多个Q&A轮次）
      if (Array.isArray(message.reference)) {
        message.reference.forEach((refItem: any, refIndex: number) => {
          // 处理doc_aggs
          if (refItem.doc_aggs) {
            refItem.doc_aggs.forEach((docAgg: any, docIndex: number) => {
              if (docAgg.doc_id) {
                documentIds.add(docAgg.doc_id);
              }
            });
          }

          // 处理chunks
          if (refItem.chunks) {
            refItem.chunks.forEach((chunk: any, chunkIndex: number) => {
              if (chunk.document_id) {
                documentIds.add(chunk.document_id);
              }
              if (chunk.id) {
                chunkIds.add(chunk.id);
              }
            });
          }
        });
      } else {
        // 处理标准格式的reference
        // 从doc_aggs中收集document_id（优先使用）
        if (message.reference?.doc_aggs) {
          message.reference.doc_aggs.forEach((docAgg, docIndex) => {
            if (docAgg.doc_id) {
              documentIds.add(docAgg.doc_id);
            }
          });
        }

        // 从chunks中收集document_id和chunk.id
        if (message.reference?.chunks) {
          message.reference.chunks.forEach((chunk, chunkIndex) => {
            if (chunk.document_id) {
              documentIds.add(chunk.document_id);
            }
            if (chunk.id) {
              chunkIds.add(chunk.id);
            }
          });
        }
      }
    });

    // 如果没有文档ID，直接返回原始消息（但保留原始引用数据）
    if (documentIds.size === 0 && chunkIds.size === 0) {
      return messages;
    }

    let enhancedMessages = messages;

    // 调用document/thumbnails API
    if (documentIds.size > 0) {
      try {
        const documentService = (await import('@/services/document-service')).default;
        const thumbnailsResponse = await documentService.getDocumentThumbnails(Array.from(documentIds));

        // 检查响应格式并安全处理
        const thumbnailsData = Array.isArray(thumbnailsResponse?.data)
          ? thumbnailsResponse.data
          : (thumbnailsResponse?.data ? [thumbnailsResponse.data] : []);

        // 增强消息数据，添加缩略图信息
        enhancedMessages = enhancedMessages.map(message => {
          if (message.reference?.chunks) {
            const enhancedChunks = message.reference.chunks.map(chunk => {
              const thumbnailData = thumbnailsData.find((thumb: any) =>
                thumb.doc_id === chunk.document_id
              );
              return {
                ...chunk,
                thumbnail: thumbnailData?.thumbnail,
                document_info: thumbnailData,
              };
            });

            return {
              ...message,
              reference: {
                ...message.reference,
                chunks: enhancedChunks,
              },
            };
          }
          return message;
        });
      } catch (error) {
        // 静默处理缩略图加载失败
      }
    }

    // 调用document/image API（基于doc_id和image_id构建URL）
    // 收集需要加载图片的chunk信息
    const imageChunks: Array<{chunk: any, doc_id: string, image_id: string}> = [];
    enhancedMessages.forEach(message => {
      if (message.reference?.chunks) {
        message.reference.chunks.forEach(chunk => {
          if (chunk.document_id && chunk.id) {
            // 使用document_id作为doc_id，chunk.id作为image_id
            imageChunks.push({
              chunk,
              doc_id: chunk.document_id,
              image_id: chunk.id
            });
          }
        });
      }
    });

    if (imageChunks.length > 0) {
      try {
        const documentService = (await import('@/services/document-service')).default;

        // 为每个chunk调用document/image API（使用URL格式）
        const imagePromises = imageChunks.map(async ({chunk, doc_id, image_id}) => {
          try {
            const imageResponse = await documentService.getDocumentImageByUrl(doc_id, image_id);
            return { chunkId: chunk.id, data: imageResponse?.data };
          } catch (error) {
            return { chunkId: chunk.id, data: null };
          }
        });

        const imageResults = await Promise.all(imagePromises);
        const imageMap = new Map(imageResults.map(result => [result.chunkId, result.data]));

        // 增强消息数据，添加图片信息
        enhancedMessages = enhancedMessages.map(message => {
          if (message.reference?.chunks) {
            const enhancedChunks = message.reference.chunks.map(chunk => {
              if (chunk.id) {
                const imageData = imageMap.get(chunk.id);
                return {
                  ...chunk,
                  image_data: imageData,
                };
              }
              return chunk;
            });

            return {
              ...message,
              reference: {
                ...message.reference,
                chunks: enhancedChunks,
              },
            };
          }
          return message;
        });

      } catch (error) {
        // 静默处理图片加载失败
      }
    }

    return enhancedMessages;
  } catch (error) {
    return messages; // 确保即使出错也返回原始消息
  }
};

// 选择下一个消息（完全按照原版RAGFlow的useSelectNextMessages）
export const useSelectNextMessages = () => {
  const {
    derivedMessages,
    setDerivedMessages,
    addNewestAnswer,
    addNewestQuestion,
    removeLatestMessage,
    removeMessageById,
  } = useSelectDerivedMessages();

  const { data: conversation, isLoading: loading } = useFetchNextConversation();
  const { conversationId, isNew } = useGetChatSearchParams();

  // 移除思考过程标签的工具函数
  const removeThinkingProcess = (text: string): string => {
    if (!text) return text;
    const thinkRegex = /<think>[\s\S]*?<\/think>/gi;
    let cleanText = text.replace(thinkRegex, '');
    cleanText = cleanText.replace(/<\/?think>/gi, '');
    return cleanText.trim();
  };

  // 修复引用信息丢失问题：正确处理历史消息的reference数据
  // 使用 useMemo 来避免无限重新渲染
  const processedHistoricalMessages = useMemo(() => {
    // 如果有conversationId且不是新对话，设置历史消息并处理引用数据
    if (conversationId && isNew !== 'true' && conversation?.message?.length > 0) {
      // 处理历史消息的引用数据，确保引用信息正确显示
      const processedMessages = buildMessageListWithUuid(conversation.message);

      // 为每个消息确保引用数据的完整性
      // 特别处理最后一条消息的引用数据，确保不会丢失
      const messagesWithReference = processedMessages.map((message, index) => {
        let processedMessage = { ...message };

        // 关键修复：对于历史消息，移除think标签，只显示最终回答
        if (processedMessage.role === 'assistant' && processedMessage.content) {
          processedMessage.content = removeThinkingProcess(processedMessage.content);
        }

        // 如果消息已经有reference数据，直接使用
        if (processedMessage.reference &&
            processedMessage.reference.chunks &&
            processedMessage.reference.chunks.length > 0) {
          return processedMessage;
        }

        // 如果是助手消息且没有reference，尝试从conversation.reference中获取
        if (processedMessage.role === 'assistant' && conversation?.reference) {
          const assistantMessages = processedMessages.filter(m => m.role === 'assistant');
          const assistantIndex = assistantMessages.findIndex(m => m.id === processedMessage.id);

          // 关键修复：第一条assistant消息（prologue/欢迎消息）不应该有reference
          // 按照原版RAGFlow的逻辑，第一条assistant消息是prologue，不参与知识库检索
          // 只有用户提问后产生的assistant回复才有reference
          // conversation.reference[0]对应的是第一个真正的问答对，而不是prologue
          if (assistantIndex > 0 && assistantIndex <= conversation.reference.length) {
            // 注意：这里使用assistantIndex-1是因为第一条assistant消息（索引0）是prologue，没有reference
            // conversation.reference[0]对应第二条assistant消息（第一个真正的回答）
            const conversationRef = conversation.reference[assistantIndex - 1];
            if (conversationRef &&
                conversationRef.chunks &&
                conversationRef.chunks.length > 0) {
              return {
                ...processedMessage,
                reference: conversationRef
              };
            }
          }
        }

        return processedMessage;
      });

      return messagesWithReference;
    }

    // 如果没有conversationId，返回空数组
    if (!conversationId) {
      return [];
    }

    return null;
  }, [
    conversationId,
    isNew,
    // 使用 JSON.stringify 来创建稳定的依赖项，避免对象引用变化导致的无限重新渲染
    JSON.stringify(conversation?.message),
    JSON.stringify(conversation?.reference)
  ]);

  // 使用 useEffect 来设置处理后的消息，但只在 processedHistoricalMessages 真正改变时触发
  useEffect(() => {
    if (processedHistoricalMessages !== null) {
      setDerivedMessages(processedHistoricalMessages);
    }
  }, [processedHistoricalMessages, setDerivedMessages]);

  return {
    derivedMessages,
    loading,
    addNewestAnswer,
    addNewestQuestion,
    removeLatestMessage,
    removeMessageById,
    setDerivedMessages,
  };
};

// 创建对话
export const useCreateConversation = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (params: ICreateConversationParams) => 
      chatService.createConversation(params),
    onSuccess: (data, variables) => {
      // 刷新对话列表
      queryClient.invalidateQueries({ 
        queryKey: ['conversations', variables.dialog_id] 
      });
      message.success('Conversation created successfully');
    },
    onError: (error) => {
      message.error('Failed to create conversation');
    },
  });
};

// 删除对话
export const useDeleteConversation = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (params: IDeleteConversationParams) => 
      chatService.deleteConversation(params),
    onSuccess: () => {
      // 刷新对话列表
      queryClient.invalidateQueries({ queryKey: ['conversations'] });
      message.success('Conversation deleted successfully');
    },
    onError: (error) => {
      message.error('Failed to delete conversation');
    },
  });
};

// 删除消息
export const useDeleteMessage = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (params: IDeleteMessageParams) => 
      chatService.deleteMessage(params),
    onSuccess: () => {
      // 刷新当前对话
      queryClient.invalidateQueries({ queryKey: ['conversation'] });
      message.success('Message deleted successfully');
    },
    onError: (error) => {
      message.error('Failed to delete message');
    },
  });
};

// 聊天状态管理Hook
// 点赞Hook
export const useThumbup = () => {
  return useMutation({
    mutationFn: ({ messageId, thumbup }: { messageId: string; thumbup: boolean }) =>
      chatService.thumbup(messageId, thumbup),
    onSuccess: () => {
      message.success('Feedback submitted');
    },
    onError: (error) => {
      message.error('Failed to submit feedback');
    },
  });
};

export const useChatState = (dialogId: string) => {
  const [state, setState] = useState<IChatState>({
    currentConversation: null,
    conversations: [],
    loading: false,
    sending: false,
    error: null,
  });

  const sseRef = useRef<(() => void) | null>(null);
  const abortControllerRef = useRef<AbortController | null>(null);

  // 获取URL参数中的conversation_id
  const { conversationId, isNew } = useGetChatSearchParams();
  const { data: conversation } = useFetchNextConversation();

  // 创建新的AbortController（参考原版web的方式）
  const createNewController = useCallback(() => {
    // 如果有旧的controller，先取消它
    if (abortControllerRef.current) {
      abortControllerRef.current.abort();
    }
    // 创建新的controller
    abortControllerRef.current = new AbortController();
    return abortControllerRef.current;
  }, []);

  // 获取对话列表
  const { data: conversationsData, isLoading: conversationsLoading } = useConversationList({
    dialog_id: dialogId,
  });

  useEffect(() => {
    if (conversationsData?.data) {
      setState(prev => ({
        ...prev,
        conversations: conversationsData.data,
        loading: conversationsLoading,
      }));
    }
  }, [conversationsData, conversationsLoading]);

  // 从URL参数设置当前对话（用于Continue Chat）
  useEffect(() => {
    if (conversationId && isNew !== 'true' && conversation) {
      setState(prev => ({
        ...prev,
        currentConversation: {
          id: conversation.id || conversationId,
          name: conversation.name || 'Untitled',
          dialog_id: dialogId,
          create_time: conversation.create_time || new Date().toISOString(),
          update_time: conversation.update_time || new Date().toISOString(),
          message: conversation.message || [],
        },
      }));
    }
  }, [conversationId, isNew, conversation, dialogId]);

  // 设置当前对话
  const setCurrentConversation = useCallback((conversation: IConversation | null) => {
    // 确保conversation的message字段是数组
    const safeConversation = conversation ? {
      ...conversation,
      message: Array.isArray(conversation.message) ? conversation.message : []
    } : null;

    setState(prev => ({
      ...prev,
      currentConversation: safeConversation,
    }));
  }, []);

  // 添加消息到当前对话
  const addMessage = useCallback((message: IMessage) => {
    setState(prev => {
      if (!prev.currentConversation) return prev;

      // 确保message字段是数组
      const currentMessages = Array.isArray(prev.currentConversation.message)
        ? prev.currentConversation.message
        : [];

      return {
        ...prev,
        currentConversation: {
          ...prev.currentConversation,
          message: [...currentMessages, message],
        },
      };
    });
  }, []);

  // 更新最后一条消息
  const updateLastMessage = useCallback((content: string, reference?: any) => {
    setState(prev => {
      if (!prev.currentConversation) return prev;

      // 确保message字段是数组
      const currentMessages = Array.isArray(prev.currentConversation.message)
        ? prev.currentConversation.message
        : [];

      if (currentMessages.length === 0) {
        return prev;
      }

      const messages = [...currentMessages];
      const lastMessage = messages[messages.length - 1];

      if (lastMessage.role === 'assistant') {
        messages[messages.length - 1] = {
          ...lastMessage,
          content: content,
          reference: reference || lastMessage.reference,
        };
      }

      return {
        ...prev,
        currentConversation: {
          ...prev.currentConversation,
          message: messages,
        },
      };
    });
  }, []);



  // 发送消息但不管理UI状态（用于与derivedMessages配合）
  const sendMessageWithoutUI = useCallback(async (
    content: string,
    files?: any[],
    onStreamUpdate?: (content: string, reference?: any) => void,
    currentMessages?: IMessage[], // 新增参数：当前的消息历史
    providedConversationId?: string // 新增参数：提供的conversation_id（用于Continue Chat）
  ) => {
    if (!dialogId || !content.trim()) return;

    setState(prev => ({ ...prev, sending: true, error: null }));

    try {
      const userMessage = {
        content: content.trim(),
        role: 'user',
        create_time: new Date().toISOString(),
        id: buildMessageUuid({ role: 'user' }), // 按照原版ragflow方式生成ID
      };

      // 直接调用handleSendMessage，不管理UI状态，传递当前消息历史和conversationId
      await handleSendMessage(userMessage, onStreamUpdate, currentMessages, providedConversationId);

    } catch (error) {
      setState(prev => ({
        ...prev,
        sending: false,
        error: `Failed to send message: ${error.message}`
      }));
      throw error; // 重新抛出错误让调用方处理
    }
  }, [dialogId, handleSendMessage]);

  // 按照原版web的方式重写发送消息流程（保留原有接口）
  const sendMessage = useCallback(async (content: string, files?: any[]) => {
    if (!dialogId || !content.trim()) return;

    setState(prev => ({ ...prev, sending: true, error: null }));

    try {
      // 按照原版web的方式：先添加用户消息到界面，然后发送
      const userMessage: IMessage = {
        content: content.trim(),
        role: 'user',
        create_time: new Date().toISOString(),
        id: buildMessageUuid({ role: 'user' }), // 按照原版ragflow方式生成ID
      };

      // 1. 先添加用户消息到界面
      addMessage(userMessage);

      // 2. 然后调用handleSendMessage逻辑，不预先添加助手占位符
      // 助手占位符会在sendMessageToCompletion中添加
      await handleSendMessage(userMessage);

    } catch (error) {
      setState(prev => ({
        ...prev,
        sending: false,
        error: `Failed to send message: ${error.message}`
      }));
    }
  }, [dialogId, addMessage, handleSendMessage]);

  // 按照原版web的handleSendMessage逻辑
  const handleSendMessage = useCallback(async (
    message: any,
    onStreamUpdate?: (content: string, reference?: any) => void,
    providedMessages?: IMessage[], // 新增参数：提供的消息历史（用于Continue Chat）
    providedConversationId?: string // 新增参数：提供的conversation_id（用于Continue Chat）
  ) => {
    // 优先使用提供的conversationId（Continue Chat场景），否则使用state中的
    let conversationId = providedConversationId || state.currentConversation?.id;

    // 修复conversation id判断逻辑：只有当conversationId为空或者明确标记为临时ID时才创建新对话
    // 避免因为URL参数问题导致重复创建conversation
    const isNewConversation = !conversationId || conversationId === '' || conversationId.startsWith('temp_');

    if (!isNewConversation) {
      // 不是新对话，直接发送消息，优先使用提供的消息历史（Continue Chat场景）
      const currentMessages = providedMessages || state.currentConversation?.message || [];
      await sendMessageToCompletion({
        message,
        onStreamUpdate,
        currentConversationId: conversationId, // 使用正确的conversationId
        messages: currentMessages
      });
    } else {
      // 是新对话，先创建对话

      // 按照原版web的方式构建参数
      const setConversationParams = {
        dialog_id: dialogId,
        name: message.content.substring(0, 50),
        is_new: true,
        conversation_id: getConversationId(), // 直接在这里生成，与原版web一致
        message: [
          {
            role: 'assistant', // 原版web使用MessageType.Assistant，这里保持字符串
            content: message.content,
          },
        ],
      };

      try {
        // 检查认证状态
        const authHeader = getAuthorization();

        if (!authHeader) {
          throw new Error('No authentication token found');
        }

        // 检查dialog_id
        if (!dialogId) {
          throw new Error('Dialog ID is missing');
        }

        const data = await chatService.setConversation(setConversationParams);

        // 按照原版web的方式，直接使用返回的data
        if (!data) {
          throw new Error('setConversation returned null/undefined');
        }

        if (typeof data !== 'object') {
          throw new Error(`setConversation returned unexpected type: ${typeof data}`);
        }

        // 按照原版web的方式，使用严格比较
        if (data.code === 0) {
          // 简化验证，先确保能进入成功分支
          const id = data.data?.id;

          if (id) {
            // 更新对话状态
            setState(prev => ({
              ...prev,
              currentConversation: {
                id: id,
                name: message.content.substring(0, 50),
                dialog_id: dialogId,
                create_time: new Date().toISOString(),
                update_time: new Date().toISOString(),
                message: data.data.message || [],
              },
            }));

            // 按照原版web的方式发送消息

            try {
              await sendMessageToCompletion({
                message,
                currentConversationId: id,
                messages: data.data.message,
                onStreamUpdate,
              });
            } catch (sendError) {
              setState(prev => ({
                ...prev,
                sending: false,
                error: `Failed to send message: ${sendError.message}`
              }));
              return;
            }
          } else {
            throw new Error('No conversation ID in response');
          }
        } else {
          // 构建错误消息
          let errorMsg = 'Failed to create conversation';
          if (data?.message) {
            errorMsg = data.message;
          } else if (data?.error) {
            errorMsg = data.error;
          } else if (data?.code !== undefined) {
            errorMsg = `API returned code ${data.code}`;
          } else if (data?.retcode !== undefined) {
            errorMsg = `API returned retcode ${data.retcode}`;
          } else if (data?.status !== undefined) {
            errorMsg = `API returned status ${data.status}`;
          } else {
            errorMsg = `Unexpected response format: ${JSON.stringify(data)}`;
          }

          throw new Error(errorMsg);
        }
      } catch (error) {
        let errorMessage = 'Failed to create conversation';
        if (error.message && error.message !== 'Failed to create conversation') {
          errorMessage = `Failed to create conversation: ${error.message}`;
        }

        setState(prev => ({
          ...prev,
          sending: false,
          error: errorMessage
        }));
      }
    }
  }, [state.currentConversation, dialogId]);

  // 按照原版web的sendMessage逻辑发送到completion接口
  const sendMessageToCompletion = useCallback(async ({
    message,
    currentConversationId,
    messages,
    onStreamUpdate,
  }: {
    message: any;
    currentConversationId?: string;
    messages?: any[];
    onStreamUpdate?: (content: string, reference?: any) => void;
  }) => {
    const conversationId = currentConversationId || state.currentConversation?.id;
    const controller = createNewController();

    // 按照原版web的参数格式
    const baseMessages = messages || state.messages || [];

    // 确保消息格式正确，过滤掉可能导致后端错误的字段
    const cleanMessage = {
      id: message.id,
      content: message.content,
      role: message.role,
      ...(message.doc_ids && { doc_ids: message.doc_ids }),
    };

    // 清理baseMessages，确保格式一致
    const cleanBaseMessages = baseMessages.map((msg: any) => ({
      id: msg.id,
      content: msg.content,
      role: msg.role,
      ...(msg.doc_ids && { doc_ids: msg.doc_ids }),
    }));

    // 构建完整的消息列表
    const allMessages = [...cleanBaseMessages, cleanMessage];

    // 确保至少有一条非system消息，避免后端msg列表为空的错误
    const hasValidMessage = allMessages.some(msg =>
      msg.role !== 'system' &&
      (msg.role !== 'assistant' || allMessages.indexOf(msg) > 0)
    );

    if (!hasValidMessage) {
      throw new Error('No valid messages to send');
    }

    // 确保发送给后端的消息列表最后一条是用户消息
    // 这是为了满足后端的assert: messages[-1]["role"] == "user"
    const lastMessage = allMessages[allMessages.length - 1];
    if (lastMessage.role !== 'user') {
      throw new Error('The last message must be from user');
    }

    // 按照原版ragflow的参数格式，只发送必需的参数
    const sendParams = {
      conversation_id: conversationId,
      messages: allMessages,
    };

    // 添加助手消息占位符（如果还没有）
    const messagesArray = state.messages || [];
    if (!messagesArray.some(m => m.role === 'assistant' && m.content === '')) {
      addMessage({
        role: 'assistant',
        content: '',
        create_time: new Date().toISOString(),
        id: buildMessageUuid({ role: 'assistant' }), // 按照原版ragflow方式生成ID
      } as IMessage);
    }

    try {
      await chatService.sendMessageWithSSE(
        sendParams,
        async (data) => {

          // 按照原版web的处理方式
          if (data && typeof data !== 'boolean') {
            let answerContent = null;
            let referenceData = null;

            // 提取答案内容和引用数据
            if (data.answer) {
              answerContent = data.answer;
              referenceData = data.reference; // 也检查同级的reference字段
            } else if (data.data?.answer) {
              answerContent = data.data.answer;
              referenceData = data.data.reference;
            } else if (data.content) {
              answerContent = data.content;
              referenceData = data.reference; // 也检查同级的reference字段
            } else if (data.message) {
              answerContent = data.message;
              referenceData = data.reference; // 也检查同级的reference字段
            } else if (data.text) {
              answerContent = data.text;
              referenceData = data.reference; // 也检查同级的reference字段
            }

            if (answerContent) {
              updateLastMessage(answerContent, referenceData);

              // 如果有流式更新回调，调用它（传递引用数据）
              if (onStreamUpdate) {
                onStreamUpdate(answerContent, referenceData);
              }

              // 注意：根据原版RAGFlow的设计，引用数据通常不持久化保存
              // 引用数据主要用于当前会话，历史消息的引用功能在原版中也是有限的
            }
          }
        },
        (error) => {
          if (error.name === 'AbortError') {
            setState(prev => ({ ...prev, sending: false }));
            return;
          }
          // HTTP fallback
          chatService.sendMessage(sendParams).then(async (response) => {
            if (response.code === 0) {
              let answerContent = null;
              let referenceData = null;

              if (response.data?.answer) {
                answerContent = response.data.answer;
                referenceData = response.data.reference;
              } else if (response.answer) {
                answerContent = response.answer;
                referenceData = response.reference; // 也检查同级的reference字段
              } else if (response.data?.content) {
                answerContent = response.data.content;
                referenceData = response.data.reference; // 也检查同级的reference字段
              }

              if (answerContent) {
                updateLastMessage(answerContent, referenceData);

                // 如果有流式更新回调，调用它（传递引用数据）
                if (onStreamUpdate) {
                  onStreamUpdate(answerContent, referenceData);
                }

                // HTTP fallback完成后也需要保存对话信息
                try {
                  const currentMessages = messages || state.currentConversation?.message || [];
                  if (conversationId && !conversationId.startsWith('temp_')) {
                    // 静默保存，不处理响应
                    const saveParams = {
                      conversation_id: conversationId,
                      dialog_id: dialogId,
                      messages: currentMessages,
                      stream: false,
                    };
                    chatService.sendMessage(saveParams).catch(error => {
                      console.warn('Failed to save conversation after HTTP fallback:', error);
                    });
                  }
                } catch (error) {
                  console.warn('Failed to save conversation after HTTP fallback:', error);
                }
              }
            }
            setState(prev => ({ ...prev, sending: false }));
          }).catch(err => {
            setState(prev => ({ ...prev, sending: false, error: 'Failed to send message' }));
          });
        },
          async () => {
            // 消息流式响应完成后，保存完整的对话信息到后台
            // SSE消息接收完成，不需要额外的completion调用
            // 按照原版ragflow的方式，SSE流式响应完成后不再发送额外的请求

            setState(prev => ({ ...prev, sending: false }));
          },
          controller
        );
    } catch (error) {
      setState(prev => ({
        ...prev,
        sending: false,
        error: 'Failed to send message'
      }));
    }
  }, [state.currentConversation, addMessage, updateLastMessage, createNewController]);



  // 停止发送（参考原版web的简洁方式）
  const stopSending = useCallback(() => {
    // 取消当前请求
    if (abortControllerRef.current) {
      abortControllerRef.current.abort();
    }

    setState(prev => ({ ...prev, sending: false }));
  }, []);

  // 清理函数
  useEffect(() => {
    return () => {
      if (sseRef.current) {
        sseRef.current();
      }
      if (abortControllerRef.current) {
        abortControllerRef.current.abort();
      }
    };
  }, []);

  // 更新conversation的消息列表
  const updateConversation = useCallback(async (params: {
    conversation_id: string;
    message: IMessage[];
    is_new: boolean;
  }) => {
    try {
      const response = await chatService.setConversation({
        conversation_id: params.conversation_id,
        message: params.message,
        is_new: params.is_new,
      });

      if (response.code === 0) {
        // 更新本地状态
        setState(prev => ({
          ...prev,
          currentConversation: prev.currentConversation ? {
            ...prev.currentConversation,
            message: params.message,
          } : null,
        }));

        // 触发conversation数据的重新获取，确保前端显示正确
        // 这里我们不需要手动触发，因为后端数据已经更新，
        // useSelectNextMessages会通过useFetchNextConversation自动获取最新数据
      }

      return response;
    } catch (error) {
      console.error('Update conversation failed:', error);
      throw error;
    }
  }, [setState]);

  return {
    ...state,
    setCurrentConversation,
    addMessage,
    updateLastMessage,
    sendMessage,
    sendMessageWithoutUI,
    stopSending,
    updateConversation: { mutateAsync: updateConversation },
  };
};
