import { Authorization } from '@/constants/authorization';
import userService, {
  getLoginChannels,
  loginWithChannel,
  ILoginRequestBody,
  IRegisterRequestBody,
  ILoginChannel,
} from '@/services/user-service';
import authorizationUtil, { redirectToLogin } from '@/utils/authorization-util';
import { useMutation, useQuery } from '@tanstack/react-query';
import { Form, message } from 'antd';
import { FormInstance } from 'antd/lib';
import { useEffect, useState } from 'react';
import { useTranslate } from '@/hooks/use-i18n';

export const useLoginChannels = () => {
  const { data, isLoading } = useQuery({
    queryKey: ['loginChannels'],
    queryFn: async () => {
      const { data: res = {} } = await getLoginChannels();
      return res.data || [];
    },
  });

  return { channels: data as ILoginChannel[], loading: isLoading };
};

export const useLoginWithChannel = () => {
  const { isPending: loading, mutateAsync } = useMutation({
    mutationKey: ['loginWithChannel'],
    mutationFn: async (channel: string) => {
      loginWithChannel(channel);
      return Promise.resolve();
    },
  });

  return { loading, login: mutateAsync };
};

export const useLogin = () => {
  const t = useTranslate();
  const {
    data,
    isPending: loading,
    mutateAsync,
  } = useMutation({
    mutationKey: ['login'],
    mutationFn: async (params: ILoginRequestBody) => {
      const { data: res = {}, response } = await userService.login(params);
      if (res.code === 0) {
        const { data } = res;
        message.success(t('messages.success.loginSuccess'));
        const authorization = response.headers.get(Authorization);
        const token = data.access_token;
        const userInfo = {
          avatar: data.avatar,
          name: data.nickname,
          email: data.email,
        };
        authorizationUtil.setItems({
          Authorization: authorization || `Bearer ${token}`,
          userInfo: JSON.stringify(userInfo),
          Token: token,
        });

        // 触发storage事件以更新认证状态
        window.dispatchEvent(new StorageEvent('storage', {
          key: 'Token',
          newValue: token,
        }));
      }
      return res.code;
    },
  });

  return { data, loading, login: mutateAsync };
};

export const useRegister = () => {
  const {
    data,
    isPending: loading,
    mutateAsync,
  } = useMutation({
    mutationKey: ['register'],
    mutationFn: async (params: IRegisterRequestBody) => {
      const { data: res = {}, response } = await userService.register(params);
      if (res.code === 0) {
        // 注册成功，但不显示message，让页面组件处理显示
        // message.success('Registration successful!');

        // 注册成功后，像登录一样设置认证信息
        const { data } = res;
        const authorization = response.headers.get(Authorization);
        const token = data.access_token;
        const userInfo = {
          avatar: data.avatar,
          name: data.nickname,
          email: data.email,
        };

        // 设置认证信息到localStorage（但不立即触发storage事件）
        // 这样可以让LLM配置逻辑正常工作，但不会触发自动跳转
        authorizationUtil.setItems({
          Authorization: authorization || `Bearer ${token}`,
          userInfo: JSON.stringify(userInfo),
          Token: token,
        });

        // 注册成功后不立即触发storage事件，避免自动跳转
        // 跳转逻辑由前端页面的倒计时控制

        // 注册成功后自动添加默认LLM配置
        // 使用setTimeout延迟执行，确保认证信息设置完成后再添加LLM配置
        setTimeout(async () => {
          // 确保认证信息已经设置
          const currentAuth = authorizationUtil.getAuthorization();
          const currentToken = authorizationUtil.getToken();

          if (!currentAuth || !currentToken) {
            console.warn('Authentication info not ready, skipping LLM configuration setup');
            return;
          }
          try {
            // 从环境变量读取LLM配置，如果没有配置则使用默认值
            const defaultConfigs = [
              {
                name: process.env.AUTO_LLM_CHAT_NAME || 'Qwen3-32B',
                llm_factory: process.env.AUTO_LLM_CHAT_FACTORY || 'VLLM',
                llm_name: process.env.AUTO_LLM_CHAT_NAME || 'Qwen3-32B',
                model_type: 'chat',
                api_base: process.env.AUTO_LLM_CHAT_API_BASE || 'http://host.docker.internal:8000/v1',
                api_key: 'x',
                max_tokens: parseInt(process.env.AUTO_LLM_CHAT_MAX_TOKENS || '8192')
              },
              {
                name: process.env.AUTO_LLM_EMBEDDING_NAME || 'bge-m3',
                llm_factory: process.env.AUTO_LLM_EMBEDDING_FACTORY || 'VLLM',
                llm_name: process.env.AUTO_LLM_EMBEDDING_NAME || 'bge-m3',
                model_type: 'embedding',
                api_base: process.env.AUTO_LLM_EMBEDDING_API_BASE || 'http://host.docker.internal:18080/v1',
                api_key: 'x',
                max_tokens: parseInt(process.env.AUTO_LLM_EMBEDDING_MAX_TOKENS || '8192')
              },
              {
                name: process.env.AUTO_LLM_RERANK_NAME || 'bge-reranker-v2-m3',
                llm_factory: process.env.AUTO_LLM_RERANK_FACTORY || 'VLLM',
                llm_name: process.env.AUTO_LLM_RERANK_NAME || 'bge-reranker-v2-m3',
                model_type: 'rerank',
                api_base: process.env.AUTO_LLM_RERANK_API_BASE || 'http://host.docker.internal:18081/v1',
                api_key: 'x',
                max_tokens: parseInt(process.env.AUTO_LLM_RERANK_MAX_TOKENS || '8192')
              }
            ];

            console.log('📋 Auto LLM configurations loaded from environment:', defaultConfigs);

            // 记录成功添加的配置，用于设置默认模型
            const addedConfigs: { [key: string]: string } = {};

            for (const config of defaultConfigs) {
              try {
                await userService.add_llm(config);
                console.log(`✅ Successfully added ${config.name} configuration`);

                // 记录成功添加的配置
                if (config.llm_factory === "VLLM") {
                    config.llm_name += "___VLLM";
                }
                const modelId = `${config.llm_name}@${config.llm_factory}`;
                if (config.model_type === 'chat') {
                  addedConfigs.llm_id = modelId;
                } else if (config.model_type === 'embedding') {
                  addedConfigs.embd_id = modelId;
                } else if (config.model_type === 'rerank') {
                  addedConfigs.rerank_id = modelId;
                }
              } catch (error) {
                console.warn(`⚠️ Failed to add ${config.name} configuration:`, error);
                // 继续添加其他配置，不中断流程
              }
            }

            console.log('Default LLM configurations setup completed');

            // 设置默认模型（稍微延迟确保LLM配置完全添加）
            if (Object.keys(addedConfigs).length > 0) {
              await new Promise(resolve => setTimeout(resolve, 1000)); // 延迟1秒
              try {
                // 获取租户信息
                const { data: tenantResponse } = await userService.get_tenant_info();
                const tenantInfo = tenantResponse?.data;

                if (tenantInfo) {
                  const defaultModelPayload = {
                    tenant_id: tenantInfo.tenant_id || tenantInfo.id,
                    name: tenantInfo.name || '',
                    // 保留现有的其他默认模型设置
                    asr_id: tenantInfo.asr_id || '',
                    img2txt_id: tenantInfo.img2txt_id || '',
                    tts_id: tenantInfo.tts_id || '',
                    // 设置新添加的模型为默认
                    ...addedConfigs,
                  };

                  console.log('🎯 Setting default models:', defaultModelPayload);
                  await userService.set_tenant_info(defaultModelPayload);
                  console.log('✅ Default models set successfully');
                } else {
                  console.warn('⚠️ Could not get tenant info for setting default models');
                }
              } catch (error) {
                console.warn('⚠️ Failed to set default models:', error);
                // 不影响注册流程
              }
            }
          } catch (error) {
            console.error('Failed to setup default LLM configurations:', error);
          }
        }, 2000); // 延迟2秒执行，确保认证信息完全设置
      } else if (
        res.message &&
        res.message.includes('registration is disabled')
      ) {
        message.error('User registration is disabled');
      }
      return res.code;
    },
  });

  return { data, loading, register: mutateAsync };
};

export const useLogout = () => {
  const {
    data,
    isPending: loading,
    mutateAsync,
  } = useMutation({
    mutationKey: ['logout'],
    mutationFn: async () => {
      const { data = {} } = await userService.logout();
      if (data.code === 0) {
        message.success('Logout successful!');
        authorizationUtil.removeAll();
        redirectToLogin();
      }
      return data.code;
    },
  });

  return { data, loading, logout: mutateAsync };
};

export const useHandleSubmittable = (form: FormInstance) => {
  const [submittable, setSubmittable] = useState<boolean>(false);

  // Watch all values
  const values = Form.useWatch([], form);

  useEffect(() => {
    form
      .validateFields({ validateOnly: true })
      .then(() => setSubmittable(true))
      .catch(() => setSubmittable(false));
  }, [form, values]);

  return { submittable };
};
