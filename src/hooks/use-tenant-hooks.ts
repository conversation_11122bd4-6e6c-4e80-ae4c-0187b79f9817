import { useQuery } from '@tanstack/react-query';
import userService from '@/services/user-service';

// 获取租户信息（包含默认模型配置）
export const useTenantInfo = () => {
  return useQuery({
    queryKey: ['tenant-info'],
    queryFn: async () => {
      const { data } = await userService.getTenantInfo();
      return data?.data;
    },
    refetchOnWindowFocus: false,
    staleTime: 5 * 60 * 1000, // 5分钟缓存
  });
};
