import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { message } from 'antd';
import chatService from '@/services/chat-service';
import { IConversation } from '@/interfaces/chat';
import { generateMockConversations, shouldUseMockData } from '@/utils/mock-conversations';

// 获取所有conversations列表的参数接口
interface IConversationsListParams {
  keywords?: string;
  page?: number;
  page_size?: number;
}

// 获取所有conversations列表
export const useConversationsList = (params: IConversationsListParams = {}) => {
  return useQuery({
    queryKey: ['conversationsList', params],
    queryFn: async () => {
      // 如果是开发环境且启用了模拟数据，返回模拟数据
      if (shouldUseMockData()) {
        const mockConversations = generateMockConversations(20);

        // 应用搜索过滤
        let filteredConversations = mockConversations;
        if (params.keywords) {
          const keywords = params.keywords.toLowerCase();
          filteredConversations = mockConversations.filter(conversation =>
            conversation.name?.toLowerCase().includes(keywords) ||
            conversation.message?.some(msg =>
              msg.content?.toLowerCase().includes(keywords)
            )
          );
        }

        // 应用分页
        if (params.page !== undefined && params.page_size !== undefined) {
          const startIndex = params.page * params.page_size;
          const endIndex = startIndex + params.page_size;
          filteredConversations = filteredConversations.slice(startIndex, endIndex);
        }

        return filteredConversations;
      }

      // 首先获取所有dialogs
      const { data: dialogsResponse } = await chatService.listDialogs();
      const dialogs = dialogsResponse?.data || [];

      if (dialogs.length === 0) {
        return [];
      }

      // 为每个dialog获取其conversations
      const allConversations: (IConversation & { dialog_name?: string })[] = [];

      for (const dialog of dialogs) {
        try {
          const { data: conversationsResponse } = await chatService.listConversations({
            dialog_id: dialog.id,
            page: 0,
            page_size: 1000, // 获取所有conversations
          });

          const conversations = conversationsResponse?.data || [];
          // 为每个conversation添加dialog信息
          const conversationsWithDialog = conversations.map(conv => ({
            ...conv,
            dialog_name: dialog.name,
          }));
          allConversations.push(...conversationsWithDialog);
        } catch (error) {
          console.error(`Failed to fetch conversations for dialog ${dialog.id}:`, error);
        }
      }

      // 应用搜索过滤
      let filteredConversations = allConversations;
      if (params.keywords) {
        const keywords = params.keywords.toLowerCase();
        filteredConversations = allConversations.filter(conversation =>
          conversation.name?.toLowerCase().includes(keywords) ||
          conversation.message?.some(msg => 
            msg.content?.toLowerCase().includes(keywords)
          )
        );
      }

      // 按更新时间排序（最新的在前）
      filteredConversations.sort((a, b) => 
        new Date(b.update_time).getTime() - new Date(a.update_time).getTime()
      );

      // 应用分页
      if (params.page !== undefined && params.page_size !== undefined) {
        const startIndex = params.page * params.page_size;
        const endIndex = startIndex + params.page_size;
        filteredConversations = filteredConversations.slice(startIndex, endIndex);
      }

      return filteredConversations;
    },
    refetchOnWindowFocus: false,
    staleTime: 2 * 60 * 1000, // 2分钟缓存
  });
};

// 删除conversation
export const useDeleteConversation = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (conversationId: string) => {
      return await chatService.deleteConversation({
        conversation_ids: [conversationId],
      });
    },
    onSuccess: () => {
      // 刷新conversations列表
      queryClient.invalidateQueries({ queryKey: ['conversationsList'] });
      // 也刷新相关的conversations查询
      queryClient.invalidateQueries({ queryKey: ['conversations'] });
      message.success('Conversation deleted successfully');
    },
    onError: (error) => {
      console.error('Delete conversation failed:', error);
      message.error('Failed to delete conversation');
    },
  });
};

// 批量删除conversations
export const useBatchDeleteConversations = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (conversationIds: string[]) => {
      return await chatService.deleteConversation({
        conversation_ids: conversationIds,
      });
    },
    onSuccess: (data, variables) => {
      // 刷新conversations列表
      queryClient.invalidateQueries({ queryKey: ['conversationsList'] });
      queryClient.invalidateQueries({ queryKey: ['conversations'] });
      message.success(`${variables.length} conversation(s) deleted successfully`);
    },
    onError: (error) => {
      console.error('Batch delete conversations failed:', error);
      message.error('Failed to delete conversations');
    },
  });
};

// 获取conversation统计信息
export const useConversationsStats = () => {
  return useQuery({
    queryKey: ['conversationsStats'],
    queryFn: async () => {
      // 首先获取所有dialogs
      const { data: dialogsResponse } = await chatService.listDialogs();
      const dialogs = dialogsResponse?.data || [];

      if (dialogs.length === 0) {
        return {
          total: 0,
          withMessages: 0,
          recentlyActive: 0,
          empty: 0,
        };
      }

      // 为每个dialog获取其conversations
      const allConversations: IConversation[] = [];

      for (const dialog of dialogs) {
        try {
          const { data: conversationsResponse } = await chatService.listConversations({
            dialog_id: dialog.id,
            page: 0,
            page_size: 1000,
          });

          const conversations = conversationsResponse?.data || [];
          allConversations.push(...conversations);
        } catch (error) {
          console.error(`Failed to fetch conversations for dialog ${dialog.id}:`, error);
        }
      }

      const total = allConversations.length;
      const withMessages = allConversations.filter(c => c.message && c.message.length > 0).length;
      const recentlyActive = allConversations.filter(c => {
        const updateTime = new Date(c.update_time);
        const oneDayAgo = new Date(Date.now() - 24 * 60 * 60 * 1000);
        return updateTime > oneDayAgo;
      }).length;

      return {
        total,
        withMessages,
        recentlyActive,
        empty: total - withMessages,
      };
    },
    refetchOnWindowFocus: false,
    staleTime: 5 * 60 * 1000, // 5分钟缓存
  });
};
