import { useState, useCallback, useEffect } from 'react';
import { message } from 'antd';
import aiReadService from '@/services/ai-read-service';

export interface AIReadingFile {
  file_id: string;
  original_filename: string;
  file_type: string;
  file_size: number;
  processing_status: 'uploaded' | 'converting_to_pdf' | 'calling_parser' | 'processing' | 'saving_results' | 'generating_summary' | 'completed' | 'failed';
  processing_progress: number;
  processing_message: string;
  content_summary?: string;
  create_time: number;
  update_time: number;
}

export interface AIReadingConversation {
  conversation_id: string;
  question: string;
  answer: string;
  confidence_score: number;
  session_id: string;
  processing_status: string;
  create_time: number;
  update_time: number;
}

export const useAIReading = () => {
  const [files, setFiles] = useState<AIReadingFile[]>([]);
  const [currentFile, setCurrentFile] = useState<AIReadingFile | null>(null);
  const [conversations, setConversations] = useState<AIReadingConversation[]>([]);
  const [loading, setLoading] = useState(false);
  const [uploading, setUploading] = useState(false);
  const [processing, setProcessing] = useState<Record<string, boolean>>({});
  const [pollingFiles, setPollingFiles] = useState<Set<string>>(new Set()); // 正在轮询的文件ID

  // 获取文件列表
  const fetchFiles = useCallback(async () => {
    try {
      setLoading(true);
      const response = await aiReadService.getFileList();
      if (response.code === 0) {
        setFiles(response.data.files);

        // 如果没有当前文件且有文件列表，选择第一个
        if (!currentFile && response.data.files.length > 0) {
          setCurrentFile(response.data.files[0]);
        }
      } else {
        message.error(response.message || '获取文件列表失败');
      }
    } catch (error) {
      console.error('获取文件列表失败:', error);
      message.error('获取文件列表失败');
    } finally {
      setLoading(false);
    }
  }, []); // 移除currentFile依赖，避免循环调用

  // 上传文件
  const uploadFile = useCallback(async (file: File) => {
    try {
      setUploading(true);
      const response = await aiReadService.uploadFile(file);
      if (response.code === 0) {
        message.success('文件上传成功');
        
        // 刷新文件列表
        await fetchFiles();
        
        // 自动开始处理文件
        await processFile(response.data.file_id);
        
        return response.data.file_id;
      } else {
        message.error(response.message || '文件上传失败');
        return null;
      }
    } catch (error) {
      console.error('文件上传失败:', error);
      message.error('文件上传失败');
      return null;
    } finally {
      setUploading(false);
    }
  }, [fetchFiles]);

  // 处理文件
  const processFile = useCallback(async (fileId: string) => {
    try {
      setProcessing(prev => ({ ...prev, [fileId]: true }));
      const response = await aiReadService.processFile(fileId);
      if (response.code === 0) {
        message.success('开始处理文件');
        
        // 开始轮询状态
        pollFileStatus(fileId);
      } else {
        message.error(response.message || '处理文件失败');
      }
    } catch (error) {
      console.error('处理文件失败:', error);
      message.error('处理文件失败');
    } finally {
      setProcessing(prev => ({ ...prev, [fileId]: false }));
    }
  }, []);

  // 轮询文件状态
  const pollFileStatus = useCallback(async (fileId: string) => {
    // 检查是否已经在轮询中
    if (pollingFiles.has(fileId)) {
      console.log(`文件 ${fileId} 已在轮询中，跳过重复轮询`);
      return;
    }

    // 添加到轮询列表
    setPollingFiles(prev => new Set(prev).add(fileId));

    const maxAttempts = 60; // 最多轮询10分钟
    let attempts = 0;

    const poll = async () => {
      try {
        const response = await aiReadService.getFileStatus(fileId);
        if (response.code === 0) {
          const status = response.data.processing_status;

          // 更新文件列表中的状态
          setFiles(prev => prev.map(file =>
            file.file_id === fileId
              ? {
                  ...file,
                  processing_status: status,
                  processing_progress: response.data.processing_progress,
                  processing_message: response.data.processing_message,
                  content_summary: response.data.content_summary
                }
              : file
          ));

          // 更新当前文件状态
          if (currentFile?.file_id === fileId) {
            setCurrentFile(prev => prev ? {
              ...prev,
              processing_status: status,
              processing_progress: response.data.processing_progress,
              processing_message: response.data.processing_message,
              content_summary: response.data.content_summary
            } : null);
          }

          // 检查是否完成或失败
          if (status === 'completed') {
            message.success('文件处理完成，内容已更新');

            // 刷新完整的文件列表以确保获取最新数据
            setTimeout(() => {
              fetchFiles();
            }, 1000);

            // 从轮询列表中移除
            setPollingFiles(prev => {
              const newSet = new Set(prev);
              newSet.delete(fileId);
              return newSet;
            });
            return;
          } else if (status === 'failed') {
            message.error(`文件处理失败: ${response.data.processing_message}`);
            // 从轮询列表中移除
            setPollingFiles(prev => {
              const newSet = new Set(prev);
              newSet.delete(fileId);
              return newSet;
            });
            return;
          }

          // 只有在处理中的状态才继续轮询
          if (['processing', 'converting_to_pdf', 'calling_parser', 'saving_results', 'generating_summary'].includes(status)) {
            attempts++;
            if (attempts < maxAttempts) {
              setTimeout(poll, 5000); // 5秒后再次检查，提高响应速度
            } else {
              message.warning('文件处理超时，请手动刷新状态');
              // 从轮询列表中移除
              setPollingFiles(prev => {
                const newSet = new Set(prev);
                newSet.delete(fileId);
                return newSet;
              });
            }
          } else {
            // 其他状态停止轮询
            setPollingFiles(prev => {
              const newSet = new Set(prev);
              newSet.delete(fileId);
              return newSet;
            });
          }
        }
      } catch (error) {
        console.error('获取文件状态失败:', error);
        attempts++;
        if (attempts < maxAttempts) {
          setTimeout(poll, 10000);
        } else {
          // 从轮询列表中移除
          setPollingFiles(prev => {
            const newSet = new Set(prev);
            newSet.delete(fileId);
            return newSet;
          });
        }
      }
    };

    poll();
  }, [currentFile, pollingFiles, fetchFiles]);

  // 与文档聊天
  const chatWithDocument = useCallback(async (fileId: string, question: string, sessionId?: string) => {
    try {
      const response = await aiReadService.chatWithDocument(fileId, question, sessionId);

      if (response.code === 0) {
        // 刷新对话历史 - 暂时不使用session_id过滤
        await fetchConversations(fileId);
        return response.data;
      } else {
        message.error(response.message || '问答失败');
        return null;
      }
    } catch (error) {
      console.error('问答失败:', error);
      message.error('问答失败');
      return null;
    }
  }, []);

  // 获取对话历史
  const fetchConversations = useCallback(async (fileId: string, sessionId?: string) => {
    try {
      // 获取所有对话记录（不过滤session_id）
      const response = await aiReadService.getConversations(fileId, 1, 50);

      if (response.code === 0) {
        setConversations(response.data.conversations);
      } else {
        message.error(response.message || '获取对话历史失败');
      }
    } catch (error) {
      message.error('获取对话历史失败');
    }
  }, []);

  // 选择当前文件
  const selectFile = useCallback((file: AIReadingFile) => {
    setCurrentFile(file);
    // 清空对话历史，等待用户选择会话
    setConversations([]);
  }, []);

  // 删除文件
  const deleteFile = useCallback(async (fileId: string) => {
    try {
      const response = await aiReadService.deleteFile(fileId);
      if (response.code === 0) {
        message.success('文件删除成功');

        // 如果删除的是当前文件，清空当前文件
        if (currentFile?.file_id === fileId) {
          setCurrentFile(null);
          setConversations([]);
        }

        // 刷新文件列表
        await fetchFiles();
      } else {
        message.error(response.message || '删除文件失败');
      }
    } catch (error) {
      console.error('删除文件失败:', error);
      message.error('删除文件失败');
    }
  }, [currentFile, fetchFiles]);

  // 刷新文件状态
  const refreshFileStatus = useCallback(async (fileId: string) => {
    try {
      const response = await aiReadService.getFileStatus(fileId);
      if (response.code === 0) {
        setFiles(prev => prev.map(file => 
          file.file_id === fileId 
            ? { 
                ...file, 
                processing_status: response.data.processing_status,
                processing_progress: response.data.processing_progress,
                processing_message: response.data.processing_message,
                content_summary: response.data.content_summary
              }
            : file
        ));

        if (currentFile?.file_id === fileId) {
          setCurrentFile(prev => prev ? {
            ...prev,
            processing_status: response.data.processing_status,
            processing_progress: response.data.processing_progress,
            processing_message: response.data.processing_message,
            content_summary: response.data.content_summary
          } : null);
        }
      }
    } catch (error) {
      console.error('刷新文件状态失败:', error);
      message.error('刷新文件状态失败');
    }
  }, [currentFile]);

  // 初始化时获取文件列表
  useEffect(() => {
    fetchFiles();
  }, []); // 只在组件挂载时调用一次

  return {
    // 状态
    files,
    currentFile,
    conversations,
    loading,
    uploading,
    processing,

    // 方法
    uploadFile,
    processFile,
    deleteFile,
    chatWithDocument,
    fetchConversations,
    selectFile,
    refreshFileStatus,
    fetchFiles,
  };
};
