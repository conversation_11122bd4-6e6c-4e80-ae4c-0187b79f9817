import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { message } from 'antd';
import { useTranslation } from 'react-i18next';
import { knowledgeBaseApi } from '@/services/api';
import type { KnowledgeBase } from '@/services/api';

// 获取知识库列表Hook
export const useKnowledgeBaseList = (params?: {
  page?: number;
  page_size?: number;
  orderby?: string;
  desc?: boolean;
  keywords?: string;
}) => {
  return useQuery({
    queryKey: ['knowledgeBaseList', params],
    queryFn: async () => {
      const { data: res } = await knowledgeBaseApi.getList(params);
      return res.data;
    },
    staleTime: 30000, // 30秒
  });
};

// 获取知识库详情Hook
export const useKnowledgeBaseDetail = (kb_id: string) => {
  return useQuery({
    queryKey: ['knowledgeBaseDetail', kb_id],
    queryFn: async () => {
      const { data: res } = await knowledgeBaseApi.getDetail({ kb_id });
      return res.data;
    },
    enabled: !!kb_id,
    staleTime: 60000, // 1分钟
  });
};

// 创建知识库Hook
export const useCreateKnowledgeBase = () => {
  const { t } = useTranslation();
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (params: {
      name: string;
      language?: string;
      description?: string;
      permission?: string;
      parser_id?: string;
      parser_config?: Record<string, any>;
    }) => {
      const { data: res } = await knowledgeBaseApi.create(params);
      return res.data;
    },
    onSuccess: (data) => {
      message.success(t('knowledgeBase.createSuccess'));
      // 刷新知识库列表
      queryClient.invalidateQueries({ queryKey: ['knowledgeBaseList'] });
    },
    onError: (error: any) => {
      console.error('Create knowledge base failed:', error);
      message.error(error.message || '创建知识库失败');
    },
  });
};

// 更新知识库Hook
export const useUpdateKnowledgeBase = () => {
  const { t } = useTranslation();
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (params: {
      kb_id: string;
      name?: string;
      language?: string;
      description?: string;
      permission?: string;
      parser_id?: string;
      parser_config?: Record<string, any>;
    }) => {
      const { data: res } = await knowledgeBaseApi.update(params);
      return res.data;
    },
    onSuccess: (data, variables) => {
      message.success('更新知识库成功');
      // 刷新相关查询
      queryClient.invalidateQueries({ queryKey: ['knowledgeBaseList'] });
      queryClient.invalidateQueries({ queryKey: ['knowledgeBaseDetail', variables.kb_id] });
    },
    onError: (error: any) => {
      console.error('Update knowledge base failed:', error);
      message.error(error.message || '更新知识库失败');
    },
  });
};

// 删除知识库Hook
export const useDeleteKnowledgeBase = () => {
  const { t } = useTranslation();
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (kb_id: string) => {
      const { data: res } = await knowledgeBaseApi.delete({ kb_id });
      return res.data;
    },
    onSuccess: () => {
      message.success(t('knowledgeBase.deleteSuccess'));
      // 刷新知识库列表
      queryClient.invalidateQueries({ queryKey: ['knowledgeBaseList'] });
    },
    onError: (error: any) => {
      console.error('Delete knowledge base failed:', error);
      message.error(error.message || '删除知识库失败');
    },
  });
};

// 知识库统计Hook
export const useKnowledgeBaseStats = () => {
  return useQuery({
    queryKey: ['knowledgeBaseStats'],
    queryFn: async () => {
      // 获取知识库列表来计算统计信息
      const { data: res } = await knowledgeBaseApi.getList({ page_size: 1000 });
      const kbs = res.data.kbs || [];

      return {
        total: kbs.length,
        totalDocs: kbs.reduce((sum: number, kb: any) => sum + (kb.doc_num || 0), 0),
        totalChunks: kbs.reduce((sum: number, kb: any) => sum + (kb.chunk_num || 0), 0),
        recentKbs: kbs.slice(0, 5), // 最近的5个知识库
      };
    },
    staleTime: 60000, // 1分钟
  });
};
