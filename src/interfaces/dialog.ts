// Dialog相关接口定义，参考原版web/src/interfaces/database/chat.ts

export interface PromptConfig {
  empty_response: string;
  parameters: Parameter[];
  prologue: string;
  system: string;
  tts?: boolean;
  quote?: boolean;
  keyword?: boolean;
  refine_multiturn?: boolean;
  reasoning?: boolean;
}

export interface Parameter {
  key: string;
  optional: boolean;
}

export interface LlmSetting {
  Creative: Variable;
  Custom: Variable;
  Evenly: Variable;
  Precise: Variable;
}

export interface Variable {
  frequency_penalty?: number;
  max_tokens?: number;
  presence_penalty?: number;
  temperature?: number;
  top_p?: number;
}

export interface IDialog {
  create_date: string;
  create_time: number;
  description: string;
  icon: string;
  id: string;
  dialog_id: string;
  kb_ids: string[];
  kb_names: string[];
  language: string;
  llm_id: string;
  llm_setting: Variable;
  llm_setting_type: string;
  name: string;
  prompt_config: PromptConfig;
  prompt_type: string;
  status: string;
  tenant_id: string;
  update_date: string;
  update_time: number;
  vector_similarity_weight: number;
  similarity_threshold: number;
  top_n?: number;
  rerank_id?: string;
  top_k?: number;
}

// Dialog创建/更新参数 - 与原版IDialog保持一致
export interface IDialogParams {
  dialog_id?: string;
  name: string;
  description?: string;
  icon?: string;
  kb_ids?: string[];
  language?: string;
  llm_id?: string;
  llm_setting?: Variable;
  llm_setting_type?: string;
  prompt_config?: PromptConfig;
  prompt_type?: string;
  vector_similarity_weight?: number;
  similarity_threshold?: number;
  // 新增缺少的参数
  status?: string;
  tenant_id?: string;
}

// Dialog列表查询参数
export interface IDialogListParams {
  page?: number;
  page_size?: number;
  orderby?: string;
  desc?: boolean;
  keywords?: string;
}

// Dialog删除参数
export interface IDialogRemoveParams {
  dialogIds: string[];
}

// Dialog获取参数
export interface IDialogGetParams {
  dialogId: string;
}

// API响应格式
export interface IDialogResponse<T = any> {
  code: number;
  message?: string;
  data: T;
}

export interface IDialogListResponse {
  dialogs: IDialog[];
  total: number;
}
