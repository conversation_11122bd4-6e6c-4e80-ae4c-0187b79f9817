export interface IDocument {
  id: string;
  name: string;
  kb_id: string;
  type: string;
  size: number;
  chunk_num: number;
  token_num: number;
  parser_id: string;
  parser_config: any;
  status: string;
  run: string;
  progress: number;
  progress_msg: string;
  process_begin_at: string;
  process_duation: number;
  create_time: string;
  update_time: string;
  thumbnail?: string;
  location?: string;
}

export interface IDocumentListParams {
  kb_id: string;
  page?: number;
  page_size?: number;
  keywords?: string;
  orderby?: string;
  desc?: boolean;
  run_status?: string[];
  types?: string[];
}

export interface IDocumentListResponse {
  total: number;
  docs: IDocument[];
}

export interface IUploadDocumentParams {
  kb_id: string;
  file: File;
}

export interface IWebCrawlParams {
  kb_id: string;
  name: string;
  url: string;
}

export interface IChangeDocumentStatusParams {
  doc_id: string;
  status: string;
}

export interface IRunDocumentParams {
  doc_ids: string[];
  run: string;
  delete?: boolean;
}

export interface IDeleteDocumentParams {
  doc_ids: string[];
}

export interface IRenameDocumentParams {
  doc_id: string;
  name: string;
}

export interface IChangeParserParams {
  doc_id: string;
  parser_id: string;
  parser_config?: any;
}

export interface IDocumentChunk {
  chunk_id: string; // 后端返回的字段名是chunk_id，不是id
  content_with_weight: string;
  doc_id: string;
  docnm_kwd: string;
  important_kwd: string[];
  question_kwd: string[];
  image_id: string;
  available_int: number;
  positions: number[][];
}

export interface IChunkListParams {
  doc_id: string;
  page?: number;
  size?: number;
  keywords?: string;
}

export interface IChunkListResponse {
  total: number;
  chunks: IDocumentChunk[];
}
