// 聊天相关接口定义

export interface IMessage {
  id: string;
  role: 'user' | 'assistant';
  content: string;
  create_time?: string;
  reference?: {
    chunks: IReference[];
    total: number;
    doc_aggs?: IDocumentAgg[];
  };
}

export interface IDocumentAgg {
  doc_id: string;
  doc_name: string;
  count: number;
}

export interface IReference {
  id: string;
  content: string;
  document_id: string;
  document_name: string;
  dataset_id: string;
  image_id?: string;
  positions?: number[][];
  url?: string;
  similarity: number;
  vector_similarity?: number;
  term_similarity?: number;
  doc_type?: string;
  // 增强字段
  thumbnail?: string;
  document_info?: any;
  image_data?: any;
  // 兼容旧格式
  name?: string;
  kb_id?: string;
  kb_name?: string;
}

export interface IConversation {
  id: string;
  name: string;
  dialog_id: string;
  create_time: string;
  update_time: string;
  message: IMessage[];
}

export interface IConversationListParams {
  dialog_id: string;
  page?: number;
  page_size?: number;
}

export interface IConversationListResponse {
  code: number;
  data: IConversation[];
  message: string;
}

export interface IConversationResponse {
  code: number;
  data: IConversation;
  message: string;
}

export interface ISendMessageParams {
  conversation_id?: string;
  dialog_id: string;
  messages: IMessage[];
}

export interface ISendMessageResponse {
  code: number;
  data: {
    answer: string;
    reference: IReference[];
    conversation_id: string;
  };
  message: string;
}

export interface ICreateConversationParams {
  dialog_id: string;
  name?: string;
}

export interface ICreateConversationResponse {
  code: number;
  data: IConversation;
  message: string;
}

export interface IDeleteConversationParams {
  conversation_id: string;
}

export interface IDeleteConversationResponse {
  code: number;
  data: null;
  message: string;
}

export interface IDeleteMessageParams {
  conversation_id: string;
  message_id: string;
}

export interface IDeleteMessageResponse {
  code: number;
  data: null;
  message: string;
}

// 聊天状态
export interface IChatState {
  currentConversation: IConversation | null;
  conversations: IConversation[];
  loading: boolean;
  sending: boolean;
  error: string | null;
}

// 消息输入状态
export interface IMessageInputState {
  value: string;
  files: any[];
  uploading: boolean;
}

// SSE响应数据
export interface ISSEData {
  answer: string;
  reference: IReference[];
  conversation_id: string;
  message_id: string;
}

// 聊天搜索参数
export interface IChatSearchParams {
  dialog_id: string;
  conversation_id?: string;
  is_new?: boolean;
}
