export interface IKnowledge {
  id: string;
  name: string;
  description: string;
  language: string;
  permission: string;
  doc_num: number;
  chunk_num: number;
  parser_id: string;
  parser_config: Record<string, any>;
  embd_id: string;
  avatar: string;
  tenant_id: string;
  create_time: string;
  update_time: string;
  created_by: string;
}

export interface IDocument {
  id: string;
  name: string;
  type: string;
  size: number;
  chunk_num: number;
  kb_id: string;
  parser_id: string;
  parser_config: Record<string, any>;
  source_type: string;
  type_: string;
  created_by: string;
  create_time: string;
  update_time: string;
  run: string;
  progress: number;
  progress_msg: string;
  process_begin_at: string;
  process_duation: number;
}

export interface IChunk {
  id: string;
  content_ltks: string;
  content_with_weight: string;
  doc_id: string;
  docnm_kwd: string;
  important_kwd: string[];
  img_id: string;
  kb_id: string;
  page_num: number;
  positions: string;
  create_time: string;
  update_time: string;
}

export interface IConversation {
  id: string;
  name: string;
  message: IMessage[];
  kb_ids: string[];
  llm_id: string;
  prompt_config: Record<string, any>;
  create_time: string;
  update_time: string;
}

export interface IMessage {
  id: string;
  content: string;
  role: 'user' | 'assistant';
  create_time: string;
  update_time: string;
}
