export interface ResponseType<T = any> {
  code: number;
  message: string;
  data?: T;
}

export interface ResponsePostType<T = any> extends ResponseType<T> {
  response: Response;
}

export interface PaginationProps {
  current: number;
  pageSize: number;
  total: number;
}

export interface BaseListResponse<T = any> {
  total: number;
  list: T[];
}

export interface IUserInfo {
  id: string;
  nickname: string;
  email: string;
  avatar?: string;
  access_token: string;
  create_time: string;
  update_time: string;
  is_superuser: boolean;
  login_channel: string;
  last_login_time: string;
}

export interface ITenantInfo {
  tenant_id: string;
  name: string;
  llm_id: string;
  embd_id: string;
  asr_id: string;
  img2txt_id: string;
  rerank_id: string;
  parser_ids: string;
  create_time: string;
  update_time: string;
}
