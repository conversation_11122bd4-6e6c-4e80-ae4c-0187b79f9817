/**
 * 复制文本到剪贴板的通用工具函数
 * 支持现代浏览器的 Clipboard API 和传统的 execCommand 方法
 */

export const copyToClipboard = async (text: string): Promise<boolean> => {
  try {
    if (navigator.clipboard && navigator.clipboard.writeText) {
      // 现代浏览器的 Clipboard API
      await navigator.clipboard.writeText(text);
      return true;
    } else {
      // 兼容性方案：使用传统的 document.execCommand
      const textArea = document.createElement('textarea');
      textArea.value = text;
      textArea.style.position = 'fixed';
      textArea.style.left = '-999999px';
      textArea.style.top = '-999999px';
      document.body.appendChild(textArea);
      textArea.focus();
      textArea.select();
      
      const successful = document.execCommand('copy');
      document.body.removeChild(textArea);
      
      return successful;
    }
  } catch (error) {
    return false;
  }
};

/**
 * 检查是否支持剪贴板操作
 */
export const isClipboardSupported = (): boolean => {
  return !!(
    (navigator.clipboard && navigator.clipboard.writeText) ||
    document.execCommand
  );
};

/**
 * 从剪贴板读取文本（仅支持现代浏览器）
 */
export const readFromClipboard = async (): Promise<string | null> => {
  try {
    if (navigator.clipboard && navigator.clipboard.readText) {
      return await navigator.clipboard.readText();
    } else {
      return null;
    }
  } catch (error) {
    return null;
  }
};
