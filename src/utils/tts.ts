/**
 * TTS工具函数
 * 用于处理文本转语音功能的相关工具
 */

/**
 * 清洗文本，去除[ID:*]和换行符
 * @param text 原始文本
 * @returns 清洗后的文本
 */
export function cleanTextForTTS(text: string): string {
  if (!text || typeof text !== 'string') return '';

  // 去除[ID:*]格式的标识符
  let cleanedText = text.replace(/\[ID:[^\]]*\]/g, '');

  // 去除换行符和回车符
  cleanedText = cleanedText.replace(/[\n\r]/g, ' ');

  // 去除HTML标签（如果有）
  cleanedText = cleanedText.replace(/<[^>]*>/g, '');

  // 去除特殊字符，只保留中文、英文、数字、常用标点符号
  cleanedText = cleanedText.replace(/[^\u4e00-\u9fa5a-zA-Z0-9\s，。！？；：、""''（）【】《》\-\.]/g, '');

  // 去除多余的空格
  cleanedText = cleanedText.replace(/\s+/g, ' ').trim();

  // 确保文本不会被后端分割成空字符串
  // 后端使用 re.split(r"[，。/《》？；：！\n\r:;]+", text) 进行分割
  // 如果文本以这些字符结尾，会产生空字符串
  cleanedText = cleanedText.replace(/[，。！？；：]+$/, '');

  // 如果文本为空，返回空字符串
  if (!cleanedText.trim()) {
    return '';
  }

  return cleanedText;
}

/**
 * 将文本分句，按照40字为界限
 * @param text 要分句的文本
 * @returns 分句后的文本数组
 */
export function splitTextForTTS(text: string): string[] {
  if (!text || typeof text !== 'string') return [];

  const cleanedText = cleanTextForTTS(text);

  // 如果清洗后的文本为空或只有空格，返回空数组
  if (!cleanedText || cleanedText.trim().length === 0) {
    console.warn('TTS: 文本清洗后为空', { original: text, cleaned: cleanedText });
    return [];
  }

  // 如果文本长度小于等于40字，直接返回
  if (cleanedText.length <= 40) {
    return [cleanedText];
  }
  
  // 按照标点符号分句
  const sentences = cleanedText.split(/([。！？；：，])/);
  const result: string[] = [];
  let currentSentence = '';
  
  for (let i = 0; i < sentences.length; i++) {
    const part = sentences[i];
    
    // 如果是标点符号，直接添加到当前句子
    if (/[。！？；：，]/.test(part)) {
      currentSentence += part;
      
      // 检查当前句子长度
      if (currentSentence.length >= 40) {
        result.push(currentSentence.trim());
        currentSentence = '';
      }
    } else {
      // 如果添加这部分会超过40字，先保存当前句子
      if (currentSentence.length + part.length > 40 && currentSentence.length > 0) {
        result.push(currentSentence.trim());
        currentSentence = part;
      } else {
        currentSentence += part;
      }
    }
  }
  
  // 添加最后一个句子
  if (currentSentence.trim()) {
    result.push(currentSentence.trim());
  }
  
  // 如果没有分句成功，按照40字强制分割
  if (result.length === 0) {
    for (let i = 0; i < cleanedText.length; i += 40) {
      result.push(cleanedText.slice(i, i + 40));
    }
  }
  
  // 过滤并验证每个句子
  return result
    .filter(s => s.length > 0)
    .map(s => {
      // 确保句子不会被后端分割成空字符串
      let cleaned = s.replace(/[，。！？；：]+$/, '').trim();
      // 如果清理后为空，保留原句子但去掉末尾标点
      if (!cleaned) {
        cleaned = s.replace(/[，。！？；：\s]+$/, '').trim();
      }
      return cleaned;
    })
    .filter(s => s.length > 0);
}

/**
 * 测试文本清洗和分句功能
 * @param text 测试文本
 */
export function testTTSTextProcessing(text: string): void {
  console.group('🎵 TTS文本处理测试');
  console.log('原始文本:', text);

  const cleaned = cleanTextForTTS(text);
  console.log('清洗后文本:', cleaned);
  console.log('清洗后长度:', cleaned.length);

  const sentences = splitTextForTTS(text);
  console.log('分句结果:', sentences);
  console.log('分句数量:', sentences.length);

  sentences.forEach((sentence, index) => {
    console.log(`句子${index + 1} (${sentence.length}字):`, sentence);
  });

  console.groupEnd();
}

/**
 * 测试后端分割逻辑
 * @param text 测试文本
 */
export function testBackendSplit(text: string): void {
  console.group('🎵 后端分割测试');
  console.log('原始文本:', text);

  // 模拟后端分割逻辑
  const parts = text.split(/[，。/《》？；：！\n\r:;]+/);
  console.log('分割结果:', parts);
  console.log('有效部分:', parts.filter(p => p.trim().length > 0));
  console.log('空部分数量:', parts.filter(p => p.trim().length === 0).length);

  console.groupEnd();
}

/**
 * 音频播放状态
 */
export enum AudioPlayState {
  IDLE = 'idle',
  LOADING = 'loading',
  PLAYING = 'playing',
  PAUSED = 'paused',
  ERROR = 'error'
}

/**
 * 音频播放管理器
 */
export class AudioManager {
  private audio: HTMLAudioElement | null = null;
  private currentState: AudioPlayState = AudioPlayState.IDLE;
  private onStateChange?: (state: AudioPlayState) => void;
  private onError?: (error: string) => void;
  private loadedDataHandler?: () => void;
  private endedHandler?: () => void;
  private errorHandler?: (e: Event) => void;
  
  constructor(
    onStateChange?: (state: AudioPlayState) => void,
    onError?: (error: string) => void
  ) {
    this.onStateChange = onStateChange;
    this.onError = onError;
  }
  
  /**
   * 播放音频
   * @param audioBlob 音频数据
   */
  async playAudio(audioBlob: Blob): Promise<void> {
    try {
      this.setState(AudioPlayState.LOADING);

      // 清理当前音频资源（但不调用pause）
      this.cleanup();

      // 创建新的音频对象
      const audioUrl = URL.createObjectURL(audioBlob);
      console.log('🎵 创建音频URL:', audioUrl);
      console.log('🎵 音频Blob详情:', {
        size: audioBlob.size,
        type: audioBlob.type,
        url: audioUrl
      });

      this.audio = new Audio(audioUrl);

      // 设置音频属性
      this.audio.preload = 'auto';
      this.audio.volume = 1.0;

      console.log('🎵 音频元素创建完成:', {
        src: this.audio.src,
        readyState: this.audio.readyState,
        networkState: this.audio.networkState
      });

      // 创建事件处理器
      this.loadedDataHandler = () => {
        console.log('🎵 音频数据加载完成');
      };

      this.endedHandler = () => {
        console.log('🎵 音频播放结束');
        this.setState(AudioPlayState.IDLE);
        this.cleanup();
      };

      this.errorHandler = (e) => {
        console.error('🎵 音频播放错误:', e);
        this.setState(AudioPlayState.ERROR);
        this.onError?.('音频播放失败');
        this.cleanup();
      };

      // 简化播放逻辑，直接在loadeddata事件中播放
      this.loadedDataHandler = async () => {
        try {
          console.log('🎵 音频数据加载完成，开始播放');
          await this.audio!.play();
          this.setState(AudioPlayState.PLAYING);
        } catch (playError) {
          console.error('🎵 音频播放失败:', playError);
          this.setState(AudioPlayState.ERROR);
          this.onError?.('音频播放失败: ' + (playError as Error).message);
        }
      };

      // 添加事件监听器
      this.audio.addEventListener('loadeddata', this.loadedDataHandler);
      this.audio.addEventListener('ended', this.endedHandler);
      this.audio.addEventListener('error', this.errorHandler);

      // 开始加载音频
      this.audio.load();

    } catch (error) {
      this.setState(AudioPlayState.ERROR);
      this.onError?.('音频播放失败: ' + (error as Error).message);
      this.cleanup();
    }
  }
  
  /**
   * 暂停播放
   */
  pause(): void {
    if (this.audio && this.currentState === AudioPlayState.PLAYING) {
      this.audio.pause();
      this.setState(AudioPlayState.PAUSED);
    }
  }
  
  /**
   * 恢复播放
   */
  resume(): void {
    if (this.audio && this.currentState === AudioPlayState.PAUSED) {
      this.audio.play();
      this.setState(AudioPlayState.PLAYING);
    }
  }
  
  /**
   * 停止播放
   */
  stop(): void {
    if (this.audio) {
      this.audio.pause();
      this.audio.currentTime = 0;
      this.cleanup();
    }
    this.setState(AudioPlayState.IDLE);
  }
  
  /**
   * 获取当前状态
   */
  getState(): AudioPlayState {
    return this.currentState;
  }
  
  /**
   * 设置状态
   */
  private setState(state: AudioPlayState): void {
    this.currentState = state;
    this.onStateChange?.(state);
  }
  
  /**
   * 清理资源
   */
  private cleanup(): void {
    if (this.audio) {
      const audioUrl = this.audio.src;

      // 正确移除事件监听器
      if (this.loadedDataHandler) {
        this.audio.removeEventListener('loadeddata', this.loadedDataHandler);
      }
      if (this.endedHandler) {
        this.audio.removeEventListener('ended', this.endedHandler);
      }
      if (this.errorHandler) {
        this.audio.removeEventListener('error', this.errorHandler);
      }

      this.audio = null;

      // 清理事件处理器引用
      this.loadedDataHandler = undefined;
      this.endedHandler = undefined;
      this.errorHandler = undefined;

      // 释放URL对象
      if (audioUrl && audioUrl.startsWith('blob:')) {
        URL.revokeObjectURL(audioUrl);
      }
    }
  }
  
  /**
   * 销毁管理器
   */
  destroy(): void {
    this.stop();
    this.cleanup();
    this.onStateChange = undefined;
    this.onError = undefined;
  }
}
