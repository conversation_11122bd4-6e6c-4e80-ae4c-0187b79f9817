/**
 * Mock conversations data for development and testing
 */

import { IConversation, IMessage } from '@/interfaces/chat';

/**
 * 检查是否应该使用模拟数据
 */
export const shouldUseMockData = (): boolean => {
  // 检查环境变量配置
  const useMockData = process.env.USE_MOCK_DATA;
  
  // 在开发环境中，如果明确设置了USE_MOCK_DATA=true，则使用模拟数据
  if (useMockData === 'true') {
    return true;
  }
  
  // 默认不使用模拟数据
  return false;
};

/**
 * 生成模拟的对话消息
 */
const generateMockMessages = (count: number = 3): IMessage[] => {
  const messages: IMessage[] = [];
  
  for (let i = 0; i < count; i++) {
    // 用户消息
    messages.push({
      id: `msg_user_${i}`,
      role: 'user',
      content: `这是第${i + 1}个用户问题，关于知识库的使用方法。`,
      create_time: new Date(Date.now() - (count - i) * 60000).toISOString(),
      update_time: new Date(Date.now() - (count - i) * 60000).toISOString(),
    });
    
    // 助手回复
    messages.push({
      id: `msg_assistant_${i}`,
      role: 'assistant',
      content: `这是第${i + 1}个助手回答，详细解释了知识库的使用方法和相关功能。`,
      create_time: new Date(Date.now() - (count - i) * 60000 + 30000).toISOString(),
      update_time: new Date(Date.now() - (count - i) * 60000 + 30000).toISOString(),
      reference: {
        chunks: [
          {
            id: `chunk_${i}_1`,
            content_ltks: `相关文档内容片段 ${i + 1}-1`,
            content_with_weight: `带权重的内容片段 ${i + 1}-1`,
            doc_id: `doc_${i}`,
            docnm_kwd: `文档${i + 1}.pdf`,
            important_kwd: ['知识库', '使用方法'],
            img_id: `img_${i}_1`,
            kb_id: `kb_${i}`,
            page_num: i + 1,
            positions: `[{"x": 100, "y": 200}]`,
            create_time: new Date().toISOString(),
            update_time: new Date().toISOString(),
          }
        ],
        total: 1,
        doc_aggs: [
          {
            doc_id: `doc_${i}`,
            doc_name: `文档${i + 1}.pdf`,
            count: 1,
          }
        ]
      }
    });
  }
  
  return messages;
};

/**
 * 生成模拟的对话列表
 */
export const generateMockConversations = (count: number = 10): IConversation[] => {
  const conversations: IConversation[] = [];
  
  for (let i = 0; i < count; i++) {
    conversations.push({
      id: `conv_${i}`,
      name: `模拟对话 ${i + 1}`,
      dialog_id: `dialog_${Math.floor(i / 3)}`, // 每3个对话属于同一个dialog
      create_time: new Date(Date.now() - i * 3600000).toISOString(), // 每小时一个
      update_time: new Date(Date.now() - i * 3600000 + 1800000).toISOString(), // 30分钟后更新
      message: generateMockMessages(Math.floor(Math.random() * 5) + 1), // 1-5条消息
    });
  }
  
  return conversations;
};

/**
 * 生成模拟的对话统计数据
 */
export const generateMockConversationStats = () => {
  return {
    total: 25,
    withMessages: 20,
    recentlyActive: 8,
    empty: 5,
  };
};

/**
 * 模拟API延迟
 */
export const mockApiDelay = (ms: number = 500): Promise<void> => {
  return new Promise(resolve => setTimeout(resolve, ms));
};
