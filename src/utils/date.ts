/**
 * 格式化日期显示
 * @param dateValue 日期值，可以是字符串、数字或Date对象
 * @param format 格式类型：'date' | 'datetime' | 'time'
 * @returns 格式化后的日期字符串，如果日期无效则返回 '-'
 */
export const formatDate = (
  dateValue: string | number | Date | null | undefined,
  format: 'date' | 'datetime' | 'time' = 'date'
): string => {
  // 检查输入值是否有效
  if (!dateValue) {
    return '-';
  }

  let date: Date;

  try {
    // 处理不同类型的输入
    if (typeof dateValue === 'string') {
      // 如果是空字符串或无效字符串
      if (dateValue.trim() === '' || dateValue === 'null' || dateValue === 'undefined') {
        return '-';
      }
      date = new Date(dateValue);
    } else if (typeof dateValue === 'number') {
      // 处理时间戳
      date = new Date(dateValue);
    } else if (dateValue instanceof Date) {
      date = dateValue;
    } else {
      return '-';
    }

    // 检查日期是否有效
    if (isNaN(date.getTime())) {
      return '-';
    }

    // 根据格式类型返回相应的格式
    switch (format) {
      case 'date':
        return date.toLocaleDateString('en-US', {
          year: 'numeric',
          month: '2-digit',
          day: '2-digit',
        });
      case 'datetime':
        return date.toLocaleString('en-US', {
          year: 'numeric',
          month: '2-digit',
          day: '2-digit',
          hour: '2-digit',
          minute: '2-digit',
          second: '2-digit',
          hour12: false,
        });
      case 'time':
        return date.toLocaleTimeString('en-US', {
          hour: '2-digit',
          minute: '2-digit',
          second: '2-digit',
          hour12: false,
        });
      default:
        return date.toLocaleDateString();
    }
  } catch (error) {
    return '-';
  }
};

/**
 * 格式化相对时间（如：2 hours ago, 3 days ago）
 * @param dateValue 日期值
 * @returns 相对时间字符串
 */
export const formatRelativeTime = (
  dateValue: string | number | Date | null | undefined
): string => {
  if (!dateValue) {
    return '-';
  }

  try {
    const date = new Date(dateValue);
    if (isNaN(date.getTime())) {
      return '-';
    }

    const now = new Date();
    const diffInSeconds = Math.floor((now.getTime() - date.getTime()) / 1000);

    if (diffInSeconds < 60) {
      return 'Just now';
    } else if (diffInSeconds < 3600) {
      const minutes = Math.floor(diffInSeconds / 60);
      return `${minutes} minute${minutes > 1 ? 's' : ''} ago`;
    } else if (diffInSeconds < 86400) {
      const hours = Math.floor(diffInSeconds / 3600);
      return `${hours} hour${hours > 1 ? 's' : ''} ago`;
    } else if (diffInSeconds < 2592000) {
      const days = Math.floor(diffInSeconds / 86400);
      return `${days} day${days > 1 ? 's' : ''} ago`;
    } else if (diffInSeconds < 31536000) {
      const months = Math.floor(diffInSeconds / 2592000);
      return `${months} month${months > 1 ? 's' : ''} ago`;
    } else {
      const years = Math.floor(diffInSeconds / 31536000);
      return `${years} year${years > 1 ? 's' : ''} ago`;
    }
  } catch (error) {
    console.warn('Relative time formatting error:', error, 'Input:', dateValue);
    return '-';
  }
};

/**
 * 检查日期是否有效
 * @param dateValue 日期值
 * @returns 是否为有效日期
 */
export const isValidDate = (
  dateValue: string | number | Date | null | undefined
): boolean => {
  if (!dateValue) {
    return false;
  }

  try {
    const date = new Date(dateValue);
    return !isNaN(date.getTime());
  } catch {
    return false;
  }
};

/**
 * 格式化持续时间（秒转换为可读格式）
 * @param seconds 秒数
 * @returns 格式化的持续时间字符串
 */
export const formatDuration = (seconds: number | null | undefined): string => {
  if (!seconds || seconds <= 0) {
    return '-';
  }

  const hours = Math.floor(seconds / 3600);
  const minutes = Math.floor((seconds % 3600) / 60);
  const remainingSeconds = Math.floor(seconds % 60);

  if (hours > 0) {
    return `${hours}h ${minutes}m ${remainingSeconds}s`;
  } else if (minutes > 0) {
    return `${minutes}m ${remainingSeconds}s`;
  } else {
    return `${remainingSeconds}s`;
  }
};
