let api_host = `/v1`;

export { api_host };

export default {
  // user
  login: `${api_host}/user/login`,
  logout: `${api_host}/user/logout`,
  register: `${api_host}/user/register`,
  setting: `${api_host}/user/setting`,
  user_info: `${api_host}/user/info`,
  tenant_info: `${api_host}/user/tenant_info`,
  set_tenant_info: `${api_host}/user/set_tenant_info`,
  login_channels: `${api_host}/user/login/channels`,
  login_channel: (channel: string) => `${api_host}/user/login/${channel}`,

  // knowledge base
  create_kb: `${api_host}/kb/create`,
  update_kb: `${api_host}/kb/update`,
  rm_kb: `${api_host}/kb/rm`,
  get_kb_detail: `${api_host}/kb/detail`,
  kb_list: `${api_host}/kb/list`,

  // document
  get_document_list: `${api_host}/document/list`,
  document_change_status: `${api_host}/document/change_status`,
  document_rm: `${api_host}/document/rm`,
  document_delete: `${api_host}/document/delete`,
  document_create: `${api_host}/document/create`,
  document_change_parser: `${api_host}/document/change_parser`,
  document_thumbnails: `${api_host}/document/thumbnails`,
  document_image: `${api_host}/document/image`,
  document_upload: `${api_host}/document/upload`,
  web_crawl: `${api_host}/document/web_crawl`,
  document_infos: `${api_host}/document/infos`,
  upload_and_parse: `${api_host}/document/upload_and_parse`,

  // chunk
  chunk_list: `${api_host}/chunk/list`,

// llm - 修正为RAGFlow标准路径
llm_factories: `${api_host}/llm/factories`,
llm_list: `${api_host}/llm/list`,
llm_my_llms: `${api_host}/llm/my_llms`,
llm_set_api_key: `${api_host}/llm/set_api_key`,
llm_add_llm: `${api_host}/llm/add_llm`,
llm_delete_llm: `${api_host}/llm/delete_llm`,
llm_delete_factory: `${api_host}/llm/delete_factory`,
llm_test: `${api_host}/llm/test`,

// llm model - 与原版web目录保持一致的命名
factories_list: `${api_host}/llm/factories`,
my_llm: `${api_host}/llm/my_llms`,
set_api_key: `${api_host}/llm/set_api_key`,
add_llm: `${api_host}/llm/add_llm`,
delete_llm: `${api_host}/llm/delete_llm`,
deleteFactory: `${api_host}/llm/delete_factory`,

// retrieval
retrieval_test: `${api_host}/chunk/retrieval_test`,
  create_chunk: `${api_host}/chunk/create`,
  set_chunk: `${api_host}/chunk/set`,
  get_chunk: `${api_host}/chunk/get`,
  switch_chunk: `${api_host}/chunk/switch`,
  rm_chunk: `${api_host}/chunk/rm`,

  // chat
  setDialog: `${api_host}/dialog/set`,
  getDialog: `${api_host}/dialog/get`,
  removeDialog: `${api_host}/dialog/rm`,
  listDialog: `${api_host}/dialog/list`,
  setConversation: `${api_host}/conversation/set`,
  getConversation: `${api_host}/conversation/get`,
  getConversationSSE: `${api_host}/conversation/getsse`,
  listConversation: `${api_host}/conversation/list`,
  removeConversation: `${api_host}/conversation/rm`,
  completeConversation: `${api_host}/conversation/completion`,
  deleteMessage: `${api_host}/conversation/delete_msg`,
  thumbup: `${api_host}/conversation/thumbup`,
  tts: `${api_host}/conversation/tts`,
  ask: `${api_host}/conversation/ask`,
  getRelatedQuestions: `${api_host}/conversation/related_questions`,



  // system
  getSystemStatus: `${api_host}/system/status`,
  getSystemVersion: `${api_host}/system/version`,
  getSystemTokenList: `${api_host}/system/token/list`,
  removeSystemToken: `${api_host}/system/token/rm`,
  createSystemToken: `${api_host}/system/token/create`,
  getSystemConfig: `${api_host}/system/config`,

  // tenant
  set_tenant_info: `${api_host}/user/set_tenant_info`,
  tenant_info: `${api_host}/user/tenant_info`,

  // document
  document_list: `${api_host}/document/list`,
  document_upload: `${api_host}/document/upload`,
  document_web_crawl: `${api_host}/document/web_crawl`,
  document_change_status: `${api_host}/document/change_status`,
  document_run: `${api_host}/document/run`,
  document_delete: `${api_host}/document/rm`,
  document_rename: `${api_host}/document/rename`,
  document_change_parser: `${api_host}/document/change_parser`,
  document_get: `${api_host}/document/get`,
  document_parse: `${api_host}/document/parse`,

  // chunk
  chunk_list: `${api_host}/chunk/list`,

  // dialog/chat - 参考原版web/src/utils/api.ts
  setDialog: `${api_host}/dialog/set`,
  getDialog: `${api_host}/dialog/get`,
  removeDialog: `${api_host}/dialog/rm`,
  listDialog: `${api_host}/dialog/list`,
};
