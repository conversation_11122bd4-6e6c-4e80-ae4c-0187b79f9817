/**
 * 会话引用数据处理工具
 * 
 * 解决多文档多chunk时[id:]显示错误的问题
 */

import { IMessage, IReference, IDocumentAgg } from '@/interfaces/chat';

/**
 * 处理历史消息中的引用数据，确保[id:]索引正确
 * 
 * @param messages 消息列表
 * @returns 处理后的消息列表
 */
export function processHistoricalMessagesReferences(messages: IMessage[]): IMessage[] {
  return messages.map((message, messageIndex) => {
    if (!message.reference) {
      return message;
    }

    // 检查是否是多维数组格式（历史消息可能包含多个Q&A轮次）
    if (Array.isArray(message.reference)) {
      // 合并所有reference项的chunks和doc_aggs
      let allChunks: IReference[] = [];
      let allDocAggs: IDocumentAgg[] = [];

      message.reference.forEach((refItem: any, refIndex: number) => {
        
        if (refItem.chunks && Array.isArray(refItem.chunks)) {
          // 确保每个chunk都有image_id字段（原版RAGFlow使用这个字段）
          const processedChunks = refItem.chunks.map((chunk: any) => ({
            ...chunk,
            image_id: chunk.image_id || chunk.id, // 确保有image_id字段
          }));
          allChunks = allChunks.concat(processedChunks);
        }
        
        if (refItem.doc_aggs && Array.isArray(refItem.doc_aggs)) {
          allDocAggs = allDocAggs.concat(refItem.doc_aggs);
        }
      });

      // 重构为标准格式（按照原版RAGFlow的结构）
      message.reference = {
        chunks: allChunks,
        total: allChunks.length,
        doc_aggs: allDocAggs
      };
    }

    return message;
  });
}

/**
 * 验证[id:]索引的有效性
 * 
 * @param content 消息内容
 * @param chunks 引用的chunks
 * @returns 验证结果
 */
export function validateIdReferences(content: string, chunks: IReference[]): {
  isValid: boolean;
  invalidIds: number[];
  maxValidId: number;
} {
  const referenceReg = /\[ID:(\d+)\]/g;
  const matches = Array.from(content.matchAll(referenceReg));
  const invalidIds: number[] = [];
  const maxValidId = chunks.length;
  
  matches.forEach(match => {
    const idStr = match[1];
    const id = parseInt(idStr, 10);
    
    // [ID:1] 对应 chunks[0]，所以有效范围是 1 到 chunks.length
    if (id < 1 || id > maxValidId) {
      invalidIds.push(id);
    }
  });
  
  return {
    isValid: invalidIds.length === 0,
    invalidIds,
    maxValidId
  };
}

/**
 * 修复[id:]索引，确保所有引用都在有效范围内
 * 
 * @param content 消息内容
 * @param chunks 引用的chunks
 * @returns 修复后的内容
 */
export function fixIdReferences(content: string, chunks: IReference[]): string {
  const validation = validateIdReferences(content, chunks);
  
  if (validation.isValid) {
    return content;
  }

  // 将无效的[ID:]替换为有效范围内的ID
  return content.replace(/\[ID:(\d+)\]/g, (match, idStr) => {
    const id = parseInt(idStr, 10);

    if (id < 1 || id > validation.maxValidId) {
      // 将无效ID映射到有效范围内
      const validId = Math.min(Math.max(1, id), validation.maxValidId);
      return `[ID:${validId}]`;
    }

    return match;
  });
}

/**
 * 获取[id:]对应的chunk数据
 * 
 * @param idStr ID字符串（如 "1", "2"）
 * @param chunks 引用的chunks
 * @returns chunk数据或null
 */
export function getChunkByIdReference(idStr: string, chunks: IReference[]): IReference | null {
  const id = parseInt(idStr, 10);
  const chunkIndex = id - 1; // [ID:1] 对应 chunks[0]

  if (chunkIndex < 0 || chunkIndex >= chunks.length) {
    return null;
  }

  return chunks[chunkIndex];
}

/**
 * 统计文档引用信息
 * 
 * @param chunks 引用的chunks
 * @returns 文档统计信息
 */
export function aggregateDocumentReferences(chunks: IReference[]): IDocumentAgg[] {
  const docMap = new Map<string, { doc_name: string; count: number }>();
  
  chunks.forEach(chunk => {
    const docId = chunk.document_id;
    const docName = chunk.document_name;
    
    if (docMap.has(docId)) {
      docMap.get(docId)!.count++;
    } else {
      docMap.set(docId, { doc_name: docName, count: 1 });
    }
  });
  
  return Array.from(docMap.entries()).map(([doc_id, info]) => ({
    doc_id,
    doc_name: info.doc_name,
    count: info.count
  }));
}


