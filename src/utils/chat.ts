// 聊天相关工具函数
import { IMessage } from '@/interfaces/chat';
import { isEmpty } from 'lodash';

export const generateMessageId = () => {
  return `msg_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
};

export const formatMessageTime = (timestamp: string | number) => {
  const date = new Date(timestamp);
  return date.toLocaleTimeString();
};

// 生成对话ID (参考原版RAGFlow)
export const getConversationId = () => {
  // 生成UUID并移除连字符
  return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
    const r = Math.random() * 16 | 0;
    const v = c === 'x' ? r : (r & 0x3 | 0x8);
    return v.toString(16);
  }).replace(/-/g, '');
};

// 检查对话ID是否存在
export const isConversationIdExist = (conversationId: string) => {
  return conversationId && conversationId !== 'undefined' && conversationId !== '';
};

// 构建消息UUID
// 按照原版ragflow的实现：如果消息已有ID就返回，否则生成新的ID
export const buildMessageUuid = (message: any): string => {
  if ('id' in message && message.id) {
    return message.id;
  }
  // 生成类似uuid的唯一ID
  const timestamp = Date.now();
  const randomStr = Math.random().toString(36).substring(2, 15);
  const randomStr2 = Math.random().toString(36).substring(2, 15);
  return `${timestamp}-${randomStr}-${randomStr2}`;
};

// 处理conversation中的reference多维数组结构
export const processHistoricalMessagesReferences = (messages?: IMessage[]): IMessage[] => {
  if (!messages) return [];

  return messages.map((msg, index) => {
    // 检查消息内容中是否有[ID:*]标记
    const hasIdMarkers = /\[ID:\d+\]/.test(msg.content || '');
    const hasReference = msg.reference && typeof msg.reference === 'object';



    // 处理conversation中的reference多维数组结构
    if (msg.reference && Array.isArray(msg.reference)) {

      // 为每个chunks数组独立建立document数组存储对应关系
      let allChunks: any[] = [];
      let documentMap = new Map<string, any>(); // 用于存储document信息，避免重复

      msg.reference.forEach((refItem: any, refIndex: number) => {
        if (refItem.chunks && Array.isArray(refItem.chunks)) {
          // 为每个chunk建立独立的document对应关系
          refItem.chunks.forEach((chunk: any, chunkIndex: number) => {
            // 确保chunk有正确的索引信息（用于[ID:]映射）
            const enhancedChunk = {
              ...chunk,
              // 确保关键字段存在
              id: chunk.id || chunk.chunk_id || `chunk_${refIndex}_${chunkIndex}`,
              document_id: chunk.document_id || chunk.doc_id,
              document_name: chunk.document_name || chunk.docnm_kwd,
              image_id: chunk.image_id || chunk.img_id,
              // 添加原始索引信息，用于调试
              _original_ref_index: refIndex,
              _original_chunk_index: chunkIndex,
              _global_chunk_index: allChunks.length, // 全局索引，用于[ID:]映射
              // 添加调试信息
              _debug_fields: {
                original_doc_id: chunk.doc_id,
                original_document_id: chunk.document_id,
                original_img_id: chunk.img_id,
                original_image_id: chunk.image_id,
                docnm_kwd: chunk.docnm_kwd,
                document_name: chunk.document_name
              }
            };

            console.log(`🔍 Processing chunk ${refIndex}-${chunkIndex}:`, {
              chunkId: enhancedChunk.id,
              documentId: enhancedChunk.document_id,
              imageId: enhancedChunk.image_id,
              documentName: enhancedChunk.document_name,
              debugFields: enhancedChunk._debug_fields
            });

            allChunks.push(enhancedChunk);

            // 收集document信息
            if (enhancedChunk.document_id) {
              if (!documentMap.has(enhancedChunk.document_id)) {
                documentMap.set(enhancedChunk.document_id, {
                  doc_id: enhancedChunk.document_id,
                  doc_name: enhancedChunk.document_name || `Document ${enhancedChunk.document_id}`,
                  count: 0,
                  url: chunk.url // 如果有URL信息
                });
              }
              // 增加该文档的chunk计数
              const docInfo = documentMap.get(enhancedChunk.document_id);
              docInfo.count += 1;
            }
          });
        }

        // 处理已有的doc_aggs信息
        if (refItem.doc_aggs && Array.isArray(refItem.doc_aggs)) {
          refItem.doc_aggs.forEach((docAgg: any) => {
            if (docAgg.doc_id && !documentMap.has(docAgg.doc_id)) {
              documentMap.set(docAgg.doc_id, {
                doc_id: docAgg.doc_id,
                doc_name: docAgg.doc_name,
                count: docAgg.count || 0,
                url: docAgg.url
              });
            }
          });
        }
      });

      // 转换为doc_aggs数组
      const allDocAggs = Array.from(documentMap.values());

      // 重构为标准格式
      msg.reference = {
        chunks: allChunks,
        total: allChunks.length,
        doc_aggs: allDocAggs
      };

      console.log(`✅ Processed reference data for message ${index}:`, {
        chunks: allChunks.length,
        doc_aggs: allDocAggs.length,
        documentMap: Array.from(documentMap.keys()),
        chunkMapping: allChunks.map((chunk, idx) => ({
          globalIndex: idx,
          chunkId: chunk.id,
          documentId: chunk.document_id,
          originalRefIndex: chunk._original_ref_index,
          originalChunkIndex: chunk._original_chunk_index
        }))
      });
    }

    // 验证引用数据的索引映射（按照原版RAGFlow的索引方式）
    if (msg.reference?.chunks && hasIdMarkers) {
      const idMatches = (msg.content || '').match(/\[ID:\d+\]/g) || [];
      const maxIdInContent = Math.max(...idMatches.map((match: string) => {
        const idStr = match.match(/\[ID:(\d+)\]/)?.[1];
        return idStr ? parseInt(idStr, 10) : 0;
      }));

      const chunksCount = msg.reference.chunks.length;

      // 按照原版RAGFlow的方式：[ID:0] 对应 chunks[0]，所以最大ID应该是 chunksCount - 1
      if (maxIdInContent >= chunksCount) {
        console.warn(`⚠️ Message ${index} has [ID:${maxIdInContent}] but only ${chunksCount} chunks available (max valid ID: ${chunksCount - 1})`);
      } else {
        console.log(`✅ Message ${index} reference mapping is valid (max ID: ${maxIdInContent}, chunks: ${chunksCount})`);
      }
    }

    return msg;
  });
};

// 为消息列表构建UUID（修正版本：保留reference字段）
export const buildMessageListWithUuid = (messages?: IMessage[]): IMessage[] => {
  // 首先处理reference多维数组结构
  const processedMessages = processHistoricalMessagesReferences(messages);

  return (
    processedMessages?.map((x: IMessage) => ({
      ...x, // 保留所有字段，包括处理后的reference
      id: buildMessageUuid(x),
    })) ?? []
  );
};

// 为消息项构建引用数据（按照原版RAGFlow实现）
export const buildMessageItemReference = (
  conversation: { message: IMessage[]; reference: any[] },
  message: IMessage,
) => {
  const assistantMessages = conversation.message?.filter(
    (x) => x.role === 'assistant',
  );
  const referenceIndex = assistantMessages.findIndex(
    (x) => x.id === message.id,
  );

  // 关键修复：按照原版RAGFlow和后端逻辑，第一条assistant消息（索引0）是prologue，不应该有reference
  // 第一条assistant消息是欢迎消息，它不参与知识库检索，因此没有reference
  // conversation.reference[0]对应的是第一个真正的问答对（第二条assistant消息）
  if (referenceIndex === 0) {
    return { doc_aggs: [], chunks: [], total: 0 };
  }

  // 首先检查消息本身是否有reference
  const messageReference = message?.reference;
  if (messageReference && !isEmpty(messageReference)) {
    return messageReference;
  }

  // 如果消息没有reference，从conversation.reference中获取
  // 注意：这里使用referenceIndex-1是因为第一条assistant消息（索引0）是prologue，没有对应的reference
  // conversation.reference[0]对应第二条assistant消息（referenceIndex=1）
  const conversationReferenceIndex = referenceIndex - 1;
  if (conversationReferenceIndex >= 0 && conversationReferenceIndex < conversation.reference.length) {
    const conversationReference = conversation.reference[conversationReferenceIndex];
    if (conversationReference && !isEmpty(conversationReference)) {
      return conversationReference;
    }
  }

  return { doc_aggs: [], chunks: [], total: 0 };
};

// 构建带角色的消息UUID
export const buildMessageUuidWithRole = (message: any, role: string) => {
  return `${role}_${message.id || Date.now()}`;
};
