/**
 * TTS工具函数测试
 */

import { cleanTextForTTS, splitTextForTTS } from '../tts';

describe('TTS工具函数测试', () => {
  describe('cleanTextForTTS', () => {
    test('应该去除[ID:*]标识符', () => {
      const input = '这是一段文本[ID:123]，包含标识符[ID:456]。';
      const expected = '这是一段文本，包含标识符。';
      expect(cleanTextForTTS(input)).toBe(expected);
    });

    test('应该去除换行符', () => {
      const input = '这是第一行\n这是第二行\n这是第三行';
      const expected = '这是第一行 这是第二行 这是第三行';
      expect(cleanTextForTTS(input)).toBe(expected);
    });

    test('应该去除多余的空格', () => {
      const input = '这是   一段   有多余空格的   文本';
      const expected = '这是 一段 有多余空格的 文本';
      expect(cleanTextForTTS(input)).toBe(expected);
    });

    test('应该处理空字符串', () => {
      expect(cleanTextForTTS('')).toBe('');
      expect(cleanTextForTTS('   ')).toBe('');
    });

    test('应该处理复合情况', () => {
      const input = '这是[ID:123]一段\n包含多种\n\n标记的[ID:456]文本   。';
      const expected = '这是一段 包含多种 标记的文本 。';
      expect(cleanTextForTTS(input)).toBe(expected);
    });
  });

  describe('splitTextForTTS', () => {
    test('短文本应该不分割', () => {
      const input = '这是一段短文本。';
      const result = splitTextForTTS(input);
      expect(result).toEqual(['这是一段短文本。']);
    });

    test('长文本应该按标点符号分割', () => {
      const input = '这是第一句话。这是第二句话！这是第三句话？这是第四句话；这是第五句话：这是第六句话，这是第七句话。';
      const result = splitTextForTTS(input);
      expect(result.length).toBeGreaterThan(1);
      expect(result.every(sentence => sentence.length <= 40)).toBe(true);
    });

    test('超长文本应该强制分割', () => {
      const input = '这是一段非常非常非常非常非常非常非常非常非常非常非常非常非常非常非常非常非常非常非常非常长的文本没有标点符号';
      const result = splitTextForTTS(input);
      expect(result.length).toBeGreaterThan(1);
      expect(result.every(sentence => sentence.length <= 40)).toBe(true);
    });

    test('应该处理包含[ID:*]的文本', () => {
      const input = '这是[ID:123]一段包含标识符的文本[ID:456]，应该被正确处理。';
      const result = splitTextForTTS(input);
      expect(result[0]).not.toContain('[ID:');
    });

    test('应该处理空字符串', () => {
      expect(splitTextForTTS('')).toEqual([]);
      expect(splitTextForTTS('   ')).toEqual([]);
    });

    test('应该处理只有换行符的文本', () => {
      const input = '\n\n\n';
      const result = splitTextForTTS(input);
      expect(result).toEqual([]);
    });
  });
});
