import { STORAGE_KEYS } from '@/constants';

// 用户信息接口
export interface UserInfo {
  avatar?: string;
  name: string;  // 对应nickname
  email: string;
}

// 认证工具类
class AuthService {
  /**
   * 设置用户token
   * @param token 用户token
   */
  setToken(token: string): void {
    localStorage.setItem(STORAGE_KEYS.TOKEN, token);
  }

  /**
   * 获取用户token
   * @returns 用户token
   */
  getToken(): string | null {
    return localStorage.getItem(STORAGE_KEYS.TOKEN);
  }

  /**
   * 移除用户token
   */
  removeToken(): void {
    localStorage.removeItem(STORAGE_KEYS.TOKEN);
  }

  /**
   * 设置Authorization
   * @param authorization Authorization值
   */
  setAuthorization(authorization: string): void {
    localStorage.setItem(STORAGE_KEYS.AUTHORIZATION, authorization);
  }

  /**
   * 获取Authorization
   * @returns Authorization值
   */
  getAuthorization(): string | null {
    return localStorage.getItem(STORAGE_KEYS.AUTHORIZATION);
  }

  /**
   * 设置用户信息
   * @param userInfo 用户信息
   */
  setUserInfo(userInfo: UserInfo): void {
    localStorage.setItem(STORAGE_KEYS.USER_INFO, JSON.stringify(userInfo));
  }

  /**
   * 批量设置存储项
   * @param pairs 键值对对象
   */
  setItems(pairs: Record<string, string>): void {
    Object.entries(pairs).forEach(([key, value]) => {
      localStorage.setItem(key, value);
    });
  }

  /**
   * 获取用户信息
   * @returns 用户信息
   */
  getUserInfo(): UserInfo | null {
    const userInfoStr = localStorage.getItem(STORAGE_KEYS.USER_INFO);
    if (userInfoStr) {
      try {
        return JSON.parse(userInfoStr);
      } catch (error) {
        console.error('Failed to parse user info:', error);
        this.removeUserInfo();
        return null;
      }
    }
    return null;
  }

  /**
   * 移除用户信息
   */
  removeUserInfo(): void {
    localStorage.removeItem(STORAGE_KEYS.USER_INFO);
  }

  /**
   * 检查是否已登录
   * @returns 是否已登录
   */
  isAuthenticated(): boolean {
    const authorization = this.getAuthorization();
    const userInfo = this.getUserInfo();
    return !!(authorization && userInfo);
  }

  /**
   * 登出
   */
  logout(): void {
    this.removeToken();
    this.removeUserInfo();
    localStorage.removeItem(STORAGE_KEYS.AUTHORIZATION);
    // 清除其他相关数据
    localStorage.removeItem(STORAGE_KEYS.LANGUAGE);
    localStorage.removeItem(STORAGE_KEYS.THEME);
  }

  /**
   * 跳转到登录页
   */
  redirectToLogin(): void {
    const currentPath = window.location.pathname;
    if (currentPath !== '/login') {
      window.location.href = '/login';
    }
  }

  /**
   * 跳转到知识库页面（与原项目保持一致）
   */
  redirectToKnowledge(): void {
    window.location.href = '/knowledge';
  }

  /**
   * 跳转到首页
   */
  redirectToDashboard(): void {
    window.location.href = '/dashboard';
  }

  /**
   * 设置语言
   * @param language 语言代码
   */
  setLanguage(language: string): void {
    localStorage.setItem(STORAGE_KEYS.LANGUAGE, language);
  }

  /**
   * 获取语言
   * @returns 语言代码
   */
  getLanguage(): string {
    return localStorage.getItem(STORAGE_KEYS.LANGUAGE) || 'zh';
  }

  /**
   * 设置主题
   * @param theme 主题名称
   */
  setTheme(theme: string): void {
    localStorage.setItem(STORAGE_KEYS.THEME, theme);
  }

  /**
   * 获取主题
   * @returns 主题名称
   */
  getTheme(): string {
    return localStorage.getItem(STORAGE_KEYS.THEME) || 'light';
  }

  /**
   * 检查权限
   * @param permission 权限代码
   * @returns 是否有权限
   */
  hasPermission(permission: string): boolean {
    // 这里可以根据实际需求实现权限检查逻辑
    const userInfo = this.getUserInfo();
    if (!userInfo) return false;
    
    // 简单的权限检查示例
    // 实际项目中可能需要更复杂的权限系统
    return true;
  }

  /**
   * 获取用户角色
   * @returns 用户角色
   */
  getUserRole(): string | null {
    const userInfo = this.getUserInfo();
    return userInfo ? 'user' : null; // 简化的角色系统
  }

  /**
   * 检查是否为管理员
   * @returns 是否为管理员
   */
  isAdmin(): boolean {
    const role = this.getUserRole();
    return role === 'admin';
  }

  /**
   * 刷新token
   * @param newToken 新的token
   */
  refreshToken(newToken: string): void {
    this.setToken(newToken);
    const userInfo = this.getUserInfo();
    if (userInfo) {
      userInfo.access_token = newToken;
      this.setUserInfo(userInfo);
    }
  }

  /**
   * 获取Authorization头
   * @returns Authorization头值
   */
  getAuthorizationHeader(): string | null {
    return this.getAuthorization();
  }
}

// 创建认证服务实例
const authService = new AuthService();

export default authService;
