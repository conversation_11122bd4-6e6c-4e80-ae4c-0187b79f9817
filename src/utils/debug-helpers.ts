// 数据验证工具函数

/**
 * 验证conversation数据结构的完整性
 */
export const validateConversationData = (conversation: any): boolean => {
  if (!conversation) {
    return false;
  }

  if (typeof conversation !== 'object') {
    return false;
  }

  if (!conversation.id) {
    return false;
  }

  if (!Array.isArray(conversation.message)) {
    return false;
  }

  return true;
};

/**
 * 验证messages数组的完整性
 */
export const validateMessagesArray = (messages: any): boolean => {
  if (!Array.isArray(messages)) {
    return false;
  }

  return true;
};

/**
 * 安全地获取messages数组
 */
export const safeGetMessages = (conversation: any): any[] => {
  if (!conversation) {
    return [];
  }

  if (!conversation.message) {
    return [];
  }

  if (!Array.isArray(conversation.message)) {
    return [];
  }

  return conversation.message;
};
