import { Authorization } from '@/constants/authorization';
import authorizationUtil, {
  getAuthorization,
  redirectToLogin,
} from '@/utils/authorization-util';
import { message, notification } from 'antd';
import { RequestMethod, extend } from 'umi-request';
import { convertTheKeysOfTheObjectToSnake } from './index';
import i18n from '@/utils/i18n';

const FAILED_TO_FETCH = 'Failed to fetch';

const RetcodeMessage = {
  200: 'Success',
  201: 'Created',
  202: 'Accepted',
  204: 'No Content',
  400: 'Bad Request',
  401: 'Unauthorized',
  403: 'Forbidden',
  404: 'Not Found',
  406: 'Not Acceptable',
  410: 'Gone',
  413: 'Request Entity Too Large',
  422: 'Unprocessable Entity',
  500: 'Internal Server Error',
  502: 'Bad Gateway',
  503: 'Service Unavailable',
  504: 'Gateway Timeout',
};

type ResultCode =
  | 200
  | 201
  | 202
  | 204
  | 400
  | 401
  | 403
  | 404
  | 406
  | 410
  | 413
  | 422
  | 500
  | 502
  | 503
  | 504;

const errorHandler = (error: {
  response: Response;
  message: string;
}): Response => {
  const { response } = error;
  if (error.message === FAILED_TO_FETCH) {
    notification.error({
      description: i18n.t('messages.error.networkError', 'Network connection error, please check your network'),
      message: i18n.t('messages.error.networkErrorTitle', 'Network Error'),
    });
  } else {
    if (response && response.status) {
      const errorText =
        RetcodeMessage[response.status as ResultCode] || response.statusText;
      const { status, url } = response;
      notification.error({
        message: i18n.t('messages.error.requestError', `Request Error ${status}: ${url}`, { status, url }),
        description: errorText,
      });
    }
  }
  return response ?? { data: { code: 1999 } };
};

const request: RequestMethod = extend({
  errorHandler,
  timeout: 300000,
  getResponse: true,
  headers: {
    'Content-Type': 'application/json',
  },
});

request.interceptors.request.use((url: string, options: any) => {
  // 检查是否是FormData，如果是则跳过数据转换以保持FormData完整性
  const isFormData = options.data instanceof FormData;
  const data = options.skipDataConversion || isFormData
    ? options.data
    : convertTheKeysOfTheObjectToSnake(options.data);
  const params = convertTheKeysOfTheObjectToSnake(options.params);

  // 构建headers
  let finalHeaders = {
    ...(options.skipToken
      ? undefined
      : { [Authorization]: getAuthorization() }),
    ...options.headers,
  };

  // 如果是FormData，删除任何Content-Type让浏览器自动设置
  if (isFormData) {
    delete finalHeaders['Content-Type'];
  } else {
    finalHeaders['Content-Type'] = 'application/json';
  }

  console.log('isFormData:', isFormData);
  console.log('Final data:', data);
  console.log('Final headers:', finalHeaders);

  return {
    url,
    options: {
      ...options,
      data,
      params,
      headers: finalHeaders,
      interceptors: true,
    },
  };
});

request.interceptors.response.use(async (response: Response, options) => {
  if (response?.status === 413 || response?.status === 504) {
    message.error(RetcodeMessage[response?.status as ResultCode]);
  }

  if (options.responseType === 'blob') {
    return response;
  }

  const data: any = await response?.clone()?.json();
  if (data?.code === 100) {
    message.error(data?.message);
  } else if (data?.code === 401) {
    notification.error({
      message: data?.message,
      description: data?.message,
      duration: 3,
    });
    authorizationUtil.removeAll();
    redirectToLogin();
  } else if (data?.code !== 0) {
    notification.error({
      message: `Error: ${data?.code}`,
      description: data?.message,
      duration: 3,
    });
  }
  return response;
});

export default request;

export const get = (url: string) => {
  return request.get(url);
};

export const post = (url: string, body: any) => {
  return request.post(url, { data: body });
};
