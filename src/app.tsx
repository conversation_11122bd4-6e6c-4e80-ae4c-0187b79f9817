import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { ConfigProvider } from 'antd';
import enUS from 'antd/locale/en_US';
import zhCN from 'antd/locale/zh_CN';
import { useState, useEffect } from 'react';
import { useI18n } from '@/hooks/use-i18n';
import '@/utils/i18n'; // 初始化i18n

// Create a client
const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      retry: 1,
      refetchOnWindowFocus: false,
      staleTime: 5 * 60 * 1000, // 5 minutes
    },
  },
});

// 内部组件，用于访问i18n hooks
const AppConfigProvider: React.FC<{ children: React.ReactElement }> = ({ children }) => {
  const { getCurrentLanguage } = useI18n();
  const [antdLocale, setAntdLocale] = useState(enUS);

  useEffect(() => {
    const currentLang = getCurrentLanguage();
    if (currentLang.startsWith('zh')) {
      setAntdLocale(zhCN);
    } else {
      setAntdLocale(enUS);
    }
  }, [getCurrentLanguage]);

  return (
    <ConfigProvider
      locale={antdLocale}
      theme={{
        token: {
          colorPrimary: '#667eea',
          borderRadius: 8,
        },
        components: {
          Button: {
            borderRadius: 8,
          },
          Input: {
            borderRadius: 8,
          },
          Card: {
            borderRadius: 12,
          },
        },
      }}
    >
      {children}
    </ConfigProvider>
  );
};

export function rootContainer(container: React.ReactElement) {
  return (
    <QueryClientProvider client={queryClient}>
      <AppConfigProvider>
        {container}
      </AppConfigProvider>
    </QueryClientProvider>
  );
}
