import request from '@/utils/request';
import { getAuthorization } from '@/utils/authorization-util';

// 聊天机器人相关接口类型定义
export interface IChatBotSession {
  session_id: string;
  user_id: string;
  created_at: number;
  last_active: number;
}

export interface IChatBotMessage {
  role: 'user' | 'assistant' | 'system';
  content: string;
  timestamp: number;
  message_id: string;
  metadata?: Record<string, any>;
}

export interface ICreateSessionParams {
  user_id?: string;
  tenant_id?: string;
  system_prompt?: string;
}

export interface ICreateSessionResponse {
  code: number;
  data: {
    session_id: string;
  };
  message?: string;
}

export interface ISendMessageParams {
  message: string;
  stream?: boolean;
  system_prompt?: string;
}

export interface ISendMessageResponse {
  code: number;
  data: {
    message_id: string;
    content: string;
    metadata?: Record<string, any>;
  };
  message?: string;
}

export interface IGetHistoryResponse {
  code: number;
  data: {
    messages: IChatBotMessage[];
  };
  message?: string;
}

export interface IClearSessionResponse {
  code: number;
  data: {
    success: boolean;
  };
  message?: string;
}

export interface IServiceStatsResponse {
  code: number;
  data: {
    memory_stats: {
      total_sessions: number;
      total_users: number;
      max_messages_per_session: number;
      max_session_age_hours: number;
      max_total_sessions: number;
    };
    processor_stats: {
      max_input_length: number;
      max_output_length: number;
      enable_content_filter: boolean;
      sensitive_patterns_count: number;
    };
    default_gen_config: {
      temperature: number;
      top_p: number;
      max_tokens: number;
      frequency_penalty: number;
      presence_penalty: number;
    };
  };
  message?: string;
}

export interface IHealthCheckResponse {
  code: number;
  data: {
    status: string;
    timestamp: number;
  };
  message?: string;
}

export interface ISessionItem {
  session_id: string;
  session_name: string;
  first_message: string;
  message_count: number;
  last_message_time: string;
  create_time: string;
}

export interface IGetUserSessionsParams {
  limit?: number;
  offset?: number;
}

export interface IGetUserSessionsResponse {
  code: number;
  data: {
    sessions: ISessionItem[];
    total: number;
  };
  message?: string;
}

export interface ISessionDetails {
  session: ISessionItem;
  messages: IChatBotMessage[];
}

export interface IGetSessionDetailsResponse {
  code: number;
  data: ISessionDetails;
  message?: string;
}

export interface IDeleteSessionResponse {
  code: number;
  data: {
    success: boolean;
  };
  message?: string;
}

// 聊天机器人服务类
class ChatBotService {
  private baseUrl = '/api/v1/chatbot';
  private tenantId: string | null = null;

  // 获取用户信息
  private async getUserInfo(): Promise<{ user_id: string; tenant_id: string }> {
    try {
      const response = await request.get('/v1/user/tenant_info', {
        headers: {
          Authorization: getAuthorization(),
        },
      });

      // 注意：request配置了getResponse: true，所以响应格式是 { data: {...}, response: Response }
      const responseData = response.data || response;

      if (responseData.code === 0 && responseData.data) {
        const tenantId = responseData.data.tenant_id;

        // 在RAGFlow中，tenant_id通常就是用户的ID
        // 但我们也可以从用户信息接口获取真实的用户ID
        const userResponse = await request.get('/v1/user/info', {
          headers: {
            Authorization: getAuthorization(),
          },
        });

        const userResponseData = userResponse.data || userResponse;
        let userId = tenantId; // 默认使用tenant_id作为user_id

        if (userResponseData.code === 0 && userResponseData.data) {
          userId = userResponseData.data.id;
        }

        this.tenantId = tenantId;
        return { user_id: userId, tenant_id: tenantId };
      } else {
        throw new Error(responseData.message || '获取租户信息失败');
      }
    } catch (error) {
      throw new Error('无法获取用户信息，请确保已登录');
    }
  }

  // 获取租户信息（保持向后兼容）
  private async getTenantInfo(): Promise<string> {
    if (this.tenantId) {
      return this.tenantId;
    }

    const { tenant_id } = await this.getUserInfo();
    return tenant_id;
  }

  // 创建聊天会话
  async createSession(params: ICreateSessionParams = {}): Promise<ICreateSessionResponse> {
    try {
      const authorization = getAuthorization();

      // 获取用户信息和租户ID
      const { user_id, tenant_id } = await this.getUserInfo();

      // 如果没有提供user_id，使用从API获取的用户ID
      if (!params.user_id) {
        params.user_id = user_id;
      }

      // 添加tenant_id到参数中
      params.tenant_id = tenant_id;

      const response = await request.post(`${this.baseUrl}/sessions`, {
        data: params,
        headers: {
          'Content-Type': 'application/json',
          Authorization: authorization,
        },
      });

      // 注意：request配置了getResponse: true，所以需要返回response.data
      return response.data || response;
    } catch (error) {
      throw error;
    }
  }

  // 发送消息（非流式）
  async sendMessage(
    sessionId: string,
    params: ISendMessageParams
  ): Promise<ISendMessageResponse> {
    try {
      const authorization = getAuthorization();

      // 获取租户ID
      const { tenant_id } = await this.getUserInfo();

      const response = await request.post(`${this.baseUrl}/sessions/${sessionId}/messages`, {
        data: { ...params, tenant_id, stream: false },
        headers: {
          'Content-Type': 'application/json',
          Authorization: authorization,
        },
      });
      return response.data || response;
    } catch (error) {
      throw error;
    }
  }

  // 发送消息（流式）
  async sendMessageStream(
    sessionId: string,
    params: ISendMessageParams,
    onMessage: (content: string) => void,
    onComplete: (messageId: string) => void,
    onError: (error: string) => void,
    abortController?: AbortController
  ): Promise<void> {
    try {
      const authorization = getAuthorization();

      // 获取租户ID
      const { tenant_id } = await this.getUserInfo();

      // 使用相对路径，让代理处理转发
      const streamUrl = `${this.baseUrl}/sessions/${sessionId}/messages`;

      const response = await fetch(streamUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'text/event-stream',
          'Cache-Control': 'no-cache',
          Authorization: authorization,
        },
        body: JSON.stringify({ ...params, tenant_id, stream: true }),
        signal: abortController?.signal,
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const reader = response.body?.getReader();
      if (!reader) {
        throw new Error('无法获取响应流');
      }

      const decoder = new TextDecoder();
      let buffer = '';

      try {
        while (true) {
          const { done, value } = await reader.read();
          if (done) break;

          buffer += decoder.decode(value, { stream: true });
          const lines = buffer.split('\n');
          buffer = lines.pop() || '';

          for (const line of lines) {
            if (line.startsWith('data: ')) {
              const jsonStr = line.slice(6).trim();

              // 跳过空数据或特殊SSE消息
              if (!jsonStr || jsonStr === '[DONE]' || jsonStr === 'null') {
                continue;
              }

              try {
                const data = JSON.parse(jsonStr);

                if (data.error) {
                  onError(data.error);
                  return;
                }

                if (data.content !== undefined) {
                  onMessage(data.content);
                }

                if (data.done && data.message_id) {
                  onComplete(data.message_id);
                  return;
                }
              } catch (parseError) {
                // 如果解析失败，尝试作为纯文本处理
                if (jsonStr.length > 0 && !jsonStr.startsWith('{')) {
                  onMessage(jsonStr);
                }
              }
            }
          }
        }
      } finally {
        reader.releaseLock();
      }
    } catch (error) {
      onError(error instanceof Error ? error.message : '发送消息失败');
    }
  }

  // 获取会话历史
  async getHistory(sessionId: string, limit?: number): Promise<IGetHistoryResponse> {
    try {
      const params = limit ? { limit } : {};
      const response = await request.get(`${this.baseUrl}/sessions/${sessionId}/history`, {
        params,
        headers: {
          Authorization: getAuthorization(),
        },
      });
      return response.data || response;
    } catch (error) {
      throw error;
    }
  }

  // 清空会话
  async clearSession(sessionId: string): Promise<IClearSessionResponse> {
    try {
      const response = await request.delete(`${this.baseUrl}/sessions/${sessionId}`, {
        headers: {
          Authorization: getAuthorization(),
        },
      });
      return response.data || response;
    } catch (error) {
      throw error;
    }
  }

  // 获取服务统计
  async getStats(): Promise<IServiceStatsResponse> {
    try {
      const response = await request.get(`${this.baseUrl}/stats`, {
        headers: {
          Authorization: getAuthorization(),
        },
      });
      return response.data || response;
    } catch (error) {
      throw error;
    }
  }

  // 健康检查
  async healthCheck(): Promise<IHealthCheckResponse> {
    try {
      const response = await request.get(`${this.baseUrl}/health`);
      return response.data || response;
    } catch (error) {
      throw error;
    }
  }

  // 获取用户会话列表
  async getUserSessions(params: IGetUserSessionsParams = {}): Promise<IGetUserSessionsResponse> {
    try {
      const authorization = getAuthorization();

      const response = await request.get(`${this.baseUrl}/sessions`, {
        params,
        headers: {
          'Content-Type': 'application/json',
          Authorization: authorization,
        },
      });
      return response.data || response;
    } catch (error) {
      throw error;
    }
  }

  // 获取会话详情
  async getSessionDetails(sessionId: string, limit?: number): Promise<IGetSessionDetailsResponse> {
    try {
      const authorization = getAuthorization();

      const params = limit ? { limit } : {};
      const response = await request.get(`${this.baseUrl}/sessions/${sessionId}/details`, {
        params,
        headers: {
          'Content-Type': 'application/json',
          Authorization: authorization,
        },
      });
      return response.data || response;
    } catch (error) {
      throw error;
    }
  }

  // 删除会话
  async deleteSession(sessionId: string): Promise<IDeleteSessionResponse> {
    try {
      const authorization = getAuthorization();

      const response = await request.delete(`${this.baseUrl}/sessions/${sessionId}/delete`, {
        headers: {
          'Content-Type': 'application/json',
          Authorization: authorization,
        },
      });
      return response.data || response;
    } catch (error) {
      throw error;
    }
  }
}

// 导出服务实例
export const chatBotService = new ChatBotService();
export default chatBotService;
