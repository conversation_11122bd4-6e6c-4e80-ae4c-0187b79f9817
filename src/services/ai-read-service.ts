import request from '@/utils/request';
import api from '@/utils/api';

interface ChatMessage {
  id: string;
  type: 'user' | 'assistant';
  content: string;
  timestamp: string;
}

interface GenerateSummaryParams {
  documentContent: string;
  documentName: string;
  llmId: string;
  prompt: string;
}

interface AskQuestionParams {
  question: string;
  documentContent: string;
  documentName: string;
  llmId: string;
  chatHistory: ChatMessage[];
}

// AI阅读文件接口
interface AIReadingFile {
  file_id: string;
  original_filename: string;
  file_type: string;
  file_size: number;
  processing_status: string;
  processing_progress: number;
  processing_message: string;
  content_summary?: string;
  create_time: number;
  update_time: number;
}

interface AIReadingConversation {
  conversation_id: string;
  question: string;
  answer: string;
  confidence_score: number;
  session_id: string;
  processing_status: string;
  create_time: number;
  update_time: number;
}

interface UploadFileResponse {
  code: number;
  message: string;
  data: {
    file_id: string;
    original_filename: string;
    file_size: number;
    processing_status: string;
  };
}

interface FileListResponse {
  code: number;
  message: string;
  data: {
    files: AIReadingFile[];
    page: number;
    page_size: number;
    total: number;
  };
}

interface FileStatusResponse {
  code: number;
  message: string;
  data: {
    file_id: string;
    processing_status: string;
    processing_progress: number;
    processing_message: string;
    mineru_state?: string;
    content_summary?: string;
  };
}

interface ChatResponse {
  code: number;
  message: string;
  data: {
    conversation_id: string;
    question: string;
    answer: string;
    confidence_score: number;
    session_id: string;
  };
}

interface ConversationListResponse {
  code: number;
  message: string;
  data: {
    conversations: AIReadingConversation[];
    page: number;
    page_size: number;
    total: number;
    file_info: {
      file_id: string;
      original_filename: string;
      content_summary?: string;
    };
  };
}

// 上传文件到AI阅读系统
export const uploadFile = async (file: File): Promise<UploadFileResponse> => {
  const formData = new FormData();
  formData.append('file', file);

  console.log('=== DEBUG FILE UPLOAD ===');
  console.log('File object:', file);
  console.log('File name:', file.name);
  console.log('File size:', file.size);
  console.log('File type:', file.type);
  console.log('FormData entries:');
  for (let pair of formData.entries()) {
    console.log(pair[0], pair[1]);
  }

  try {
    console.log('Making request with FormData...');
    const response = await request.post('/api/v1/ai-reading/upload', {
      data: formData,
      requestType: 'form',
      skipDataConversion: true,
      // 不设置Content-Type，让浏览器自动设置multipart/form-data和boundary
    });

    console.log('Full response:', response);
    console.log('Response data:', response.data);
    return response.data;
  } catch (error) {
    console.error('File upload failed:', error);
    throw error;
  }
};

// 获取文件列表
export const getFileList = async (page: number = 1, pageSize: number = 10): Promise<FileListResponse> => {
  try {
    const { data } = await request.get(`/api/v1/ai-reading/files?page=${page}&page_size=${pageSize}`);
    return data;
  } catch (error) {
    console.error('Get file list failed:', error);
    throw error;
  }
};

// 获取文件状态
export const getFileStatus = async (fileId: string): Promise<FileStatusResponse> => {
  try {
    const { data } = await request.get(`/api/v1/ai-reading/files/${fileId}/status`);
    return data;
  } catch (error) {
    console.error('Get file status failed:', error);
    throw error;
  }
};

// 开始处理文件
export const processFile = async (fileId: string): Promise<any> => {
  try {
    const { data } = await request.post(`/api/v1/ai-reading/files/${fileId}/process`);
    return data;
  } catch (error) {
    console.error('Process file failed:', error);
    throw error;
  }
};

// 删除文件
export const deleteFile = async (fileId: string): Promise<any> => {
  try {
    const { data } = await request.delete(`/api/v1/ai-reading/files/${fileId}`);
    return data;
  } catch (error) {
    console.error('Delete file failed:', error);
    throw error;
  }
};

// 与文档聊天
export const chatWithDocument = async (
  fileId: string,
  question: string,
  sessionId?: string
): Promise<ChatResponse> => {
  try {
    console.log('=== CHAT SERVICE START ===');
    console.log('API URL:', `/api/v1/ai-reading/files/${fileId}/chat`);
    console.log('Request data:', { question, session_id: sessionId });

    const { data } = await request.post(`/api/v1/ai-reading/files/${fileId}/chat`, {
      data: {
        question,
        session_id: sessionId,
      },
    });

    console.log('Chat API response:', data);
    return data;
  } catch (error) {
    console.error('Chat with document failed:', error);
    throw error;
  }
};

// 获取文件的PDF预览URL
export const getPdfPreviewUrl = (fileId: string): string => {
  return `/api/v1/ai-reading/files/${fileId}/pdf`;
};

// 获取对话历史
export const getConversations = async (
  fileId: string,
  page: number = 1,
  pageSize: number = 20,
  sessionId?: string
): Promise<ConversationListResponse> => {
  try {
    let url = `/api/v1/ai-reading/files/${fileId}/conversations?page=${page}&page_size=${pageSize}`;
    if (sessionId) {
      url += `&session_id=${sessionId}`;
    }
    const { data } = await request.get(url);
    return data;
  } catch (error) {
    throw error;
  }
};

// 解析文档内容 (保持向后兼容)
export const parseDocument = async (file: File): Promise<string> => {
  try {
    // 使用新的上传API
    const uploadResult = await uploadFile(file);
    if (uploadResult.code === 0) {
      const fileId = uploadResult.data.file_id;

      // 开始处理文件
      await processFile(fileId);

      // 轮询检查状态直到完成
      let attempts = 0;
      const maxAttempts = 30; // 最多等待5分钟

      while (attempts < maxAttempts) {
        await new Promise(resolve => setTimeout(resolve, 10000)); // 等待10秒
        const statusResult = await getFileStatus(fileId);

        if (statusResult.code === 0) {
          if (statusResult.data.processing_status === 'completed') {
            return statusResult.data.content_summary || '';
          } else if (statusResult.data.processing_status === 'failed') {
            throw new Error(statusResult.data.processing_message || 'Processing failed');
          }
        }

        attempts++;
      }

      throw new Error('Processing timeout');
    } else {
      throw new Error(uploadResult.message || 'Upload failed');
    }
  } catch (error) {
    console.error('Document parsing failed:', error);
    throw error;
  }
};

// 生成文档总结 (保持向后兼容，但使用新的API)
export const generateDocumentSummary = async (params: GenerateSummaryParams): Promise<string> => {
  try {
    // 如果有文档内容，先上传文件
    const blob = new Blob([params.documentContent], { type: 'text/plain' });
    const file = new File([blob], params.documentName, { type: 'text/plain' });

    const uploadResult = await uploadFile(file);
    if (uploadResult.code === 0) {
      const fileId = uploadResult.data.file_id;

      // 开始处理文件
      await processFile(fileId);

      // 等待处理完成
      let attempts = 0;
      const maxAttempts = 30;

      while (attempts < maxAttempts) {
        await new Promise(resolve => setTimeout(resolve, 10000));
        const statusResult = await getFileStatus(fileId);

        if (statusResult.code === 0) {
          if (statusResult.data.processing_status === 'completed') {
            return statusResult.data.content_summary || 'No summary generated';
          } else if (statusResult.data.processing_status === 'failed') {
            throw new Error(statusResult.data.processing_message || 'Processing failed');
          }
        }

        attempts++;
      }

      throw new Error('Processing timeout');
    } else {
      throw new Error(uploadResult.message || 'Upload failed');
    }
  } catch (error) {
    console.error('Summary generation failed:', error);
    throw error;
  }
};

// 询问文档问题 (使用新的聊天API)
export const askDocumentQuestion = async (params: AskQuestionParams): Promise<string> => {
  try {
    // 如果有文档内容，先上传文件
    const blob = new Blob([params.documentContent], { type: 'text/plain' });
    const file = new File([blob], params.documentName, { type: 'text/plain' });

    const uploadResult = await uploadFile(file);
    if (uploadResult.code === 0) {
      const fileId = uploadResult.data.file_id;

      // 开始处理文件
      await processFile(fileId);

      // 等待处理完成
      let attempts = 0;
      const maxAttempts = 30;

      while (attempts < maxAttempts) {
        await new Promise(resolve => setTimeout(resolve, 10000));
        const statusResult = await getFileStatus(fileId);

        if (statusResult.code === 0 && statusResult.data.processing_status === 'completed') {
          // 文件处理完成，开始问答
          const chatResult = await chatWithDocument(fileId, params.question);
          if (chatResult.code === 0) {
            return chatResult.data.answer;
          } else {
            throw new Error(chatResult.message || 'Chat failed');
          }
        } else if (statusResult.data.processing_status === 'failed') {
          throw new Error(statusResult.data.processing_message || 'Processing failed');
        }

        attempts++;
      }

      throw new Error('Processing timeout');
    } else {
      throw new Error(uploadResult.message || 'Upload failed');
    }
  } catch (error) {
    throw error;
  }
};

const aiReadService = {
  // 新的API方法
  uploadFile,
  getFileList,
  getFileStatus,
  processFile,
  deleteFile,
  chatWithDocument,
  getConversations,

  // 保持向后兼容的方法
  parseDocument,
  generateDocumentSummary,
  askDocumentQuestion,
};

export default aiReadService;
