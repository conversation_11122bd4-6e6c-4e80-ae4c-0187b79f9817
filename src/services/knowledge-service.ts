import api from '@/utils/api';
import request, { post } from '@/utils/request';
import { getAuthorization } from '@/utils/authorization-util';

export interface IKnowledgeListParams {
  page?: number;
  page_size?: number;
  orderby?: string;
  desc?: boolean;
  keywords?: string;
}

export interface IKnowledgeListBody {
  owner_ids?: string[];
}

export interface ICreateKnowledgeParams {
  name: string;
  description?: string;
  language?: string;
  permission?: string;
  parser_id?: string;
  parser_config?: Record<string, any>;
  embd_id?: string;
}

export interface IUpdateKnowledgeParams extends ICreateKnowledgeParams {
  kb_id: string;
}

const knowledgeService = {
  // Knowledge Base Management
  createKnowledge: (params: ICreateKnowledgeParams) => {
    return request.post(api.create_kb, { data: params });
  },
  updateKnowledge: (params: IUpdateKnowledgeParams) => {
    return request.post(api.update_kb, { data: params });
  },
  deleteKnowledge: (kb_id: string) => {
    return request.post(api.rm_kb, { data: { kb_id } });
  },
  getKnowledgeDetail: (kb_id: string) => {
    return request.get(`${api.get_kb_detail}?kb_id=${kb_id}`);
  },
  getKnowledgeList: (params?: IKnowledgeListParams, body?: IKnowledgeListBody) => {
    const queryParams = new URLSearchParams();
    if (params?.page !== undefined) queryParams.append('page', params.page.toString());
    if (params?.page_size !== undefined) queryParams.append('page_size', params.page_size.toString());
    if (params?.orderby) queryParams.append('orderby', params.orderby);
    if (params?.desc !== undefined) queryParams.append('desc', params.desc.toString());
    if (params?.keywords) queryParams.append('keywords', params.keywords);
    
    const url = queryParams.toString() ? `${api.kb_list}?${queryParams.toString()}` : api.kb_list;
    return request.post(url, { data: body || {} });
  },

  // Document Management
  getDocumentList: (params: any, body?: any) => {
    // 根据参考实现，使用POST方法，参数分为查询参数和请求体
    return request.post(api.get_document_list, {
      params: {
        kb_id: params.kb_id,
        page: params.page,
        page_size: params.page_size,
        keywords: params.keywords,
        orderby: params.orderby || 'create_time',
        desc: params.desc !== undefined ? params.desc : true,
      },
      data: {
        run_status: body?.run_status || [],
        types: body?.types || [],
      }
    });
  },
  uploadDocument: (formData: FormData) => {
    // 使用原生fetch API来避免umi-request的数据转换问题
    const apiUrl = `${window.location.origin}${api.document_upload}`;

    return fetch(apiUrl, {
      method: 'POST',
      body: formData,
      headers: {
        'Authorization': getAuthorization(), // 使用正确的认证方式
        'X-API-KEY': process.env.API_KEY || 'ragflow-lhYmE2YTc2M2FiYjExZjA5YTdlMDAwYz',
      },
    }).then(response => {
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      return response.json();
    }).then(data => ({ data }));
  },
  deleteDocument: (doc_id: string) => {
    return request.post(api.document_rm, { data: { doc_id } });
  },
  changeDocumentStatus: (params: { doc_id: string; status: string }) => {
    return request.post(api.document_change_status, { data: params });
  },

  // Chunk Management
  getChunkList: (params: any) => {
    return request.post(api.chunk_list, { data: params });
  },
  createChunk: (params: any) => {
    return request.post(api.create_chunk, { data: params });
  },
  updateChunk: (params: any) => {
    return request.post(api.set_chunk, { data: params });
  },
  deleteChunk: (chunk_id: string) => {
    return request.post(api.rm_chunk, { data: { chunk_id } });
  },
};

// 参考实现的listDocument函数
export const listDocument = (
  params?: any,
  body?: any,
) => {
  console.log('listDocument called with:', { params, body }); // Debug log
  return request.post(api.get_document_list, {
    data: body || {},
    params: params || {}
  });
};

export default knowledgeService;
