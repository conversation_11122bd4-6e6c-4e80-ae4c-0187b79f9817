import request from '@/utils/request';
import api from '@/utils/api';
import { getAuthorization } from '@/utils/authorization-util';
import { Authorization } from '@/constants/authorization';
import {
  IConversationListParams,
  IConversationListResponse,
  IConversationResponse,
  ISendMessageParams,
  ISendMessageResponse,
  ICreateConversationParams,
  ICreateConversationResponse,
  IDeleteConversationParams,
  IDeleteConversationResponse,
  IDeleteMessageParams,
  IDeleteMessageResponse,
} from '@/interfaces/chat';

const chatService = {
  // 获取dialogs列表
  listDialogs: (params?: any): Promise<any> => {
    return request.get(api.listDialog, {
      params,
    });
  },

  // 获取对话列表
  listConversations: (params: IConversationListParams): Promise<IConversationListResponse> => {
    return request.get(api.listConversation, {
      params,
    });
  },

  // 获取单个对话
  getConversation: (params: { conversationId: string }): Promise<IConversationResponse> => {
    return request.get(api.getConversation, {
      params: { conversation_id: params.conversationId },
    });
  },

  // 设置对话 (创建或更新对话) - 简化为与原版web一致
  setConversation: async (params: any): Promise<any> => {

    try {
      const response = await request.post(api.setConversation, {
        data: params,
      });

      // 提取内层数据
      if (response && response.data) {
        return response.data;
      } else {
        return response;
      }

    } catch (error) {
      throw error;
    }
  },


  // 创建新对话 - 使用setConversation接口
  createConversation: (params: ICreateConversationParams): Promise<ICreateConversationResponse> => {
    return request.post(api.setConversation, {
      data: params,
    });
  },

  // 发送消息 (普通请求)
  sendMessage: (params: ISendMessageParams): Promise<ISendMessageResponse> => {
    return request.post(api.completeConversation, {
      data: params,
    });
  },

  // 发送消息 (SSE流式响应) - 简化版本
  sendMessageWithSSE: async (
    params: ISendMessageParams,
    onMessage: (data: any) => void,
    onError: (error: any) => void,
    onComplete: () => void,
    controller?: AbortController
  ) => {
    try {
      const authHeader = getAuthorization();
      if (!authHeader) {
        throw new Error('No authentication header found for SSE request');
      }

      const response = await fetch(api.completeConversation, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'text/event-stream',
          'Authorization': authHeader,
        },
        body: JSON.stringify(params),
        signal: controller?.signal,
      });

      if (!response.ok) {
        const errorText = await response.text();
        if (response.status === 401) {
          throw new Error(`401 Unauthorized: Authentication failed for SSE request. ${errorText}`);
        } else {
          throw new Error(`HTTP ${response.status} ${response.statusText}: ${errorText}`);
        }
      }

      const reader = response.body?.getReader();
      if (!reader) {
        throw new Error('No response body reader');
      }

      const decoder = new TextDecoder();
      let buffer = '';

      while (true) {
        const { done, value } = await reader.read();

        if (done) {
          onComplete();
          break;
        }

        const chunk = decoder.decode(value, { stream: true });
        buffer += chunk;
        const lines = buffer.split('\n');
        buffer = lines.pop() || '';

        for (const line of lines) {

          if (line.startsWith('data: ') || line.startsWith('data:')) {
            // 标准SSE格式（支持有空格和无空格两种格式）
            const data = line.startsWith('data: ') ? line.slice(6).trim() : line.slice(5).trim();

            if (data === '[DONE]') {
              onComplete();
              return;
            }

            if (data) {
              try {
                const parsed = JSON.parse(data);
                onMessage(parsed);
              } catch (error) {
                // 静默处理解析错误
              }
            }
          } else if (line.trim()) {
            // 非标准格式：尝试直接解析JSON
            const trimmedLine = line.trim();
            if (trimmedLine === '[DONE]') {
              onComplete();
              return;
            }

            if (trimmedLine && (trimmedLine.startsWith('{') || trimmedLine.startsWith('['))) {
              try {
                const parsed = JSON.parse(trimmedLine);
                onMessage(parsed);
              } catch (error) {
                // 忽略非JSON行
              }
            }
          }
        }
      }

    } catch (error) {
      // 检查是否是AbortError（用户主动取消）
      if (error.name === 'AbortError') {
        // 不调用onError，因为这是正常的取消操作
        return;
      }

      // 其他错误才调用onError
      onError(error);
    }
  },

  // 发送消息 (SSE流式响应) - 使用fetch实现
  sendMessageSSE: async (
    params: ISendMessageParams,
    onMessage: (data: any) => void,
    onError: (error: any) => void,
    onComplete: () => void,
    controller?: AbortController
  ) => {

    try {
      const response = await fetch(api.completeConversation, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'text/event-stream',
        },
        body: JSON.stringify(params),
        signal: controller?.signal,
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const reader = response.body?.getReader();
      if (!reader) {
        throw new Error('No response body reader');
      }

      const decoder = new TextDecoder();
      let buffer = '';

      const readStream = async () => {
        try {
          while (true) {
            const { done, value } = await reader.read();

            if (done) {
              onComplete();
              break;
            }

            buffer += decoder.decode(value, { stream: true });
            const lines = buffer.split('\n');
            buffer = lines.pop() || '';

            for (const line of lines) {
              if (line.startsWith('data: ')) {
                const data = line.slice(6);
                if (data === '[DONE]') {
                  onComplete();
                  return;
                }
                try {
                  const parsed = JSON.parse(data);
                  onMessage(parsed);
                } catch (error) {
                  // 静默处理解析错误
                }
              }
            }
          }
        } catch (error) {
          if (error.name !== 'AbortError') {
            onError(error);
          }
        }
      };

      readStream();

      // 返回关闭函数
      return () => {
        controller?.abort();
        reader.cancel();
      };

    } catch (error) {
      onError(error);
      return () => {};
    }
  },

  // 删除对话
  deleteConversation: (params: IDeleteConversationParams): Promise<IDeleteConversationResponse> => {
    return request.post(api.removeConversation, {
      data: params,
    });
  },

  // 删除消息
  deleteMessage: (params: IDeleteMessageParams): Promise<IDeleteMessageResponse> => {
    return request.post(api.deleteMessage, {
      data: params,
    });
  },

  // 点赞/点踩
  thumbup: (messageId: string, thumbup: boolean): Promise<any> => {
    return request.post(api.thumbup, {
      data: {
        message_id: messageId,
        thumbup,
      },
    });
  },

  // 文本转语音
  tts: (text: string): Promise<any> => {
    return request.post(api.tts, {
      data: { text },
    });
  },

  // 获取相关问题
  getRelatedQuestions: (conversationId: string): Promise<any> => {
    return request.get(api.getRelatedQuestions, {
      params: { conversation_id: conversationId },
    });
  },
};

export default chatService;
