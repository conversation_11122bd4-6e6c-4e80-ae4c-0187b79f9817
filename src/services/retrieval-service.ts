import request from 'umi-request';
import api from '@/utils/api';

export interface IRetrievalTestParams {
  kb_id: string | string[];
  question: string;
  similarity_threshold?: number;
  vector_similarity_weight?: number;
  top_k?: number;
  use_kg?: boolean;
  highlight?: boolean;
  page?: number;
  size?: number;
  doc_ids?: string[];
  rerank_id?: string;
}

export interface IRetrievalChunk {
  chunk_id: string;
  content_with_weight: string;
  doc_id: string;
  docnm_kwd: string;
  similarity: number;
  important_kwd: string[];
  question_kwd: string[];
  image_id?: string;
  available_int?: number;
}

export interface IRetrievalTestResponse {
  chunks: IRetrievalChunk[];
  total: number;
  labels?: string[];
}

const retrievalService = {
  // 检索测试
  testRetrieval: (params: IRetrievalTestParams) => {
    return request.post(api.retrieval_test, { data: params });
  },
};

export default retrievalService;
