import api from '@/utils/api';
import request from '@/utils/request';

export interface IUserInfo {
  id: string;
  nickname: string;
  email: string;
  avatar?: string;
  create_time: string;
  update_time: string;
}

export interface ILoginRequestBody {
  email: string;
  password: string;
}

export interface IRegisterRequestBody {
  nickname: string;
  email: string;
  password: string;
}

export interface ILoginChannel {
  channel: string;
  display_name: string;
  icon: string;
}

export interface IUserSettingParams {
  nickname?: string;
  email?: string;
  password?: string;
  new_password?: string;
  avatar?: string;
  language?: string;
}

export interface ISystemStatus {
  doc_engine: {
    type: string;
    status: string;
    elapsed: string;
    error?: string;
  };
  storage: {
    type: string;
    status: string;
    elapsed: string;
    error?: string;
  };
  database: {
    database: string;
    status: string;
    elapsed: string;
    error?: string;
  };
  redis: {
    status: string;
    elapsed: string;
    error?: string;
  };
}

export interface ISystemVersion {
  version: string;
}

export interface ISystemConfig {
  registerEnabled: number;
}

// 获取登录渠道
export const getLoginChannels = () => {
  return request.get(api.login_channels);
};

// 使用渠道登录
export const loginWithChannel = (channel: string) => {
  window.location.href = `${api.login}/${channel}`;
};

const userService = {
  // 用户登录
  login: (params: ILoginRequestBody) => {
    return request.post(api.login, { data: params });
  },

  // 用户注册
  register: (params: IRegisterRequestBody) => {
    return request.post(api.register, { data: params });
  },

  // 用户登出
  logout: () => {
    return request.get(api.logout);
  },

  // 获取用户信息
  getUserInfo: () => {
    return request.get(api.user_info);
  },

  // 更新用户设置
  updateUserSetting: (params: IUserSettingParams) => {
    return request.post(api.setting, { data: params });
  },

  // 获取系统状态
  getSystemStatus: () => {
    return request.get(api.getSystemStatus);
  },

  // 获取系统版本
  getSystemVersion: () => {
    return request.get(api.getSystemVersion);
  },

  // 获取系统配置
  getSystemConfig: () => {
    return request.get(api.getSystemConfig);
  },

  // LLM相关方法 - 与原版web目录保持一致
  factories_list: () => {
    return request.get(api.factories_list);
  },

  llm_list: (params?: { model_type?: string }) => {
    return request.get(api.llm_list, { params });
  },

  my_llm: () => {
    return request.get(api.my_llm);
  },

  set_api_key: (params: any) => {
    return request.post(api.set_api_key, { data: params });
  },

  add_llm: (params: any) => {
    return request.post(api.add_llm, { data: params });
  },

  delete_llm: (params: any) => {
    return request.post(api.delete_llm, { data: params });
  },

  deleteFactory: (params: any) => {
    return request.post(api.deleteFactory, { data: params });
  },

  // 设置租户信息（包含默认模型）
  setTenantInfo: (params: any) => {
    return request.post(api.set_tenant_info, { data: params });
  },

  // 获取租户信息
  getTenantInfo: () => {
    return request.get(api.tenant_info);
  },

  // 获取租户信息（原版web兼容方法）
  get_tenant_info: () => {
    return request.get(api.tenant_info);
  },

  // 设置租户信息（原版web兼容方法）
  set_tenant_info: (params: any) => {
    return request.post(api.set_tenant_info, { data: params });
  },
};

export default userService;
