import request from '@/utils/request';
import registerServer from '@/utils/register-server';
import { API_ENDPOINTS } from '@/constants';
import type { UserInfo } from '@/utils/auth';

// 登录请求参数
export interface LoginParams {
  email: string;
  password: string;
}

// 登录响应数据
export interface LoginResponse {
  access_token: string;
  email: string;
  nickname: string;
  avatar?: string;
  id: string;
  create_time: string;
  update_time: string;
}

// 知识库信息
export interface KnowledgeBase {
  id: string;
  name: string;
  description?: string;
  language: string;
  permission: string;
  doc_num: number;
  chunk_num: number;
  parser_id: string;
  parser_config: Record<string, any>;
  create_time: string;
  update_time: string;
  avatar?: string;
  tenant_id: string;
}

// 文档信息
export interface Document {
  id: string;
  name: string;
  type: string;
  size: number;
  chunk_num: number;
  kb_id: string;
  parser_id: string;
  parser_config: Record<string, any>;
  source_type: string;
  status: string;
  progress: number;
  progress_msg: string;
  process_begin_at?: string;
  process_duation?: number;
  run: string;
  create_time: string;
  update_time: string;
  thumbnail?: string;
}

// 对话信息
export interface Dialog {
  id: string;
  name: string;
  description?: string;
  language: string;
  kb_ids: string[];
  llm_id: string;
  llm_setting: Record<string, any>;
  prompt_config: Record<string, any>;
  create_time: string;
  update_time: string;
}

// 文件信息
export interface FileItem {
  id: string;
  name: string;
  type: string;
  size: number;
  location: string;
  parent_id: string;
  create_time: string;
  update_time: string;
}

// 用户API服务
const userApiConfig = {
  login: { url: API_ENDPOINTS.USER_LOGIN, method: 'POST' },
  logout: { url: API_ENDPOINTS.USER_LOGOUT, method: 'POST' },
  getUserInfo: { url: API_ENDPOINTS.USER_INFO, method: 'GET' },
};

export const userApi = registerServer(userApiConfig, request);

// 知识库API服务 - 使用与原项目相同的方式
const knowledgeBaseApiConfig = {
  getList: { url: API_ENDPOINTS.KB_LIST, method: 'POST' },
  create: { url: API_ENDPOINTS.KB_CREATE, method: 'POST' },
  update: { url: API_ENDPOINTS.KB_UPDATE, method: 'POST' },
  delete: { url: API_ENDPOINTS.KB_DELETE, method: 'POST' },
  getDetail: { url: API_ENDPOINTS.KB_DETAIL, method: 'GET' },
};

export const knowledgeBaseApi = registerServer(knowledgeBaseApiConfig, request);

// 文档API服务
const documentApiConfig = {
  getList: { url: API_ENDPOINTS.DOC_LIST, method: 'POST' },
  upload: { url: API_ENDPOINTS.DOC_UPLOAD, method: 'POST' },
  delete: { url: API_ENDPOINTS.DOC_DELETE, method: 'POST' },
  rename: { url: API_ENDPOINTS.DOC_RENAME, method: 'POST' },
  run: { url: API_ENDPOINTS.DOC_RUN, method: 'POST' },
};

export const documentApi = registerServer(documentApiConfig, request);

// 对话API服务
const chatApiConfig = {
  getDialogList: { url: API_ENDPOINTS.CHAT_LIST, method: 'GET' },
  createDialog: { url: API_ENDPOINTS.CHAT_CREATE, method: 'POST' },
  deleteDialog: { url: API_ENDPOINTS.CHAT_DELETE, method: 'POST' },
  sendMessage: { url: API_ENDPOINTS.CONVERSATION_ASK, method: 'POST' },
  getConversationList: { url: API_ENDPOINTS.CONVERSATION_LIST, method: 'GET' },
};

export const chatApi = registerServer(chatApiConfig, request);

// 文件API服务
const fileApiConfig = {
  getList: { url: API_ENDPOINTS.FILE_LIST, method: 'GET' },
  upload: { url: API_ENDPOINTS.FILE_UPLOAD, method: 'POST' },
  delete: { url: API_ENDPOINTS.FILE_DELETE, method: 'POST' },
  rename: { url: API_ENDPOINTS.FILE_RENAME, method: 'POST' },
  move: { url: API_ENDPOINTS.FILE_MOVE, method: 'POST' },
  createFolder: { url: API_ENDPOINTS.FILE_CREATE_FOLDER, method: 'POST' },
};

export const fileApi = registerServer(fileApiConfig, request);

// 系统API服务
const systemApiConfig = {
  getStatus: { url: API_ENDPOINTS.SYSTEM_STATUS, method: 'GET' },
  getVersion: { url: API_ENDPOINTS.SYSTEM_VERSION, method: 'GET' },
};

export const systemApi = registerServer(systemApiConfig, request);

// LLM API服务
const llmApiConfig = {
  getList: { url: API_ENDPOINTS.LLM_LIST, method: 'GET' },
  getFactories: { url: API_ENDPOINTS.LLM_FACTORIES, method: 'GET' },
  add: { url: API_ENDPOINTS.LLM_ADD, method: 'POST' },
  delete: { url: API_ENDPOINTS.LLM_DELETE, method: 'POST' },
};

export const llmApi = registerServer(llmApiConfig, request);
