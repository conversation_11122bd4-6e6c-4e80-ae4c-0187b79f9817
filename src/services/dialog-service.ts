import request from '@/utils/request';
import api from '@/utils/api';
import {
  IDialog,
  IDialogParams,
  IDialogListParams,
  IDialogRemoveParams,
  IDialogGetParams,
  IDialogResponse,
  IDialogListResponse,
} from '@/interfaces/dialog';

const dialogService = {
  // 设置/创建Dialog - POST /v1/dialog/set
  setDialog: (params: IDialog): Promise<IDialogResponse<IDialog>> => {
    return request.post(api.setDialog, {
      data: params,
    });
  },

  // 获取Dialog列表 - GET /v1/dialog/list
  listDialog: (params: IDialogListParams = {}): Promise<IDialogResponse<IDialog[]>> => {
    return request.get(api.listDialog, {
      params: {
        page: params.page || 1,
        page_size: params.page_size || 20,
        orderby: params.orderby || 'create_time',
        desc: params.desc !== false,
        keywords: params.keywords || '',
      },
    });
  },

  // 获取Dialog详情 - GET /v1/dialog/get
  getDialog: (params: IDialogGetParams): Promise<IDialogResponse<IDialog>> => {
    return request.get(api.getDialog, {
      params: {
        dialog_id: params.dialogId,
      },
    });
  },

  // 删除Dialog - POST /v1/dialog/rm
  removeDialog: (params: IDialogRemoveParams): Promise<IDialogResponse<any>> => {
    console.log('🚀 removeDialog called with params:', params);
    // 参考原版register-server实现，直接传递参数对象
    // register-server会自动将params作为data发送
    console.log('🚀 sending request data:', params);
    return request.post(api.removeDialog, {
      data: params, // 直接传递整个params对象
    });
  },

  // 创建Dialog的便捷方法
  createDialog: (params: Omit<IDialogParams, 'dialog_id'>): Promise<IDialogResponse<IDialog>> => {
    return dialogService.setDialog(params);
  },

  // 更新Dialog的便捷方法
  updateDialog: (params: IDialogParams): Promise<IDialogResponse<IDialog>> => {
    if (!params.dialog_id) {
      throw new Error('dialog_id is required for update');
    }
    return dialogService.setDialog(params);
  },

  // 删除单个Dialog的便捷方法
  deleteDialog: (dialogId: string): Promise<IDialogResponse<any>> => {
    return dialogService.removeDialog({ dialogIds: [dialogId] });
  },
};

export default dialogService;
