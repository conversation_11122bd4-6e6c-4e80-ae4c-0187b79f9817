import request from '@/utils/request';
import api from '@/utils/api';
import type {
  IDocument,
  IDocumentListParams,
  IDocumentListResponse,
  IUploadDocumentParams,
  IWebCrawlParams,
  IChangeDocumentStatusParams,
  IRunDocumentParams,
  IDeleteDocumentParams,
  IRenameDocumentParams,
  IChangeParserParams,
  IChunkListParams,
  IChunkListResponse,
} from '@/interfaces/document';

const documentService = {
  // 获取文档列表
  getDocumentList: (params: IDocumentListParams) => {
    const { kb_id, ...queryParams } = params;
    return request.post(`${api.document_list}?kb_id=${kb_id}`, {
      data: {
        run_status: queryParams.run_status || [],
        types: queryParams.types || [],
      },
      params: {
        page: queryParams.page || 0,
        page_size: queryParams.page_size || 20,
        keywords: queryParams.keywords || '',
        orderby: queryParams.orderby || 'create_time',
        desc: queryParams.desc !== false ? 'true' : 'false',
      },
    });
  },

  // 上传文档
  uploadDocument: (params: IUploadDocumentParams) => {
    const formData = new FormData();
    formData.append('kb_id', params.kb_id);
    formData.append('file', params.file);
    
    return request.post(api.document_upload, {
      data: formData,
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
  },

  // 网页爬取
  webCrawl: (params: IWebCrawlParams) => {
    const formData = new FormData();
    formData.append('kb_id', params.kb_id);
    formData.append('name', params.name);
    formData.append('url', params.url);
    
    return request.post(api.document_web_crawl, {
      data: formData,
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
  },

  // 更改文档状态
  changeDocumentStatus: (params: IChangeDocumentStatusParams) => {
    return request.post(api.document_change_status, {
      data: params,
    });
  },

  // 运行/停止文档解析
  runDocument: (params: IRunDocumentParams) => {
    return request.post(api.document_run, {
      data: params,
    });
  },

  // 删除文档
  deleteDocument: (params: IDeleteDocumentParams) => {
    return request.post(api.document_delete, {
      data: params,
    });
  },

  // 重命名文档
  renameDocument: (params: IRenameDocumentParams) => {
    return request.post(api.document_rename, {
      data: params,
    });
  },

  // 更改解析器
  changeParser: (params: IChangeParserParams) => {
    return request.post(api.document_change_parser, {
      data: params,
    });
  },

  // 获取文档内容
  getDocument: (docId: string) => {
    return request.get(`${api.document_get}/${docId}`);
  },

  // 下载文档
  downloadDocument: (docId: string) => {
    return `${api.document_get}/${docId}`;
  },

  // 获取文档chunks
  getDocumentChunks: (params: IChunkListParams) => {
    return request.post(api.chunk_list, {
      data: params,
    });
  },

  // 解析文档预览
  parseDocument: (file: File) => {
    const formData = new FormData();
    formData.append('file', file);
    
    return request.post(api.document_parse, {
      data: formData,
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
  },

  // 解析URL预览
  parseUrl: (url: string) => {
    return request.post(api.document_parse, {
      data: { url },
    });
  },

  // 获取文档缩略图（修复参数格式）
  getDocumentThumbnails: (doc_ids: string[]) => {
    console.log('🔍 documentService.getDocumentThumbnails - Input doc_ids:', doc_ids);

    // 按照正确的格式构建参数：/v1/document/thumbnails?doc_ids={doc_id1}&doc_ids={doc_id2}&doc_ids={doc_id3}
    const params = new URLSearchParams();
    doc_ids.forEach(doc_id => {
      params.append('doc_ids', doc_id);
    });

    const url = `${api.document_thumbnails}?${params.toString()}`;
    console.log('🔍 documentService.getDocumentThumbnails - Request URL:', url);

    return request.get(api.document_thumbnails, {
      params: Object.fromEntries(params.entries()),
      paramsSerializer: (params: any) => {
        // 确保多个doc_ids参数正确序列化
        const searchParams = new URLSearchParams();
        if (params.doc_ids) {
          if (Array.isArray(params.doc_ids)) {
            params.doc_ids.forEach((id: string) => searchParams.append('doc_ids', id));
          } else {
            searchParams.append('doc_ids', params.doc_ids);
          }
        }
        return searchParams.toString();
      }
    });
  },

  // 获取文档图片（基于chunk.id）
  getDocumentImage: (chunk_id: string, page?: number) => {
    const params: any = { chunk_id };
    if (page !== undefined) {
      params.page = page;
    }
    return request.get(api.document_image, {
      params,
    });
  },

  // 获取文档图片（基于doc_id和image_id的URL格式）
  getDocumentImageByUrl: (doc_id: string, image_id: string) => {
    // 构建URL格式：/v1/document/image/{doc_id}-thumbnail_{image_id}.png
    const imageUrl = `${api.document_image}/${doc_id}-thumbnail_${image_id}.png`;
    return request.get(imageUrl);
  },

  // 获取文档详细信息
  getDocumentInfo: (doc_id: string) => {
    return request.get(api.document_infos, {
      params: { doc_id },
    });
  },
};

export default documentService;
