import request from 'umi-request';
import api from '@/utils/api';

export interface ILLMModel {
  llm_name: string;
  model_type: string;
  fid: string;
  available: boolean;
}

export interface ILLMFactory {
  name: string;
  logo: string;
  tags: string;
  status: string;
  model_types: string[];
}

export interface ILLMListResponse {
  [factoryName: string]: ILLMModel[];
}

export interface IMyLLMResponse {
  [factoryName: string]: {
    tags: string;
    llm: Array<{
      type: string;
      name: string;
      used_token: number;
    }>;
  };
}

export interface IMyLLMFactory {
  tags: string;
  llm: Array<{
    type: string;
    name: string;
    used_token: number;
  }>;
}

export interface ISetApiKeyParams {
  llm_factory: string;
  api_key: string;
  api_base?: string;
  model_type?: string;
  llm_name?: string;
}

export interface IAddLLMParams {
  llm_factory: string;
  llm_name?: string;
  api_key?: string;
  api_base?: string;
  [key: string]: any;
}

const llmService = {
  // 获取所有可用的LLM模型列表
  getModelList: (modelType?: string) => {
    const params = modelType ? { model_type: modelType } : {};
    return request.get(api.llm_list, {
      params,
    });
  },

  // 获取LLM工厂列表
  getFactories: () => {
    return request.get(api.llm_factories);
  },

  // 获取我的LLM模型
  getMyModels: () => {
    return request.get(api.llm_my_llms);
  },

  // 获取我的LLM模型 (别名)
  getMyLLMs: () => {
    return request.get(api.llm_my_llms);
  },

  // 获取embedding模型列表
  getEmbeddingModels: () => {
    return request.get(api.llm_list, {
      params: {
        model_type: 'embedding',
      },
    });
  },

  // 获取rerank模型列表
  getRerankModels: () => {
    return request.get(api.llm_list, {
      params: {
        model_type: 'rerank',
      },
    });
  },

  // 获取chat模型列表
  getChatModels: () => {
    return request.get(api.llm_list, {
      params: {
        model_type: 'chat',
      },
    });
  },

  // 设置API密钥
  setApiKey: (params: ISetApiKeyParams) => {
    return request.post(api.llm_set_api_key, {
      data: params,
    });
  },

  // 添加LLM模型
  addModel: (params: IAddLLMParams) => {
    return request.post(api.llm_add_llm, {
      data: params,
    });
  },

  // 添加LLM模型 (别名)
  addLLM: (params: IAddLLMParams) => {
    return request.post(api.llm_add_llm, {
      data: params,
    });
  },

  // 删除LLM模型
  deleteModel: (params: {
    llm_factory: string;
    llm_name: string;
  }) => {
    return request.post(api.llm_delete_llm, {
      data: params,
    });
  },

  // 删除LLM工厂
  deleteFactory: (params: {
    llm_factory: string;
  }) => {
    return request.post(api.llm_delete_factory, {
      data: params,
    });
  },

  // 测试模型连接
  testModel: (params: {
    llm_factory: string;
    llm_name: string;
    api_key: string;
    base_url?: string;
  }) => {
    return request.post(api.llm_test, {
      data: params,
    });
  },

  // 获取租户信息
  getTenantInfo: () => {
    return request.get('/v1/user/info');
  },

  // 设置默认模型
  setDefaultModels: (params: {
    tenant_id: string;
    llm_id?: string;
    embd_id?: string;
    asr_id?: string;
    img2txt_id?: string;
    rerank_id?: string;
    tts_id?: string;
  }) => {
    return request.post('/v1/user/set_default_models', {
      data: params,
    });
  },
};

export default llmService;
