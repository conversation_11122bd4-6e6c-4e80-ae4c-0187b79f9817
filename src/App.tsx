import React from 'react';
import { Routes, Route, Navigate } from 'react-router-dom';
import { Spin } from 'antd';
import { useAuthState } from '@/hooks/useAuth';
import { ROUTES } from '@/constants';
import LoginPage from '@/pages/Login';
import DashboardPage from '@/pages/Dashboard';
import KnowledgeBasePage from '@/pages/KnowledgeBase';
import DocumentsPage from '@/pages/Documents';
import ChatPage from '@/pages/Chat';
import FilesPage from '@/pages/Files';
import SettingsPage from '@/pages/Settings';
import Layout from '@/components/Layout';
import ProtectedRoute from '@/components/ProtectedRoute';

const App: React.FC = () => {
  const { isAuthenticated, loading } = useAuthState();

  // 显示加载状态
  if (loading) {
    return (
      <div style={{ 
        display: 'flex', 
        justifyContent: 'center', 
        alignItems: 'center', 
        height: '100vh' 
      }}>
        <Spin size="large" />
      </div>
    );
  }

  return (
    <Routes>
      {/* 登录页面 */}
      <Route 
        path={ROUTES.LOGIN} 
        element={
          isAuthenticated ? (
            <Navigate to={ROUTES.DASHBOARD} replace />
          ) : (
            <LoginPage />
          )
        } 
      />

      {/* 受保护的路由 */}
      <Route
        path="/*"
        element={
          <ProtectedRoute>
            <Layout>
              <Routes>
                {/* 默认重定向到仪表板 */}
                <Route path="/" element={<Navigate to={ROUTES.DASHBOARD} replace />} />
                
                {/* 仪表板 */}
                <Route path={ROUTES.DASHBOARD} element={<DashboardPage />} />
                
                {/* 知识库管理 */}
                <Route path={ROUTES.KNOWLEDGE_BASE} element={<KnowledgeBasePage />} />
                <Route path={`${ROUTES.KNOWLEDGE_BASE}/:id`} element={<KnowledgeBasePage />} />
                
                {/* 文档管理 */}
                /*
                <Route path={ROUTES.DOCUMENTS} element={<DocumentsPage />} />
                <Route path={`${ROUTES.DOCUMENTS}/:kbId`} element={<DocumentsPage />} />
                */
                {/* 对话聊天 */}
                <Route path={ROUTES.CHAT} element={<ChatPage />} />
                <Route path={`${ROUTES.CHAT}/:dialogId`} element={<ChatPage />} />
                
                {/* 文件管理 */}
                <Route path={ROUTES.FILES} element={<FilesPage />} />
                
                {/* 系统设置 */}
                <Route path={ROUTES.SETTINGS} element={<SettingsPage />} />
                <Route path={`${ROUTES.SETTINGS}/:section`} element={<SettingsPage />} />
                
                {/* 404页面 */}
                <Route path="*" element={<Navigate to={ROUTES.DASHBOARD} replace />} />
              </Routes>
            </Layout>
          </ProtectedRoute>
        }
      />
    </Routes>
  );
};

export default App;
