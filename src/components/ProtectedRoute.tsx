import React from 'react';
import { Navigate } from 'react-router-dom';
import { useAuthState } from '@/hooks/useAuth';
import { ROUTES } from '@/constants';

interface ProtectedRouteProps {
  children: React.ReactNode;
  permission?: string;
}

const ProtectedRoute: React.FC<ProtectedRouteProps> = ({ 
  children, 
  permission 
}) => {
  const { isAuthenticated, userInfo } = useAuthState();

  // 如果未认证，重定向到登录页
  if (!isAuthenticated) {
    return <Navigate to={ROUTES.LOGIN} replace />;
  }

  // 如果需要特定权限但用户没有，可以在这里处理
  if (permission && userInfo) {
    // 这里可以添加权限检查逻辑
    // 例如：if (!userInfo.permissions.includes(permission)) return <AccessDenied />
  }

  return <>{children}</>;
};

export default ProtectedRoute;
