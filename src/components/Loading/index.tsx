import { Spin } from 'antd';
import React from 'react';
import { useTranslate } from '@/hooks/use-i18n';

interface LoadingProps {
  tip?: string;
  size?: 'small' | 'default' | 'large';
}

const Loading: React.FC<LoadingProps> = ({ tip, size = 'large' }) => {
  const t = useTranslate();
  const defaultTip = tip || t('common.loading', 'Loading...');
  return (
    <div
      style={{
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center',
        height: '100vh',
        flexDirection: 'column',
      }}
    >
      <Spin size={size} tip={defaultTip} />
    </div>
  );
};

export default Loading;
