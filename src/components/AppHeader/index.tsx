import {
  MenuUnfoldOutlined,
  MenuFoldOutlined,
  UserOutlined,
  LogoutOutlined,
  SearchOutlined,
} from '@ant-design/icons';
import { Layout, Button, Input, Avatar, Dropdown, Typography, Space } from 'antd';
import { useAuth } from '@/hooks/auth-hooks';
import { useLogout } from '@/hooks/login-hooks';
import { useTranslate } from '@/hooks/use-i18n';
import LanguageSwitcher from '@/components/LanguageSwitcher';
import styles from './index.less';

const { Header } = Layout;
const { Search } = Input;
const { Text } = Typography;

interface AppHeaderProps {
  collapsed: boolean;
  onToggleCollapse: () => void;
  showSearch?: boolean;
  searchPlaceholder?: string;
  onSearch?: (value: string) => void;
}

const AppHeader: React.FC<AppHeaderProps> = ({
  collapsed,
  onToggleCollapse,
  showSearch = true,
  searchPlaceholder,
  onSearch,
}) => {
  const t = useTranslate();
  const { userInfo } = useAuth();
  const { logout } = useLogout();

  const defaultSearchPlaceholder = searchPlaceholder || t('placeholders.searchPlaceholder');

  const handleLogout = async () => {
    try {
      await logout();
    } catch (error) {
      // Handle logout error silently
    }
  };

  const userMenuItems = [
    {
      key: 'profile',
      icon: <UserOutlined />,
      label: t('settings.profile'),
      onClick: () => {
        // TODO: Navigate to profile page
      },
    },
    {
      type: 'divider' as const,
    },
    {
      key: 'logout',
      icon: <LogoutOutlined />,
      label: t('auth.signOut'),
      onClick: handleLogout,
    },
  ];

  return (
    <Header className={styles.header}>
      <div className={styles.headerLeft}>
        <Button
          type="text"
          icon={collapsed ? <MenuUnfoldOutlined /> : <MenuFoldOutlined />}
          onClick={onToggleCollapse}
          className={styles.trigger}
        />
        {showSearch && (
          <Search
            placeholder={defaultSearchPlaceholder}
            allowClear
            style={{ width: 300 }}
            onSearch={onSearch}
            className={styles.search}
          />
        )}
      </div>
      
      <div className={styles.headerRight}>
        <Space size="middle">
          <LanguageSwitcher size="small" />
          <Dropdown
            menu={{ items: userMenuItems }}
            trigger={['click']}
            placement="bottomRight"
          >
            <div className={styles.userInfo}>
              <Avatar
                size="small"
                icon={<UserOutlined />}
                src={userInfo?.avatar}
                className={styles.avatar}
              />
              <Text className={styles.username}>
                {userInfo?.nickname || userInfo?.email || t('common.user', 'User')}
              </Text>
            </div>
          </Dropdown>
        </Space>
      </div>
    </Header>
  );
};

export default AppHeader;
