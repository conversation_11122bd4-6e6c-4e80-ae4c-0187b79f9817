.header {
  background: #fff;
  padding: 0 24px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  display: flex;
  align-items: center;
  justify-content: space-between;
  position: relative;
  z-index: 99;
}

.headerLeft {
  display: flex;
  align-items: center;
  gap: 24px;
}

.trigger {
  font-size: 18px;
  line-height: 64px;
  padding: 0 12px;
  cursor: pointer;
  transition: color 0.3s;
  color: #666;
  
  &:hover {
    color: #1890ff;
    background: rgba(24, 144, 255, 0.1);
  }
}

.search {
  .ant-input {
    border-radius: 20px;
    border: 1px solid #e8e8e8;
    
    &:hover {
      border-color: #667eea;
    }
    
    &:focus,
    &.ant-input-focused {
      border-color: #667eea;
      box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.1);
    }
  }
  
  .ant-input-search-button {
    border-radius: 0 20px 20px 0;
    border-left: none;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    
    &:hover {
      background: linear-gradient(135deg, #5a67d8 0%, #6b46c1 100%);
    }
  }
}

.headerRight {
  display: flex;
  align-items: center;
  gap: 16px;
}

.userInfo {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 12px;
  border-radius: 20px;
  cursor: pointer;
  transition: all 0.3s ease;
  
  &:hover {
    background: rgba(102, 126, 234, 0.1);
  }
}

.avatar {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.username {
  color: #1f2937;
  font-weight: 500;
  font-size: 14px;
}

@media (max-width: 768px) {
  .header {
    padding: 0 16px;
  }
  
  .headerLeft {
    gap: 12px;
  }
  
  .search {
    width: 200px;
    
    .ant-input {
      font-size: 14px;
    }
  }
  
  .username {
    display: none;
  }
}

@media (max-width: 480px) {
  .header {
    padding: 0 12px;
  }
  
  .headerLeft {
    gap: 8px;
  }
  
  .search {
    width: 150px;
  }
}
