.triggerButton {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  border-radius: 8px;
  transition: all 0.3s;
  color: #666;

  &:hover {
    background: #f0f0f0;
    color: #1890ff;
  }

  .anticon {
    font-size: 18px;
  }
}

.floatButton {
  bottom: 80px;
  right: 24px;
  width: 56px;
  height: 56px;
  background: #667eea;
  border: none;
  box-shadow: 0 4px 16px rgba(102, 126, 234, 0.3);

  &:hover {
    background: #5a6fd8;
    box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4);
  }

  .anticon {
    color: white;
    font-size: 24px;
  }
}

.chatBotDrawer {
  .ant-drawer-content {
    background: #fafafa;
  }

  .ant-drawer-header {
    background: white;
    border-bottom: 1px solid #e8e8e8;
  }

  .ant-drawer-body {
    background: #fafafa;
  }
}

.drawerHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}

.headerContent {
  display: flex;
  align-items: center;
  gap: 8px;
}

.headerIcon {
  color: #667eea;
  font-size: 18px;
}

.headerTitle {
  font-size: 16px;
  font-weight: 600;
  color: #262626;
}

.closeButton {
  color: #8c8c8c;
  
  &:hover {
    color: #262626;
    background: #f5f5f5;
  }
}

.drawerContent {
  height: 100%;
  display: flex;
  flex-direction: column;
  padding: 16px;
}

// 响应式设计
@media (max-width: 768px) {
  .chatBotDrawer {
    .ant-drawer-content-wrapper {
      width: 100vw !important;
    }
  }

  .floatButton {
    bottom: 60px;
    right: 16px;
    width: 48px;
    height: 48px;

    .anticon {
      font-size: 20px;
    }
  }

  .drawerContent {
    padding: 8px;
  }
}

// 暗色主题支持
@media (prefers-color-scheme: dark) {
  .triggerButton {
    color: #d9d9d9;

    &:hover {
      background: #262626;
      color: #1890ff;
    }
  }

  .chatBotDrawer {
    .ant-drawer-content {
      background: #1f1f1f;
    }

    .ant-drawer-header {
      background: #262626;
      border-bottom-color: #404040;
    }

    .ant-drawer-body {
      background: #1f1f1f;
    }
  }

  .headerTitle {
    color: #d9d9d9;
  }

  .closeButton {
    color: #8c8c8c;
    
    &:hover {
      color: #d9d9d9;
      background: #404040;
    }
  }

  .drawerContent {
    background: #1f1f1f;
  }
}
