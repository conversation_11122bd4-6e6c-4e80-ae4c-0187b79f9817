import React, { useState, useEffect, useImperativeHandle, forwardRef } from 'react';
import {
  Card,
  List,
  Button,
  Typography,
  Space,
  Empty,
  Spin,
  message,
  Tooltip,
  Popconfirm,
  Tag,
} from 'antd';
import {
  MessageOutlined,
  DeleteOutlined,
  ClockCircleOutlined,
  CommentOutlined,
} from '@ant-design/icons';
import { useChatBotHistory } from '@/hooks/use-chatbot-hooks';
import { useTranslate } from '@/hooks/use-i18n';
import styles from './ChatHistoryList.less';

const { Text, Title } = Typography;

interface ChatHistoryListProps {
  onSelectSession?: (sessionId: string) => void;
  selectedSessionId?: string;
  className?: string;
  style?: React.CSSProperties;
  onRefresh?: () => void; // 新增刷新回调函数
}

export interface ChatHistoryListRef {
  refreshSessions: () => void;
}

interface SessionItem {
  session_id: string;
  session_name: string;
  first_message: string;
  message_count: number;
  last_message_time: string;
  create_time: string;
}

const ChatHistoryList = forwardRef<ChatHistoryListRef, ChatHistoryListProps>(({
  onSelectSession,
  selectedSessionId,
  className,
  style,
  onRefresh,
}, ref) => {
  const t = useTranslate();
  const [sessions, setSessions] = useState<SessionItem[]>([]);
  const [loading, setLoading] = useState(false);
  const [total, setTotal] = useState(0);
  
  const {
    getUserSessions,
    deleteSession,
    isLoading: hookLoading,
  } = useChatBotHistory();

  // 加载会话列表
  const loadSessions = async () => {
    try {
      setLoading(true);
      console.log('开始加载对话记录...');
      const response = await getUserSessions({ limit: 20, offset: 0 });

      console.log('API响应:', response);

      if (response.code === 0) {
        setSessions(response.data.sessions || []);
        setTotal(response.data.total || 0);
        console.log(`成功加载 ${response.data.sessions?.length || 0} 条对话记录`);
      } else {
        console.error('API返回错误:', response);
        if (response.code === 401) {
          message.error('用户未登录或登录已过期，请重新登录');
        } else {
          message.error(response.message || '加载对话记录失败');
        }
      }
    } catch (error) {
      console.error('加载对话记录异常:', error);
      message.error('加载对话记录失败，请重试');
    } finally {
      setLoading(false);
    }
  };

  // 组件挂载时加载数据
  useEffect(() => {
    loadSessions();
  }, []);

  // 暴露刷新方法给父组件
  useImperativeHandle(ref, () => ({
    refreshSessions: loadSessions,
  }));

  // 处理会话选择
  const handleSelectSession = (sessionId: string) => {
    if (onSelectSession) {
      onSelectSession(sessionId);
    }
  };

  // 处理删除会话
  const handleDeleteSession = async (sessionId: string, event: React.MouseEvent) => {
    event.stopPropagation(); // 阻止事件冒泡
    
    try {
      const response = await deleteSession(sessionId);
      
      if (response.code === 0) {
        message.success('对话记录已删除');
        // 重新加载列表
        loadSessions();
        
        // 如果删除的是当前选中的会话，清除选中状态
        if (selectedSessionId === sessionId && onSelectSession) {
          onSelectSession('');
        }
      } else {
        message.error(response.message || '删除失败');
      }
    } catch (error) {
      message.error('删除失败，请重试');
    }
  };

  // 格式化时间
  const formatTime = (timeStr: string) => {
    try {
      const date = new Date(timeStr);
      const now = new Date();
      const diffMs = now.getTime() - date.getTime();
      const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));
      
      if (diffDays === 0) {
        return date.toLocaleTimeString('zh-CN', { 
          hour: '2-digit', 
          minute: '2-digit' 
        });
      } else if (diffDays === 1) {
        return '昨天';
      } else if (diffDays < 7) {
        return `${diffDays}天前`;
      } else {
        return date.toLocaleDateString('zh-CN', {
          month: 'short',
          day: 'numeric'
        });
      }
    } catch {
      return timeStr;
    }
  };

  // 渲染会话项
  const renderSessionItem = (session: SessionItem) => {
    const isSelected = selectedSessionId === session.session_id;
    
    return (
      <List.Item
        key={session.session_id}
        className={`${styles.sessionItem} ${isSelected ? styles.selectedSession : ''}`}
        onClick={() => handleSelectSession(session.session_id)}
      >
        <div className={styles.sessionContent}>
          <div className={styles.sessionHeader}>
            <div className={styles.sessionTitle}>
              <MessageOutlined className={styles.sessionIcon} />
              <Text 
                strong={isSelected}
                className={styles.sessionName}
                ellipsis={{ tooltip: session.session_name }}
              >
                {session.session_name || '新对话'}
              </Text>
            </div>
            <div className={styles.sessionActions}>
              <Tooltip title="删除对话">
                <Popconfirm
                  title="确定要删除这个对话吗？"
                  description="删除后无法恢复"
                  onConfirm={(e) => handleDeleteSession(session.session_id, e!)}
                  onCancel={(e) => e?.stopPropagation()}
                  okText="删除"
                  cancelText="取消"
                  okType="danger"
                >
                  <Button
                    type="text"
                    size="small"
                    icon={<DeleteOutlined />}
                    className={styles.deleteButton}
                    onClick={(e) => e.stopPropagation()}
                  />
                </Popconfirm>
              </Tooltip>
            </div>
          </div>
          
          {session.first_message && (
            <div className={styles.sessionPreview}>
              <Text type="secondary" className={styles.previewText}>
                {session.first_message.length > 60 
                  ? `${session.first_message.substring(0, 60)}...` 
                  : session.first_message
                }
              </Text>
            </div>
          )}
          
          <div className={styles.sessionMeta}>
            <Space size="small">
              <Tag icon={<CommentOutlined />} color="blue" size="small">
                {session.message_count}条消息
              </Tag>
              <Text type="secondary" className={styles.timeText}>
                <ClockCircleOutlined className={styles.timeIcon} />
                {formatTime(session.last_message_time)}
              </Text>
            </Space>
          </div>
        </div>
      </List.Item>
    );
  };

  return (
    <Card
      className={`${styles.chatHistoryList} ${className || ''}`}
      style={style}
      bodyStyle={{ padding: 0, height: '100%' }}
    >
      <div className={styles.historyHeader}>
        <Space>
          <MessageOutlined />
          <Title level={5} style={{ margin: 0 }}>
            对话记录
          </Title>
        </Space>
        <Text type="secondary" className={styles.totalText}>
          共{total}个对话
        </Text>
      </div>

      <div className={styles.historyContainer}>
        {loading || hookLoading ? (
          <div className={styles.loadingContainer}>
            <Spin size="large" />
            <Text type="secondary">加载中...</Text>
          </div>
        ) : sessions.length === 0 ? (
          <div className={styles.emptyContainer}>
            <Empty
              image={Empty.PRESENTED_IMAGE_SIMPLE}
              description="暂无对话记录"
            />
          </div>
        ) : (
          <List
            className={styles.sessionsList}
            dataSource={sessions}
            renderItem={renderSessionItem}
            split={false}
          />
        )}
      </div>
    </Card>
  );
});

ChatHistoryList.displayName = 'ChatHistoryList';

export default ChatHistoryList;
