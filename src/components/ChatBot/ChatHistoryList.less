.chatHistoryList {
  height: 100%;
  display: flex;
  flex-direction: column;

  .historyHeader {
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 100%;
    flex-shrink: 0; // 防止头部被压缩
    padding: 16px 24px 12px;
    border-bottom: 1px solid #f0f0f0;
    background: #fafafa;

    .totalText {
      font-size: 12px;
    }
  }

  .historyContainer {
    flex: 1;
    overflow: hidden;
    display: flex;
    flex-direction: column;
    min-height: 0; // 确保flex子元素能正确收缩
    height: calc(100% - 60px); // 减去头部高度
  }
  
  .loadingContainer {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 200px;
    gap: 16px;
  }
  
  .emptyContainer {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 200px;
  }
  
  .sessionsList {
    flex: 1;
    overflow-y: auto;
    padding: 8px 0;
    min-height: 0; // 确保能正确滚动
    max-height: calc(100% - 16px); // 减去padding

    &::-webkit-scrollbar {
      width: 6px; // 稍微增加滚动条宽度，更容易操作
    }

    &::-webkit-scrollbar-track {
      background: #f5f5f5;
      border-radius: 3px;
      margin: 4px 0; // 上下留一点空间
    }

    &::-webkit-scrollbar-thumb {
      background: #d9d9d9;
      border-radius: 3px;

      &:hover {
        background: #bfbfbf;
      }

      &:active {
        background: #999999;
      }
    }

    // 确保滚动条在需要时显示
    &:hover::-webkit-scrollbar-thumb {
      background: #bfbfbf;
    }
  }
  
  .sessionItem {
    padding: 12px 16px !important;
    margin: 4px 8px;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.2s ease;
    border: 1px solid transparent;
    
    &:hover {
      background-color: #f5f5f5;
      border-color: #d9d9d9;
    }
    
    &.selectedSession {
      background-color: #e6f7ff;
      border-color: #1890ff;
      
      .sessionName {
        color: #1890ff;
      }
    }
  }
  
  .sessionContent {
    width: 100%;
    display: flex;
    flex-direction: column;
    gap: 8px;
  }
  
  .sessionHeader {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    gap: 8px;
  }
  
  .sessionTitle {
    display: flex;
    align-items: center;
    gap: 8px;
    flex: 1;
    min-width: 0;
  }
  
  .sessionIcon {
    color: #1890ff;
    font-size: 14px;
    flex-shrink: 0;
  }
  
  .sessionName {
    font-size: 14px;
    font-weight: 500;
    flex: 1;
    min-width: 0;
  }
  
  .sessionActions {
    display: flex;
    align-items: center;
    opacity: 0;
    transition: opacity 0.2s ease;
  }
  
  .sessionItem:hover .sessionActions {
    opacity: 1;
  }
  
  .deleteButton {
    color: #ff4d4f;
    
    &:hover {
      background-color: #fff2f0;
      border-color: #ffccc7;
    }
  }
  
  .sessionPreview {
    margin-left: 22px;
    
    .previewText {
      font-size: 12px;
      line-height: 1.4;
      display: -webkit-box;
      -webkit-line-clamp: 2;
      -webkit-box-orient: vertical;
      overflow: hidden;
    }
  }
  
  .sessionMeta {
    margin-left: 22px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    
    .timeText {
      font-size: 11px;
      display: flex;
      align-items: center;
      gap: 4px;
    }
    
    .timeIcon {
      font-size: 10px;
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .chatHistoryList {
    .sessionItem {
      padding: 8px 12px !important;
      margin: 2px 4px;
    }
    
    .sessionName {
      font-size: 13px;
    }
    
    .previewText {
      font-size: 11px;
    }
    
    .timeText {
      font-size: 10px;
    }
  }
}

// 暗色主题支持
[data-theme='dark'] {
  .chatHistoryList {
    .sessionItem {
      &:hover {
        background-color: #262626;
        border-color: #434343;
      }
      
      &.selectedSession {
        background-color: #111b26;
        border-color: #177ddc;
        
        .sessionName {
          color: #177ddc;
        }
      }
    }
    
    .deleteButton {
      &:hover {
        background-color: #2a1215;
        border-color: #a8071a;
      }
    }
  }
}
