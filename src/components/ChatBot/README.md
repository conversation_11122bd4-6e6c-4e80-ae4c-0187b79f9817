# RAGFlow 聊天机器人 UI 组件

这是RAGFlow系统的前端聊天机器人UI组件，提供完整的聊天机器人交互界面。

## 组件结构

```
src/components/ChatBot/
├── index.tsx              # 主聊天机器人组件
├── index.less            # 主组件样式
├── ChatBotSidebar.tsx    # 侧边栏聊天机器人
├── ChatBotSidebar.less   # 侧边栏样式
└── README.md             # 组件文档
```

## 功能特性

### ✅ 核心功能
- **智能对话**: 基于RAGFlow后端聊天机器人API
- **流式回复**: 支持实时流式响应显示
- **会话管理**: 自动创建和管理聊天会话
- **消息历史**: 完整的对话历史记录
- **响应式设计**: 适配桌面和移动设备

### ✅ 用户体验
- **现代UI**: 美观的聊天界面设计
- **Markdown支持**: 支持Markdown格式的AI回复
- **实时反馈**: 显示AI思考状态和错误信息
- **快捷操作**: 支持Enter发送、Shift+Enter换行
- **多语言**: 支持中英文界面

### ✅ 集成方式
- **浮动按钮**: 右下角浮动聊天按钮
- **侧边栏**: 左侧导航栏聊天入口
- **独立页面**: 完整的聊天机器人页面

## 组件使用

### 1. 基础聊天组件

```tsx
import ChatBot from '@/components/ChatBot';

<ChatBot
  height={600}
  enableStream={true}
  systemPrompt="你是一个有用的AI助手"
/>
```

### 2. 侧边栏聊天组件

```tsx
import ChatBotSidebar from '@/components/ChatBot/ChatBotSidebar';

<ChatBotSidebar 
  placement="right"
  width={400}
  trigger="float"
/>
```

### 3. 聊天机器人页面

```tsx
import ChatBotPage from '@/pages/chatbot';

// 路由配置
{
  path: '/chatbot',
  component: '@/pages/chatbot',
}
```

## API 接口

组件调用以下后端API接口：

- `POST /api/v1/chatbot/sessions` - 创建会话
- `POST /api/v1/chatbot/sessions/{id}/messages` - 发送消息
- `GET /api/v1/chatbot/sessions/{id}/history` - 获取历史
- `DELETE /api/v1/chatbot/sessions/{id}` - 清空会话
- `GET /api/v1/chatbot/stats` - 服务统计
- `GET /api/v1/chatbot/health` - 健康检查

## 组件属性

### ChatBot 组件

| 属性 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| className | string | - | 自定义CSS类名 |
| style | CSSProperties | - | 自定义样式 |
| height | number | 600 | 组件高度 |
| enableStream | boolean | true | 是否启用流式回复 |
| systemPrompt | string | - | 系统提示词 |

### ChatBotSidebar 组件

| 属性 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| placement | 'left' \| 'right' | 'right' | 侧边栏位置 |
| width | number | 400 | 侧边栏宽度 |
| trigger | 'button' \| 'float' | 'button' | 触发方式 |
| defaultOpen | boolean | false | 默认是否打开 |
| className | string | - | 自定义CSS类名 |
| style | CSSProperties | - | 自定义样式 |

## 自定义Hooks

### useChatBot

```tsx
import { useChatBot } from '@/hooks/use-chatbot-hooks';

const {
  sessionId,
  messages,
  isLoading,
  isStreaming,
  error,
  createSession,
  sendMessage,
  sendMessageStream,
  loadHistory,
  clearSession,
  resetState,
} = useChatBot();
```

### useChatBotStats

```tsx
import { useChatBotStats } from '@/hooks/use-chatbot-hooks';

const {
  stats,
  isLoading,
  error,
  fetchStats,
} = useChatBotStats();
```

## 样式定制

### 主题色彩

```less
// 主要颜色
@primary-color: #667eea;
@success-color: #52c41a;
@error-color: #ff4d4f;

// 用户消息
.userMessage {
  background: @primary-color;
  color: white;
}

// AI消息
.assistantMessage {
  background: white;
  border: 1px solid #e8e8e8;
}
```

### 响应式断点

```less
// 移动设备
@media (max-width: 768px) {
  .chatBot {
    // 移动端样式
  }
}
```

## 国际化

组件支持中英文界面，翻译键值：

```json
{
  "chatbot": {
    "title": "AI助手",
    "send": "发送",
    "clear": "清空对话",
    "reset": "重置会话",
    "inputPlaceholder": "请输入您的问题...",
    "thinking": "正在思考中...",
    "noMessages": "暂无对话消息"
  }
}
```

## 集成到主应用

### 1. 在AppLayout中添加浮动按钮

```tsx
// src/components/AppLayout/index.tsx
import ChatBotSidebar from '@/components/ChatBot/ChatBotSidebar';

<ChatBotSidebar 
  placement="right"
  width={400}
  trigger="float"
/>
```

### 2. 在侧边栏添加菜单项

```tsx
// src/components/AppSidebar/index.tsx
{
  key: 'chatbot',
  icon: <CustomerServiceOutlined />,
  label: t('navigation.chatbot'),
  onClick: () => setChatBotVisible(true),
}
```

### 3. 添加路由配置

```tsx
// .umirc.ts
{
  path: '/chatbot',
  component: '@/pages/chatbot',
}
```

## 开发调试

### 1. 启动开发服务器

```bash
cd hbweb
npm start
```

### 2. 访问聊天机器人

- 浮动按钮：右下角聊天图标
- 侧边栏：左侧导航栏"AI助手"
- 独立页面：`http://localhost:8000/chatbot`

### 3. 调试API

确保后端聊天机器人服务正在运行：

```bash
# 检查健康状态
curl http://localhost:9380/api/v1/chatbot/health

# 查看服务统计
curl http://localhost:9380/api/v1/chatbot/stats
```

## 故障排除

### 常见问题

1. **聊天机器人无法加载**
   - 检查后端服务是否启动
   - 确认API代理配置正确
   - 查看浏览器控制台错误

2. **消息发送失败**
   - 检查用户认证状态
   - 确认会话是否正确创建
   - 查看网络请求状态

3. **流式回复不工作**
   - 检查浏览器是否支持SSE
   - 确认网络连接稳定
   - 查看后端流式接口状态

### 调试技巧

```tsx
// 启用调试模式
const DEBUG = process.env.NODE_ENV === 'development';

if (DEBUG) {
  console.log('ChatBot Debug:', { sessionId, messages, isLoading });
}
```

## 更新日志

### v1.0.0 (2024-12)
- ✅ 初始版本发布
- ✅ 基础聊天功能
- ✅ 流式回复支持
- ✅ 响应式设计
- ✅ 多语言支持
- ✅ 集成到主应用

## 贡献指南

1. 遵循React和TypeScript最佳实践
2. 使用Ant Design组件库
3. 保持代码风格一致
4. 添加适当的类型定义
5. 编写必要的测试用例

## 许可证

本组件遵循Apache License 2.0许可证。
