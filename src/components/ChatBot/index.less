.chatBot {
  display: flex;
  flex-direction: column;
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  overflow: hidden;

  .chatHeader {
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 100%;
  }

  .chatContainer {
    display: flex;
    flex-direction: column;
    height: 100%;
    background: #fafafa;
  }

  .messagesContainer {
    flex: 1;
    overflow-y: auto;
    padding: 16px;
    background: linear-gradient(to bottom, #f8f9fa, #ffffff);
    
    &::-webkit-scrollbar {
      width: 6px;
    }
    
    &::-webkit-scrollbar-track {
      background: #f1f1f1;
      border-radius: 3px;
    }
    
    &::-webkit-scrollbar-thumb {
      background: #c1c1c1;
      border-radius: 3px;
      
      &:hover {
        background: #a8a8a8;
      }
    }
  }

  .loadingContainer {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 100%;
    gap: 16px;
  }

  .emptyContainer {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 100%;
  }

  .messagesList {
    display: flex;
    flex-direction: column;
    gap: 16px;
  }

  .messageItem {
    display: flex;
    margin-bottom: 16px;

    &.userMessage {
      justify-content: flex-end;

      .messageContent {
        max-width: 70%;
        background: #667eea;
        color: white;
        border-radius: 18px 18px 4px 18px;
        box-shadow: 0 2px 8px rgba(102, 126, 234, 0.3);
      }

      .userAvatar {
        background: #667eea;
        order: 2;
        margin-left: 8px;
      }

      .messageHeader {
        flex-direction: row-reverse;
      }

      .messageText {
        color: white;
      }
    }

    &.assistantMessage {
      justify-content: flex-start;

      .messageContent {
        max-width: 80%;
        background: white;
        border: 1px solid #e8e8e8;
        border-radius: 18px 18px 18px 4px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
      }

      .assistantAvatar {
        background: #52c41a;
        margin-right: 8px;
      }
    }
  }

  .messageContent {
    padding: 12px 16px;
    position: relative;
  }

  .messageHeader {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 8px;
  }

  .messageTime {
    font-size: 12px;
    opacity: 0.7;
  }

  .messageBody {
    line-height: 1.6;

    // Markdown 内容样式
    :global {
      p {
        margin: 0 0 8px 0;
        
        &:last-child {
          margin-bottom: 0;
        }
      }

      code {
        background: rgba(0, 0, 0, 0.06);
        padding: 2px 4px;
        border-radius: 4px;
        font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
        font-size: 0.9em;
      }

      pre {
        background: #f6f8fa;
        border: 1px solid #e1e4e8;
        border-radius: 6px;
        padding: 12px;
        margin: 8px 0;
        overflow-x: auto;
        
        code {
          background: none;
          padding: 0;
        }
      }

      blockquote {
        border-left: 4px solid #dfe2e5;
        padding-left: 12px;
        margin: 8px 0;
        color: #6a737d;
      }

      ul, ol {
        margin: 8px 0;
        padding-left: 20px;
      }

      li {
        margin: 4px 0;
      }

      h1, h2, h3, h4, h5, h6 {
        margin: 12px 0 8px 0;
        font-weight: 600;
      }

      table {
        border-collapse: collapse;
        margin: 8px 0;
        width: 100%;
        
        th, td {
          border: 1px solid #dfe2e5;
          padding: 6px 12px;
          text-align: left;
        }
        
        th {
          background: #f6f8fa;
          font-weight: 600;
        }
      }
    }
  }

  .messageText {
    word-wrap: break-word;
    white-space: pre-wrap;
  }

  .streamingIndicator {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 12px 16px;
    background: rgba(102, 126, 234, 0.1);
    border-radius: 18px;
    margin-left: 40px;
    max-width: 200px;
  }

  .inputContainer {
    background: white;
    border-top: 1px solid #e8e8e8;
    padding: 16px;
  }

  .inputArea {
    display: flex;
    gap: 12px;
    align-items: flex-end;
  }

  .messageInput {
    flex: 1;
    border-radius: 20px;
    border: 1px solid #d9d9d9;
    transition: all 0.3s;

    &:focus {
      border-color: #667eea;
      box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.2);
    }

    .ant-input {
      border: none;
      box-shadow: none;
      padding: 8px 16px;
      
      &:focus {
        box-shadow: none;
      }
    }
  }

  .sendButton {
    border-radius: 20px;
    height: auto;
    padding: 8px 20px;
    background: #667eea;
    border-color: #667eea;
    display: flex;
    align-items: center;
    gap: 6px;
    min-height: 40px;

    &:hover {
      background: #5a6fd8;
      border-color: #5a6fd8;
    }

    &:disabled {
      background: #d9d9d9;
      border-color: #d9d9d9;
    }
  }

  .errorText {
    margin-top: 8px;
    font-size: 12px;
    display: block;
  }
}

// 响应式设计
@media (max-width: 768px) {
  .chatBot {
    .messageItem {
      &.userMessage .messageContent,
      &.assistantMessage .messageContent {
        max-width: 85%;
      }
    }

    .inputArea {
      flex-direction: column;
      gap: 8px;

      .sendButton {
        align-self: flex-end;
        min-width: 80px;
      }
    }
  }
}

// 暗色主题支持
@media (prefers-color-scheme: dark) {
  .chatBot {
    .chatContainer {
      background: #1f1f1f;
    }

    .messagesContainer {
      background: linear-gradient(to bottom, #2a2a2a, #1f1f1f);
    }

    .messageItem {
      &.assistantMessage .messageContent {
        background: #2a2a2a;
        border-color: #404040;
        color: #e8e8e8;
      }
    }

    .inputContainer {
      background: #2a2a2a;
      border-top-color: #404040;
    }

    .messageInput {
      background: #1f1f1f;
      border-color: #404040;
      
      .ant-input {
        background: transparent;
        color: #e8e8e8;
      }
    }

    .streamingIndicator {
      background: rgba(102, 126, 234, 0.2);
      color: #e8e8e8;
    }
  }
}
