import React, { useState } from 'react';
import {
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  Badge,
  Tooltip,
  FloatButton,
} from 'antd';
import {
  RobotOutlined,
  MessageOutlined,
  CloseOutlined,
} from '@ant-design/icons';
import ChatBot from './index';
import { useTranslate } from '@/hooks/use-i18n';
import styles from './ChatBotSidebar.less';

interface ChatBotSidebarProps {
  placement?: 'left' | 'right';
  width?: number;
  trigger?: 'button' | 'float';
  defaultOpen?: boolean;
  className?: string;
  style?: React.CSSProperties;
}

const ChatBotSidebar: React.FC<ChatBotSidebarProps> = ({
  placement = 'right',
  width = 400,
  trigger = 'button',
  defaultOpen = false,
  className,
  style,
}) => {
  const t = useTranslate();
  const [visible, setVisible] = useState(defaultOpen);
  const [hasNewMessage, setHasNewMessage] = useState(false);

  // 监听defaultOpen变化
  React.useEffect(() => {
    setVisible(defaultOpen);
  }, [defaultOpen]);

  const handleOpen = () => {
    setVisible(true);
    setHasNewMessage(false);
  };

  const handleClose = () => {
    setVisible(false);
  };

  const renderTrigger = () => {
    if (trigger === 'float') {
      return (
        <FloatButton
          icon={<RobotOutlined />}
          tooltip={t('chatbot.title')}
          onClick={handleOpen}
          className={styles.floatButton}
          badge={{ dot: hasNewMessage }}
        />
      );
    }

    return (
      <Tooltip title={t('chatbot.title')} placement={placement === 'left' ? 'right' : 'left'}>
        <Badge dot={hasNewMessage}>
          <Button
            type="text"
            icon={<MessageOutlined />}
            onClick={handleOpen}
            className={styles.triggerButton}
            size="large"
          />
        </Badge>
      </Tooltip>
    );
  };

  return (
    <>
      {renderTrigger()}
      
      <Drawer
        title={
          <div className={styles.drawerHeader}>
            <div className={styles.headerContent}>
              <RobotOutlined className={styles.headerIcon} />
              <span className={styles.headerTitle}>{t('chatbot.title')}</span>
            </div>
            <Button
              type="text"
              icon={<CloseOutlined />}
              onClick={handleClose}
              size="small"
              className={styles.closeButton}
            />
          </div>
        }
        placement={placement}
        width={width}
        open={visible}
        onClose={handleClose}
        className={`${styles.chatBotDrawer} ${className || ''}`}
        style={style}
        closable={false}
        bodyStyle={{ padding: 0 }}
        headerStyle={{ 
          padding: '16px 24px',
          borderBottom: '1px solid #f0f0f0',
        }}
        destroyOnClose={false}
        mask={false}
        push={false}
      >
        <div className={styles.drawerContent}>
          <ChatBot
            height={window.innerHeight - 120}
            enableStream={true}
            systemPrompt={t('chatbot.defaultSystemPrompt', '你是汉邦高科的AI助手，专门帮助用户使用汉邦高科系统。请用中文回答问题，保持友好和专业的语调。')}
          />
        </div>
      </Drawer>
    </>
  );
};

export default ChatBotSidebar;
