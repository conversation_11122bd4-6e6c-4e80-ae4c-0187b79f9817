import React, { useState, useRef, useEffect } from 'react';
import {
  Card,
  Input,
  Button,
  List,
  Avatar,
  Typography,
  Space,
  Spin,
  Empty,
  message,
  Tooltip,
  Divider,
} from 'antd';
import {
  SendOutlined,
  StopOutlined,
  UserOutlined,
  RobotOutlined,
  ClearOutlined,
  PlusOutlined,
  SettingOutlined,
  CopyOutlined,
  RedoOutlined,
} from '@ant-design/icons';
import { useChatBot, useChatBotHistory } from '@/hooks/use-chatbot-hooks';
import { useTranslate } from '@/hooks/use-i18n';
import MarkdownContent from '@/components/MarkdownContent';
import { copyToClipboard } from '@/utils/clipboard';
import styles from './index.less';

const { TextArea } = Input;
const { Text, Title } = Typography;

interface ChatBotProps {
  className?: string;
  style?: React.CSSProperties;
  height?: number;
  enableStream?: boolean;
  systemPrompt?: string;
  selectedSessionId?: string;
  onRefreshSessions?: () => void; // 新增刷新会话列表的回调
}

const ChatBot: React.FC<ChatBotProps> = ({
  className,
  style,
  height = 600,
  enableStream = true,
  systemPrompt,
  selectedSessionId,
  onRefreshSessions,
}) => {
  const t = useTranslate();
  const [inputValue, setInputValue] = useState('');
  const [isInitialized, setIsInitialized] = useState(false);
  const [isInitializing, setIsInitializing] = useState(false);
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const lastSelectedSessionRef = useRef<string>('');
  
  const {
    sessionId,
    messages,
    isLoading,
    isStreaming,
    error,
    createSession,
    sendMessage,
    sendMessageStream,
    stopStreaming,
    clearSession,
    resetState,
    setMessages,
  } = useChatBot();

  const { getSessionDetails } = useChatBotHistory();

  // 初始化会话
  // 移除自动创建会话的逻辑，只有在用户主动发送消息时才创建会话
  // useEffect(() => {
  //   if (!isInitialized && !selectedSessionId && !isInitializing) {
  //     setIsInitializing(true);
  //     const initSession = async () => {
  //       try {
  //         await createSession({ system_prompt: systemPrompt });
  //         setIsInitialized(true);
  //       } catch (error) {
  //         console.error('初始化聊天会话失败:', error);
  //       } finally {
  //         setIsInitializing(false);
  //       }
  //     };
  //     initSession();
  //   }
  // }, [isInitialized, selectedSessionId, isInitializing, systemPrompt]);

  // 处理选中的会话ID变化
  useEffect(() => {
    // 防止重复处理相同的sessionId
    if (lastSelectedSessionRef.current === selectedSessionId) {
      return;
    }
    lastSelectedSessionRef.current = selectedSessionId || '';

    if (selectedSessionId) {
      setIsInitializing(true);
      const loadSelectedSession = async () => {
        try {
          const response = await getSessionDetails(selectedSessionId);
          if (response.code === 0 && response.data.messages) {
            // 转换消息格式并确保正确排序
            const formattedMessages = response.data.messages
              .map(msg => ({
                role: msg.role,
                content: msg.content,
                timestamp: msg.timestamp,
                message_id: msg.message_id,
                sequence_number: msg.sequence_number || 0, // 添加sequence_number
              }))
              .sort((a, b) => {
                // 首先按sequence_number排序
                if (a.sequence_number !== b.sequence_number) {
                  return a.sequence_number - b.sequence_number;
                }
                // 如果sequence_number相同，按timestamp排序
                return a.timestamp - b.timestamp;
              });



            // 设置消息和会话ID到ChatBot组件
            setMessages(formattedMessages, selectedSessionId);
            setIsInitialized(true);
            setIsInitializing(false);
          } else {
            setIsInitializing(false);
          }
        } catch (error) {
          setIsInitializing(false);
        }
      };
      loadSelectedSession();
    } else if (!selectedSessionId && isInitialized) {
      // 如果没有选中会话且已初始化，重置状态
      resetState();
      setIsInitialized(false);
      setIsInitializing(false);
    }
  }, [selectedSessionId]);

  // 自动滚动到底部
  useEffect(() => {
    if (messagesEndRef.current) {
      messagesEndRef.current.scrollIntoView({ behavior: 'smooth' });
    }
  }, [messages]);

  // 发送消息
  const handleSendMessage = async () => {
    if (!inputValue.trim()) {
      message.warning(t('chatbot.error.messageEmpty'));
      return;
    }

    const messageText = inputValue.trim();
    setInputValue('');

    try {
      // 如果没有会话，先创建一个新会话
      if (!sessionId) {
        const newSessionId = await createSession({ system_prompt: systemPrompt });

        // 等待一小段时间让state更新
        await new Promise(resolve => setTimeout(resolve, 100));
      }

      // 发送消息
      if (enableStream) {
        await sendMessageStream(messageText);
      } else {
        await sendMessage(messageText);
      }
    } catch (error) {
      message.error(t('chatbot.error.sendFailed'));
    }
  };

  // 处理键盘事件
  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  };

  // 清空会话
  const handleClearSession = async () => {
    try {
      await clearSession();
      message.success(t('chatbot.success.sessionCleared'));
    } catch (error) {
      message.error(t('chatbot.error.sendFailed'));
    }
  };

  // 重新初始化会话
  const handleResetSession = async () => {
    try {
      resetState();
      setIsInitialized(false);
      await createSession({ system_prompt: systemPrompt });
      setIsInitialized(true);
      message.success(t('chatbot.success.sessionReset'));

      // 刷新对话记录列表
      if (onRefreshSessions) {
        onRefreshSessions();
      }
    } catch (error) {
      message.error(t('chatbot.error.sendFailed'));
    }
  };

  // 复制文本到剪贴板
  const handleCopy = async (text: string) => {
    const success = await copyToClipboard(text);
    if (success) {
      message.success(t('chat.copySuccess'));
    } else {
      message.error(t('chat.copyFailed'));
    }
  };

  // 重新发送消息
  const handleResend = async (content: string) => {
    if (isStreaming) {
      message.warning(t('chatbot.thinking'));
      return;
    }

    try {
      if (enableStream) {
        await sendMessageStream(content);
      } else {
        await sendMessage(content);
      }
    } catch (error) {
      message.error(t('chatbot.error.sendFailed'));
    }
  };

  // 渲染消息项
  const renderMessageItem = (msg: any, index: number) => {
    const isUser = msg.role === 'user';
    const isSystem = msg.role === 'system';

    if (isSystem) return null; // 不显示系统消息

    return (
      <div
        key={`${msg.message_id}-${index}`}
        className={`${styles.messageItem} ${isUser ? styles.userMessage : styles.assistantMessage}`}
      >
        <div className={styles.messageContent}>
          <div className={styles.messageHeader}>
            <Avatar
              size="small"
              icon={isUser ? <UserOutlined /> : <RobotOutlined />}
              className={isUser ? styles.userAvatar : styles.assistantAvatar}
            />
            <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
              <Text type="secondary" className={styles.messageTime}>
                {new Date(msg.timestamp).toLocaleTimeString()}
              </Text>
              {/* 操作按钮 */}
              <Space size="small">
                <Tooltip title={t('tooltips.copyToClipboard')}>
                  <Button
                    type="text"
                    size="small"
                    icon={<CopyOutlined />}
                    onClick={() => handleCopy(msg.content)}
                    style={{
                      color: isUser ? 'rgba(255, 255, 255, 0.8)' : '#666',
                      opacity: 0.7
                    }}
                  />
                </Tooltip>
                {isUser && (
                  <Tooltip title={t('common.retry')}>
                    <Button
                      type="text"
                      size="small"
                      icon={<RedoOutlined />}
                      onClick={() => handleResend(msg.content)}
                      loading={isStreaming}
                      style={{
                        color: isUser ? 'rgba(255, 255, 255, 0.8)' : '#666',
                        opacity: 0.7
                      }}
                    />
                  </Tooltip>
                )}
              </Space>
            </div>
          </div>
          <div className={styles.messageBody}>
            {isUser ? (
              <Text className={styles.messageText}>{msg.content}</Text>
            ) : (
              <MarkdownContent content={msg.content} />
            )}
          </div>
        </div>
      </div>
    );
  };

  return (
    <Card
      className={`${styles.chatBot} ${className || ''}`}
      style={{ height, ...style }}
      title={
        <div className={styles.chatHeader}>
          <Space>
            <RobotOutlined />
            <Title level={5} style={{ margin: 0 }}>
              {t('chatbot.title')}
            </Title>
          </Space>
          <Space>
            <Tooltip title={t('chatbot.clear')}>
              <Button
                type="text"
                size="small"
                icon={<ClearOutlined />}
                onClick={handleClearSession}
                disabled={isLoading || isStreaming || messages.length === 0}
              />
            </Tooltip>
            <Tooltip title={t('chatbot.reset')}>
              <Button
                type="text"
                size="small"
                icon={<PlusOutlined style={{ fontSize: '16px', fontWeight: 'bold' }} />}
                onClick={handleResetSession}
                disabled={isLoading || isStreaming}
                style={{
                  fontSize: '16px',
                  width: '32px',
                  height: '32px',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center'
                }}
              />
            </Tooltip>
          </Space>
        </div>
      }
      bodyStyle={{ padding: 0, height: height - 60 }}
    >
      <div className={styles.chatContainer}>
        {/* 消息列表区域 */}
        <div className={styles.messagesContainer}>
          {isInitializing || (isLoading && !sessionId) ? (
            <div className={styles.loadingContainer}>
              <Spin size="large" />
              <Text type="secondary">{t('chatbot.initializing')}</Text>
            </div>
          ) : messages.length === 0 ? (
            <div className={styles.emptyContainer}>
              <Empty
                image={Empty.PRESENTED_IMAGE_SIMPLE}
                description={t('chatbot.noMessages')}
              />
            </div>
          ) : (
            <div className={styles.messagesList}>
              {messages.map((msg, index) => renderMessageItem(msg, index))}
              {isStreaming && (
                <div className={styles.streamingIndicator}>
                  <Spin size="small" />
                  <Text type="secondary">{t('chatbot.thinking')}</Text>
                </div>
              )}
              <div ref={messagesEndRef} />
            </div>
          )}
        </div>

        {/* 输入区域 */}
        <div className={styles.inputContainer}>
          <Divider style={{ margin: '8px 0' }} />
          <div className={styles.inputArea}>
            <TextArea
              value={inputValue}
              onChange={(e) => setInputValue(e.target.value)}
              onKeyPress={handleKeyPress}
              placeholder={t('chatbot.inputPlaceholder')}
              autoSize={{ minRows: 1, maxRows: 4 }}
              disabled={isLoading || isStreaming || !isInitialized}
              className={styles.messageInput}
            />
            <Button
              type="primary"
              icon={isStreaming ? <StopOutlined /> : <SendOutlined />}
              onClick={isStreaming ? stopStreaming : handleSendMessage}
              disabled={(!inputValue.trim() && !isStreaming) || isLoading || !isInitialized}
              className={styles.sendButton}
              danger={isStreaming}
            >
              {isStreaming ? t('chatbot.stop') : t('chatbot.send')}
            </Button>
          </div>
          {error && (
            <Text type="danger" className={styles.errorText}>
              {error}
            </Text>
          )}
        </div>
      </div>
    </Card>
  );
};

export default ChatBot;
