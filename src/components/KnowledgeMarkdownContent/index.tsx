import React, { useMemo, useEffect, useState, useCallback } from 'react';
import { Popover, Button } from 'antd';
import { InfoCircleOutlined, FileTextOutlined } from '@ant-design/icons';
import ReactMarkdown from 'react-markdown';
import remarkGfm from 'remark-gfm';
import rehypeRaw from 'rehype-raw';
import documentService from '@/services/document-service';
import { useTranslate } from '@/hooks/use-i18n';
import './index.less';

interface IReference {
  id: string;
  image_id?: string;
  content: string;
  document_id: string;
  document_name: string;
  similarity: number;
  thumbnail?: string;
  document_info?: any;
}

interface KnowledgeMarkdownContentProps {
  content: string;
  reference?: {
    chunks: IReference[];
    total: number;
    doc_aggs?: Array<{
      doc_id: string;
      doc_name: string;
      count: number;
    }>;
  } | null;
  messageRole?: 'user' | 'assistant' | 'system'; // 添加消息角色信息
  isWelcomeMessage?: boolean; // 添加是否为欢迎消息的标识
  isStreaming?: boolean; // 添加是否为流式传输的标识
}

const KnowledgeMarkdownContent: React.FC<KnowledgeMarkdownContentProps> = ({
  content,
  reference,
  messageRole,
  isWelcomeMessage = false,
  isStreaming = false,
}) => {
  const t = useTranslate();

  // 获取文档缩略图
  const [fileThumbnails, setFileThumbnails] = useState<Record<string, string>>({});

  // 使用 useMemo 来稳定 documentIds，避免不必要的 API 调用
  const documentIds = useMemo(() => {
    const docAggs = reference?.doc_aggs;
    if (docAggs && docAggs.length > 0) {
      return docAggs.map(x => x.doc_id).filter(Boolean);
    }
    return [];
  }, [reference?.doc_aggs]);

  useEffect(() => {
    if (documentIds.length > 0) {
      documentService.getDocumentThumbnails(documentIds)
        .then(response => {
          if (response?.data) {
            setFileThumbnails(response.data);
          }
        })
        .catch(() => {
          // 静默处理缩略图加载失败
        });
    }
  }, [documentIds]);

  // 知识库对话专用的引用正则表达式，支持 ##数字$$ 和 [ID:数字] 格式
  const referenceReg = /(\[ID:(\d+)\]|##(\d+)\$\$)/g;

  // 从匹配字符串中提取chunk索引 - 使用 useCallback 避免重新创建
  // 注意：[ID:1] 对应 chunks[0]，所以需要减1来获取正确的数组索引
  const getChunkIndex = useCallback((match: string) => {
    let extractedNumber = 0;

    // 处理新格式 [ID:1]
    if (match.startsWith('[ID:') && match.endsWith(']')) {
      extractedNumber = Number(match.slice(4, -1)); // 从 "[ID:1]" 中提取 "1"
    }
    // 处理知识库对话格式 ##1$$
    else if (match.startsWith('##') && match.endsWith('$$')) {
      extractedNumber = Number(match.slice(2, -2)); // 从 "##1$$" 中提取 "1"
    }
    // 默认尝试提取数字
    else {
      const numMatch = match.match(/\d+/);
      extractedNumber = numMatch ? Number(numMatch[0]) : 1; // 默认为1而不是0
    }

    // [ID:1] 对应 chunks[0]，所以需要减1
    // 但要确保索引不会小于0
    return Math.max(0, extractedNumber - 1);
  }, []);



  // 获取引用信息 - 使用 useCallback 避免重新创建
  const getReferenceInfo = useCallback((chunkIndex: number) => {
    const chunks = reference?.chunks ?? [];
    const chunkItem = chunks[chunkIndex];

    if (!chunkItem) {
      return {
        documentUrl: undefined,
        fileThumbnail: '',
        fileExtension: '',
        imageId: undefined,
        chunkItem: null,
        documentId: undefined,
        document: null,
      };
    }

    const document = reference?.doc_aggs?.find(
      (x) => x?.doc_id === chunkItem?.document_id,
    );

    const documentId = chunkItem?.document_id;
    const imageId = chunkItem?.image_id;
    const fileThumbnail = documentId ? fileThumbnails[documentId] : '';

    return {
      documentUrl: undefined,
      fileThumbnail,
      fileExtension: '',
      imageId,
      chunkItem,
      documentId,
      document,
    };
  }, [reference?.chunks, reference?.doc_aggs, fileThumbnails]);

  // 生成Popover内容 - 使用 useCallback 避免重新创建
  const getPopoverContent = useCallback((chunkIndex: number) => {
    const {
      fileThumbnail,
      imageId,
      chunkItem,
      documentId,
      document,
    } = getReferenceInfo(chunkIndex);

    if (!chunkItem) {
      return (
        <div style={{ maxWidth: 300, padding: '8px' }}>
          <div style={{ color: '#ff4d4f', fontSize: '14px' }}>
            {t('knowledge.referenceNotAvailable', '引用内容不可用')}
          </div>
          <div style={{ fontSize: '12px', color: '#666', marginTop: '4px' }}>
            {t('knowledge.referenceDataNotFound', '{{index}} 对应的引用数据未找到', { index: chunkIndex })}
          </div>
        </div>
      );
    }



    const rawDimageId = imageId;
    const newimageId = rawDimageId ? rawDimageId.split('-')[0] : rawDimageId;

    return (
      <div style={{ maxWidth: '50vw', padding: '8px' }}>
        <div style={{ display: 'flex', gap: '12px' }}>
          {/* 图片预览 */}
          {newimageId && (
            <div style={{ flexShrink: 0 }}>
              <img
                src={`/v1/document/get/${documentId}?chunk_id=${chunkItem?.id}&image_id=${newimageId}`}
                alt=""
                style={{ width: '120px', height: '120px', objectFit: 'cover', borderRadius: '4px' }}
                onError={(e) => {
                  (e.target as HTMLImageElement).style.display = 'none';
                }}
              />
            </div>
          )}

          <div style={{ flex: 1, maxWidth: '40vw' }}>
            {/* 引用内容 */}
            <div
              dangerouslySetInnerHTML={{
                __html: chunkItem?.content ?? '',
              }}
              style={{
                fontSize: '13px',
                lineHeight: '1.5',
                marginBottom: '8px',
                padding: '8px',
                background: '#f9f9f9',
                borderRadius: '4px'
              }}
            />

            {/* 文档信息 */}
            {documentId && (
              <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
                {fileThumbnail ? (
                  <img
                    src={fileThumbnail}
                    alt=""
                    style={{ width: '24px', height: '24px' }}
                  />
                ) : (
                  <FileTextOutlined style={{ color: '#1890ff', fontSize: '16px' }} />
                )}
                <Button
                  type="link"
                  style={{ padding: 0, height: 'auto', fontSize: '12px' }}
                >
                  {document?.doc_name}
                </Button>
              </div>
            )}
          </div>
        </div>
      </div>
    );
  }, [getReferenceInfo, t]);



  // 检查内容是否包含引用标记，并且有有效的引用数据
  const hasReferences = useMemo(() => {
    if (typeof content !== 'string') return false;

    // 如果是欢迎消息，不显示引用
    if (isWelcomeMessage) return false;

    // 如果是系统消息，不显示引用
    if (messageRole === 'system') return false;

    // 如果是用户消息，不显示引用
    if (messageRole === 'user') return false;

    // 特殊处理：空内容的assistant消息不显示引用
    if (messageRole === 'assistant' && !content.trim()) {
      return false;
    }

    // 检查是否有引用标记
    const hasReferenceMarkers = referenceReg.test(content);
    if (!hasReferenceMarkers) return false;

    // 检查是否有引用数据
    const hasReferenceData = reference &&
                            reference.chunks &&
                            reference.chunks.length > 0;

    // 根据原版ragflow的逻辑：检查引用数据中是否有document_id
    // 只有包含document_id的引用才应该显示
    const hasDocumentIds = hasReferenceData &&
                           reference.chunks.some(chunk => chunk.document_id);

    return hasReferenceMarkers && hasReferenceData && hasDocumentIds;
  }, [content, reference, messageRole, isWelcomeMessage]);

  // 检查内容是否包含think标签，并且只在流式传输时显示
  const hasThinkTags = useMemo(() => {
    const result = typeof content === 'string' &&
                   /<think>[\s\S]*?<\/think>/i.test(content) &&
                   isStreaming; // 只在流式传输时显示think标签

    return result;
  }, [content, isStreaming]);

  // 处理think标签的渲染 - 只在流式传输时显示思考过程
  const renderWithThinkTags = useMemo(() => {
    return (text: string) => {
      if (!hasThinkTags) return text;

      // 将think标签内容用特殊样式显示，只在流式传输过程中显示思考过程
      return text.replace(
        /<think>([\s\S]*?)<\/think>/gi,
        '<div style="background: #f0f8ff; border-left: 4px solid #1890ff; padding: 8px 12px; margin: 8px 0; border-radius: 4px; font-style: italic; color: #666;"><strong>💭 思考过程:</strong><br/>$1</div>'
      );
    };
  }, [hasThinkTags]);

  // 创建一个智能渲染函数，能够处理markdown和引用标记
  // 参考原版RAGFlow的实现，使用更高效的方式处理引用
  const renderContentWithMarkdownAndReferences = useMemo(() => {
    return (text: string) => {
      if (!hasReferences) {
        // 如果没有引用标记，直接用ReactMarkdown渲染
        return (
          <ReactMarkdown
            remarkPlugins={[remarkGfm]}
            rehypePlugins={[rehypeRaw]}
          >
            {text}
          </ReactMarkdown>
        );
      }

      // 参考原版RAGFlow的方式：使用自定义组件处理引用
      // 这样可以保持Markdown的完整语义结构
      return (
        <ReactMarkdown
          remarkPlugins={[remarkGfm]}
          rehypePlugins={[rehypeRaw]}
          components={{
            // 自定义文本处理组件，用于处理引用标记
            p: ({ children }) => <p>{renderTextWithReferences(children)}</p>,
            span: ({ children }) => <span>{renderTextWithReferences(children)}</span>,
            div: ({ children }) => <div>{renderTextWithReferences(children)}</div>,
            li: ({ children }) => <li>{renderTextWithReferences(children)}</li>,
            td: ({ children }) => <td>{renderTextWithReferences(children)}</td>,
            th: ({ children }) => <th>{renderTextWithReferences(children)}</th>,
            h1: ({ children }) => <h1>{renderTextWithReferences(children)}</h1>,
            h2: ({ children }) => <h2>{renderTextWithReferences(children)}</h2>,
            h3: ({ children }) => <h3>{renderTextWithReferences(children)}</h3>,
            h4: ({ children }) => <h4>{renderTextWithReferences(children)}</h4>,
            h5: ({ children }) => <h5>{renderTextWithReferences(children)}</h5>,
            h6: ({ children }) => <h6>{renderTextWithReferences(children)}</h6>,
            blockquote: ({ children }) => <blockquote>{renderTextWithReferences(children)}</blockquote>,
          }}
        >
          {text}
        </ReactMarkdown>
      );
    };
  }, [hasReferences, renderTextWithReferences]);

  // 处理文本中的引用标记，将其替换为引用图标
  const renderTextWithReferences = useCallback((children: React.ReactNode): React.ReactNode => {
    if (typeof children === 'string') {
      // 检查是否包含引用标记
      if (!referenceReg.test(children)) {
        return children;
      }

      const parts: React.ReactNode[] = [];
      let lastIndex = 0;
      let match;

      // 重置正则表达式的lastIndex
      referenceReg.lastIndex = 0;

      while ((match = referenceReg.exec(children)) !== null) {
        const matchStart = match.index;
        const matchEnd = matchStart + match[0].length;
        const chunkIndex = getChunkIndex(match[0]);
        const { chunkItem } = getReferenceInfo(chunkIndex);

        // 添加匹配前的文本
        if (matchStart > lastIndex) {
          const beforeText = children.slice(lastIndex, matchStart);
          if (beforeText) {
            parts.push(beforeText);
          }
        }

        // 添加引用图标
        parts.push(
          <Popover
            key={`ref-${matchStart}`}
            content={getPopoverContent(chunkIndex)}
            trigger="hover"
            placement="top"
          >
            <InfoCircleOutlined
              style={{
                color: chunkItem ? '#1890ff' : '#999',
                cursor: 'pointer',
                margin: '0 2px',
                fontSize: '14px',
                display: 'inline',
                verticalAlign: 'baseline'
              }}
            />
          </Popover>
        );

        lastIndex = matchEnd;
      }

      // 添加最后剩余的文本
      if (lastIndex < children.length) {
        const afterText = children.slice(lastIndex);
        if (afterText) {
          parts.push(afterText);
        }
      }

      return parts.length > 1 ? <>{parts}</> : children;
    }

    // 如果children是数组，递归处理每个元素
    if (Array.isArray(children)) {
      return children.map((child, index) =>
        React.isValidElement(child)
          ? React.cloneElement(child, { key: index })
          : renderTextWithReferences(child)
      );
    }

    // 如果是React元素，递归处理其children
    if (React.isValidElement(children)) {
      return React.cloneElement(children, {},
        renderTextWithReferences(children.props.children)
      );
    }

    return children;
  }, [getChunkIndex, getReferenceInfo, getPopoverContent]);

  return (
    <div className="knowledge-markdown-content">
      {hasThinkTags ? (
        // 如果有think标签，先处理think标签再渲染
        renderContentWithMarkdownAndReferences(renderWithThinkTags(content))
      ) : (
        // 直接渲染内容
        renderContentWithMarkdownAndReferences(content)
      )}
    </div>
  );
};

export default KnowledgeMarkdownContent;
