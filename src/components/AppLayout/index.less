.appLayout {
  min-height: 100vh;
  background: #f0f2f5;
}

.content {
  background: #f0f2f5;
  padding: 0;
  overflow: auto;
  
  .ant-layout-content {
    min-height: calc(100vh - 64px);
  }
}

@media (max-width: 768px) {
  .appLayout {
    .ant-layout-sider {
      position: fixed;
      left: 0;
      top: 0;
      bottom: 0;
      z-index: 1000;
      transform: translateX(-100%);
      transition: transform 0.3s ease;
      
      &:not(.ant-layout-sider-collapsed) {
        transform: translateX(0);
      }
    }
    
    .ant-layout-sider-collapsed {
      transform: translateX(-100%);
    }
  }
  
  .content {
    margin-left: 0;
  }
}
