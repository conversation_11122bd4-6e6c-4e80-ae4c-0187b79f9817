import { Layout } from 'antd';
import { useState, ReactNode } from 'react';
import AppSidebar from '@/components/AppSidebar';
import AppHeader from '@/components/AppHeader';
import AuthGuard from '@/components/AuthGuard';
import ChatBotSidebar from '@/components/ChatBot/ChatBotSidebar';
import styles from './index.less';

const { Content } = Layout;

interface AppLayoutProps {
  children: ReactNode;
  showSearch?: boolean;
  searchPlaceholder?: string;
  onSearch?: (value: string) => void;
}

const AppLayoutContent: React.FC<AppLayoutProps> = ({
  children,
  showSearch = true,
  searchPlaceholder = "Search...",
  onSearch,
}) => {
  const [collapsed, setCollapsed] = useState(false);

  const handleToggleCollapse = () => {
    setCollapsed(!collapsed);
  };

  return (
    <Layout className={styles.appLayout}>
      <AppSidebar 
        collapsed={collapsed} 
        onCollapse={setCollapsed}
      />
      
      <Layout>
        <AppHeader
          collapsed={collapsed}
          onToggleCollapse={handleToggleCollapse}
          showSearch={showSearch}
          searchPlaceholder={searchPlaceholder}
          onSearch={onSearch}
        />
        
        <Content className={styles.content}>
          {children}
        </Content>
      </Layout>

      {/* 聊天机器人浮动按钮 */}
      <ChatBotSidebar
        placement="right"
        width={400}
        trigger="float"
      />
    </Layout>
  );
};

const AppLayout: React.FC<AppLayoutProps> = (props) => {
  return (
    <AuthGuard requireAuth={true}>
      <AppLayoutContent {...props} />
    </AuthGuard>
  );
};

export default AppLayout;
