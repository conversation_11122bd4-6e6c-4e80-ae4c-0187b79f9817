import React, { useState, useEffect } from 'react';
import { Spin, Alert, Typography, Button } from 'antd';
import { DownloadOutlined } from '@ant-design/icons';

const { Text } = Typography;

interface WordViewerProps {
  file?: File | string; // 支持File对象或URL字符串
  className?: string;
}

const WordViewer: React.FC<WordViewerProps> = ({ file, className }) => {
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string>('');
  const [wordUrl, setWordUrl] = useState<string>('');

  useEffect(() => {
    if (!file) {
      setError('No Word file provided');
      setLoading(false);
      return;
    }

    // 检查文件类型
    let fileName = '';
    if (typeof file === 'string') {
      fileName = file.toLowerCase();
    } else {
      fileName = file.name.toLowerCase();
    }

    if (!fileName.endsWith('.docx') && !fileName.endsWith('.doc')) {
      setError('Unsupported Word file format');
      setLoading(false);
      return;
    }

    // 参考PDF预览过程，创建文件URL
    let url = '';
    if (typeof file === 'string') {
      url = file;
    } else {
      url = URL.createObjectURL(file);
    }

    setWordUrl(url);
    setLoading(false);

    return () => {
      if (typeof file !== 'string' && url) {
        URL.revokeObjectURL(url);
      }
    };
  }, [file]);

  // 处理下载
  const handleDownload = () => {
    if (!file || typeof file === 'string') return;

    const url = URL.createObjectURL(file);
    const link = document.createElement('a');
    link.href = url;
    link.download = file.name;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    URL.revokeObjectURL(url);
  };

  // 参考PDF预览的渲染方式
  const renderWordPreview = () => {
    if (error) {
      return (
        <Alert
          message="Word Document Error"
          description={error}
          type="error"
          showIcon
        />
      );
    }

    if (!wordUrl) {
      return (
        <Alert
          message="Word Document Not Available"
          description="Unable to load Word file for preview"
          type="warning"
          showIcon
        />
      );
    }

    // Word文件预览：显示文件信息和下载选项
    return (
      <div style={{
        width: '100%',
        minHeight: '400px',
        border: '1px solid #d9d9d9',
        borderRadius: '6px',
        padding: '24px',
        background: '#fafafa',
        display: 'flex',
        flexDirection: 'column',
        alignItems: 'center',
        justifyContent: 'center'
      }}>
        <div style={{ textAlign: 'center', marginBottom: '24px' }}>
          <div style={{ fontSize: '48px', color: '#1890ff', marginBottom: '16px' }}>
            📄
          </div>
          <h3 style={{ margin: '0 0 8px 0', color: '#262626' }}>
            {typeof file !== 'string' ? file.name : fileName.split('/').pop() || 'Word Document'}
          </h3>
          <Text type="secondary">
            Word Document Preview
          </Text>
        </div>

        <div style={{ marginBottom: '24px', textAlign: 'center' }}>
          <Text type="secondary" style={{ display: 'block', marginBottom: '4px' }}>
            File size: {typeof file !== 'string' ? (file.size / 1024).toFixed(2) : 'Unknown'} KB
          </Text>
          <Text type="secondary" style={{ display: 'block', marginBottom: '4px' }}>
            Type: {typeof file !== 'string' ? (file.type || 'application/vnd.openxmlformats-officedocument.wordprocessingml.document') : 'Word Document'}
          </Text>
          {typeof file !== 'string' && file.lastModified && (
            <Text type="secondary" style={{ display: 'block' }}>
              Modified: {new Date(file.lastModified).toLocaleString()}
            </Text>
          )}
        </div>

        <div style={{ textAlign: 'center' }}>
          <Text type="secondary" style={{ display: 'block', marginBottom: '16px' }}>
            Word documents cannot be previewed directly in the browser.
            <br />
            Please download the file to view its contents.
          </Text>

          {typeof file !== 'string' && (
            <Button
              type="primary"
              icon={<DownloadOutlined />}
              onClick={handleDownload}
            >
              Download Document
            </Button>
          )}
        </div>
      </div>
    );
  };

  if (loading) {
    return (
      <div style={{ textAlign: 'center', padding: '40px' }}>
        <Spin size="large" />
        <div style={{ marginTop: 16 }}>
          <Text type="secondary">Loading Word document...</Text>
        </div>
      </div>
    );
  }

  return (
    <div className={className}>
      {renderWordPreview()}
    </div>
  );
};

export default WordViewer;
