.documentPreview {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.loadingContainer {
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-align: center;
}

.emptyContainer {
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.documentHeader {
  .ant-card-body {
    padding: 12px 16px;
  }
}

.headerContent {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
}

.fileInfo {
  flex: 1;
  min-width: 0;
}

.fileIcon {
  font-size: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  background: #f5f5f5;
  border-radius: 8px;
  flex-shrink: 0;
}

.fileDetails {
  flex: 1;
  min-width: 0;
}

.fileName {
  margin: 0 0 4px 0 !important;
  font-size: 16px;
  line-height: 1.3;
  word-break: break-word;
}

.fileSize,
.uploadTime {
  font-size: 12px;
}

.headerActions {
  flex-shrink: 0;
  margin-left: 16px;
}

.previewCard {
  flex: 1;
  display: flex;
  flex-direction: column;
  
  .ant-card-body {
    flex: 1;
    display: flex;
    flex-direction: column;
    padding: 16px;
  }
}

.previewLoading {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 40px;
}

.previewContent {
  flex: 1;
  display: flex;
  flex-direction: column;
  min-height: 0;
  transition: transform 0.3s ease;
  transform-origin: top left;
}

.textPreview {
  flex: 1;
  display: flex;
  flex-direction: column;
  min-height: 0;
}

.textContent {
  flex: 1;
  background: #fafafa;
  border: 1px solid #f0f0f0;
  border-radius: 6px;
  padding: 16px;
  margin: 0;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 13px;
  line-height: 1.6;
  white-space: pre-wrap;
  word-break: break-word;
  overflow-y: auto;
  max-height: 450px;
  
  // 自定义滚动条
  &::-webkit-scrollbar {
    width: 6px;
  }
  
  &::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 3px;
  }
  
  &::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 3px;
    
    &:hover {
      background: #a8a8a8;
    }
  }
}

.extractedContent {
  flex: 1;
  display: flex;
  flex-direction: column;
  min-height: 0;
}

.contentBody {
  flex: 1;
  background: #fafafa;
  border: 1px solid #f0f0f0;
  border-radius: 6px;
  padding: 16px;
  overflow-y: auto;
  max-height: 450px;
  
  .ant-typography {
    margin-bottom: 0;
    line-height: 1.6;
    white-space: pre-wrap;
    word-break: break-word;
  }
  
  // 自定义滚动条
  &::-webkit-scrollbar {
    width: 6px;
  }
  
  &::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 3px;
  }
  
  &::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 3px;
    
    &:hover {
      background: #a8a8a8;
    }
  }
}

// PDF查看器样式
.pdfViewer {
  width: 100%;

  iframe {
    border: none;
    border-radius: 6px;
  }
}

// Word查看器样式
.wordViewer {
  width: 100%;

  .word-content {
    background: #fff;
    border: 1px solid #d9d9d9;
    border-radius: 6px;
    padding: 24px;
    font-family: 'Times New Roman', serif;
    line-height: 1.6;

    h1, h2, h3, h4, h5, h6 {
      color: #1890ff;
      margin: 16px 0 8px 0;
    }

    p {
      margin: 8px 0;
    }

    ul, ol {
      margin: 8px 0;
      padding-left: 24px;
    }
  }
}

// 全屏模式样式
.fullscreenMode {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: #fff;
  z-index: 1000;
  padding: 20px;

  .previewContent {
    height: calc(100vh - 120px);
    overflow: auto;
  }
}

// 响应式设计
@media (max-width: 768px) {
  .headerContent {
    flex-direction: column;
    gap: 12px;
  }
  
  .headerActions {
    margin-left: 0;
    align-self: flex-end;
  }
  
  .fileIcon {
    width: 32px;
    height: 32px;
    font-size: 20px;
  }
  
  .fileName {
    font-size: 14px;
  }
  
  .textContent,
  .contentBody {
    max-height: 250px;
    padding: 12px;
    font-size: 12px;
  }
}
