import React, { useState, useEffect, useCallback } from 'react';
import {
  Card,
  Typography,
  Spin,
  Alert,
  Empty,
  Divider,
  Tag,
  Space,
  Button,
  message,
} from 'antd';
import {
  FileTextOutlined,
  FilePdfOutlined,
  FileWordOutlined,
  FileOutlined,
  DownloadOutlined,
  EyeOutlined,
  FullscreenOutlined,
  ZoomInOutlined,
  ZoomOutOutlined,
} from '@ant-design/icons';
import { useTranslate } from '@/hooks/use-i18n';
import SimplePDFViewer from './SimplePDFViewer';
import WordViewer from './WordViewer';
import styles from './index.less';

const { Title, Text, Paragraph } = Typography;

interface DocumentPreviewProps {
  document: {
    id: string;
    name: string;
    content: string;
    type: string;
    size: number;
    uploadTime: string;
    url?: string; // 文件URL，用于PDF和Word预览
    file?: File; // 原始文件对象
  } | null;
  loading?: boolean;
}

const DocumentPreview: React.FC<DocumentPreviewProps> = ({
  document,
  loading = false,
}) => {
  const t = useTranslate();
  const [previewContent, setPreviewContent] = useState<string>('');
  const [previewLoading, setPreviewLoading] = useState(false);
  const [viewMode, setViewMode] = useState<'preview' | 'fullscreen'>('preview');
  const [zoom, setZoom] = useState(1.0);

  // 获取文件图标
  const getFileIcon = (fileName: string, fileType: string) => {
    const ext = fileName.toLowerCase().split('.').pop();
    const type = fileType.toLowerCase();

    if (type.includes('pdf') || ext === 'pdf') {
      return <FilePdfOutlined style={{ color: '#ff4d4f' }} />;
    }
    if (type.includes('word') || ['doc', 'docx'].includes(ext || '')) {
      return <FileWordOutlined style={{ color: '#1890ff' }} />;
    }
    if (type.includes('text') || ['txt', 'md'].includes(ext || '')) {
      return <FileTextOutlined style={{ color: '#52c41a' }} />;
    }
    return <FileOutlined style={{ color: '#8c8c8c' }} />;
  };

  // 格式化文件大小
  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  // 获取文件类型标签颜色
  const getFileTypeColor = (type: string) => {
    if (type.includes('pdf')) return 'red';
    if (type.includes('word')) return 'blue';
    if (type.includes('text')) return 'green';
    return 'default';
  };

  // 获取文件类型
  const getFileType = useCallback((fileName: string, mimeType: string, hasUrl?: boolean) => {
    const ext = fileName.toLowerCase().split('.').pop();

    // PDF文件
    if (mimeType.includes('pdf') || ext === 'pdf') return 'pdf';

    // Office文件（Word, PowerPoint）- 如果有URL则显示为PDF预览
    const officeExts = ['doc', 'docx', 'ppt', 'pptx'];
    const isOfficeFile = mimeType.includes('word') ||
                        mimeType.includes('powerpoint') ||
                        mimeType.includes('presentation') ||
                        officeExts.includes(ext || '');

    if (isOfficeFile && hasUrl) return 'pdf';
    if (isOfficeFile) return 'word'; // 如果没有URL，仍然显示为word类型

    // 文本文件
    if (mimeType.includes('text') || ['txt', 'md'].includes(ext || '')) return 'text';

    return 'other';
  }, []);

  // 处理文档预览
  useEffect(() => {
    if (document) {
      setPreviewLoading(true);
      const fileType = getFileType(document.name, document.type, !!document.url);

      if (fileType === 'text') {
        // 文本文件直接显示内容
        setTimeout(() => {
          setPreviewContent(document.content);
          setPreviewLoading(false);
        }, 300);
      } else {
        // PDF和Word文件不需要预处理内容
        setPreviewLoading(false);
      }
    }
  }, [document, getFileType]);

  // 处理下载
  const handleDownload = useCallback(() => {
    if (!document) return;

    try {
      if (document.url) {
        // 如果有URL，直接下载
        const link = document.createElement('a');
        link.href = document.url;
        link.download = document.name;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
      } else if (document.file) {
        // 如果有文件对象，创建URL下载
        const url = URL.createObjectURL(document.file);
        const link = document.createElement('a');
        link.href = url;
        link.download = document.name;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        URL.revokeObjectURL(url);
      } else {
        // 文本内容下载
        const blob = new Blob([document.content], { type: document.type || 'text/plain' });
        const url = URL.createObjectURL(blob);
        const link = document.createElement('a');
        link.href = url;
        link.download = document.name;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        URL.revokeObjectURL(url);
      }
      message.success(t('documents.downloadStarted'));
    } catch (error) {
      console.error('Download failed:', error);
      message.error(t('documents.downloadStarted'));
    }
  }, [document, t]);

  // 处理全屏切换
  const handleFullscreen = useCallback(() => {
    setViewMode(viewMode === 'preview' ? 'fullscreen' : 'preview');
  }, [viewMode]);

  // 处理缩放
  const handleZoomIn = useCallback(() => {
    setZoom(prev => Math.min(prev + 0.25, 3.0));
  }, []);

  const handleZoomOut = useCallback(() => {
    setZoom(prev => Math.max(prev - 0.25, 0.5));
  }, []);

  if (loading) {
    return (
      <div className={styles.loadingContainer}>
        <Spin size="large" />
        <Text type="secondary" style={{ marginTop: 16, display: 'block' }}>
          {t('documents.loadingDocument', 'Loading document...')}
        </Text>
      </div>
    );
  }

  if (!document) {
    return (
      <div className={styles.emptyContainer}>
        <Empty
          image={Empty.PRESENTED_IMAGE_SIMPLE}
          description={t('documents.noDocumentSelected', 'No document selected')}
        />
      </div>
    );
  }

  return (
    <div className={styles.documentPreview}>
      {/* 文档信息头部 */}
      <Card className={styles.documentHeader} size="small">
        <div className={styles.headerContent}>
          <div className={styles.fileInfo}>
            <Space align="start" size={12}>
              <div className={styles.fileIcon}>
                {getFileIcon(document.name, document.type)}
              </div>
              <div className={styles.fileDetails}>
                <Title level={4} className={styles.fileName}>
                  {document.name}
                </Title>
                <Space size={8} wrap>
                  <Tag color={getFileTypeColor(document.type)}>
                    {document.type || 'Unknown'}
                  </Tag>
                  <Text type="secondary" className={styles.fileSize}>
                    {formatFileSize(document.size)}
                  </Text>
                  <Text type="secondary" className={styles.uploadTime}>
                    {new Date(document.uploadTime).toLocaleString()}
                  </Text>
                </Space>
              </div>
            </Space>
          </div>
          
          <div className={styles.headerActions}>
            <Space>
              <Button
                type="text"
                icon={<ZoomOutOutlined />}
                size="small"
                onClick={handleZoomOut}
                disabled={zoom <= 0.5}
              >
                {Math.round(zoom * 100)}%
              </Button>
              <Button
                type="text"
                icon={<ZoomInOutlined />}
                size="small"
                onClick={handleZoomIn}
                disabled={zoom >= 3.0}
              />
              <Button
                type="text"
                icon={<FullscreenOutlined />}
                size="small"
                onClick={handleFullscreen}
              >
                {t('documents.fullView')}
              </Button>
              <Button
                type="text"
                icon={<DownloadOutlined />}
                size="small"
                onClick={handleDownload}
              >
                {t('documents.download')}
              </Button>
            </Space>
          </div>
        </div>
      </Card>

      <Divider style={{ margin: '12px 0' }} />

      {/* 文档内容预览 */}
      <Card 
        title={
          <Space>
            <FileTextOutlined />
            {t('documents.preview')}
          </Space>
        }
        className={styles.previewCard}
        size="small"
      >
        {previewLoading ? (
          <div className={styles.previewLoading}>
            <Spin />
            <Text type="secondary" style={{ marginLeft: 8 }}>
              {t('common.loading')}
            </Text>
          </div>
        ) : (
          <div className={styles.previewContent} style={{ transform: `scale(${zoom})`, transformOrigin: 'top left' }}>
            {(() => {
              const fileType = getFileType(document.name, document.type, !!document.url);

              switch (fileType) {
                case 'pdf':
                  return (
                    <SimplePDFViewer
                      file={document.file || document.url}
                      className={styles.pdfViewer}
                    />
                  );

                case 'word':
                  return (
                    <WordViewer
                      file={document.file}
                      className={styles.wordViewer}
                    />
                  );

                case 'text':
                  return (
                    <div className={styles.textPreview}>
                      <pre className={styles.textContent}>
                        {previewContent}
                      </pre>
                    </div>
                  );

                default:
                  return (
                    <div className={styles.extractedContent}>
                      <Alert
                        message={t('documents.extractedContent')}
                        description={t('documents.extractedContentDesc')}
                        type="info"
                        showIcon
                        style={{ marginBottom: 16 }}
                      />
                      <div className={styles.contentBody}>
                        <Paragraph>
                          {previewContent || document.content}
                        </Paragraph>
                      </div>
                    </div>
                  );
              }
            })()}
          </div>
        )}
      </Card>
    </div>
  );
};

export default DocumentPreview;
