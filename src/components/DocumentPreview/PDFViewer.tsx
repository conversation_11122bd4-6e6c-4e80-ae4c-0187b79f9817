import React, { useState, useEffect } from 'react';
import { Spin, Alert, Button, Space, Typography } from 'antd';
import { LeftOutlined, RightOutlined, ZoomInOutlined, ZoomOutOutlined } from '@ant-design/icons';
import { Document, Page, pdfjs } from 'react-pdf';
import request from '@/utils/request';
import 'react-pdf/dist/Page/AnnotationLayer.css';
import 'react-pdf/dist/Page/TextLayer.css';

const { Text } = Typography;

// 设置PDF.js worker - 使用CDN版本
pdfjs.GlobalWorkerOptions.workerSrc = `https://unpkg.com/pdfjs-dist@${pdfjs.version}/build/pdf.worker.min.js`;

interface PDFViewerProps {
  file?: File | string;
  className?: string;
}

const PDFViewer: React.FC<PDFViewerProps> = ({ file, className }) => {
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string>('');
  const [numPages, setNumPages] = useState<number>(0);
  const [pageNumber, setPageNumber] = useState<number>(1);
  const [scale, setScale] = useState<number>(1.0);
  const [pdfData, setPdfData] = useState<string | ArrayBuffer | null>(null);
  const [useFallback, setUseFallback] = useState<boolean>(false);

  useEffect(() => {
    if (!file) {
      setError('No PDF file provided');
      setLoading(false);
      return;
    }

    const loadPDF = async () => {
      try {
        setLoading(true);
        setError('');
        setUseFallback(false);
        console.log('Loading PDF, file type:', typeof file, 'file:', file);

        if (typeof file === 'string') {
          // 对于URL字符串，使用request获取PDF数据以携带认证头
          console.log('Fetching PDF from URL with authentication:', file);
          try {
            const response = await request(file, {
              method: 'GET',
              responseType: 'blob',
              getResponse: true,
            });

            if (response.response.ok) {
              const arrayBuffer = await response.data.arrayBuffer();
              console.log('PDF data loaded with authentication, size:', arrayBuffer.byteLength);
              setPdfData(arrayBuffer);
            } else {
              throw new Error(`Failed to fetch PDF: ${response.response.status} ${response.response.statusText}`);
            }
          } catch (fetchError) {
            console.log('Authenticated fetch failed, using fallback method:', fetchError);
            setUseFallback(true);
            setLoading(false);
          }
        } else if (file instanceof File) {
          // 如果是File对象，直接使用
          console.log('Using File object:', file.name, file.size);
          setPdfData(file);
        } else {
          throw new Error('Invalid file type');
        }
      } catch (err) {
        console.error('Error loading PDF:', err);
        setError(err instanceof Error ? err.message : 'Failed to load PDF');
        setLoading(false);
      }
    };

    loadPDF();

    return () => {
      // Cleanup will be handled when component unmounts
    };
  }, [file]);

  // PDF事件处理函数
  const onDocumentLoadSuccess = ({ numPages }: { numPages: number }) => {
    console.log('PDF document loaded successfully, pages:', numPages);
    setNumPages(numPages);
    setLoading(false);
    setError('');
  };

  const onDocumentLoadError = (error: Error) => {
    console.error('Error loading PDF document with react-pdf:', error);
    console.log('Falling back to iframe method');
    setUseFallback(true);
    setLoading(false);
  };

  const onPageLoadSuccess = () => {
    console.log('PDF page loaded successfully');
  };

  const onPageLoadError = (error: Error) => {
    console.error('Error loading PDF page:', error);
  };

  // 页面导航函数
  const goToPrevPage = () => {
    setPageNumber(prev => Math.max(1, prev - 1));
  };

  const goToNextPage = () => {
    setPageNumber(prev => Math.min(numPages, prev + 1));
  };

  // 缩放函数
  const zoomIn = () => {
    setScale(prev => Math.min(3.0, prev + 0.2));
  };

  const zoomOut = () => {
    setScale(prev => Math.max(0.5, prev - 0.2));
  };

  // 使用react-pdf渲染PDF - 避免浏览器下载行为
  const renderPDFPreview = () => {
    if (error) {
      return (
        <Alert
          message="PDF Preview Error"
          description={error}
          type="error"
          showIcon
        />
      );
    }

    if (!pdfData && !useFallback) {
      return (
        <Alert
          message="PDF Not Available"
          description="Unable to load PDF file for preview"
          type="warning"
          showIcon
        />
      );
    }

    // Fallback: 如果react-pdf失败，显示错误信息而不是使用object标签
    // 因为object标签也无法携带认证头，会遇到同样的401错误
    if (useFallback && typeof file === 'string') {
      console.log('PDF preview failed, showing error message');
      return (
        <div style={{
          width: '100%',
          height: '700px',
          border: '1px solid #d9d9d9',
          borderRadius: '6px',
          overflow: 'hidden',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center'
        }}>
          <div style={{
            padding: '20px',
            textAlign: 'center',
            color: '#666'
          }}>
            <Alert
              message="PDF预览暂时不可用"
              description="由于认证限制，无法直接预览PDF文件。请尝试刷新页面或联系管理员。"
              type="warning"
              showIcon
              style={{ marginBottom: 16 }}
            />
            <Button
              type="primary"
              onClick={() => window.location.reload()}
            >
              刷新页面
            </Button>
          </div>
        </div>
      );
    }

    return (
      <div style={{
        width: '100%',
        border: '1px solid #d9d9d9',
        borderRadius: '6px',
        backgroundColor: '#f5f5f5',
        overflow: 'auto',
        maxHeight: '700px'
      }}>
        {/* 控制栏 */}
        <div style={{
          padding: '8px 16px',
          borderBottom: '1px solid #d9d9d9',
          backgroundColor: '#fff',
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center'
        }}>
          <Space>
            <Button
              icon={<LeftOutlined />}
              onClick={goToPrevPage}
              disabled={pageNumber <= 1}
              size="small"
            />
            <span style={{ margin: '0 8px' }}>
              {pageNumber} / {numPages}
            </span>
            <Button
              icon={<RightOutlined />}
              onClick={goToNextPage}
              disabled={pageNumber >= numPages}
              size="small"
            />
          </Space>
          <Space>
            <Button
              icon={<ZoomOutOutlined />}
              onClick={zoomOut}
              disabled={scale <= 0.5}
              size="small"
            />
            <span style={{ margin: '0 8px' }}>
              {Math.round(scale * 100)}%
            </span>
            <Button
              icon={<ZoomInOutlined />}
              onClick={zoomIn}
              disabled={scale >= 3.0}
              size="small"
            />
          </Space>
        </div>

        {/* PDF内容 */}
        <div style={{
          display: 'flex',
          justifyContent: 'center',
          padding: '16px',
          minHeight: '400px'
        }}>
          <Document
            file={pdfData}
            onLoadSuccess={onDocumentLoadSuccess}
            onLoadError={onDocumentLoadError}
            loading={
              <div style={{ textAlign: 'center', padding: '40px' }}>
                <Spin size="large" />
                <div style={{ marginTop: 16 }}>
                  <Text type="secondary">Loading PDF...</Text>
                </div>
              </div>
            }
          >
            <Page
              pageNumber={pageNumber}
              scale={scale}
              renderTextLayer={false}
              renderAnnotationLayer={false}
              onLoadSuccess={onPageLoadSuccess}
              onLoadError={onPageLoadError}
            />
          </Document>
        </div>
      </div>
    );
  };

  if (loading) {
    return (
      <div style={{ textAlign: 'center', padding: '40px' }}>
        <Spin size="large" />
        <div style={{ marginTop: 16 }}>
          <Text type="secondary">Loading PDF...</Text>
        </div>
      </div>
    );
  }

  return (
    <div className={className}>
      {renderPDFPreview()}
    </div>
  );
};

export default PDFViewer;
