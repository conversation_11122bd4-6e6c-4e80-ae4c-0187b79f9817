import React, { useState, useRef, useCallback } from 'react';
import {
  Input,
  Button,
  Space,
  Upload,
  Tooltip,
  message,
  Flex
} from 'antd';
import {
  SendOutlined,
  PaperClipOutlined,
  StopOutlined,
  AudioOutlined
} from '@ant-design/icons';
import { useTranslate } from '@/hooks/use-i18n';

const { TextArea } = Input;

interface MessageInputProps {
  onSend: (content: string, files?: any[]) => void;
  onStop?: () => void;
  loading?: boolean;
  disabled?: boolean;
  placeholder?: string;
  maxLength?: number;
  showFileUpload?: boolean;
  showVoiceInput?: boolean;
}

const MessageInput: React.FC<MessageInputProps> = ({
  onSend,
  onStop,
  loading = false,
  disabled = false,
  placeholder,
  maxLength = 2000,
  showFileUpload = true,
  showVoiceInput = false,
}) => {
  const t = useTranslate();
  const [value, setValue] = useState('');
  const [files, setFiles] = useState<any[]>([]);
  const textAreaRef = useRef<any>(null);

  const defaultPlaceholder = placeholder || t('placeholders.typeMessage');

  // 处理发送
  const handleSend = useCallback(() => {
    if (!value.trim() && files.length === 0) {
      message.warning('Please enter a message or upload a file');
      return;
    }

    if (value.length > maxLength) {
      message.error(`Message too long. Maximum ${maxLength} characters allowed.`);
      return;
    }

    onSend(value.trim(), files);
    setValue('');
    setFiles([]);
    
    // 重新聚焦到输入框
    setTimeout(() => {
      textAreaRef.current?.focus();
    }, 100);
  }, [value, files, onSend, maxLength]);

  // 处理键盘事件
  const handleKeyDown = useCallback((e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      if (e.shiftKey) {
        // Shift + Enter 换行
        return;
      } else {
        // Enter 发送
        e.preventDefault();
        if (!loading && !disabled) {
          handleSend();
        }
      }
    }
  }, [handleSend, loading, disabled]);

  // 处理文件上传
  const handleFileChange = useCallback((info: any) => {
    const { fileList } = info;
    setFiles(fileList);
  }, []);

  // 处理停止
  const handleStop = useCallback(() => {
    onStop?.();
  }, [onStop]);

  // 文件上传配置
  const uploadProps = {
    multiple: true,
    fileList: files,
    onChange: handleFileChange,
    beforeUpload: () => false, // 阻止自动上传
    showUploadList: {
      showPreviewIcon: false,
      showRemoveIcon: true,
    },
  };

  return (
    <div style={{ 
      padding: '16px', 
      borderTop: '1px solid #f0f0f0',
      background: '#fff'
    }}>
      {/* 文件列表 */}
      {files.length > 0 && (
        <div style={{ marginBottom: '12px' }}>
          <Upload {...uploadProps}>
            <div style={{ display: 'none' }} />
          </Upload>
        </div>
      )}

      {/* 输入区域 */}
      <Flex gap={8} align="flex-end">
        {/* 文本输入 */}
        <div style={{ flex: 1 }}>
          <TextArea
            ref={textAreaRef}
            value={value}
            onChange={(e) => setValue(e.target.value)}
            onKeyDown={handleKeyDown}
            placeholder={defaultPlaceholder}
            disabled={disabled}
            autoSize={{ minRows: 1, maxRows: 6 }}
            maxLength={maxLength}
            showCount
            style={{ resize: 'none' }}
          />
        </div>

        {/* 操作按钮 */}
        <Space>
          {/* 文件上传按钮 */}
          {showFileUpload && (
            <Upload {...uploadProps} showUploadList={false}>
              <Tooltip title={t('tooltips.uploadFile')}>
                <Button
                  icon={<PaperClipOutlined />}
                  disabled={disabled}
                  type="text"
                />
              </Tooltip>
            </Upload>
          )}

          {/* 语音输入按钮 */}
          {showVoiceInput && (
            <Tooltip title={t('tooltips.voiceInput')}>
              <Button
                icon={<AudioOutlined />}
                disabled={disabled}
                type="text"
              />
            </Tooltip>
          )}

          {/* 发送/停止按钮 */}
          {loading ? (
            <Tooltip title={t('tooltips.stopGenerating')}>
              <Button
                icon={<StopOutlined />}
                onClick={handleStop}
                type="primary"
                danger
              />
            </Tooltip>
          ) : (
            <Tooltip title={t('tooltips.sendMessage')}>
              <Button
                icon={<SendOutlined />}
                onClick={handleSend}
                disabled={disabled || (!value.trim() && files.length === 0)}
                type="primary"
              />
            </Tooltip>
          )}
        </Space>
      </Flex>

      {/* 提示文本 */}
      <div style={{ 
        marginTop: '8px', 
        fontSize: '12px', 
        color: '#999',
        textAlign: 'center'
      }}>
        Press Enter to send, Shift + Enter for new line
      </div>
    </div>
  );
};

export default MessageInput;
