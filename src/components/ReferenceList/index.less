.reference-list {
  margin-top: 12px;

  .reference-documents {
    max-height: 300px;
    overflow-y: auto;
  }

  .reference-document-card {
    transition: all 0.2s ease;

    &:hover {
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    }

    .document-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 0;
    }

    .document-chunks {
      .chunk-item {
        transition: all 0.2s ease;

        &:hover {
          box-shadow: 0 1px 4px rgba(0, 0, 0, 0.1);
        }
      }
    }
  }

  // 滚动条样式
  .reference-documents::-webkit-scrollbar {
    width: 6px;
  }

  .reference-documents::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 3px;
  }

  .reference-documents::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 3px;

    &:hover {
      background: #a8a8a8;
    }
  }

  // 响应式设计
  @media (max-width: 768px) {
    .reference-document-card {
      .document-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 8px;
      }
    }

    .chunk-item {
      .ant-typography {
        font-size: 11px !important;
      }
    }
  }
}
