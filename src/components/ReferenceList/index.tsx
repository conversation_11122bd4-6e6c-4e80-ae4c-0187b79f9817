import React from 'react';
import { <PERSON>, Typography, Tag, Divider, Button, Space } from 'antd';
import { FileTextOutlined, LinkOutlined } from '@ant-design/icons';
import './index.less';

const { Text, Paragraph } = Typography;

interface IReference {
  id: string;
  content: string;
  document_id: string;
  document_name: string;
  dataset_id: string;
  similarity: number;
  url?: string;
}

interface IDocumentAgg {
  doc_id: string;
  doc_name: string;
  count: number;
}

interface ReferenceListProps {
  references: {
    chunks: IReference[];
    total: number;
    doc_aggs?: IDocumentAgg[];
  };
  onDocumentClick?: (documentId: string, documentName: string) => void;
  messageId?: string; // 添加messageId，确保每个message有独立的引用列表
}

const ReferenceList: React.FC<ReferenceListProps> = ({
  references,
  onDocumentClick,
  messageId
}) => {
  if (!references?.chunks || references.chunks.length === 0) {
    return null;
  }

  // 优先使用doc_aggs数据，如果没有则从chunks中提取
  let groupedReferences: Record<string, { document_id: string; document_name: string; chunks: IReference[]; count?: number }> = {};

  if (references.doc_aggs && references.doc_aggs.length > 0) {
    // 使用doc_aggs数据构建文档分组
    references.doc_aggs.forEach(docAgg => {
      groupedReferences[docAgg.doc_id] = {
        document_id: docAgg.doc_id,
        document_name: docAgg.doc_name,
        count: docAgg.count,
        chunks: []
      };
    });

    // 将chunks分配到对应的文档组
    references.chunks.forEach(chunk => {
      if (groupedReferences[chunk.document_id]) {
        groupedReferences[chunk.document_id].chunks.push(chunk);
      }
    });
  } else {
    // 回退到从chunks中分组
    groupedReferences = references.chunks.reduce((acc, ref) => {
      const key = ref.document_id;
      if (!acc[key]) {
        acc[key] = {
          document_id: ref.document_id,
          document_name: ref.document_name,
          chunks: []
        };
      }
      acc[key].chunks.push(ref);
      return acc;
    }, {} as Record<string, { document_id: string; document_name: string; chunks: IReference[] }>);
  }

  const handleDocumentClick = (documentId: string, documentName: string) => {
    onDocumentClick?.(documentId, documentName);
  };

  // 处理文件预览
  const handleDocumentPreview = (documentId: string, documentName: string) => {
    // 根据错误分析，需要使用正确的document_id
    // 如果当前的documentId实际上是错误的，需要从chunks中找到正确的document_id

    // 查找该文档对应的chunks，获取正确的document_id
    const documentChunks = references?.chunks?.filter(chunk =>
      chunk.document_id === documentId || chunk.doc_id === documentId
    ) || [];

    // 尝试不同的预览URL格式
    const possibleUrls = [
      `/v1/document/get/${documentId}`,
      `/v1/document/${documentId}`,
    ];

    // 使用第一个URL尝试预览
    const previewUrl = possibleUrls[0];

    // 在新窗口中打开文档预览
    window.open(previewUrl, '_blank');
  };

  return (
    <div className="reference-list">
      <Divider style={{ margin: '16px 0 12px 0' }}>
        <Text type="secondary" style={{ fontSize: '12px' }}>
          <FileTextOutlined style={{ marginRight: 4 }} />
          引用来源 ({references.total} 个片段，{Object.keys(groupedReferences).length} 个文档)
          {messageId && (
            <Text style={{ fontSize: '11px', color: '#999', marginLeft: '8px' }}>
              [消息: {messageId.slice(-8)}]
            </Text>
          )}
        </Text>
      </Divider>
      
      <div className="reference-documents">
        {Object.values(groupedReferences).map((group) => (
          <Card
            key={group.document_id}
            size="small"
            className="reference-document-card"
            style={{
              marginBottom: '8px',
              background: '#fafafa',
              border: '1px solid #e8e8e8'
            }}
          >
            <div className="document-header">
              <Space align="center">
                <FileTextOutlined style={{ color: '#1890ff' }} />

                {/* 文档缩略图（如果有） */}
                {group.chunks[0]?.thumbnail && (
                  <img
                    src={group.chunks[0].thumbnail}
                    alt="Document thumbnail"
                    style={{
                      width: '24px',
                      height: '24px',
                      objectFit: 'cover',
                      border: '1px solid #d9d9d9',
                      borderRadius: '2px'
                    }}
                  />
                )}

                <Text
                  strong
                  style={{
                    fontSize: '13px',
                    cursor: 'pointer',
                    color: '#1890ff'
                  }}
                  onClick={() => handleDocumentPreview(group.document_id, group.document_name)}
                  title="点击预览文档"
                >
                  {group.document_name}
                </Text>
              </Space>

                {/* 文档操作按钮 */}
                <Space size="small">
                  <Button
                    type="text"
                    size="small"
                    icon={<LinkOutlined />}
                    onClick={() => handleDocumentPreview(group.document_id, group.document_name)}
                    style={{ fontSize: '11px', padding: '2px 6px' }}
                    title="在新窗口中预览文档"
                  >
                    预览
                  </Button>
                </Space>
              </div>


          </Card>
        ))}
      </div>
    </div>
  );
};

export default ReferenceList;
