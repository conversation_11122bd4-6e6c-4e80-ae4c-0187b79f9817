.sidebar {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  box-shadow: 2px 0 8px rgba(0, 0, 0, 0.15);
  position: relative;
  z-index: 100;
  
  .ant-layout-sider-children {
    display: flex;
    flex-direction: column;
    height: 100vh;
  }
  
  .ant-layout-sider-trigger {
    background: rgba(255, 255, 255, 0.1);
    color: white;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    
    &:hover {
      background: rgba(255, 255, 255, 0.2);
    }
  }
}

.logo {
  padding: 24px 16px;
  text-align: center;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;

  .logoImage {
    width: 144px;
    height:40px;
    object-fit: contain;
    border-radius: 8px;
    background: rgba(255, 255, 255, 0.1);
    padding: 4px;
  }

  h4 {
    color: white !important;
    font-weight: 600;
    letter-spacing: 1px;
    margin: 0 !important;
  }
}

.menu {
  flex: 1;
  border-right: none;
  background: transparent;
  padding: 16px 0;
  
  .ant-menu-item {
    margin: 4px 12px;
    border-radius: 8px;
    height: 44px;
    line-height: 44px;
    display: flex;
    align-items: center;
    transition: all 0.3s ease;
    
    &:hover {
      background: rgba(255, 255, 255, 0.1);
      color: white;
    }
    
    &.ant-menu-item-selected {
      background: rgba(255, 255, 255, 0.2);
      color: white;
      font-weight: 500;
      
      &::after {
        display: none;
      }
    }
    
    .ant-menu-title-content {
      margin-left: 12px;
    }
    
    .anticon {
      font-size: 16px;
      min-width: 16px;
    }
  }
  
  &.ant-menu-inline-collapsed {
    .ant-menu-item {
      padding: 0 calc(50% - 8px);
      
      .ant-menu-title-content {
        display: none;
      }
    }
  }
}

@media (max-width: 768px) {
  .sidebar {
    position: fixed;
    left: 0;
    top: 0;
    bottom: 0;
    z-index: 1000;
    transform: translateX(-100%);
    transition: transform 0.3s ease;
    
    &.ant-layout-sider-collapsed {
      transform: translateX(0);
    }
  }
  
  .logo {
    padding: 16px;
  }
  
  .menu {
    padding: 12px 0;
    
    .ant-menu-item {
      margin: 2px 8px;
      height: 40px;
      line-height: 40px;
    }
  }
}
