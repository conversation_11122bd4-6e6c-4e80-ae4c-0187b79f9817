import {
  DatabaseOutlined,
  BookOutlined,
  FileTextOutlined,
  MessageOutlined,
  RobotOutlined,
  SettingOutlined,
  ReadOutlined,
  CustomerServiceOutlined,
  PictureOutlined,
} from '@ant-design/icons';
import { Layout, Menu, Typography, Button } from 'antd';
import { useNavigate, useLocation } from 'umi';
import { message } from 'antd';
import { useTranslate } from '@/hooks/use-i18n';
import styles from './index.less';

const { Sider } = Layout;
const { Title } = Typography;

interface AppSidebarProps {
  collapsed: boolean;
  onCollapse?: (collapsed: boolean) => void;
}

const AppSidebar: React.FC<AppSidebarProps> = ({ collapsed, onCollapse }) => {
  const navigate = useNavigate();
  const location = useLocation();
  const t = useTranslate();

  const menuItems = [
    {
      key: 'dashboard',
      icon: <DatabaseOutlined />,
      label: t('navigation.dashboard'),
      onClick: () => navigate('/dashboard'),
    },
    {
      key: 'chatbot',
      icon: <CustomerServiceOutlined />,
      label: t('navigation.chatbot'),
      onClick: () => navigate('/chatbot'),
    },
    {
      key: 'ai-read',
      icon: <ReadOutlined />,
      label: t('navigation.aiRead'),
      onClick: () => navigate('/ai-read'),
    },
    {
      key: 'text-to-image',
      icon: <PictureOutlined />,
      label: t('navigation.textToImage'),
      onClick: () => navigate('/text-to-image'),
    },
    {
      key: 'dialogs',
      icon: <MessageOutlined />,
      label: t('navigation.dialogs'),
      onClick: () => navigate('/dialogs'),
    },
    {
      key: 'conversations',
      icon: <MessageOutlined />,
      label: t('navigation.conversations'),
      onClick: () => navigate('/conversations'),
    },
    {
      key: 'knowledge',
      icon: <BookOutlined />,
      label: t('navigation.knowledgeBase'),
      onClick: () => navigate('/knowledge'),
    },
    // {
    //   key: 'documents',
    //   icon: <FileTextOutlined />,
    //   label: t('navigation.documents'),
    //   onClick: () => navigate('/documents'),
    // },
    
    {
      key: 'models',
      icon: <RobotOutlined />,
      label: t('navigation.aiModels'),
      onClick: () => navigate('/ai-models'),
    },
    {
      key: 'settings',
      icon: <SettingOutlined />,
      label: t('navigation.settings'),
      onClick: () => navigate('/settings'),
    },
    
  ];

  // 根据当前路径确定选中的菜单项
  const getSelectedKey = () => {
    const pathname = location.pathname;
    if (pathname.startsWith('/knowledge')) {
      return ['knowledge'];
    } else if (pathname.startsWith('/dashboard')) {
      return ['dashboard'];
    } else if (pathname.startsWith('/ai-read')) {
      return ['ai-read'];
    } else if (pathname.startsWith('/dialogs')) {
      return ['dialogs'];
    // } else if (pathname.startsWith('/documents')) {
    //   return ['documents'];
    } else if (pathname.startsWith('/conversations')) {
      return ['conversations'];
    } else if (pathname.startsWith('/ai-models')) {
      return ['models'];
    } else if (pathname.startsWith('/settings')) {
      return ['settings'];
    } else if (pathname.startsWith('/chatbot')) {
      return ['chatbot'];
    }
    return ['dashboard'];
  };

  return (
    <Sider
      trigger={null}
      collapsible
      collapsed={collapsed}
      className={styles.sidebar}
      width={240}
      onCollapse={onCollapse}
    >
      <div className={styles.logo}>
        {!collapsed && (
          <img
            src="/HB-logo.png"
            alt="汉邦高科"
            className={styles.logoImage}
          />
        )}
        
      </div>
      <Menu
        theme="dark"
        mode="inline"
        selectedKeys={getSelectedKey()}
        items={menuItems}
        className={styles.menu}
      />
    </Sider>
  );
};

export default AppSidebar;
