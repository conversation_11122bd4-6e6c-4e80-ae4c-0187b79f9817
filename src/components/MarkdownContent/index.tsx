import React, { useMemo, useEffect, useState, useCallback } from 'react';
import { Popover, Button, Tag } from 'antd';
import { InfoCircleOutlined, FileTextOutlined } from '@ant-design/icons';
import ReactMarkdown from 'react-markdown';
import remarkGfm from 'remark-gfm';
import rehypeRaw from 'rehype-raw';
// 移除 react-string-replace 依赖，使用原生方法
import documentService from '@/services/document-service';
import './index.less';

interface IReference {
  id: string;
  image_id?: string; // 按照原版RAGFlow的字段
  content: string;
  document_id: string;
  document_name: string;
  similarity: number;
  thumbnail?: string;
  document_info?: any;
}

interface MarkdownContentProps {
  content: string;
  reference?: {
    chunks: IReference[];
    total: number;
    doc_aggs?: Array<{
      doc_id: string;
      doc_name: string;
      count: number;
    }>;
  } | null;
}

const MarkdownContent: React.FC<MarkdownContentProps> = ({ content, reference }) => {
  // 按照原版RAGFlow的方式，在组件中调用document/thumbnails API
  const [fileThumbnails, setFileThumbnails] = useState<Record<string, string>>({});

  // 使用 useMemo 来稳定 documentIds，避免不必要的 API 调用
  const documentIds = useMemo(() => {
    const docAggs = reference?.doc_aggs;
    if (docAggs && docAggs.length > 0) {
      return docAggs.map(x => x.doc_id).filter(Boolean);
    }
    return [];
  }, [reference?.doc_aggs]);

  useEffect(() => {
    if (documentIds.length > 0) {
      documentService.getDocumentThumbnails(documentIds)
        .then(response => {
          if (response?.data) {
            setFileThumbnails(response.data);
          }
        })
        .catch(() => {
          // 静默处理缩略图加载失败
        });
    }
  }, [documentIds]);


  // 引用正则表达式，匹配 [ID:数字] 和 ##数字$$ 格式（按照原版RAGFlow的方式）
  const referenceReg = /(\[ID:(\d+)\]|##(\d+)\$\$)/g;

  // 按照原版RAGFlow的方式，从匹配字符串中提取chunk索引
  const getChunkIndex = useCallback((match: string) => {
    // 处理新格式 [ID:1]
    if (match.startsWith('[ID:') && match.endsWith(']')) {
      return Number(match.slice(4, -1)); // 从 "[ID:1]" 中提取 "1"
    }
    // 处理旧格式 ##1$$（兼容性）
    if (match.startsWith('##') && match.endsWith('$$')) {
      return Number(match.slice(2, -2)); // 从 "##1$$" 中提取 "1"
    }
    // 默认尝试提取数字
    const numMatch = match.match(/\d+/);
    return numMatch ? Number(numMatch[0]) : 0;
  }, []);

  // 获取引用内容的函数（按照原版RAGFlow的索引方式）
  const getReferenceContent = (chunkIndex: number) => {
    if (!reference?.chunks || chunkIndex < 0 || chunkIndex >= reference.chunks.length) {

      return null;
    }
    return reference.chunks[chunkIndex];
  };

  // 按照原版RAGFlow的方式获取引用信息（完全复制原版逻辑）
  const getReferenceInfo = (chunkIndex: number) => {
    const chunks = reference?.chunks ?? [];
    const chunkItem = chunks[chunkIndex];

    // 如果chunkItem不存在，返回空值
    if (!chunkItem) {
      return {
        documentUrl: undefined,
        fileThumbnail: '',
        fileExtension: '',
        imageId: undefined,
        chunkItem: null,
        documentId: undefined,
        document: null,
      };
    }

    // 每个chunk独立存储document信息，通过document_id查找对应的document
    const document = reference?.doc_aggs?.find(
      (x) => x?.doc_id === chunkItem?.document_id,
    );
    const documentId = document?.doc_id || chunkItem?.document_id; // 优先使用doc_aggs中的，否则使用chunk中的
    const documentUrl = undefined; // document对象中没有url字段
    const fileThumbnail = documentId ? fileThumbnails[documentId] : '';

    // 按照原版RAGFlow的方式：imageId来自chunkItem.image_id
    const imageId = chunkItem?.image_id;



    return {
      documentUrl,
      fileThumbnail,
      fileExtension: '', // 可以从document.doc_name中提取
      imageId,
      chunkItem,
      documentId,
      document,
    };
  };



  // 按照原版RAGFlow的方式生成Popover内容
  const getPopoverContent = (chunkIndex: number) => {
    const {
      fileThumbnail,
      imageId,
      chunkItem,
      documentId,
      document,
    } = getReferenceInfo(chunkIndex);

    if (!chunkItem) {
      return (
        <div style={{ maxWidth: 300, padding: '8px' }}>
          <div style={{ color: '#ff4d4f', fontSize: '14px' }}>
            引用内容不可用
          </div>
          <div style={{ fontSize: '12px', color: '#666', marginTop: '4px' }}>
            [ID:{chunkIndex}] 对应的引用数据未找到
          </div>
        </div>
      );
    }


    const rawDimageId = imageId;
    const newimageId = rawDimageId ? rawDimageId.split('-')[0] : rawDimageId;

    return (
      <div style={{ maxWidth: 450, maxHeight: 400, overflow: 'auto' }}>
        <div key={chunkItem?.id} className="flex gap-2">
          {/* 按照要求的格式显示图片预览 */}
          {imageId && documentId && (
            <Popover
              placement="left"
              content={
                <img
                  src={`/v1/document/image/${newimageId}-thumbnail_${documentId}.png`}
                  alt=""
                  style={{ maxWidth: '400px', maxHeight: '300px' }}
                />
              }
            >
              <img
                src={`/v1/document/image/${newimageId}-thumbnail_${documentId}.png`}
                alt=""
                style={{ width: '60px', height: '60px', objectFit: 'cover', borderRadius: '4px' }}
              />
            </Popover>
          )}
          <div style={{ flex: 1, maxWidth: '40vw' }}>
            {/* 引用内容 */}
            <div
              dangerouslySetInnerHTML={{
                __html: chunkItem?.content ?? '',
              }}
              style={{
                fontSize: '13px',
                lineHeight: '1.5',
                marginBottom: '8px',
                padding: '8px',
                background: '#f9f9f9',
                borderRadius: '4px'
              }}
            />

            {/* 文档信息 */}
            {documentId && (
              <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
                {fileThumbnail ? (
                  <img
                    src={fileThumbnail}
                    alt=""
                    style={{ width: '24px', height: '24px' }}
                  />
                ) : (
                  <FileTextOutlined style={{ color: '#1890ff', fontSize: '16px' }} />
                )}
                <Button
                  type="link"
                  style={{ padding: 0, height: 'auto', fontSize: '12px' }}
                >
                  {document?.doc_name}
                </Button>
              </div>
            )}
          </div>
        </div>
      </div>
    );
  };

  // 按照原版RAGFlow的方式处理引用标记的渲染（使用原生方法）
  const renderWithReferences = useMemo(() => {
    return (text: string) => {
      // 查找所有[ID:数字]和##数字$$标记
      const matches = Array.from(text.matchAll(referenceReg));
      if (matches.length === 0) {
        return text;
      }

      // 分割文本并插入React元素
      const parts: (string | React.ReactElement)[] = [];
      let lastIndex = 0;

      matches.forEach((match, i) => {
        const matchStart = match.index!;
        const matchEnd = matchStart + match[0].length;
        const chunkIndex = getChunkIndex(match[0]); // 使用原版RAGFlow的索引提取方式

        // 使用getReferenceInfo获取完整的引用信息
        const { chunkItem } = getReferenceInfo(chunkIndex);



        // 添加匹配前的文本
        if (matchStart > lastIndex) {
          parts.push(text.slice(lastIndex, matchStart));
        }

        // 显示信息图标（按照原版RAGFlow的方式）
        parts.push(
          <Popover
            key={`popover-${i}`}
            content={getPopoverContent(chunkIndex)}
            trigger="hover"
            placement="top"
          >
            <InfoCircleOutlined
              style={{
                color: chunkItem ? '#1890ff' : '#999',
                cursor: 'pointer',
                margin: '0 2px',
                fontSize: '14px'
              }}
            />
          </Popover>
        );

        lastIndex = matchEnd;
      });

      // 添加最后剩余的文本
      if (lastIndex < text.length) {
        parts.push(text.slice(lastIndex));
      }

      return <>{parts}</>;
    };
  }, [reference]);

  // 移除自定义Markdown组件，因为我们现在直接处理引用标记

  // 检查内容是否包含[ID:*]或##*$$标记，并且有有效的引用数据
  const hasReferences = useMemo(() => {
    return typeof content === 'string' &&
           referenceReg.test(content) &&
           reference &&
           reference.chunks &&
           reference.chunks.length > 0;
  }, [content, reference]);

  // 检查内容是否包含think标签
  const hasThinkTags = useMemo(() => {
    const result = typeof content === 'string' && /<think>[\s\S]*?<\/think>/i.test(content);

    return result;
  }, [content]);

  // 处理think标签的渲染 - 在流式传输过程中显示思考过程
  const renderWithThinkTags = useMemo(() => {
    return (text: string) => {
      if (!hasThinkTags) return text;



      // 将think标签内容用特殊样式显示，在流式传输过程中显示思考过程
      return text.replace(
        /<think>([\s\S]*?)<\/think>/gi,
        '<div style="background: #f0f8ff; border-left: 4px solid #1890ff; padding: 8px 12px; margin: 8px 0; border-radius: 4px; font-style: italic; color: #666;"><strong>💭 思考过程:</strong><br/>$1</div>'
      );
    };
  }, [hasThinkTags]);

  return (
    <div className="markdown-content">
      {hasReferences ? (
        // 如果有引用标记，需要同时处理引用和think标签
        <div>
          {hasThinkTags ? (
            <ReactMarkdown
              remarkPlugins={[remarkGfm]}
              rehypePlugins={[rehypeRaw]}
            >
              {renderWithThinkTags(renderWithReferences(content) as string)}
            </ReactMarkdown>
          ) : (
            <div>
              {renderWithReferences(content)}
            </div>
          )}
        </div>
      ) : hasThinkTags ? (
        // 如果有think标签，先处理think标签再用ReactMarkdown渲染
        <ReactMarkdown
          remarkPlugins={[remarkGfm]}
          rehypePlugins={[rehypeRaw]}
        >
          {renderWithThinkTags(content)}
        </ReactMarkdown>
      ) : (
        // 如果没有特殊标记，使用ReactMarkdown正常渲染
        <ReactMarkdown
          remarkPlugins={[remarkGfm]}
          rehypePlugins={[rehypeRaw]}
        >
          {content}
        </ReactMarkdown>
      )}
    </div>
  );
};

export default MarkdownContent;
