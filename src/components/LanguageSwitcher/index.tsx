import React from 'react';
import { Select, Space } from 'antd';
import { GlobalOutlined } from '@ant-design/icons';
import { useI18n } from '@/hooks/use-i18n';
import { useTranslate } from '@/hooks/use-i18n';
import styles from './index.less';

const { Option } = Select;

interface LanguageSwitcherProps {
  size?: 'small' | 'middle' | 'large';
  showIcon?: boolean;
  style?: React.CSSProperties;
}

const LanguageSwitcher: React.FC<LanguageSwitcherProps> = ({
  size = 'middle',
  showIcon = true,
  style,
}) => {
  const t = useTranslate();
  const { getCurrentLanguage, changeLanguage } = useI18n();

  const handleLanguageChange = async (value: string) => {
    try {
      await changeLanguage(value);
    } catch (error) {
      console.error('Language change failed:', error);
    }
  };

  const currentLang = getCurrentLanguage();
  const normalizedLang = currentLang.startsWith('zh') ? 'zh' : 'en';

  return (
    <Space style={style}>
      {showIcon && <GlobalOutlined />}
      <Select
        value={normalizedLang}
        onChange={handleLanguageChange}
        size={size}
        className={styles.languageSelect}
        bordered={false}
      >
        <Option value="en">{t('languages.english', 'English')}</Option>
        <Option value="zh">{t('languages.chinese', '中文')}</Option>
      </Select>
    </Space>
  );
};

export default LanguageSwitcher;
