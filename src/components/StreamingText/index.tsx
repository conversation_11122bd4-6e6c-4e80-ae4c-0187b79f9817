import React, { useEffect, useRef, useState } from 'react';
import ReactMarkdown from 'react-markdown';
import remarkGfm from 'remark-gfm';
import rehypeRaw from 'rehype-raw';

interface StreamingTextProps {
  content: string;
  isStreaming: boolean;
  onContentUpdate?: (content: string) => void;
}

const StreamingText: React.FC<StreamingTextProps> = ({ 
  content, 
  isStreaming, 
  onContentUpdate 
}) => {
  const [displayContent, setDisplayContent] = useState('');
  const [isComplete, setIsComplete] = useState(false);
  const contentRef = useRef<string>('');
  const timeoutRef = useRef<NodeJS.Timeout | null>(null);

  // 当内容变化时，立即更新显示
  useEffect(() => {
    // 清除之前的延迟更新
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
    }

    if (content !== contentRef.current) {
      contentRef.current = content;

      if (isStreaming) {
        // 流式传输过程中，立即显示完整内容（包含think标签）
        setDisplayContent(content);
        setIsComplete(false);

        console.log('🎬 StreamingText 实时更新:', {
          contentLength: content.length,
          hasThinkTags: /<think>[\s\S]*?<\/think>/i.test(content),
          timestamp: new Date().toISOString(),
          performanceNow: performance.now()
        });

        // 通知父组件内容已更新
        onContentUpdate?.(content);

        // 强制重新渲染
        setTimeout(() => {
          setDisplayContent(prev => prev === content ? content + '' : content);
        }, 0);
      } else {
        // 流式传输结束，移除think标签
        const cleanContent = content.replace(/<think>[\s\S]*?<\/think>/gi, '').trim();
        setDisplayContent(cleanContent);
        setIsComplete(true);

        console.log('🏁 StreamingText 完成更新:', {
          originalLength: content.length,
          cleanLength: cleanContent.length,
          removedThinkTags: content !== cleanContent,
          timestamp: new Date().toISOString()
        });
      }
    }
  }, [content, isStreaming, onContentUpdate]);

  // 处理think标签的渲染
  const renderContent = (text: string) => {
    if (!text) return '';
    
    // 如果正在流式传输，显示think标签内容
    if (isStreaming && /<think>[\s\S]*?<\/think>/i.test(text)) {
      return text.replace(
        /<think>([\s\S]*?)<\/think>/gi,
        '<div style="background: #f0f8ff; border-left: 4px solid #1890ff; padding: 8px 12px; margin: 8px 0; border-radius: 4px; font-style: italic; color: #666;"><strong>💭 思考过程:</strong><br/>$1</div>'
      );
    }
    
    return text;
  };

  return (
    <div className="streaming-text" key={`streaming-${Date.now()}`}>
      <ReactMarkdown
        remarkPlugins={[remarkGfm]}
        rehypePlugins={[rehypeRaw]}
      >
        {renderContent(displayContent)}
      </ReactMarkdown>
      
      {/* 调试信息 */}
      {process.env.NODE_ENV === 'development' && (
        <div style={{ 
          fontSize: '10px', 
          color: '#999', 
          marginTop: '4px',
          fontFamily: 'monospace'
        }}>
          长度: {displayContent.length} | 
          流式: {isStreaming ? '是' : '否'} | 
          完成: {isComplete ? '是' : '否'} |
          Think标签: {/<think>[\s\S]*?<\/think>/i.test(displayContent) ? '有' : '无'}
        </div>
      )}
    </div>
  );
};

export default StreamingText;
