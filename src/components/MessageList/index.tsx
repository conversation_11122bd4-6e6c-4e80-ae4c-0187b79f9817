import React, { useEffect, useRef, useState } from 'react';
import {
  List,
  Avatar,
  Typography,
  Space,
  Button,
  Tooltip,
  Tag,
  Card,
  Divider,
  Empty,
  message as antdMessage
} from 'antd';
import {
  UserOutlined,
  RobotOutlined,
  LikeOutlined,
  DislikeOutlined,
  CopyOutlined,
  DeleteOutlined,
  SoundOutlined,
  RedoOutlined,
  PauseOutlined,
  PlayCircleOutlined,
  StopOutlined,
  LoadingOutlined
} from '@ant-design/icons';
import { IMessage, IReference } from '@/interfaces/chat';
import MarkdownContent from '@/components/MarkdownContent';
import KnowledgeMarkdownContent from '@/components/KnowledgeMarkdownContent';
import ReferenceList from '@/components/ReferenceList';
import { copyToClipboard } from '@/utils/clipboard';

import { buildMessageItemReference } from '@/utils/chat';
import { useFetchNextConversation } from '@/hooks/use-chat-hooks';
import { useTranslate } from '@/hooks/use-i18n';
import { useGlobalTTS } from '@/hooks/use-tts-hooks';
import './index.less';

const { Text, Paragraph } = Typography;

interface MessageListProps {
  messages: IMessage[];
  loading?: boolean;
  onThumbup?: (messageId: string, thumbup: boolean) => void;
  onDelete?: (messageId: string) => void;
  onCopy?: (content: string) => void;
  onTTS?: (content: string) => void;
  onResend?: (content: string) => void;
  showActions?: boolean;
  showEmptyState?: boolean; // 是否显示空状态，默认为true
  useKnowledgeMarkdown?: boolean; // 是否使用知识库专用的Markdown组件
  isStreaming?: boolean; // 是否正在流式传输
}

interface MessageItemProps {
  message: IMessage;
  onThumbup?: (messageId: string, thumbup: boolean) => void;
  onDelete?: (messageId: string) => void;
  onCopy?: (content: string) => void;
  onTTS?: (content: string) => void;
  onResend?: (content: string) => void;
  showActions?: boolean;
  useKnowledgeMarkdown?: boolean; // 是否使用知识库专用的Markdown组件
  isWelcomeMessage?: boolean; // 是否为欢迎消息
  isStreaming?: boolean; // 是否正在流式传输
  currentPlayingMessageId?: string | null; // 当前播放TTS的消息ID
  setCurrentPlayingMessageId?: (id: string | null) => void; // 设置当前播放TTS的消息ID
}



// 单条消息组件
const MessageItem: React.FC<MessageItemProps> = ({
  message,
  onThumbup,
  onDelete,
  onCopy,
  onTTS,
  onResend,
  showActions = true,
  useKnowledgeMarkdown = false,
  isWelcomeMessage = false,
  isStreaming = false,
  currentPlayingMessageId,
  setCurrentPlayingMessageId,
}) => {

  // 容错处理：对于缺少role字段的消息进行智能判断
  const messageRole = message.role || (() => {
    // 如果消息包含引用标记，很可能是assistant消息
    if (message.content && /(\[ID:\d+\]|##\d+\$\$)/.test(message.content)) {
      return 'assistant';
    }

    return 'user';
  })();

  const isUser = messageRole === 'user';
  const isAssistant = messageRole === 'assistant';

  const t = useTranslate();

  const handleCopy = async () => {
    const success = await copyToClipboard(message.content);
    if (success) {
      antdMessage.success(t('messages.success.copied'));
      onCopy?.(message.content);
    } else {
      antdMessage.error(t('messages.error.copyFailed'));
    }
  };

  // TTS功能
  const tts = useGlobalTTS();

  const handleTTS = () => {
    if (tts.state.isPlaying && currentPlayingMessageId === message.id) {
      // 如果当前消息正在播放，则暂停/恢复
      tts.toggleTTS();
    } else {
      // 开始播放当前消息
      setCurrentPlayingMessageId(message.id);
      tts.startTTS(message.content);
    }
    onTTS?.(message.content);
  };

  const handleStopTTS = () => {
    tts.stopTTS();
    setCurrentPlayingMessageId(null);
  };

  const handleThumbup = (thumbup: boolean) => {
    onThumbup?.(message.id, thumbup);
  };

  const handleDelete = () => {
    onDelete?.(message.id);
  };

  const handleResend = () => {
    onResend?.(message.content);
  };

  return (
    <div style={{ 
      display: 'flex', 
      justifyContent: isUser ? 'flex-end' : 'flex-start',
      marginBottom: '16px'
    }}>
      <div style={{ 
        maxWidth: '70%',
        display: 'flex',
        flexDirection: isUser ? 'row-reverse' : 'row',
        alignItems: 'flex-start',
        gap: '8px'
      }}>
        {/* 头像 */}
        <Avatar 
          icon={isUser ? <UserOutlined /> : <RobotOutlined />}
          style={{ 
            backgroundColor: isUser ? '#1890ff' : '#52c41a',
            flexShrink: 0
          }}
        />



        {/* 消息内容 */}
        <div style={{
          background: isUser ? '#1890ff' : '#f5f5f5',
          color: isUser ? '#fff' : '#000',
          padding: '12px 16px',
          borderRadius: '12px',
          borderTopLeftRadius: isUser ? '12px' : '4px',
          borderTopRightRadius: isUser ? '4px' : '12px',
          position: 'relative'
        }}>
          {/* 消息文本 */}
          {message.content ? (
            <>
              {isAssistant ? (
                // 助手消息使用MarkdownContent组件
                useKnowledgeMarkdown ? (
                  <KnowledgeMarkdownContent
                    content={message.content}
                    reference={message.reference}
                    messageRole={messageRole}
                    isWelcomeMessage={isWelcomeMessage}
                    isStreaming={isStreaming}
                  />
                ) : (
                  <MarkdownContent
                    content={message.content}
                    reference={message.reference}
                  />
                )
              ) : (
                // 用户消息也使用Markdown渲染，但不显示引用
                useKnowledgeMarkdown ? (
                  <KnowledgeMarkdownContent
                    content={message.content}
                    reference={null}
                    messageRole={messageRole}
                    isWelcomeMessage={false}
                    isStreaming={isStreaming}
                  />
                ) : (
                  <MarkdownContent
                    content={message.content}
                    reference={null}
                  />
                )
              )}
            </>
          ) : (
            <div style={{
              whiteSpace: 'pre-wrap',
              wordBreak: 'break-word',
              lineHeight: '1.5',
              color: '#999'
            }}>
              {isAssistant ? t('chatbot.thinking') : ''}
            </div>
          )}

          {/* 引用信息 - 每个message独立的引用文件列表 */}
          {isAssistant && message.reference &&
           message.reference.chunks &&
           message.reference.chunks.length > 0 && (
            <ReferenceList
              references={message.reference}
              onDocumentClick={(documentId, documentName) => {
                // 文档查看逻辑
                // TODO: 实现文档查看功能
              }}
              messageId={message.id} // 传递messageId，确保每个message有独立的引用列表
            />
          )}

          {/* 操作按钮 */}
          {showActions && message.content && (
            <div style={{ 
              marginTop: '8px',
              display: 'flex',
              justifyContent: isUser ? 'flex-start' : 'flex-end',
              gap: '4px'
            }}>
              <Space size="small">
                {/* 复制按钮 */}
                <Tooltip title={t('tooltips.copyToClipboard')}>
                  <Button
                    type="text"
                    size="small"
                    icon={<CopyOutlined />}
                    onClick={handleCopy}
                    style={{
                      color: isUser ? '#fff' : '#666',
                      opacity: 0.7
                    }}
                  />
                </Tooltip>

                {/* 重新发送按钮 - 仅用户消息显示 */}
                {isUser && onResend && (
                  <Tooltip title={t('common.retry')}>
                    <Button
                      type="text"
                      size="small"
                      icon={<RedoOutlined />}
                      onClick={handleResend}
                      style={{
                        color: isUser ? '#fff' : '#666',
                        opacity: 0.7
                      }}
                    />
                  </Tooltip>
                )}

                {/* TTS按钮 */}
                {isAssistant && (() => {
                  const isCurrentPlaying = currentPlayingMessageId === message.id;
                  const isPlaying = isCurrentPlaying && tts.state.isPlaying;
                  const isPaused = isCurrentPlaying && tts.state.isPaused;
                  const isLoading = isCurrentPlaying && tts.state.isLoading;

                  let icon = <SoundOutlined />;
                  let tooltip = t('dialog.tts');

                  if (isLoading) {
                    icon = <LoadingOutlined />;
                    tooltip = '正在加载...';
                  } else if (isPlaying) {
                    icon = <PauseOutlined />;
                    tooltip = '暂停播放';
                  } else if (isPaused) {
                    icon = <PlayCircleOutlined />;
                    tooltip = '继续播放';
                  }

                  return (
                    <Space size={0}>
                      <Tooltip title={tooltip}>
                        <Button
                          type="text"
                          size="small"
                          icon={icon}
                          onClick={handleTTS}
                          style={{
                            color: isCurrentPlaying ? '#1890ff' : '#666',
                            opacity: isCurrentPlaying ? 1 : 0.7
                          }}
                          loading={isLoading}
                        />
                      </Tooltip>

                      {/* 停止按钮 - 只在播放时显示 */}
                      {isCurrentPlaying && (isPlaying || isPaused) && (
                        <Tooltip title="停止播放">
                          <Button
                            type="text"
                            size="small"
                            icon={<StopOutlined />}
                            onClick={handleStopTTS}
                            style={{ color: '#ff4d4f', opacity: 0.7 }}
                          />
                        </Tooltip>
                      )}
                    </Space>
                  );
                })()}

                {/* 点赞/点踩按钮 */}
                {isAssistant && (
                  <>
                    <Tooltip title={t('common.yes')}>
                      <Button 
                        type="text" 
                        size="small"
                        icon={<LikeOutlined />}
                        onClick={() => handleThumbup(true)}
                        style={{ color: '#666', opacity: 0.7 }}
                      />
                    </Tooltip>
                    <Tooltip title="Dislike">
                      <Button 
                        type="text" 
                        size="small"
                        icon={<DislikeOutlined />}
                        onClick={() => handleThumbup(false)}
                        style={{ color: '#666', opacity: 0.7 }}
                      />
                    </Tooltip>
                  </>
                )}

                {/* 删除按钮 */}
                <Tooltip title={t('tooltips.deleteItem')}>
                  <Button 
                    type="text" 
                    size="small"
                    icon={<DeleteOutlined />}
                    onClick={handleDelete}
                    style={{ 
                      color: isUser ? '#fff' : '#666',
                      opacity: 0.7
                    }}
                  />
                </Tooltip>
              </Space>
            </div>
          )}

          {/* 时间戳 */}
          {message.create_time && (
            <div style={{ 
              fontSize: '11px',
              opacity: 0.7,
              marginTop: '4px',
              textAlign: isUser ? 'right' : 'left',
              color: isUser ? '#fff' : '#999'
            }}>
              {new Date(message.create_time).toLocaleTimeString()}
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

// 消息列表组件
const MessageList: React.FC<MessageListProps> = ({
  messages,
  loading = false,
  onThumbup,
  onDelete,
  onCopy,
  onTTS,
  onResend,
  showActions = true,
  showEmptyState = true,
  useKnowledgeMarkdown = false,
  isStreaming = false,
}) => {
  const messagesEndRef = useRef<HTMLDivElement>(null);

  // TTS状态管理
  const [currentPlayingMessageId, setCurrentPlayingMessageId] = useState<string | null>(null);

  // 获取conversation数据，用于buildMessageItemReference
  const { data: conversation } = useFetchNextConversation();



  // 自动滚动到底部
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  }, [messages]);

  // 确保messages是数组类型
  const safeMessages = Array.isArray(messages) ? messages : [];

  if (safeMessages.length === 0) {
    // 如果不显示空状态，返回空的div
    if (!showEmptyState) {
      return <div style={{ height: '100%' }} />;
    }

    return (
      <div style={{
        height: '100%',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center'
      }}>
        <Empty
          description="No messages yet. Start a conversation!"
          image={Empty.PRESENTED_IMAGE_SIMPLE}
        />
      </div>
    );
  }

  return (
    <div style={{ 
      height: '100%', 
      overflowY: 'auto',
      padding: '16px',
      background: '#fff'
    }}>
      {safeMessages.map((message, index) => {
        // 优先使用消息自身的reference数据（历史消息），如果没有则动态构建（新消息）
        let messageReference = message.reference;
        
        // 如果消息内容为空，不显示引用信息
        if (!message.content.trim()) {
          messageReference = undefined;
        } else {
          // 只有当消息没有reference数据时，才尝试动态构建
          if (!messageReference && conversation && message.role === 'assistant') {
            messageReference = buildMessageItemReference(
              {
                message: safeMessages,
                reference: conversation.reference || [],
              },
              message,
            );
          }

          // 确保引用数据的完整性，如果引用数据为空对象则设为undefined
          if (messageReference &&
              (!messageReference.chunks || messageReference.chunks.length === 0) &&
              (!messageReference.doc_aggs || messageReference.doc_aggs.length === 0)) {
            messageReference = undefined;
          }

          // 根据原版ragflow的逻辑：检查消息中是否有document_id来判断是否显示引用信息
          // 只有包含document_id的消息才应该显示引用信息
          if (messageReference && message.role === 'assistant') {
            const hasDocumentIds = messageReference.chunks &&
                                  messageReference.chunks.some(chunk => chunk.document_id);
            if (!hasDocumentIds) {
              messageReference = undefined;
            }
          }
        }

        // 为渲染生成带role前缀的key，但保持原始message.id不变
        const renderKey = message.role === 'assistant' ? `assistant_${message.id}` : `user_${message.id}`;

        // 计算是否为欢迎消息：第一条assistant消息就是欢迎消息
        const isWelcomeMessage = (() => {
          if (message.role !== 'assistant') return false;
          const assistantMessages = safeMessages.filter((m: IMessage) => m.role === 'assistant');
          const assistantIndex = assistantMessages.findIndex((m: IMessage) => m.id === message.id);
          return assistantIndex === 0; // 第一条assistant消息是欢迎消息
        })();

        return (
          <MessageItem
            key={renderKey}
            message={{
              ...message,
              reference: messageReference, // 优先使用原始reference，否则使用动态构建的
            }}
            onThumbup={onThumbup}
            onDelete={onDelete}
            onCopy={onCopy}
            onTTS={onTTS}
            onResend={onResend}
            showActions={showActions}
            useKnowledgeMarkdown={useKnowledgeMarkdown}
            isWelcomeMessage={isWelcomeMessage}
            isStreaming={isStreaming}
            currentPlayingMessageId={currentPlayingMessageId}
            setCurrentPlayingMessageId={setCurrentPlayingMessageId}
          />
        );
      })}
      
      {/* 加载指示器 */}
      {loading && (
        <div style={{ 
          display: 'flex', 
          justifyContent: 'flex-start',
          marginBottom: '16px'
        }}>
          <div style={{ 
            display: 'flex',
            alignItems: 'flex-start',
            gap: '8px'
          }}>
            <Avatar 
              icon={<RobotOutlined />}
              style={{ backgroundColor: '#52c41a' }}
            />
            <div style={{ 
              background: '#f5f5f5',
              padding: '12px 16px',
              borderRadius: '12px',
              borderTopLeftRadius: '4px'
            }}>
              <div style={{ display: 'flex', gap: '4px' }}>
                <div className="typing-dot" />
                <div className="typing-dot" />
                <div className="typing-dot" />
              </div>
            </div>
          </div>
        </div>
      )}
      
      {/* 滚动锚点 */}
      <div ref={messagesEndRef} />
    </div>
  );
};

export default MessageList;
