import React, { useState } from 'react';
import { Layout as AntLayout, Menu, Avatar, Dropdown, Button, Space, Typography } from 'antd';
import {
  MenuFoldOutlined,
  MenuUnfoldOutlined,
  UserOutlined,
  LogoutOutlined,
  SettingOutlined,
  DatabaseOutlined,
  FileTextOutlined,
  MessageOutlined,
  FolderOutlined,
  RobotOutlined,
  SearchOutlined,
  ApiOutlined,
  BarChartOutlined,
} from '@ant-design/icons';
import { useNavigate, useLocation } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import { useAuthState } from '@/hooks/useAuth';
import { ROUTES, MENU_ITEMS } from '@/constants';
import './index.less';

const { Header, Sider, Content } = AntLayout;
const { Title } = Typography;

interface LayoutProps {
  children: React.ReactNode;
}

const iconMap = {
  BarChartOutlined: BarChartOutlined,
  DatabaseOutlined: DatabaseOutlined,
  FileTextOutlined: FileTextOutlined,
  MessageOutlined: MessageOutlined,
  FolderOutlined: FolderOutlined,
  RobotOutlined: RobotOutlined,
  SearchOutlined: SearchOutlined,
  ApiOutlined: ApiOutlined,
  SettingOutlined: SettingOutlined,
};

const Layout: React.FC<LayoutProps> = ({ children }) => {
  const [collapsed, setCollapsed] = useState(false);
  const navigate = useNavigate();
  const location = useLocation();
  const { t } = useTranslation();
  const { userInfo, logout } = useAuthState();

  // 获取当前选中的菜单项
  const getSelectedKey = () => {
    const path = location.pathname;
    const menuItem = MENU_ITEMS.find(item => path.startsWith(item.path));
    return menuItem ? menuItem.key : 'dashboard';
  };

  // 处理菜单点击
  const handleMenuClick = ({ key }: { key: string }) => {
    const menuItem = MENU_ITEMS.find(item => item.key === key);
    if (menuItem) {
      navigate(menuItem.path);
    }
  };

  // 处理用户菜单点击
  const handleUserMenuClick = ({ key }: { key: string }) => {
    switch (key) {
      case 'profile':
        navigate(ROUTES.SETTINGS);
        break;
      case 'logout':
        logout.mutate();
        break;
    }
  };

  // 用户下拉菜单
  const userMenu = {
    items: [
      {
        key: 'profile',
        icon: <UserOutlined />,
        label: '个人设置',
      },
      {
        type: 'divider' as const,
      },
      {
        key: 'logout',
        icon: <LogoutOutlined />,
        label: '退出登录',
      },
    ],
    onClick: handleUserMenuClick,
  };

  // 生成菜单项
  const menuItems = MENU_ITEMS.map(item => {
    const IconComponent = iconMap[item.icon as keyof typeof iconMap];
    return {
      key: item.key,
      icon: IconComponent ? <IconComponent /> : null,
      label: t(item.label),
    };
  });

  return (
    <AntLayout className="layout-container">
      {/* 侧边栏 */}
      <Sider 
        trigger={null} 
        collapsible 
        collapsed={collapsed}
        className="layout-sider"
        width={250}
      >
        {/* Logo */}
        <div className="layout-logo">
          <div className="logo-content">
            {!collapsed && (
              <img
                src="/HB-logo.png"
                alt="汉邦高科"
                className="logo-image"
                style={{ width: '96px', height: '96px', objectFit: 'contain' }}
              />
            )}
            <DatabaseOutlined className="logo-icon" style={{ display: collapsed ? 'block' : 'none' }} />
            {!collapsed && (
              <Title level={4} className="logo-text">
                汉邦高科
              </Title>
            )}
          </div>
        </div>

        {/* 菜单 */}
        <Menu
          theme="dark"
          mode="inline"
          selectedKeys={[getSelectedKey()]}
          items={menuItems}
          onClick={handleMenuClick}
          className="layout-menu"
        />
      </Sider>

      {/* 主内容区 */}
      <AntLayout>
        {/* 顶部导航 */}
        <Header className="layout-header">
          <div className="header-left">
            <Button
              type="text"
              icon={collapsed ? <MenuUnfoldOutlined /> : <MenuFoldOutlined />}
              onClick={() => setCollapsed(!collapsed)}
              className="collapse-btn"
            />
          </div>

          <div className="header-right">
            <Space size="middle">
              {/* 用户信息 */}
              <Dropdown menu={userMenu} placement="bottomRight">
                <div className="user-info">
                  <Avatar 
                    size="small" 
                    icon={<UserOutlined />}
                    src={userInfo?.avatar}
                  />
                  <span className="user-name">{userInfo?.nickname || userInfo?.email}</span>
                </div>
              </Dropdown>
            </Space>
          </div>
        </Header>

        {/* 内容区 */}
        <Content className="layout-content">
          {children}
        </Content>
      </AntLayout>
    </AntLayout>
  );
};

export default Layout;
