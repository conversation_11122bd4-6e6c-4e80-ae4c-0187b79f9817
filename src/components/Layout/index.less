.layout-container {
  height: 100vh;
  
  .layout-sider {
    background: #001529;
    box-shadow: 2px 0 8px rgba(0, 0, 0, 0.15);
    z-index: 100;
    
    .layout-logo {
      height: 64px;
      display: flex;
      align-items: center;
      justify-content: center;
      background: rgba(255, 255, 255, 0.1);
      margin: 16px;
      border-radius: 8px;
      
      .logo-content {
        display: flex;
        align-items: center;
        gap: 12px;

        .logo-image {
          border-radius: 6px;
          background: rgba(255, 255, 255, 0.1);
          padding: 2px;
        }

        .logo-icon {
          color: #1890ff;
          font-size: 24px;
        }

        .logo-text {
          color: white !important;
          margin: 0 !important;
          font-weight: 600;
        }
      }
    }
    
    .layout-menu {
      border-right: none;
      
      .ant-menu-item {
        margin: 4px 8px;
        border-radius: 6px;
        height: 40px;
        line-height: 40px;
        
        &:hover {
          background: rgba(255, 255, 255, 0.1) !important;
        }
        
        &.ant-menu-item-selected {
          background: #1890ff !important;
          
          &::after {
            display: none;
          }
        }
        
        .ant-menu-item-icon {
          font-size: 16px;
        }
      }
    }
  }
  
  .layout-header {
    background: white;
    padding: 0 24px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    z-index: 10;
    display: flex;
    justify-content: space-between;
    align-items: center;
    
    .header-left {
      .collapse-btn {
        font-size: 16px;
        width: 40px;
        height: 40px;
        border-radius: 6px;
        
        &:hover {
          background: rgba(0, 0, 0, 0.06);
        }
      }
    }
    
    .header-right {
      .user-info {
        display: flex;
        align-items: center;
        gap: 8px;
        padding: 8px 12px;
        border-radius: 6px;
        cursor: pointer;
        transition: all 0.3s;
        
        &:hover {
          background: rgba(0, 0, 0, 0.06);
        }
        
        .user-name {
          font-size: 14px;
          color: #333;
          max-width: 120px;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }
      }
    }
  }
  
  .layout-content {
    background: #f0f2f5;
    padding: 24px;
    overflow: auto;
    
    // 自定义滚动条
    &::-webkit-scrollbar {
      width: 8px;
      height: 8px;
    }
    
    &::-webkit-scrollbar-track {
      background: #f1f1f1;
      border-radius: 4px;
    }
    
    &::-webkit-scrollbar-thumb {
      background: #c1c1c1;
      border-radius: 4px;
      
      &:hover {
        background: #a8a8a8;
      }
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .layout-container {
    .layout-sider {
      width: 200px !important;
      
      .layout-logo {
        margin: 12px;
        
        .logo-content {
          .logo-icon {
            font-size: 20px;
          }
          
          .logo-text {
            font-size: 16px !important;
          }
        }
      }
      
      .layout-menu {
        .ant-menu-item {
          height: 36px;
          line-height: 36px;
          
          .ant-menu-item-icon {
            font-size: 14px;
          }
        }
      }
    }
    
    .layout-header {
      padding: 0 16px;
      
      .header-right {
        .user-info {
          .user-name {
            max-width: 80px;
          }
        }
      }
    }
    
    .layout-content {
      padding: 16px;
    }
  }
}

@media (max-width: 576px) {
  .layout-container {
    .layout-sider {
      width: 180px !important;
    }
    
    .layout-header {
      padding: 0 12px;
      
      .header-right {
        .user-info {
          .user-name {
            display: none;
          }
        }
      }
    }
    
    .layout-content {
      padding: 12px;
    }
  }
}

// 暗色主题支持
.dark-theme {
  .layout-container {
    .layout-header {
      background: #141414;
      border-bottom: 1px solid #303030;
      
      .header-right {
        .user-info {
          &:hover {
            background: rgba(255, 255, 255, 0.1);
          }
          
          .user-name {
            color: #fff;
          }
        }
      }
    }
    
    .layout-content {
      background: #000;
    }
  }
}
