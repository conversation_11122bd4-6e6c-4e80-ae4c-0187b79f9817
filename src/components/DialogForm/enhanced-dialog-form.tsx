import React, { useEffect, useState } from 'react';
import {
  Form,
  Input,
  Select,
  InputNumber,
  Switch,
  Card,
  Row,
  Col,
  Divider,
  Typography,
  Space,
  Button,
  Upload,
  Slider,
  Flex,
  Segmented,
  Table,
  Tooltip,
  Avatar,
} from 'antd';
import { PlusOutlined, DeleteOutlined, QuestionCircleOutlined, UserOutlined } from '@ant-design/icons';
import { IDialog, IDialogParams } from '@/interfaces/dialog';
import { useLLMOptions } from '@/hooks/llm-hooks';
import { useKnowledgeBaseOptions } from '@/hooks/knowledge-base-hooks';
import { useTranslate } from '@/hooks/use-i18n';
import { v4 as uuid } from 'uuid';

const { Title, Text } = Typography;
const { TextArea } = Input;
const { Option } = Select;

// 默认System Prompt
const getDefaultSystemPrompt = () => {
  return `You are an intelligent assistant. Please summarize the content of the knowledge base to answer the question. Please list the data in the knowledge base and answer in detail. When all knowledge base content is irrelevant to the question, your answer must include the sentence "The answer you are looking for is not found in the knowledge base!" Answers need to consider chat history.
      Here is the knowledge base:
      {knowledge}
      The above is the knowledge base.`;
};

// 配置分段
enum ConfigurationSegmented {
  AssistantSetting = 'Assistant Setting',
  PromptEngine = 'Prompt Engine', 
  ModelSetting = 'Model Setting',
}

// 参数变量表格数据类型
interface VariableTableDataType {
  key: string;
  variable: string;
  optional: boolean;
}

interface EnhancedDialogFormProps {
  form: any;
  editingDialog?: IDialog | null;
  onFinish?: (values: IDialogParams) => void;
}

const EnhancedDialogForm: React.FC<EnhancedDialogFormProps> = ({
  form,
  editingDialog,
  onFinish,
}) => {
  // 获取LLM模型选项
  const { models: llmModels = [], isLoading: llmLoading } = useLLMOptions();
  
  // 获取知识库选项
  const { data: knowledgeBases = [], isLoading: kbLoading } = useKnowledgeBaseOptions();

  // 国际化
  const t = useTranslate();

  // 分段控制状态
  const [currentSegment, setCurrentSegment] = useState<ConfigurationSegmented>(
    ConfigurationSegmented.AssistantSetting
  );

  // 参数变量表格数据
  const [variableDataSource, setVariableDataSource] = useState<VariableTableDataType[]>([]);

  // 参数变量表格处理函数
  const handleAddVariable = () => {
    setVariableDataSource((state) => [
      ...state,
      {
        key: uuid(),
        variable: '',
        optional: true,
      },
    ]);
  };

  const handleRemoveVariable = (key: string) => () => {
    const newData = variableDataSource.filter((item) => item.key !== key);
    setVariableDataSource(newData);
  };

  const handleVariableOptionalChange = (row: VariableTableDataType) => (checked: boolean) => {
    const newData = [...variableDataSource];
    const index = newData.findIndex((item) => row.key === item.key);
    const item = newData[index];
    newData.splice(index, 1, {
      ...item,
      optional: checked,
    });
    setVariableDataSource(newData);
  };

  // 参数变量表格列定义
  const variableColumns = [
    {
      title: t('dialog.variable', 'Key'),
      dataIndex: 'variable',
      key: 'variable',
      render: (text: string, record: VariableTableDataType) => (
        <Input
          value={text}
          onChange={(e) => {
            const newData = [...variableDataSource];
            const index = newData.findIndex((item) => record.key === item.key);
            newData[index] = { ...newData[index], variable: e.target.value };
            setVariableDataSource(newData);
          }}
          placeholder={t('dialog.enterVariableKey', 'Enter variable key')}
        />
      ),
    },
    {
      title: t('dialog.optional', 'Optional'),
      dataIndex: 'optional',
      key: 'optional',
      width: 80,
      align: 'center' as const,
      render: (checked: boolean, record: VariableTableDataType) => (
        <Switch
          size="small"
          checked={checked}
          onChange={handleVariableOptionalChange(record)}
        />
      ),
    },
    {
      title: 'Operation',
      key: 'operation',
      width: 80,
      align: 'center' as const,
      render: (_: any, record: VariableTableDataType) => (
        <DeleteOutlined onClick={handleRemoveVariable(record.key)} />
      ),
    },
  ];

  // 初始化表单数据
  useEffect(() => {
    if (editingDialog) {
      form.setFieldsValue({
        name: editingDialog.name,
        description: editingDialog.description,
        language: editingDialog.language || 'English',
        llm_id: editingDialog.llm_id,
        kb_ids: editingDialog.kb_ids || [],
        vector_similarity_weight: editingDialog.vector_similarity_weight || 0.3,
        similarity_threshold: editingDialog.similarity_threshold || 0.2,
        // LLM设置
        temperature: editingDialog.llm_setting?.temperature || 0.1,
        top_p: editingDialog.llm_setting?.top_p || 0.3,
        presence_penalty: editingDialog.llm_setting?.presence_penalty || 0.4,
        frequency_penalty: editingDialog.llm_setting?.frequency_penalty || 0.7,
        max_tokens: editingDialog.llm_setting?.max_tokens || 512,
        // Prompt配置
        system_prompt: editingDialog.prompt_config?.system || getDefaultSystemPrompt(),
        prologue: editingDialog.prompt_config?.prologue || '',
        empty_response: editingDialog.prompt_config?.empty_response || "Sorry, I don't know.",
        tts: editingDialog.prompt_config?.tts || false,
        quote: editingDialog.prompt_config?.quote || true,
        keyword: editingDialog.prompt_config?.keyword || false,
      });
      
      // 设置参数变量
      if (editingDialog.prompt_config?.parameters) {
        setVariableDataSource(
          editingDialog.prompt_config.parameters.map((param: any) => ({
            key: uuid(),
            variable: param.key,
            optional: param.optional,
          }))
        );
      }
    } else {
      // 设置默认值
      form.setFieldsValue({
        language: 'English',
        vector_similarity_weight: 0.3,
        similarity_threshold: 0.2,
        temperature: 0.1,
        top_p: 0.3,
        presence_penalty: 0.4,
        frequency_penalty: 0.7,
        max_tokens: 512,
        system_prompt: getDefaultSystemPrompt(),
        empty_response: "Sorry, I don't know.",
        tts: false,
        quote: true,
        keyword: false,
      });
    }
  }, [editingDialog, form]);

  const handleFormSubmit = (values: any) => {
    // 构建参数变量
    const parameters = variableDataSource
      .filter((x) => x.variable.trim() !== '')
      .map((x) => ({ key: x.variable, optional: x.optional }));
    
    // 构建完整的Dialog参数
    const dialogParams: IDialogParams = {
      dialog_id: editingDialog?.id, // 使用id字段而不是dialog_id字段
      name: values.name,
      description: values.description,
      language: values.language,
      llm_id: values.llm_id,
      kb_ids: values.kb_ids || [],
      vector_similarity_weight: values.vector_similarity_weight,
      similarity_threshold: values.similarity_threshold,
      // 构建LLM设置
      llm_setting: {
        temperature: values.temperature,
        top_p: values.top_p,
        presence_penalty: values.presence_penalty,
        frequency_penalty: values.frequency_penalty,
        max_tokens: values.max_tokens,
      },
      // 构建Prompt配置
      prompt_config: {
        system: values.system_prompt || getDefaultSystemPrompt(),
        prologue: values.prologue || '',
        empty_response: values.empty_response || "Sorry, I don't know.",
        tts: values.tts || false,
        quote: values.quote || true,
        keyword: values.keyword || false,
        parameters: parameters,
      },
      prompt_type: 'simple',
      status: 'active',
    };

    console.log('🚀 final enhanced dialogParams:', dialogParams);
    console.log('🚀 is update operation:', !!dialogParams.dialog_id);
    
    onFinish?.(dialogParams);
  };

  // 知识库选项
  const knowledgeOptions = knowledgeBases.map((kb: any) => ({
    label: (
      <Space>
        <Avatar size={20} icon={<UserOutlined />} src={kb.avatar} />
        {kb.name}
      </Space>
    ),
    value: kb.id,
  }));

  // 上传按钮
  const uploadButton = (
    <button style={{ border: 0, background: 'none' }} type="button">
      <PlusOutlined />
      <div style={{ marginTop: 8 }}>Upload</div>
    </button>
  );

  return (
    <Form
      form={form}
      layout="vertical"
      onFinish={handleFormSubmit}
      requiredMark={false}
      scrollToFirstError
    >
      {/* 分段控制 */}
      <div style={{ marginBottom: 24 }}>
        <Segmented
          value={currentSegment}
          onChange={(value) => setCurrentSegment(value as ConfigurationSegmented)}
          options={[
            { label: t('dialog.assistantSettings', 'Assistant Setting'), value: ConfigurationSegmented.AssistantSetting },
            { label: t('dialog.promptEngine', 'Prompt Engine'), value: ConfigurationSegmented.PromptEngine },
            { label: t('dialog.modelSettings', 'Model Setting'), value: ConfigurationSegmented.ModelSetting },
          ]}
          block
        />
      </div>

      {/* Assistant Setting */}
      {currentSegment === ConfigurationSegmented.AssistantSetting && (
        <div>
          <Form.Item
            name="name"
            label={t('dialog.assistantName', 'Assistant Name')}
            rules={[{ required: true, message: t('dialog.assistantNameRequired', 'Please enter assistant name') }]}
          >
            <Input placeholder={t('dialog.enterAssistantName', 'Enter assistant name')} />
          </Form.Item>

          <Form.Item name="description" label={t('dialog.description', 'Description')}>
            <Input placeholder={t('dialog.enterDescription', 'Enter description')} />
          </Form.Item>

          <Form.Item
            name="icon"
            label={t('dialog.assistantAvatar', 'Assistant Avatar')}
            valuePropName="fileList"
          >
            <Upload
              listType="picture-card"
              maxCount={1}
              beforeUpload={() => false}
              showUploadList={{ showPreviewIcon: false, showRemoveIcon: false }}
            >
              {uploadButton}
            </Upload>
          </Form.Item>

          <Form.Item
            name="language"
            label={t('dialog.language', 'Language')}
            initialValue="English"
          >
            <Select>
              <Option value="Chinese">{t('languages.chinese', 'Chinese')}</Option>
              <Option value="English">{t('languages.english', 'English')}</Option>
            </Select>
          </Form.Item>

          <Form.Item
            name="empty_response"
            label={t('dialog.emptyResponse', 'Empty Response')}
            tooltip={t('dialog.emptyResponseTooltip', 'Response when no relevant information is found')}
          >
            <Input placeholder={t('dialog.emptyResponsePlaceholder', 'Sorry, I don\'t know.')} />
          </Form.Item>

          <Form.Item
            name="prologue"
            label={t('dialog.setOpener', 'Set An Opener')}
            tooltip={t('dialog.setOpenerTooltip', 'Opening message shown to users')}
            initialValue={t('dialog.defaultOpener', 'Hi! I\'m your assistant, how can I help you?')}
          >
            <TextArea rows={3} />
          </Form.Item>

          <Form.Item
            label={t('dialog.quote', 'Quote')}
            valuePropName="checked"
            name="quote"
            tooltip={t('dialog.quoteTooltip', 'Enable quote in responses')}
            initialValue={true}
          >
            <Switch />
          </Form.Item>

          <Form.Item
            label={t('dialog.keyword', 'Keyword')}
            valuePropName="checked"
            name="keyword"
            tooltip={t('dialog.keywordTooltip', 'Enable keyword extraction')}
            initialValue={false}
          >
            <Switch />
          </Form.Item>

          <Form.Item
            label={t('dialog.tts', 'TTS')}
            valuePropName="checked"
            name="tts"
            tooltip={t('dialog.ttsTooltip', 'Enable text-to-speech')}
            initialValue={false}
          >
            <Switch />
          </Form.Item>

          <Form.Item
            name="kb_ids"
            label={t('dialog.knowledgeBases', 'Knowledge Bases')}
            tooltip={t('dialog.knowledgeBasesTooltip', 'Select knowledge bases for this dialog')}
          >
            <Select
              mode="multiple"
              options={knowledgeOptions}
              placeholder={t('dialog.selectKnowledgeBases', 'Select knowledge bases')}
              loading={kbLoading}
              allowClear
              showSearch
            />
          </Form.Item>
        </div>
      )}

      {/* Prompt Engine */}
      {currentSegment === ConfigurationSegmented.PromptEngine && (
        <div>
          <Form.Item
            label={t('dialog.system', 'System')}
            rules={[{ required: true, message: t('dialog.systemPromptRequired', 'System prompt is required') }]}
            tooltip={t('dialog.systemPromptTooltip', 'System instructions that define the assistant\'s behavior')}
            name="system_prompt"
            initialValue={getDefaultSystemPrompt()}
          >
            <TextArea rows={8} />
          </Form.Item>

          <Divider />

          <Form.Item
            label={t('dialog.vectorSimilarityWeight', 'Vector Similarity Weight')}
            name="vector_similarity_weight"
            tooltip={t('dialog.vectorSimilarityWeightTooltip', 'Weight for vector similarity in hybrid search')}
            initialValue={0.3}
          >
            <Slider min={0} max={1} step={0.1} />
          </Form.Item>

          <Form.Item
            label={t('dialog.similarityThreshold', 'Similarity Threshold')}
            name="similarity_threshold"
            tooltip={t('dialog.similarityThresholdTooltip', 'Minimum similarity score for retrieval results')}
            initialValue={0.2}
          >
            <Slider min={0} max={1} step={0.1} />
          </Form.Item>

          {/* 参数变量表格 */}
          <div style={{ marginTop: 24 }}>
            <Row align="middle" justify="space-between" style={{ marginBottom: 16 }}>
              <Col>
                <Space>
                  <Text strong>{t('dialog.variable', 'Variable')}</Text>
                  <Tooltip title={t('dialog.variableTooltip', 'Define custom variables for the prompt')}>
                    <QuestionCircleOutlined />
                  </Tooltip>
                </Space>
              </Col>
              <Col>
                <Button size="small" onClick={handleAddVariable}>
                  {t('common.add', 'Add')}
                </Button>
              </Col>
            </Row>

            {variableDataSource.length > 0 && (
              <Table
                dataSource={variableDataSource}
                columns={variableColumns}
                rowKey="key"
                pagination={false}
                size="small"
              />
            )}
          </div>
        </div>
      )}

      {/* Model Setting */}
      {currentSegment === ConfigurationSegmented.ModelSetting && (
        <div>
          <Form.Item
            label={t('dialog.llmModel', 'Model')}
            name="llm_id"
            tooltip={t('dialog.llmModelTooltip', 'Choose the language model for this dialog')}
            rules={[{ required: true, message: t('dialog.llmModelRequired', 'Please select LLM model') }]}
          >
            <Select
              options={llmModels.map((model) => ({
                label: model.label,
                value: model.value,
              }))}
              placeholder={t('dialog.selectLLMModel', 'Select LLM model')}
              loading={llmLoading}
              allowClear
              showSearch
            />
          </Form.Item>

          <Card title={t('dialog.modelParameters', 'Model Parameters')} size="small">
            <Row gutter={[16, 16]}>
              <Col xs={24} sm={12}>
                <Form.Item
                  name="temperature"
                  label={t('dialog.temperature', 'Temperature')}
                  tooltip={t('dialog.temperatureTooltip', 'Controls randomness in responses (0-1)')}
                >
                  <Flex gap={16} align="center">
                    <Slider
                      min={0}
                      max={1}
                      step={0.01}
                      style={{ flex: 1 }}
                    />
                    <InputNumber
                      min={0}
                      max={1}
                      step={0.01}
                      style={{ width: 80 }}
                    />
                  </Flex>
                </Form.Item>
              </Col>

              <Col xs={24} sm={12}>
                <Form.Item
                  name="top_p"
                  label={t('dialog.topP', 'Top P')}
                  tooltip={t('dialog.topPTooltip', 'Controls diversity via nucleus sampling')}
                >
                  <Flex gap={16} align="center">
                    <Slider
                      min={0}
                      max={1}
                      step={0.01}
                      style={{ flex: 1 }}
                    />
                    <InputNumber
                      min={0}
                      max={1}
                      step={0.01}
                      style={{ width: 80 }}
                    />
                  </Flex>
                </Form.Item>
              </Col>

              <Col xs={24} sm={12}>
                <Form.Item
                  name="presence_penalty"
                  label={t('dialog.presencePenalty', 'Presence Penalty')}
                  tooltip={t('dialog.presencePenaltyTooltip', 'Penalty for repeating topics')}
                >
                  <Flex gap={16} align="center">
                    <Slider
                      min={0}
                      max={2}
                      step={0.01}
                      style={{ flex: 1 }}
                    />
                    <InputNumber
                      min={0}
                      max={2}
                      step={0.01}
                      style={{ width: 80 }}
                    />
                  </Flex>
                </Form.Item>
              </Col>

              <Col xs={24} sm={12}>
                <Form.Item
                  name="frequency_penalty"
                  label={t('dialog.frequencyPenalty', 'Frequency Penalty')}
                  tooltip={t('dialog.frequencyPenaltyTooltip', 'Penalty for repeating tokens')}
                >
                  <Flex gap={16} align="center">
                    <Slider
                      min={0}
                      max={2}
                      step={0.01}
                      style={{ flex: 1 }}
                    />
                    <InputNumber
                      min={0}
                      max={2}
                      step={0.01}
                      style={{ width: 80 }}
                    />
                  </Flex>
                </Form.Item>
              </Col>

              <Col xs={24} sm={12}>
                <Form.Item
                  name="max_tokens"
                  label={t('dialog.maxTokens', 'Max Tokens')}
                  tooltip={t('dialog.maxTokensTooltip', 'Maximum number of tokens in response')}
                >
                  <Flex gap={16} align="center">
                    <Slider
                      min={1}
                      max={4096}
                      style={{ flex: 1 }}
                    />
                    <InputNumber
                      min={1}
                      max={4096}
                      style={{ width: 80 }}
                    />
                  </Flex>
                </Form.Item>
              </Col>
            </Row>
          </Card>
        </div>
      )}
    </Form>
  );
};

export default EnhancedDialogForm;
