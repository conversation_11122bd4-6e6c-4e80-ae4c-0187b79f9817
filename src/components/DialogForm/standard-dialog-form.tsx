import React, { useEffect, useState, useRef } from 'react';
import {
  Form,
  Input,
  Select,
  Switch,
  Upload,
  Slider,
  Divider,
  Row,
  Col,
  Button,
  Table,
  Tooltip,
  Avatar,
  Space,
  Flex,
  Card,
} from 'antd';
import { PlusOutlined, DeleteOutlined, QuestionCircleOutlined, UserOutlined } from '@ant-design/icons';
import { IDialog } from '@/interfaces/dialog';
import { useLLMOptions, useRerankOptions } from '@/hooks/llm-hooks';
import { useKnowledgeBaseOptions } from '@/hooks/knowledge-base-hooks';
import { useTranslate } from '@/hooks/use-i18n';

const { TextArea } = Input;
const { Option } = Select;

// 移除配置分段，所有设置显示在一个页面

// 参数变量表格数据类型
interface VariableTableDataType {
  key: string;
  variable: string;
  optional: boolean;
}

// 动态System Prompt - 根据是否有知识库来决定
const getDefaultSystemPrompt = (hasKnowledgeBase: boolean = false, t?: any) => {
  if (hasKnowledgeBase) {
    return t ? t('dialog.defaultSystemPromptWithKB', `You are an intelligent assistant. Please summarize the content of the knowledge base to answer the question. Please list the data in the knowledge base and answer in detail. When all knowledge base content is irrelevant to the question, your answer must include the sentence "The answer you are looking for is not found in the knowledge base!" Answers need to consider chat history.
Here is the knowledge base:
{knowledge}
The above is the knowledge base.`) : `You are an intelligent assistant. Please summarize the content of the knowledge base to answer the question. Please list the data in the knowledge base and answer in detail. When all knowledge base content is irrelevant to the question, your answer must include the sentence "The answer you are looking for is not found in the knowledge base!" Answers need to consider chat history.
Here is the knowledge base:
{knowledge}
The above is the knowledge base.`;
  } else {
    return t ? t('dialog.defaultSystemPrompt', `You are an intelligent assistant. Please answer questions based on your knowledge and training. Provide helpful, accurate, and detailed responses to user queries.`) : `You are an intelligent assistant. Please answer questions based on your knowledge and training. Provide helpful, accurate, and detailed responses to user queries.`;
  }
};

interface StandardDialogFormProps {
  form: any;
  editingDialog?: IDialog | null;
  onFinish?: (values: IDialog) => void;
}

const StandardDialogForm: React.FC<StandardDialogFormProps> = ({
  form,
  editingDialog,
  onFinish,
}) => {
  const t = useTranslate();

  // 获取LLM模型选项
  const { models: llmModels = [], isLoading: llmLoading } = useLLMOptions();

  // 获取Rerank模型选项
  const { rerankModels = [], isLoading: rerankLoading } = useRerankOptions();

  // 获取知识库选项
  const { data: knowledgeBases = [], isLoading: kbLoading } = useKnowledgeBaseOptions();

  // 移除分段控制，所有设置显示在一个页面

  // 参数变量表格数据
  const [variableDataSource, setVariableDataSource] = useState<VariableTableDataType[]>([]);
  const promptEngineRef = useRef<Array<{ key: string; optional: boolean }>>([]);

  // 参数预设配置
  const parameterPresets = {
    Precise: {
      temperature: 0.1,
      top_p: 0.3,
      presence_penalty: 0.4,
      frequency_penalty: 0.7,
      max_tokens: 512,
    },
    Evenly: {
      temperature: 0.5,
      top_p: 0.5,
      presence_penalty: 0.5,
      frequency_penalty: 0.5,
      max_tokens: 1024,
    },
    Creative: {
      temperature: 0.9,
      top_p: 0.7,
      presence_penalty: 0.6,
      frequency_penalty: 0.3,
      max_tokens: 2048,
    },
    Custom: {
      temperature: 0.1,
      top_p: 0.3,
      presence_penalty: 0.4,
      frequency_penalty: 0.7,
      max_tokens: 512,
    },
  };

  // 参数启用状态
  const [parameterEnabled, setParameterEnabled] = useState({
    temperature: true,
    top_p: true,
    presence_penalty: true,
    frequency_penalty: true,
    max_tokens: true,
  });

  // 生成简单的ID
  const generateId = () => `var_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

  // 处理参数预设变化
  const handleParameterPresetChange = (preset: string) => {
    if (preset !== 'Custom') {
      const presetValues = parameterPresets[preset as keyof typeof parameterPresets];
      form.setFieldsValue({
        llm_setting: presetValues,
      });

      // 当选择预设时，启用所有参数
      setParameterEnabled({
        temperature: true,
        top_p: true,
        presence_penalty: true,
        frequency_penalty: true,
        max_tokens: true,
      });
    } else {
      // Custom模式下，根据当前启用状态决定参数是否可用
      setParameterEnabled(prev => ({ ...prev }));
    }
  };

  // 处理参数启用状态变化
  const handleParameterEnabledChange = (paramName: string, enabled: boolean) => {
    setParameterEnabled(prev => ({
      ...prev,
      [paramName]: enabled,
    }));

    // 如果禁用参数，将其设置为默认值
    if (!enabled) {
      const defaultValues = parameterPresets.Precise;
      form.setFieldValue(['llm_setting', paramName], defaultValues[paramName as keyof typeof defaultValues]);
    }
  };

  // 参数变量表格处理函数
  const handleAddVariable = () => {
    setVariableDataSource((state) => [
      ...state,
      {
        key: generateId(),
        variable: '',
        optional: true,
      },
    ]);
  };

  const handleRemoveVariable = (key: string) => () => {
    const newData = variableDataSource.filter((item) => item.key !== key);
    setVariableDataSource(newData);
  };

  const handleVariableOptionalChange = (row: VariableTableDataType) => (checked: boolean) => {
    const newData = [...variableDataSource];
    const index = newData.findIndex((item) => row.key === item.key);
    const item = newData[index];
    newData.splice(index, 1, {
      ...item,
      optional: checked,
    });
    setVariableDataSource(newData);
  };

  const handleVariableChange = (record: VariableTableDataType, value: string) => {
    const newData = [...variableDataSource];
    const index = newData.findIndex((item) => record.key === item.key);
    newData[index] = { ...newData[index], variable: value };
    setVariableDataSource(newData);
  };

  // 更新promptEngineRef
  useEffect(() => {
    promptEngineRef.current = variableDataSource
      .filter((x) => x.variable.trim() !== '')
      .map((x) => ({ key: x.variable, optional: x.optional }));
  }, [variableDataSource]);

  // 监听知识库变化，动态更新系统提示
  const handleKnowledgeBaseChange = (kbIds: string[]) => {
    const hasKnowledgeBase = kbIds && kbIds.length > 0;
    const currentSystemPrompt = form.getFieldValue(['prompt_config', 'system']);

    // 如果当前是默认提示，则更新为对应的版本 - 传递t参数以支持国际化
    const defaultWithKB = getDefaultSystemPrompt(true, t);
    const defaultWithoutKB = getDefaultSystemPrompt(false, t);

    if (currentSystemPrompt === defaultWithKB || currentSystemPrompt === defaultWithoutKB || !currentSystemPrompt) {
      form.setFieldValue(['prompt_config', 'system'], getDefaultSystemPrompt(hasKnowledgeBase, t));
    }
  };

  // 参数变量表格列定义
  const variableColumns = [
    {
      title: t('dialog.variable', 'Variable'),
      dataIndex: 'variable',
      key: 'variable',
      render: (text: string, record: VariableTableDataType) => (
        <Input
          value={text}
          onChange={(e) => handleVariableChange(record, e.target.value)}
          placeholder={t('dialog.enterVariableKey', 'Enter variable key')}
        />
      ),
    },
    {
      title: t('dialog.optional', 'Optional'),
      dataIndex: 'optional',
      key: 'optional',
      width: 80,
      align: 'center' as const,
      render: (checked: boolean, record: VariableTableDataType) => (
        <Switch
          size="small"
          checked={checked}
          onChange={handleVariableOptionalChange(record)}
        />
      ),
    },
    {
      title: 'Operation',
      key: 'operation',
      width: 80,
      align: 'center' as const,
      render: (_: any, record: VariableTableDataType) => (
        <DeleteOutlined onClick={handleRemoveVariable(record.key)} />
      ),
    },
  ];

  // 初始化表单数据
  useEffect(() => {
    if (editingDialog) {
      console.log('🚀 Edit Dialog - initializing form with:', editingDialog);

      const icon = editingDialog.icon;
      let fileList: any[] = [];

      if (icon) {
        fileList = [{ uid: '1', name: 'file', thumbUrl: icon, status: 'done' }];
      }

      // 完整的表单数据回填 - 使用正确的嵌套对象结构
      form.setFieldsValue({
        // 基本信息
        name: editingDialog.name,
        description: editingDialog.description,
        language: editingDialog.language || 'Chinese',
        icon: fileList,

        // LLM设置
        llm_id: editingDialog.llm_id,

        // 检索设置 - 注意vector_similarity_weight的转换
        vector_similarity_weight: 1 - (editingDialog.vector_similarity_weight ?? 0.3),
        similarity_threshold: editingDialog.similarity_threshold ?? 0.2,
        top_n: editingDialog.top_n ?? 8,

        // Rerank设置
        rerank_id: editingDialog.rerank_id,
        top_k: editingDialog.top_k ?? 1024,

        // 知识库
        kb_ids: editingDialog.kb_ids || [],

        // Prompt配置 - 使用嵌套对象结构，根据知识库状态设置系统提示
        prompt_config: {
          system: editingDialog.prompt_config?.system || getDefaultSystemPrompt(editingDialog.kb_ids && editingDialog.kb_ids.length > 0, t),
          prologue: editingDialog.prompt_config?.prologue || t('dialog.defaultOpener', "Hi! I'm your assistant, how can I help you?"),
          empty_response: editingDialog.prompt_config?.empty_response || t('dialog.emptyResponsePlaceholder', "Sorry, I don't know."),
          tts: editingDialog.prompt_config?.tts ?? false,
          quote: editingDialog.prompt_config?.quote ?? true,
          keyword: editingDialog.prompt_config?.keyword ?? false,
          refine_multiturn: editingDialog.prompt_config?.refine_multiturn ?? false,
          reasoning: editingDialog.prompt_config?.reasoning ?? false,
        },

        // LLM参数设置 - 使用嵌套对象结构
        llm_setting: {
          temperature: editingDialog.llm_setting?.temperature ?? 0.1,
          top_p: editingDialog.llm_setting?.top_p ?? 0.3,
          presence_penalty: editingDialog.llm_setting?.presence_penalty ?? 0.4,
          frequency_penalty: editingDialog.llm_setting?.frequency_penalty ?? 0.7,
          max_tokens: editingDialog.llm_setting?.max_tokens ?? 512,
        },

        // 参数预设
        parameter: 'Custom', // 编辑时默认为Custom模式
      });

      // 设置参数变量
      if (editingDialog.prompt_config?.parameters) {
        const existingParams = editingDialog.prompt_config.parameters.map((param: any) => ({
          key: generateId(),
          variable: param.key,
          optional: param.optional,
        }));

        // 检查是否已有knowledge参数
        const hasKnowledgeParam = existingParams.some(param => param.variable === 'knowledge');

        if (!hasKnowledgeParam) {
          // 如果没有knowledge参数，添加默认的knowledge参数
          console.log('🚀 Adding missing knowledge parameter for edited dialog');
          existingParams.unshift({
            key: generateId(),
            variable: 'knowledge',
            optional: false,
          });
        }

        setVariableDataSource(existingParams);
      } else {
        // 如果没有parameters配置，设置默认的knowledge参数
        console.log('🚀 Setting default knowledge parameter for dialog without parameters');
        setVariableDataSource([
          {
            key: generateId(),
            variable: 'knowledge',
            optional: false,
          },
        ]);
      }
    } else {
      console.log('🚀 Create Dialog - setting default values');

      // 设置默认值 - 使用正确的嵌套对象结构
      form.setFieldsValue({
        language: 'Chinese',
        vector_similarity_weight: 1 - 0.3, // UI显示0.7，实际存储0.3
        similarity_threshold: 0.2,
        top_n: 8,
        top_k: 1024,

        // Prompt配置 - 使用嵌套对象结构，默认无知识库的系统提示
        prompt_config: {
          system: getDefaultSystemPrompt(false, t),
          prologue: t('dialog.defaultOpener', "Hi! I'm your assistant, how can I help you?"),
          empty_response: t('dialog.emptyResponsePlaceholder', "Sorry, I don't know."),
          tts: false,
          quote: true,
          keyword: false,
          refine_multiturn: false,
          reasoning: false,
        },

        // LLM设置 - 使用嵌套对象结构
        llm_setting: {
          temperature: 0.1,
          top_p: 0.3,
          presence_penalty: 0.4,
          frequency_penalty: 0.7,
          max_tokens: 512,
        },

        // 参数预设默认为Precise
        parameter: 'Precise',
      });

      // 为新创建的dialog设置默认的"knowledge"参数 - 参考原版RAGFlow
      console.log('🚀 Setting default knowledge parameter for new dialog');
      setVariableDataSource([
        {
          key: generateId(),
          variable: 'knowledge',
          optional: false, // 必需参数
        },
      ]);
    }
  }, [editingDialog, form]);

  const handleFormSubmit = async (values: any) => {
    console.log('🚀 Standard handleFormSubmit called');
    console.log('🚀 editingDialog:', editingDialog);
    console.log('🚀 form values:', values);

    // 确保parameters中始终包含knowledge参数，无论是否有知识库
    const currentParameters = promptEngineRef.current;
    const hasKnowledgeParam = currentParameters.some(param => param.key === 'knowledge');

    if (!hasKnowledgeParam) {
      console.log('🚀 Adding missing knowledge parameter before submit');
      // 如果没有knowledge参数，自动添加，即使没有知识库也要传输
      currentParameters.unshift({
        key: 'knowledge',
        optional: false, // 始终设置为false，确保传输
      });
    }

    console.log('🚀 Final parameters (knowledge always included):', currentParameters);

    // 处理头像上传
    const getBase64FromUploadFileList = async (fileList: any[]) => {
      if (!fileList || fileList.length === 0) return '';
      // 这里应该实现实际的base64转换逻辑
      return fileList[0]?.thumbUrl || '';
    };

    const icon = await getBase64FromUploadFileList(values.icon);
    const emptyResponse = values.prompt_config?.empty_response ?? '';

    // 按照原版的removeUselessFieldsFromValues逻辑，移除不需要的字段
    const cleanedValues = { ...values };

    // 移除UI控制字段，这些字段不应该发送到后端
    delete cleanedValues.parameter; // 参数预设选择器

    // 按照原版的数据结构构建
    const finalValues: IDialog = {
      dialog_id: editingDialog?.id, // 编辑时使用id作为dialog_id

      // 使用清理后的表单值
      ...cleanedValues,

      // 重要：转换vector_similarity_weight的值
      vector_similarity_weight: 1 - (values.vector_similarity_weight ?? 0.7),

      // 处理prompt_config，确保包含parameters、empty_response和cross_languages
      prompt_config: {
        ...values.prompt_config,
        parameters: promptEngineRef.current,
        empty_response: emptyResponse,
        // 根据示例，将语言选项放在cross_languages数组中
        cross_languages: values.language ? [values.language] : [],
      },

      // 处理头像
      icon,
    };

    console.log('🚀 final standard values:', finalValues);
    console.log('🚀 is update operation:', !!finalValues.dialog_id);
    console.log('🚀 language field check:', {
      form_language: values.language,
      cleaned_language: cleanedValues.language,
      final_language: finalValues.language
    });
    console.log('🚀 vector_similarity_weight conversion:', {
      ui_value: values.vector_similarity_weight,
      stored_value: finalValues.vector_similarity_weight
    });
    console.log('🚀 prompt_config structure:', finalValues.prompt_config);

    onFinish?.(finalValues);
  };

  // 知识库选项
  const knowledgeOptions = knowledgeBases.map((kb: any) => ({
    label: (
      <Space>
        <Avatar size={20} icon={<UserOutlined />} src={kb.avatar} />
        {kb.name}
      </Space>
    ),
    value: kb.id,
  }));

  // 上传按钮 - 调整高度为32px
  const uploadButton = (
    <button style={{ border: 0, background: 'none', height: '32px' }} type="button">
      <PlusOutlined style={{ fontSize: '14px' }} />
      <div style={{ marginTop: 4, fontSize: '12px' }}>{t('dialog.upload', 'Upload')}</div>
    </button>
  );

  return (
    <Form
      form={form}
      layout="vertical"
      onFinish={handleFormSubmit}
      requiredMark={false}
      scrollToFirstError
    >
      {/* Assistant Setting */}
      <Card title={t('dialog.assistantSettings', 'Assistant Settings')} style={{ marginBottom: 24 }}>
        <div>
          {/* 第一行：3-4个字段 */}
          <Row gutter={[16, 16]}>
            <Col xs={24} sm={12} md={8}>
              <Form.Item
                name="name"
                label={t('dialog.assistantName', 'Assistant Name')}
                rules={[{ required: true, message: t('dialog.assistantNameRequired', 'Please enter assistant name') }]}
              >
                <Input placeholder={t('dialog.enterAssistantName', 'Enter assistant name')} />
              </Form.Item>
            </Col>

            <Col xs={24} sm={12} md={8}>
              <Form.Item
                name="language"
                label={t('common.language', 'Language')}
                initialValue="Chinese"
                rules={[{ required: true, message: t('dialog.languageRequired', 'Please select language') }]}
              >
                <Select placeholder={t('dialog.selectLanguage', 'Select language')}>
                  <Option value="English">{t('languages.english', 'English')}</Option>
                  <Option value="Chinese">{t('languages.chinese', '中文')}</Option>
                  <Option value="Japanese">{t('languages.japanese', '日语')}</Option>
                  <Option value="Korean">{t('languages.korean', '韩语')}</Option>
                </Select>
              </Form.Item>
            </Col>

            
          </Row>

          

          {/* 第三行：描述字段单独一行 */}
          <Row gutter={[16, 16]}>
            <Col xs={24} sm={24} md={16}>
              <Form.Item name="description" label={t('dialog.description', 'Description')}>
                <TextArea rows={3} placeholder={t('dialog.enterDescription', 'Enter description')} />
              </Form.Item>
            </Col>
            <Col xs={24} sm={12} md={8}>
              <Form.Item
                name="icon"
                label={t('dialog.assistantAvatar', 'Assistant Avatar')}
                valuePropName="fileList"
                getValueFromEvent={(e: any) => {
                  if (Array.isArray(e)) {
                    return e;
                  }
                  return e?.fileList;
                }}
              >
                <Upload
                  listType="picture-card"
                  maxCount={1}
                  beforeUpload={() => false}
                  showUploadList={{ showPreviewIcon: false, showRemoveIcon: false }}
                  className="compact-upload"
                >
                  {uploadButton}
                </Upload>
              </Form.Item>
            </Col>
          </Row>

          {/* 第四行：3-4个开关和选择字段 */}
          <Row gutter={[16, 16]}>
            <Col xs={24} sm={12} md={6}>
              <Form.Item
                label={t('dialog.quote', 'Quote')}
                valuePropName="checked"
                name={['prompt_config', 'quote']}
                tooltip={t('dialog.quoteTooltip', 'Enable quote in responses')}
                initialValue={true}
              >
                <Switch />
              </Form.Item>
            </Col>

            <Col xs={24} sm={12} md={6}>
              <Form.Item
                label={t('dialog.keyword', 'Keyword')}
                valuePropName="checked"
                name={['prompt_config', 'keyword']}
                tooltip={t('dialog.keywordTooltip', 'Enable keyword extraction')}
                initialValue={false}
              >
                <Switch />
              </Form.Item>
            </Col>

            <Col xs={24} sm={12} md={6}>
              <Form.Item
                label={t('dialog.tts', 'TTS')}
                valuePropName="checked"
                name={['prompt_config', 'tts']}
                tooltip={t('dialog.ttsTooltip', 'Enable text-to-speech')}
                initialValue={false}
              >
                <Switch />
              </Form.Item>
            </Col>

            <Col xs={24} sm={12} md={6}>
              <Form.Item
                name={['prompt_config', 'empty_response']}
                label={t('dialog.emptyResponse', 'Empty Response')}
                tooltip={t('dialog.emptyResponseTooltip', 'Response when no relevant information is found')}
              >
                <Input placeholder={t('dialog.emptyResponsePlaceholder', "Sorry, I don't know.")} />
              </Form.Item>
            </Col>
          </Row>

          {/* 第五行：开场白单独一行 */}
          <Row gutter={[16, 16]}>
            <Col xs={24} sm={24} md={12}>
              <Form.Item
                name={['prompt_config', 'prologue']}
                label={t('dialog.setOpener', 'Set An Opener')}
                tooltip={t('dialog.setOpenerTooltip', 'Opening message shown to users')}
                initialValue={t('dialog.defaultOpener', "Hi! I'm your assistant, how can I help you?")}
              >
                <TextArea rows={3} placeholder={t('dialog.defaultOpener', "Hi! I'm your assistant, how can I help you?")} />
              </Form.Item>
            </Col>
             {/* 知识库选择 */}
            <Col xs={24} sm={12} md={6}>
              <Form.Item
                name="kb_ids"
                label={t('dialog.knowledgeBases', 'Knowledge Bases')}
                tooltip={t('dialog.knowledgeBasesTooltip', 'Select knowledge bases for this dialog')}
                rules={[
                  {
                    required: false,
                    message: 'Please select knowledge bases',
                    type: 'array',
                  },
                ]}
              >
                <Select
                  mode="multiple"
                  options={knowledgeOptions}
                  placeholder={t('dialog.selectKnowledgeBases', 'Select knowledge bases')}
                  loading={kbLoading}
                  allowClear
                  showSearch
                  onChange={handleKnowledgeBaseChange}
                />
              </Form.Item>
            </Col>
          </Row>

         
        </div>
      </Card>

      {/* Prompt Engine */}
      <Card title={t('dialog.promptEngine', 'Prompt Engine')} style={{ marginBottom: 24 }}>
        <div>
          {/* 系统提示单独一行 */}
          <Row gutter={[16, 16]}>
            <Col span={24}>
              <Form.Item
                label={t('dialog.system', 'System')}
                rules={[{ required: true, message: t('dialog.systemPromptRequired', 'System prompt is required') }]}
                tooltip={t('dialog.systemPromptTooltip', 'System instructions that define the assistant\'s behavior')}
                name={['prompt_config', 'system']}
                initialValue={getDefaultSystemPrompt(false, t)}
              >
                <TextArea rows={6} placeholder={t('dialog.enterSystemPrompt', 'Enter system prompt...')} />
              </Form.Item>
            </Col>
          </Row>

          <Divider />

          {/* 第一行：滑块参数 */}
          <Row gutter={[16, 16]}>
            <Col xs={24} sm={12} md={6}>
              <Form.Item
                label={t('dialog.similarityThreshold', 'Similarity Threshold')}
                name="similarity_threshold"
                tooltip={t('dialog.similarityThresholdTooltip', 'Minimum similarity score for retrieval results')}
                initialValue={0.2}
              >
                <Slider max={1} step={0.01} />
              </Form.Item>
            </Col>

            <Col xs={24} sm={12} md={6}>
              <Form.Item
                label={t('dialog.vectorSimilarityWeight', 'Vector Similarity Weight')}
                name="vector_similarity_weight"
                tooltip={t('dialog.vectorSimilarityWeightTooltip', 'Weight for vector similarity in hybrid search')}
                initialValue={1 - 0.3} // UI显示0.7，实际存储0.3
              >
                <Slider max={1} step={0.01} />
              </Form.Item>
            </Col>

            <Col xs={24} sm={12} md={6}>
              <Form.Item noStyle dependencies={['vector_similarity_weight']}>
                {({ getFieldValue }) => {
                  const vectorWeight = getFieldValue('vector_similarity_weight') ?? 0.7;
                  const keywordWeight = 1 - vectorWeight;
                  return (
                    <Form.Item
                      label={t('dialog.keywordSimilarityWeight', 'Keyword Similarity Weight')}
                      tooltip={t('dialog.keywordSimilarityWeightTooltip', 'Weight for keyword similarity in hybrid search')}
                    >
                      <div style={{ padding: '4px 0' }}>
                        <span style={{ fontSize: '14px', color: '#666' }}>
                          {keywordWeight.toFixed(2)}
                        </span>
                        <div style={{ fontSize: '12px', color: '#999', marginTop: '4px' }}>
                          {t('dialog.autoCalculated', 'Auto-calculated: 1 - Vector Weight')}
                        </div>
                      </div>
                    </Form.Item>
                  );
                }}
              </Form.Item>
            </Col>

            <Col xs={24} sm={12} md={6}>
              <Form.Item
                label={t('dialog.topN', 'Top N')}
                name="top_n"
                initialValue={8}
                tooltip={t('dialog.topNTooltip', 'Number of documents to retrieve')}
              >
                <Slider max={30} />
              </Form.Item>
            </Col>
          </Row>

          {/* 第二行：开关和选择器 */}
          <Row gutter={[16, 16]}>
            <Col xs={24} sm={12} md={6}>
              <Form.Item
                label={t('dialog.multiTurn', 'Multi Turn')}
                tooltip={t('dialog.multiTurnTooltip', 'Enable multi-turn conversation')}
                name={['prompt_config', 'refine_multiturn']}
                valuePropName="checked"
                initialValue={false}
              >
                <Switch />
              </Form.Item>
            </Col>

            <Col xs={24} sm={12} md={6}>
              <Form.Item
                label={t('dialog.reasoning', 'Reasoning')}
                tooltip={t('dialog.reasoningTooltip', 'Enable reasoning mode')}
                name={['prompt_config', 'reasoning']}
                valuePropName="checked"
                initialValue={false}
              >
                <Switch />
              </Form.Item>
            </Col>

            <Col xs={24} sm={12} md={6}>
              <Form.Item
                label={t('dialog.rerankModel', 'Rerank Model')}
                name="rerank_id"
                tooltip={t('dialog.rerankModelTooltip', 'Select rerank model for result reordering')}
              >
                <Select
                  options={rerankModels}
                  allowClear
                  placeholder={t('dialog.selectRerankModel', 'Select rerank model')}
                  loading={rerankLoading}
                  showSearch
                />
              </Form.Item>
            </Col>

            <Col xs={24} sm={12} md={6}>
              {/* Top K - 只有选择了Rerank模型时才显示 */}
              <Form.Item noStyle dependencies={['rerank_id']}>
                {({ getFieldValue }) => {
                  const rerankId = getFieldValue('rerank_id');
                  return (
                    rerankId && (
                      <Form.Item
                        label={t('dialog.topK', 'Top K')}
                        name="top_k"
                        initialValue={1024}
                        tooltip={t('dialog.topKTooltip', 'Number of documents to rerank')}
                      >
                        <Slider max={2048} min={1} />
                      </Form.Item>
                    )
                  );
                }}
              </Form.Item>
            </Col>
          </Row>
          
          {/* 参数变量表格 */}
          <div style={{ marginTop: 24 }}>
            <Row align="middle" justify="space-between" style={{ marginBottom: 16 }}>
              <Col span={9}>
                <Space>
                  <span>{t('dialog.variable', 'Variable')}</span>
                  <Tooltip title={t('dialog.variableTooltip', 'Define custom variables for the prompt')}>
                    <QuestionCircleOutlined />
                  </Tooltip>
                </Space>
              </Col>
              <Col span={15} style={{ textAlign: 'right' }}>
                <Button size="small" onClick={handleAddVariable}>
                  {t('dialog.add', 'Add')}
                </Button>
              </Col>
            </Row>

            {variableDataSource.length > 0 && (
              <Row>
                <Col span={7}></Col>
                <Col span={17}>
                  <Table
                    dataSource={variableDataSource}
                    columns={variableColumns}
                    rowKey="key"
                    pagination={false}
                    size="small"
                  />
                </Col>
              </Row>
            )}
          </div>
        </div>
      </Card>

      {/* Model Setting */}
      <Card>
        <div>
          {/* LLM参数设置 */}
          {/* 模型选择和参数预设 */}
          <Row gutter={[16, 16]}>
            <Col xs={24} sm={12} md={12}>
              <Form.Item
                label={t('dialogAdvanced.model')}
                name="llm_id"
                tooltip={t('dialogAdvanced.modelTooltip')}
                rules={[{ required: true, message: t('dialogAdvanced.selectModelRequired') }]}
              >
                <Select
                  options={llmModels.map((model) => ({
                    label: model.label,
                    value: model.value,
                  }))}
                  placeholder={t('dialogAdvanced.selectModelPlaceholder')}
                  loading={llmLoading}
                  allowClear
                  showSearch
                  popupMatchSelectWidth={false}
                />
              </Form.Item>
            </Col>

            <Col xs={24} sm={12} md={12}>
              <Form.Item
                label={t('dialogAdvanced.parameterPreset')}
                name="parameter"
                initialValue="Precise"
                tooltip={t('dialogAdvanced.parameterPresetTooltip')}
              >
                <Select
                  options={[
                    { label: t('dialogAdvanced.precise'), value: 'Precise' },
                    { label: t('dialogAdvanced.evenly'), value: 'Evenly' },
                    { label: t('dialogAdvanced.creative'), value: 'Creative' },
                    { label: t('dialogAdvanced.custom'), value: 'Custom' },
                  ]}
                  onChange={handleParameterPresetChange}
                />
              </Form.Item>
            </Col>
          </Row>
          <div>
            <Divider>{t('dialogAdvanced.modelParameters')}</Divider>

            {/* 第一行：Temperature和Top P */}
            <Row gutter={[16, 16]}>
              <Col xs={24} sm={12}>
                <div style={{ display: 'flex', alignItems: 'center', marginBottom: 8 }}>
                  <span style={{ marginRight: 8 }}>{t('dialogAdvanced.temperature')}</span>
                  <Form.Item noStyle dependencies={['parameter']}>
                    {({ getFieldValue }) => {
                      const preset = getFieldValue('parameter');
                      return preset === 'Custom' ? (
                        <Switch
                          size="small"
                          checked={parameterEnabled.temperature}
                          onChange={(checked) => handleParameterEnabledChange('temperature', checked)}
                        />
                      ) : null;
                    }}
                  </Form.Item>
                </div>
                <Form.Item
                  tooltip={t('dialogAdvanced.temperatureTooltip')}
                  name={['llm_setting', 'temperature']}
                  initialValue={0.1}
                >
                  <Slider
                    max={1}
                    step={0.01}
                    disabled={!parameterEnabled.temperature}
                  />
                </Form.Item>
              </Col>

              <Col xs={24} sm={12}>
                <div style={{ display: 'flex', alignItems: 'center', marginBottom: 8 }}>
                  <span style={{ marginRight: 8 }}>{t('dialogAdvanced.topP')}</span>
                  <Form.Item noStyle dependencies={['parameter']}>
                    {({ getFieldValue }) => {
                      const preset = getFieldValue('parameter');
                      return preset === 'Custom' ? (
                        <Switch
                          size="small"
                          checked={parameterEnabled.top_p}
                          onChange={(checked) => handleParameterEnabledChange('top_p', checked)}
                        />
                      ) : null;
                    }}
                  </Form.Item>
                </div>
                <Form.Item
                  tooltip={t('dialogAdvanced.topPTooltip')}
                  name={['llm_setting', 'top_p']}
                  initialValue={0.3}
                >
                  <Slider
                    max={1}
                    step={0.01}
                    disabled={!parameterEnabled.top_p}
                  />
                </Form.Item>
              </Col>
            </Row>

            {/* 第二行：Presence Penalty和Frequency Penalty */}
            <Row gutter={[16, 16]}>
              <Col xs={24} sm={12}>
                <div style={{ display: 'flex', alignItems: 'center', marginBottom: 8 }}>
                  <span style={{ marginRight: 8 }}>{t('dialogAdvanced.presencePenalty')}</span>
                  <Form.Item noStyle dependencies={['parameter']}>
                    {({ getFieldValue }) => {
                      const preset = getFieldValue('parameter');
                      return preset === 'Custom' ? (
                        <Switch
                          size="small"
                          checked={parameterEnabled.presence_penalty}
                          onChange={(checked) => handleParameterEnabledChange('presence_penalty', checked)}
                        />
                      ) : null;
                    }}
                  </Form.Item>
                </div>
                <Form.Item
                  tooltip={t('dialogAdvanced.presencePenaltyTooltip')}
                  name={['llm_setting', 'presence_penalty']}
                  initialValue={0.4}
                >
                  <Slider
                    max={2}
                    step={0.01}
                    disabled={!parameterEnabled.presence_penalty}
                  />
                </Form.Item>
              </Col>

              <Col xs={24} sm={12}>
                <div style={{ display: 'flex', alignItems: 'center', marginBottom: 8 }}>
                  <span style={{ marginRight: 8 }}>{t('dialogAdvanced.frequencyPenalty')}</span>
                  <Form.Item noStyle dependencies={['parameter']}>
                    {({ getFieldValue }) => {
                      const preset = getFieldValue('parameter');
                      return preset === 'Custom' ? (
                        <Switch
                          size="small"
                          checked={parameterEnabled.frequency_penalty}
                          onChange={(checked) => handleParameterEnabledChange('frequency_penalty', checked)}
                        />
                      ) : null;
                    }}
                  </Form.Item>
                </div>
                <Form.Item
                  tooltip={t('dialogAdvanced.frequencyPenaltyTooltip')}
                  name={['llm_setting', 'frequency_penalty']}
                  initialValue={0.7}
                >
                  <Slider
                    max={2}
                    step={0.01}
                    disabled={!parameterEnabled.frequency_penalty}
                  />
                </Form.Item>
              </Col>
            </Row>

            {/* 第三行：Max Tokens */}
            <Row gutter={[16, 16]}>
              <Col xs={24} sm={12}>
                <div style={{ display: 'flex', alignItems: 'center', marginBottom: 8 }}>
                  <span style={{ marginRight: 8 }}>{t('dialogAdvanced.maxTokens')}</span>
                  <Form.Item noStyle dependencies={['parameter']}>
                    {({ getFieldValue }) => {
                      const preset = getFieldValue('parameter');
                      return preset === 'Custom' ? (
                        <Switch
                          size="small"
                          checked={parameterEnabled.max_tokens}
                          onChange={(checked) => handleParameterEnabledChange('max_tokens', checked)}
                        />
                      ) : null;
                    }}
                  </Form.Item>
                </div>
                <Form.Item
                  tooltip={t('dialogAdvanced.maxTokensTooltip')}
                  name={['llm_setting', 'max_tokens']}
                  initialValue={512}
                >
                  <Slider
                    max={8192}
                    min={512}
                    disabled={!parameterEnabled.max_tokens}
                  />
                </Form.Item>
              </Col>
            </Row>
          </div>
        </div>
      </Card>
    </Form>
  );
};

export default StandardDialogForm;
