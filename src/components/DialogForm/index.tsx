import React, { useEffect, useState } from 'react';
import {
  Form,
  Input,
  Select,
  InputNumber,
  Switch,
  Card,
  Row,
  Col,
  Divider,
  Typography,
  Space,
  Button,
  Collapse,
  Upload,
  Slider,
  Flex,
  Segmented,
  Table,
  Tooltip,
} from 'antd';
import { PlusOutlined, DeleteOutlined, QuestionCircleOutlined, UserOutlined } from '@ant-design/icons';
import { IDialog, IDialogParams, PromptConfig, Variable } from '@/interfaces/dialog';
import { useLLMOptions } from '@/hooks/llm-hooks';
import { useKnowledgeBaseOptions } from '@/hooks/knowledge-base-hooks';
import { useTranslate } from '@/hooks/use-i18n';
import { v4 as uuid } from 'uuid';

const { Title, Text } = Typography;
const { TextArea } = Input;
const { Option } = Select;
const { Panel } = Collapse;

// 默认System Prompt
const getDefaultSystemPrompt = (t: any) => {
  return t('systemPrompts.default');
};

// 配置分段
enum ConfigurationSegmented {
  AssistantSetting = 'assistantSettings',
  PromptEngine = 'promptConfiguration',
  ModelSetting = 'modelSettings',
}

// 参数变量表格数据类型
interface VariableTableDataType {
  key: string;
  variable: string;
  optional: boolean;
}

interface DialogFormProps {
  form: any;
  editingDialog?: IDialog | null;
  onFinish?: (values: IDialogParams) => void;
}

const DialogForm: React.FC<DialogFormProps> = ({
  form,
  editingDialog,
  onFinish,
}) => {
  const t = useTranslate();

  // 获取LLM模型选项
  const { models: llmModels = [], isLoading: llmLoading } = useLLMOptions();

  // 获取知识库选项
  const { data: knowledgeBases = [], isLoading: kbLoading } = useKnowledgeBaseOptions();

  // 分段控制状态
  const [currentSegment, setCurrentSegment] = useState<ConfigurationSegmented>(
    ConfigurationSegmented.AssistantSetting
  );

  // 参数变量表格数据
  const [variableDataSource, setVariableDataSource] = useState<VariableTableDataType[]>([]);

  // 参数变量表格处理函数
  const handleAddVariable = () => {
    setVariableDataSource((state) => [
      ...state,
      {
        key: uuid(),
        variable: '',
        optional: true,
      },
    ]);
  };

  const handleRemoveVariable = (key: string) => () => {
    const newData = variableDataSource.filter((item) => item.key !== key);
    setVariableDataSource(newData);
  };

  const handleVariableOptionalChange = (row: VariableTableDataType) => (checked: boolean) => {
    const newData = [...variableDataSource];
    const index = newData.findIndex((item) => row.key === item.key);
    const item = newData[index];
    newData.splice(index, 1, {
      ...item,
      optional: checked,
    });
    setVariableDataSource(newData);
  };

  const handleVariableSave = (row: VariableTableDataType) => {
    const newData = [...variableDataSource];
    const index = newData.findIndex((item) => row.key === item.key);
    const item = newData[index];
    newData.splice(index, 1, {
      ...item,
      ...row,
    });
    setVariableDataSource(newData);
  };

  // 初始化表单数据
  useEffect(() => {
    if (editingDialog) {
      form.setFieldsValue({
        name: editingDialog.name,
        description: editingDialog.description,
        language: editingDialog.language || 'English',
        llm_id: editingDialog.llm_id,
        llm_setting_type: editingDialog.llm_setting_type || 'Precise',
        kb_ids: editingDialog.kb_ids || [],
        vector_similarity_weight: editingDialog.vector_similarity_weight || 0.3,
        similarity_threshold: editingDialog.similarity_threshold || 0.2,
        // LLM设置
        temperature: editingDialog.llm_setting?.temperature || 0.1,
        top_p: editingDialog.llm_setting?.top_p || 0.3,
        presence_penalty: editingDialog.llm_setting?.presence_penalty || 0.4,
        frequency_penalty: editingDialog.llm_setting?.frequency_penalty || 0.7,
        max_tokens: editingDialog.llm_setting?.max_tokens || 512,
        // Prompt配置
        system_prompt: editingDialog.prompt_config?.system || getDefaultSystemPrompt(),
        prologue: editingDialog.prompt_config?.prologue || '',
        empty_response: editingDialog.prompt_config?.empty_response || "Sorry, I don't know.",
        tts: editingDialog.prompt_config?.tts || false,
        parameters: editingDialog.prompt_config?.parameters || [],
      });
    } else {
      // 设置默认值
      form.setFieldsValue({
        language: 'English',
        llm_setting_type: 'Precise',
        vector_similarity_weight: 0.3,
        similarity_threshold: 0.2,
        temperature: 0.1,
        top_p: 0.3,
        presence_penalty: 0.4,
        frequency_penalty: 0.7,
        max_tokens: 512,
        system_prompt: getDefaultSystemPrompt(),
        empty_response: "Sorry, I don't know.",
        tts: false,
        parameters: [],
      });
    }
  }, [editingDialog, form]);

  const handleFormSubmit = (values: any) => {
    // 构建完整的Dialog参数
    const dialogParams: IDialogParams = {
      dialog_id: editingDialog?.id, // 使用id字段而不是dialog_id字段
      name: values.name,
      description: values.description,
      language: values.language,
      llm_id: values.llm_id,
      llm_setting_type: values.llm_setting_type,
      kb_ids: values.kb_ids || [],
      vector_similarity_weight: values.vector_similarity_weight,
      similarity_threshold: values.similarity_threshold,
      // 构建LLM设置
      llm_setting: {
        temperature: values.temperature,
        top_p: values.top_p,
        presence_penalty: values.presence_penalty,
        frequency_penalty: values.frequency_penalty,
        max_tokens: values.max_tokens,
      },
      // 构建Prompt配置
      prompt_config: {
        system: values.system_prompt || '',
        prologue: values.prologue || '',
        empty_response: values.empty_response || "Sorry, I don't know.",
        tts: values.tts || false,
        parameters: values.parameters || [],
      },
      prompt_type: 'simple',
      status: 'active',
    };

    onFinish?.(dialogParams);
  };

  return (
    <Form
      form={form}
      layout="vertical"
      onFinish={handleFormSubmit}
      requiredMark={false}
      scrollToFirstError
    >
      {/* 基本信息 */}
      <Card title={t('dialog.basicInformation', 'Basic Information')} size="small" style={{ marginBottom: 16 }}>
        <Row gutter={[16, 0]}>
          <Col xs={24} sm={12}>
            <Form.Item
              name="name"
              label={t('dialog.dialogName')}
              rules={[
                { required: true, message: t('dialog.nameRequired') },
                { max: 100, message: t('messages.validation.maxLength', { max: 100 }) },
              ]}
            >
              <Input placeholder={t('dialog.enterDialogName')} />
            </Form.Item>
          </Col>
          <Col xs={24} sm={12}>
            <Form.Item
              name="language"
              label={t('dialog.language')}
              rules={[{ required: true, message: t('dialog.languageRequired') }]}
            >
              <Select placeholder={t('dialog.selectLanguage')}>
                <Option value="English">{t('languages.english')}</Option>
                <Option value="Chinese">{t('languages.chinese')}</Option>
                <Option value="Japanese">{t('languages.japanese')}</Option>
                <Option value="Korean">{t('languages.korean')}</Option>
              </Select>
            </Form.Item>
          </Col>
        </Row>

        <Form.Item
          name="description"
          label={t('dialog.description')}
          rules={[
            { max: 500, message: t('dialog.descriptionMaxLength') },
          ]}
        >
          <TextArea
            rows={3}
            placeholder={t('dialog.enterDescription')}
            showCount
            maxLength={500}
          />
        </Form.Item>
      </Card>

      {/* 知识库选择 */}
      <Card title={t('knowledge.title')} size="small" style={{ marginBottom: 16 }}>
        <Form.Item
          name="kb_ids"
          label={t('knowledge.selectKnowledgeBases', 'Select Knowledge Bases')}
          tooltip={t('knowledge.selectKnowledgeBasesTooltip', 'Choose knowledge bases to use for this dialog')}
        >
          <Select
            mode="multiple"
            placeholder="Select knowledge bases"
            loading={kbLoading}
            allowClear
            showSearch
            filterOption={(input, option) =>
              (option?.children as unknown as string)
                ?.toLowerCase()
                .includes(input.toLowerCase())
            }
          >
            {knowledgeBases.map((kb: any) => (
              <Option key={kb.id} value={kb.id}>
                {kb.name}
              </Option>
            ))}
          </Select>
        </Form.Item>
      </Card>

      {/* LLM模型设置 */}
      <Card title={t('aiModels.modelSettings', 'LLM Model Settings')} size="small" style={{ marginBottom: 16 }}>
        <Row gutter={[16, 0]}>
          <Col xs={24} sm={12}>
            <Form.Item
              name="llm_id"
              label="LLM Model"
              rules={[{ required: true, message: 'Please select LLM model' }]}
              tooltip="Choose the language model for this dialog"
            >
              <Select
                placeholder="Select LLM model"
                loading={llmLoading}
                allowClear
                showSearch
                filterOption={(input, option) =>
                  (option?.children as unknown as string)
                    ?.toLowerCase()
                    .includes(input.toLowerCase())
                }
              >
                {llmModels.map((model: any) => (
                  <Option key={model.value} value={model.value}>
                    {model.label}
                  </Option>
                ))}
              </Select>
            </Form.Item>
          </Col>
          <Col xs={24} sm={12}>
            <Form.Item
              name="llm_setting_type"
              label="Setting Type"
              tooltip="Choose predefined LLM setting type"
            >
              <Select placeholder="Select setting type">
                <Option value="Precise">Precise</Option>
                <Option value="Evenly">Evenly</Option>
                <Option value="Creative">Creative</Option>
                <Option value="Custom">Custom</Option>
              </Select>
            </Form.Item>
          </Col>
        </Row>

        <Collapse ghost>
          <Panel header="Advanced LLM Parameters" key="llm-params">
            <Row gutter={[16, 0]}>
              <Col xs={24} sm={12} lg={6}>
                <Form.Item
                  name="temperature"
                  label="Temperature"
                  tooltip="Controls randomness in responses (0-1)"
                >
                  <InputNumber
                    min={0}
                    max={1}
                    step={0.1}
                    style={{ width: '100%' }}
                    placeholder="0.1"
                  />
                </Form.Item>
              </Col>
              <Col xs={24} sm={12} lg={6}>
                <Form.Item
                  name="top_p"
                  label="Top P"
                  tooltip="Controls diversity via nucleus sampling"
                >
                  <InputNumber
                    min={0}
                    max={1}
                    step={0.1}
                    style={{ width: '100%' }}
                    placeholder="0.3"
                  />
                </Form.Item>
              </Col>
              <Col xs={24} sm={12} lg={6}>
                <Form.Item
                  name="presence_penalty"
                  label="Presence Penalty"
                  tooltip="Penalty for repeating topics"
                >
                  <InputNumber
                    min={0}
                    max={2}
                    step={0.1}
                    style={{ width: '100%' }}
                    placeholder="0.4"
                  />
                </Form.Item>
              </Col>
              <Col xs={24} sm={12} lg={6}>
                <Form.Item
                  name="frequency_penalty"
                  label="Frequency Penalty"
                  tooltip="Penalty for repeating tokens"
                >
                  <InputNumber
                    min={0}
                    max={2}
                    step={0.1}
                    style={{ width: '100%' }}
                    placeholder="0.7"
                  />
                </Form.Item>
              </Col>
            </Row>

            <Row gutter={[16, 0]}>
              <Col xs={24} sm={12}>
                <Form.Item
                  name="max_tokens"
                  label="Max Tokens"
                  tooltip="Maximum number of tokens in response"
                >
                  <InputNumber
                    min={1}
                    max={4096}
                    style={{ width: '100%' }}
                    placeholder="512"
                  />
                </Form.Item>
              </Col>
            </Row>
          </Panel>
        </Collapse>
      </Card>

      {/* 检索设置 */}
      <Card title={t('retrieval.settings', 'Retrieval Settings')} size="small" style={{ marginBottom: 16 }}>
        <Row gutter={[16, 0]}>
          <Col xs={24} sm={12}>
            <Form.Item
              name="vector_similarity_weight"
              label="Vector Similarity Weight"
              tooltip="Weight for vector similarity in hybrid search"
            >
              <InputNumber
                min={0}
                max={1}
                step={0.1}
                style={{ width: '100%' }}
                placeholder="0.3"
              />
            </Form.Item>
          </Col>
          <Col xs={24} sm={12}>
            <Form.Item
              name="similarity_threshold"
              label="Similarity Threshold"
              tooltip="Minimum similarity score for retrieval results"
            >
              <InputNumber
                min={0}
                max={1}
                step={0.1}
                style={{ width: '100%' }}
                placeholder="0.2"
              />
            </Form.Item>
          </Col>
        </Row>
      </Card>

      {/* Prompt设置 */}
      <Card title={t('dialog.promptConfiguration')} size="small" style={{ marginBottom: 16 }}>
        <Form.Item
          name="system_prompt"
          label="System Prompt"
          tooltip="System instructions that define the assistant's behavior"
          initialValue={getDefaultSystemPrompt()}
        >
          <TextArea
            rows={6}
            placeholder="Enter system prompt to define the assistant's behavior..."
          />
        </Form.Item>

        <Form.Item
          name="prologue"
          label="Prologue"
          tooltip="Opening message shown to users"
        >
          <TextArea
            rows={2}
            placeholder="Enter prologue message (optional)"
          />
        </Form.Item>

        <Row gutter={[16, 0]}>
          <Col xs={24} sm={12}>
            <Form.Item
              name="empty_response"
              label="Empty Response"
              tooltip="Default response when no relevant information is found"
            >
              <Input placeholder="Sorry, I don't know." />
            </Form.Item>
          </Col>
          <Col xs={24} sm={12}>
            <Form.Item
              name="tts"
              label="Text-to-Speech"
              valuePropName="checked"
              tooltip="Enable text-to-speech for responses"
            >
              <Switch />
            </Form.Item>
          </Col>
        </Row>
      </Card>
    </Form>
  );
};

export default DialogForm;
