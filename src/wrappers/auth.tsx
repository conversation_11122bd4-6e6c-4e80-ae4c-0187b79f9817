import { useAuth } from '@/hooks/auth-hooks';
import { Spin } from 'antd';
import { useEffect } from 'react';
import { Navigate, useLocation } from 'umi';

interface AuthWrapperProps {
  children: React.ReactNode;
}

const AuthWrapper: React.FC<AuthWrapperProps> = ({ children }) => {
  const { isLogin } = useAuth();
  const location = useLocation();

  useEffect(() => {
    // If not logged in and not on login page, redirect to login
    if (!isLogin && location.pathname !== '/login') {
      // Store the intended destination
      sessionStorage.setItem('redirectAfterLogin', location.pathname + location.search);
    }
  }, [isLogin, location]);

  if (!isLogin) {
    return <Navigate to="/login" replace />;
  }

  return <>{children}</>;
};

export default AuthWrapper;
