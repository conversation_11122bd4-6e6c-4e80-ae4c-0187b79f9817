.dialogsPage {
  padding: 0;
  background: #f5f5f5;
  min-height: 100vh;
}

.pageHeader {
  background: #fff;
  border-bottom: 1px solid #e8e8e8;
  padding: 24px 32px;
  margin-bottom: 24px;
}

.headerContent {
  display: flex;
  justify-content: space-between;
  align-items: center;
  max-width: 1400px;
  margin: 0 auto;
}

.headerLeft {
  .ant-typography {
    margin-bottom: 8px;
  }
}

.headerRight {
  .ant-input-search {
    .ant-input {
      border-radius: 8px;
    }
  }
  
  .ant-btn-primary {
    border-radius: 8px;
    height: 40px;
    font-weight: 500;
  }
}

.dialogsContent {
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 32px;

  .ant-card {
    border-radius: 12px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
    border: 1px solid #e8e8e8;
  }
}

.tableHeader {
  margin-bottom: 16px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

// Table样式优化
.ant-table {
  .ant-table-thead > tr > th {
    background: #fafafa;
    border-bottom: 2px solid #e8e8e8;
    font-weight: 600;
    color: #1f2937;
  }

  .ant-table-tbody > tr {
    &:hover > td {
      background: #f0f9ff;
    }
  }

  .ant-table-tbody > tr > td {
    border-bottom: 1px solid #f0f0f0;
    padding: 16px;
  }
}

// Tag样式优化
.ant-tag {
  border-radius: 12px;
  font-size: 11px;
  padding: 2px 8px;
  border: none;
  font-weight: 500;

  &.ant-tag-blue {
    background: #e6f7ff;
    color: #1890ff;
  }

  &.ant-tag-green {
    background: #f6ffed;
    color: #52c41a;
  }
}

// 按钮样式优化
.ant-btn {
  border-radius: 6px;
  font-weight: 500;

  &.ant-btn-text {
    &:hover {
      background: #f0f9ff;
    }

    &.ant-btn-dangerous:hover {
      background: #fef2f2;
    }
  }
}

// 模态框样式优化
.ant-modal {
  .ant-modal-header {
    border-bottom: 1px solid #f0f0f0;
    padding: 20px 24px 16px;

    .ant-modal-title {
      font-size: 18px;
      font-weight: 600;
      color: #1f2937;
    }
  }

  .ant-modal-body {
    padding: 24px;
  }

  .ant-modal-footer {
    border-top: 1px solid #f0f0f0;
    padding: 16px 24px;

    .ant-btn {
      border-radius: 6px;
      font-weight: 500;
    }
  }
}

// 表单样式优化
.ant-form {
  .ant-form-item-label {
    padding-bottom: 8px;

    > label {
      font-weight: 500;
      color: #374151;
    }
  }

  .ant-input,
  .ant-input-number,
  .ant-select-selector {
    border-radius: 6px;
    border-color: #d1d5db;

    &:hover {
      border-color: #1890ff;
    }

    &:focus,
    &.ant-input-focused,
    &.ant-select-focused .ant-select-selector {
      border-color: #1890ff;
      box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.1);
    }
  }

  .ant-input {
    padding: 8px 12px;
  }

  textarea.ant-input {
    padding: 8px 12px;
    line-height: 1.5;
  }
}

// 响应式设计
@media (max-width: 1200px) {
  .dialogsContent {
    padding: 0 24px;
  }
}

@media (max-width: 768px) {
  .dialogsPage {
    padding: 0;
  }

  .pageHeader {
    padding: 16px 20px;
    margin-bottom: 16px;
  }

  .headerContent {
    flex-direction: column;
    gap: 16px;
    align-items: stretch;
  }

  .headerRight {
    .ant-space {
      width: 100%;
      justify-content: space-between;

      .ant-input-search {
        flex: 1;
        margin-right: 12px;
      }
    }
  }

  .dialogsContent {
    padding: 0 20px;
  }

  .tableHeader {
    flex-direction: column;
    gap: 12px;
    align-items: stretch;
  }

  // 移动端表格优化
  .ant-table {
    .ant-table-thead > tr > th,
    .ant-table-tbody > tr > td {
      padding: 8px;
      font-size: 12px;
    }

    // 隐藏部分列在小屏幕上
    .ant-table-thead > tr > th:nth-child(3),
    .ant-table-tbody > tr > td:nth-child(3),
    .ant-table-thead > tr > th:nth-child(4),
    .ant-table-tbody > tr > td:nth-child(4),
    .ant-table-thead > tr > th:nth-child(5),
    .ant-table-tbody > tr > td:nth-child(5) {
      display: none;
    }
  }
}

// 工具提示样式
.ant-tooltip {
  .ant-tooltip-inner {
    border-radius: 6px;
    font-size: 12px;
    line-height: 1.4;
  }
}

// 选择器样式优化
.ant-select {
  .ant-select-selector {
    .ant-select-selection-item {
      line-height: 30px;
    }

    .ant-select-selection-placeholder {
      line-height: 30px;
      color: #bfbfbf;
    }
  }
}

// 分页样式优化
.ant-pagination {
  .ant-pagination-item {
    border-radius: 6px;
  }

  .ant-pagination-item-active {
    border-color: #1890ff;
    background: #1890ff;

    a {
      color: #fff;
    }
  }

  .ant-pagination-prev,
  .ant-pagination-next {
    border-radius: 6px;
  }
}

// 紧凑型头像上传组件样式 - 调整为32px
.compact-upload {
  .ant-upload-select {
    height: 32px !important;
    width: 32px !important;
    min-height: 32px !important;
    min-width: 32px !important;
  }

  .ant-upload-list-picture-card-container {
    height: 32px !important;
    width: 32px !important;
  }

  .ant-upload-list-picture-card {
    height: 32px !important;
    width: 32px !important;
  }
}
