import React, { useEffect, useState } from 'react';
import { 
  Card, 
  Typography, 
  Spin, 
  Alert, 
  Button, 
  Space, 
  Descriptions, 
  Tag, 
  Divider,
  Row,
  Col,
  Switch,
  Slider,
  Table,
  Empty
} from 'antd';
import {
  ArrowLeftOutlined,
  EditOutlined,
  MessageOutlined,
  SettingOutlined,
  RobotOutlined,
  DatabaseOutlined
} from '@ant-design/icons';
import { useNavigate, useParams } from 'umi';
import AppLayout from '@/components/AppLayout';
import { useTranslate } from '@/hooks/use-i18n';
import { useFetchDialogList } from '@/hooks/use-dialog-hooks';
import { useKnowledgeBaseOptions } from '@/hooks/knowledge-base-hooks';
import { useLLMOptions } from '@/hooks/llm-hooks';
import { IDialog } from '@/interfaces/dialog';

const { Title, Text, Paragraph } = Typography;

const DialogViewPage: React.FC = () => {
  const t = useTranslate();
  const navigate = useNavigate();
  const { id } = useParams<{ id: string }>();
  
  const [currentDialog, setCurrentDialog] = useState<IDialog | null>(null);
  
  // 获取Dialog列表来找到当前Dialog
  const { data: dialogs = [], isLoading } = useFetchDialogList({});
  
  // 获取知识库和LLM选项用于显示名称
  const { data: knowledgeBases = [] } = useKnowledgeBaseOptions();
  const { models: llmModels = [] } = useLLMOptions();

  useEffect(() => {
    console.log('🚀 Dialog View - ID from URL:', id);
    console.log('🚀 Dialog View - available dialogs:', dialogs);
    
    if (id && dialogs.length > 0) {
      const dialog = dialogs.find((d: IDialog) => d.id === id);
      console.log('🚀 Dialog View - found dialog:', dialog);
      setCurrentDialog(dialog || null);
    }
  }, [id, dialogs]);

  const handleBackToDialogs = () => {
    navigate('/dialogs');
  };

  const handleEditDialog = () => {
    if (currentDialog) {
      navigate(`/dialogs?edit=${currentDialog.id}`);
    }
  };

  const handleStartChat = () => {
    if (currentDialog) {
      navigate(`/chat?dialog_id=${currentDialog.id}`);
    }
  };

  // 获取知识库名称
  const getKnowledgeBaseNames = (kbIds: string[]) => {
    if (!kbIds || kbIds.length === 0) return [];
    return kbIds.map(id => {
      const kb = knowledgeBases.find((kb: any) => kb.id === id);
      return kb ? kb.name : id;
    });
  };

  // 获取LLM模型名称
  const getLLMModelName = (llmId: string) => {
    const model = llmModels.find(m => m.value === llmId);
    return model ? model.label : llmId;
  };

  if (isLoading) {
    return (
      <AppLayout>
        <div style={{ 
          display: 'flex', 
          justifyContent: 'center', 
          alignItems: 'center', 
          height: '50vh' 
        }}>
          <Spin size="large" />
        </div>
      </AppLayout>
    );
  }

  if (!id) {
    return (
      <AppLayout>
        <Card>
          <Alert
            message={t('dialog.invalidDialogId', 'Invalid Dialog ID')}
            description={t('dialog.noDialogIdProvided', 'No dialog ID provided in the URL.')}
            type="warning"
            showIcon
            action={
              <Button type="primary" onClick={handleBackToDialogs}>
                {t('dialog.goToDialogs', 'Go to Dialogs')}
              </Button>
            }
          />
        </Card>
      </AppLayout>
    );
  }

  if (!currentDialog) {
    return (
      <AppLayout>
        <Card>
          <Alert
            message={t('dialog.dialogNotFound', 'Dialog Not Found')}
            description={
              <div>
                <p>Dialog with ID "{id}" was not found.</p>
                <p>Available dialogs: {dialogs.length}</p>
                {dialogs.length > 0 && (
                  <details>
                    <summary>Debug Info</summary>
                    <pre>{JSON.stringify(dialogs.map(d => ({ 
                      id: d.id, 
                      dialog_id: d.dialog_id, 
                      name: d.name 
                    })), null, 2)}</pre>
                  </details>
                )}
              </div>
            }
            type="error"
            showIcon
            action={
              <Button type="primary" onClick={handleBackToDialogs}>
                Go to Dialogs
              </Button>
            }
          />
        </Card>
      </AppLayout>
    );
  }

  // 参数变量表格列
  const parameterColumns = [
    {
      title: 'Key',
      dataIndex: 'key',
      key: 'key',
    },
    {
      title: 'Optional',
      dataIndex: 'optional',
      key: 'optional',
      render: (optional: boolean) => (
        <Tag color={optional ? 'blue' : 'red'}>
          {optional ? 'Optional' : 'Required'}
        </Tag>
      ),
    },
  ];

  return (
    <AppLayout>
      <div style={{ padding: '24px' }}>
        {/* Header */}
        <div style={{ marginBottom: '24px' }}>
          <Space>
            <Button 
              icon={<ArrowLeftOutlined />} 
              onClick={handleBackToDialogs}
              type="text"
            >
              {t('dialog.backToDialogs', 'Back to Dialogs')}
            </Button>
          </Space>
          
          <div style={{ marginTop: '16px', display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start' }}>
            <div>
              <Title level={2} style={{ margin: 0, display: 'flex', alignItems: 'center' }}>
                <MessageOutlined style={{ marginRight: '8px' }} />
                {currentDialog.name}
              </Title>
              {currentDialog.description && (
                <Text type="secondary" style={{ fontSize: '16px' }}>
                  {currentDialog.description}
                </Text>
              )}
            </div>
            
            <Space>
              <Button 
                type="default" 
                icon={<EditOutlined />}
                onClick={handleEditDialog}
              >
                {t('dialog.editDialog', 'Edit Dialog')}
              </Button>
              <Button 
                type="primary" 
                icon={<MessageOutlined />}
                onClick={handleStartChat}
              >
                {t('dialog.startChat', 'Start Chat')}
              </Button>
            </Space>
          </div>
        </div>

        {/* Basic Information */}
        <Card title={<><SettingOutlined style={{ marginRight: '8px' }} />{t('dialog.basicInformation', 'Basic Information')}</>} style={{ marginBottom: '24px' }}>
          <Descriptions column={2} bordered>
            <Descriptions.Item label={t('dialog.assistantName', 'Assistant Name')}>{currentDialog.name}</Descriptions.Item>
            <Descriptions.Item label={t('dialog.language', 'Language')}>{currentDialog.language || t('languages.english', 'English')}</Descriptions.Item>
            <Descriptions.Item label={t('dialog.description', 'Description')} span={2}>
              {currentDialog.description || <Text type="secondary">{t('common.noDescription', 'No description')}</Text>}
            </Descriptions.Item>
            <Descriptions.Item label={t('common.status', 'Status')}>
              <Tag color="green">{t('common.active', 'Active')}</Tag>
            </Descriptions.Item>
            <Descriptions.Item label={t('common.created', 'Created')}>
              {currentDialog.create_time ? new Date(currentDialog.create_time).toLocaleString() : t('common.unknown', 'Unknown')}
            </Descriptions.Item>
          </Descriptions>
        </Card>

        {/* Model Configuration */}
        <Card title={<><RobotOutlined style={{ marginRight: '8px' }} />Model Configuration</>} style={{ marginBottom: '24px' }}>
          <Descriptions column={2} bordered>
            <Descriptions.Item label="LLM Model" span={2}>
              {currentDialog.llm_id ? getLLMModelName(currentDialog.llm_id) : <Text type="secondary">Not configured</Text>}
            </Descriptions.Item>
            {currentDialog.llm_setting && (
              <>
                <Descriptions.Item label="Temperature">
                  {currentDialog.llm_setting.temperature ?? 'Not set'}
                </Descriptions.Item>
                <Descriptions.Item label="Top P">
                  {currentDialog.llm_setting.top_p ?? 'Not set'}
                </Descriptions.Item>
                <Descriptions.Item label="Presence Penalty">
                  {currentDialog.llm_setting.presence_penalty ?? 'Not set'}
                </Descriptions.Item>
                <Descriptions.Item label="Frequency Penalty">
                  {currentDialog.llm_setting.frequency_penalty ?? 'Not set'}
                </Descriptions.Item>
                <Descriptions.Item label="Max Tokens" span={2}>
                  {currentDialog.llm_setting.max_tokens ?? 'Not set'}
                </Descriptions.Item>
              </>
            )}
          </Descriptions>
        </Card>

        {/* Knowledge Base Configuration */}
        <Card title={<><DatabaseOutlined style={{ marginRight: '8px' }} />Knowledge Base Configuration</>} style={{ marginBottom: '24px' }}>
          <Descriptions column={2} bordered>
            <Descriptions.Item label="Knowledge Bases" span={2}>
              {currentDialog.kb_ids && currentDialog.kb_ids.length > 0 ? (
                <Space wrap>
                  {getKnowledgeBaseNames(currentDialog.kb_ids).map((name, index) => (
                    <Tag key={index} color="blue">{name}</Tag>
                  ))}
                </Space>
              ) : (
                <Text type="secondary">No knowledge bases selected</Text>
              )}
            </Descriptions.Item>
            <Descriptions.Item label="Similarity Threshold">
              {currentDialog.similarity_threshold ?? 'Not set'}
            </Descriptions.Item>
            <Descriptions.Item label="Vector Similarity Weight">
              {currentDialog.vector_similarity_weight ?? 'Not set'}
            </Descriptions.Item>
            <Descriptions.Item label="Top N">
              {currentDialog.top_n ?? 'Not set'}
            </Descriptions.Item>
            <Descriptions.Item label="Rerank Model">
              {currentDialog.rerank_id || <Text type="secondary">Not configured</Text>}
            </Descriptions.Item>
            {currentDialog.rerank_id && (
              <Descriptions.Item label="Top K">
                {currentDialog.top_k ?? 'Not set'}
              </Descriptions.Item>
            )}
          </Descriptions>
        </Card>

        {/* Prompt Configuration */}
        <Card title="Prompt Configuration" style={{ marginBottom: '24px' }}>
          {currentDialog.prompt_config ? (
            <>
              <Descriptions column={2} bordered style={{ marginBottom: '16px' }}>
                <Descriptions.Item label="Quote">
                  <Switch checked={currentDialog.prompt_config.quote} disabled />
                </Descriptions.Item>
                <Descriptions.Item label="Keyword">
                  <Switch checked={currentDialog.prompt_config.keyword} disabled />
                </Descriptions.Item>
                <Descriptions.Item label="TTS">
                  <Switch checked={currentDialog.prompt_config.tts} disabled />
                </Descriptions.Item>
                <Descriptions.Item label="Multi Turn">
                  <Switch checked={currentDialog.prompt_config.refine_multiturn} disabled />
                </Descriptions.Item>
                <Descriptions.Item label="Reasoning">
                  <Switch checked={currentDialog.prompt_config.reasoning} disabled />
                </Descriptions.Item>
                <Descriptions.Item label="Empty Response">
                  {currentDialog.prompt_config.empty_response || <Text type="secondary">Not set</Text>}
                </Descriptions.Item>
              </Descriptions>

              <Divider>System Prompt</Divider>
              <Paragraph>
                <pre style={{ 
                  whiteSpace: 'pre-wrap', 
                  background: '#f5f5f5', 
                  padding: '12px', 
                  borderRadius: '4px',
                  fontSize: '14px'
                }}>
                  {currentDialog.prompt_config.system || 'No system prompt configured'}
                </pre>
              </Paragraph>

              {currentDialog.prompt_config.prologue && (
                <>
                  <Divider>{t('dialog.prologue', 'Prologue')}</Divider>
                  <Paragraph>
                    <pre style={{ 
                      whiteSpace: 'pre-wrap', 
                      background: '#f5f5f5', 
                      padding: '12px', 
                      borderRadius: '4px',
                      fontSize: '14px'
                    }}>
                      {currentDialog.prompt_config.prologue}
                    </pre>
                  </Paragraph>
                </>
              )}

              {currentDialog.prompt_config.parameters && currentDialog.prompt_config.parameters.length > 0 && (
                <>
                  <Divider>{t('dialog.parameters', 'Parameters')}</Divider>
                  <Table
                    dataSource={currentDialog.prompt_config.parameters}
                    columns={parameterColumns}
                    rowKey="key"
                    pagination={false}
                    size="small"
                  />
                </>
              )}
            </>
          ) : (
            <Empty description={t('dialog.noPromptConfiguration', 'No prompt configuration')} />
          )}
        </Card>
      </div>
    </AppLayout>
  );
};

export default DialogViewPage;
