import React, { useState } from 'react';
import {
  Card,
  Button,
  Table,
  Space,
  Input,
  Modal,
  Form,
  message,
  Popconfirm,
  Tag,
  Typography,
  Tooltip,
} from 'antd';
import {
  PlusOutlined,
  EditOutlined,
  DeleteOutlined,
  EyeOutlined,
  MessageOutlined,
} from '@ant-design/icons';
import { useNavigate } from 'react-router-dom';
import type { ColumnsType } from 'antd/es/table';
import {
  useFetchDialogList,
  useSetDialog,
  useDeleteDialog,
  useBatchDeleteDialogs,
} from '@/hooks/use-dialog-hooks';
import { IDialog, IDialogParams } from '@/interfaces/dialog';
import AppLayout from '@/components/AppLayout';
import StandardDialogForm from '@/components/DialogForm/standard-dialog-form';
import { useTranslate } from '@/hooks/use-i18n';
import styles from './index.less';

const { Title, Text } = Typography;
const { Search } = Input;

const DialogsContent: React.FC = () => {
  const t = useTranslate();
  const navigate = useNavigate();
  const [form] = Form.useForm();
  const [searchKeywords, setSearchKeywords] = useState('');
  const [modalVisible, setModalVisible] = useState(false);
  const [editingDialog, setEditingDialog] = useState<IDialog | null>(null);
  const [selectedRowKeys, setSelectedRowKeys] = useState<React.Key[]>([]);

  // 获取Dialog列表
  const { data: dialogs = [], isLoading: loading, refetch } = useFetchDialogList({
    keywords: searchKeywords,
    page_size: 100,
  });

  // 创建/更新Dialog
  const { mutateAsync: setDialog, isPending: saving } = useSetDialog();

  // 删除Dialog
  const { mutateAsync: deleteDialog } = useDeleteDialog();

  // 批量删除Dialog
  const { mutateAsync: batchDeleteDialogs } = useBatchDeleteDialogs();

  const handleCreateDialog = () => {
    setEditingDialog(null);
    form.resetFields();
    setModalVisible(true);
  };

  const handleEditDialog = (dialog: IDialog) => {
    setEditingDialog(dialog);
    setModalVisible(true);
  };

  const handleSaveDialog = async (params: IDialog) => {
    try {
      await setDialog(params);
      setModalVisible(false);
      form.resetFields();
      setEditingDialog(null);
    } catch (error) {
      console.error('Save dialog failed:', error);
    }
  };

  const handleDeleteDialog = async (dialogId: string) => {
    await deleteDialog(dialogId);
  };

  const handleBatchDelete = async () => {
    if (selectedRowKeys.length === 0) {
      message.warning(t('dialog.selectDialogsToDelete', 'Please select dialogs to delete'));
      return;
    }

    Modal.confirm({
      title: t('dialog.deleteDialogs', 'Delete Dialogs'),
      content: t('dialog.batchDeleteConfirm', `Are you sure you want to delete ${selectedRowKeys.length} dialog(s)?`, { count: selectedRowKeys.length }),
      okText: t('common.delete', 'Delete'),
      okType: 'danger',
      onOk: async () => {
        await batchDeleteDialogs(selectedRowKeys as string[]);
        setSelectedRowKeys([]);
      },
    });
  };

  const handleViewDialog = (dialog: IDialog) => {
    console.log('🚀 handleViewDialog - dialog:', dialog);
    console.log('🚀 handleViewDialog - dialog.id:', dialog.id);
    console.log('🚀 handleViewDialog - dialog.dialog_id:', dialog.dialog_id);
    navigate(`/dialogs/${dialog.id}/view`);
  };

  const handleChatDialog = (dialog: IDialog) => {
    console.log('🚀 handleChatDialog - dialog:', dialog);
    console.log('🚀 handleChatDialog - dialog.id:', dialog.id);
    console.log('🚀 handleChatDialog - dialog.dialog_id:', dialog.dialog_id);
    navigate(`/chat?dialog_id=${dialog.id}`);
  };

  const columns: ColumnsType<IDialog> = [
    {
      title: t('dialog.dialogName'),
      dataIndex: 'name',
      key: 'name',
      render: (text: string, record: IDialog) => (
        <div>
          <Text strong>{text}</Text>
          {record.description && (
            <div>
              <Text type="secondary" style={{ fontSize: '12px' }}>
                {record.description}
              </Text>
            </div>
          )}
        </div>
      ),
    },
    {
      title: t('dialog.language'),
      dataIndex: 'language',
      key: 'language',
      width: 100,
      render: (language: string) => (
        <Tag color="blue">{language?.toUpperCase() || 'EN'}</Tag>
      ),
    },
    {
      title: t('dialog.knowledgeBases', 'Knowledge Bases'),
      dataIndex: 'kb_names',
      key: 'kb_names',
      width: 200,
      render: (kbNames: string[]) => (
        <div>
          {kbNames?.slice(0, 2).map((name, index) => (
            <Tag key={index} style={{ marginBottom: 4 }}>
              {name}
            </Tag>
          ))}
          {kbNames?.length > 2 && (
            <Tooltip title={kbNames.slice(2).join(', ')}>
              <Tag>+{kbNames.length - 2} {t('common.more', 'more')}</Tag>
            </Tooltip>
          )}
        </div>
      ),
    },
    {
      title: t('dialog.llmModel', 'LLM Model'),
      dataIndex: 'llm_id',
      key: 'llm_id',
      width: 150,
      render: (llmId: string) => (
        <Text code style={{ fontSize: '12px' }}>
          {llmId || t('dialog.notSet', 'Not set')}
        </Text>
      ),
    },
    // 状态列已隐藏 - 根据用户要求不显示
    // {
    //   title: t('common.status', 'Status'),
    //   dataIndex: 'status',
    //   key: 'status',
    //   width: 100,
    //   render: (status: string) => (
    //     <Tag color={status === 'active' ? 'green' : 'default'}>
    //       {status || 'active'}
    //     </Tag>
    //   ),
    // },
    {
      title: t('common.created', 'Created'),
      dataIndex: 'create_date',
      key: 'create_date',
      width: 120,
      render: (date: string) => (
        <Text style={{ fontSize: '12px' }}>
          {new Date(date).toLocaleDateString()}
        </Text>
      ),
    },
    {
      title: t('common.actions', 'Actions'),
      key: 'actions',
      width: 200,
      render: (_, record: IDialog) => (
        <Space size="small">
          <Tooltip title={t('common.view')}>
            <Button
              type="text"
              icon={<EyeOutlined />}
              onClick={() => handleViewDialog(record)}
            />
          </Tooltip>
          <Tooltip title={t('dialog.startChat', 'Start Chat')}>
            <Button
              type="text"
              icon={<MessageOutlined />}
              onClick={() => handleChatDialog(record)}
            />
          </Tooltip>
          <Tooltip title={t('common.edit')}>
            <Button
              type="text"
              icon={<EditOutlined />}
              onClick={() => handleEditDialog(record)}
            />
          </Tooltip>
          <Popconfirm
            title={t('dialog.deleteConfirm')}
            description={t('dialog.deleteConfirm')}
            onConfirm={() => handleDeleteDialog(record.id)}
            okText={t('common.delete')}
            okType="danger"
          >
            <Tooltip title={t('common.delete')}>
              <Button type="text" danger icon={<DeleteOutlined />} />
            </Tooltip>
          </Popconfirm>
        </Space>
      ),
    },
  ];

  const rowSelection = {
    selectedRowKeys,
    onChange: (newSelectedRowKeys: React.Key[]) => {
      setSelectedRowKeys(newSelectedRowKeys);
    },
  };

  return (
    <div className={styles.dialogsPage}>
      <div className={styles.pageHeader}>
        <div className={styles.headerContent}>
          <div className={styles.headerLeft}>
            <Title level={2} style={{ margin: 0 }}>
              {t('dialog.title')}
            </Title>
            <Text type="secondary">
              {t('dialog.manageDescription', 'Manage your chat dialogs and configurations')}
            </Text>
          </div>
          <div className={styles.headerRight}>
            <Space>
              <Search
                placeholder={t('dialog.searchPlaceholder', 'Search dialogs...')}
                allowClear
                style={{ width: 300 }}
                value={searchKeywords}
                onChange={(e) => setSearchKeywords(e.target.value)}
                loading={loading}
              />
              <Button
                type="primary"
                icon={<PlusOutlined />}
                onClick={handleCreateDialog}
                size="large"
              >
                {t('dialog.createDialog')}
              </Button>
            </Space>
          </div>
        </div>
      </div>

      <div className={styles.dialogsContent}>
        <Card>
          <div className={styles.tableHeader}>
            <Space>
              {selectedRowKeys.length > 0 && (
                <Button
                  danger
                  onClick={handleBatchDelete}
                  icon={<DeleteOutlined />}
                >
                  {t('dialog.deleteSelected', 'Delete Selected')} ({selectedRowKeys.length})
                </Button>
              )}
            </Space>
          </div>

          <Table
            rowSelection={rowSelection}
            columns={columns}
            dataSource={dialogs}
            rowKey="id"
            loading={loading}
            pagination={{
              total: dialogs.length,
              pageSize: 20,
              showSizeChanger: true,
              showQuickJumper: true,
              showTotal: (total, range) =>
                t('dialog.dialogsRange', '{{start}}-{{end}} of {{total}} dialogs', {
                  start: range[0],
                  end: range[1],
                  total
                }),
            }}
          />
        </Card>
      </div>

      {/* Create/Edit Dialog Modal */}
      <Modal
        title={editingDialog ? t('dialog.editDialog', 'Edit Dialog') : t('dialog.createDialog')}
        open={modalVisible}
        onCancel={() => {
          setModalVisible(false);
          form.resetFields();
          setEditingDialog(null);
        }}
        footer={[
          <Button
            key="cancel"
            onClick={() => {
              setModalVisible(false);
              form.resetFields();
              setEditingDialog(null);
            }}
          >
            {t('common.cancel', 'Cancel')}
          </Button>,
          <Button
            key="submit"
            type="primary"
            loading={saving}
            onClick={() => form.submit()}
          >
            {editingDialog ? t('common.update', 'Update') : t('common.create', 'Create')}
          </Button>,
        ]}
        width={900}
        style={{ top: 20 }}
        bodyStyle={{ maxHeight: '70vh', overflowY: 'auto' }}
      >
        <StandardDialogForm
          form={form}
          editingDialog={editingDialog}
          onFinish={handleSaveDialog}
        />
      </Modal>
    </div>
  );
};

const Dialogs = () => {
  return (
    <AppLayout>
      <DialogsContent />
    </AppLayout>
  );
};

export default Dialogs;
