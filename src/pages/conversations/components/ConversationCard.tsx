import React from 'react';
import {
  <PERSON>,
  Button,
  Space,
  Typography,
  Tag,
  Tooltip,
  Popconfirm,
  Avatar,
} from 'antd';
import {
  MessageOutlined,
  DeleteOutlined,
  EyeOutlined,
  ClockCircleOutlined,
  UserOutlined,
  RobotOutlined,
} from '@ant-design/icons';
import { useNavigate } from 'umi';
import { IConversation } from '@/interfaces/chat';
import { formatRelativeTime } from '@/utils/date';
import './ConversationCard.less';

const { Text, Title } = Typography;

interface ConversationCardProps {
  conversation: IConversation & { dialog_name?: string };
  onDelete?: (conversationId: string) => void;
}

const ConversationCard: React.FC<ConversationCardProps> = ({
  conversation,
  onDelete,
}) => {
  const navigate = useNavigate();

  const handleViewConversation = () => {
    // 导航到聊天页面查看对话
    navigate(`/chat?dialog_id=${conversation.dialog_id}&conversation_id=${conversation.id}`);
  };

  const handleDeleteConversation = () => {
    if (onDelete) {
      onDelete(conversation.id);
    }
  };

  // 获取最后一条消息
  const lastMessage = conversation.message && conversation.message.length > 0 
    ? conversation.message[conversation.message.length - 1] 
    : null;

  // 计算消息统计
  const messageCount = conversation.message?.length || 0;
  const userMessageCount = conversation.message?.filter(m => m.role === 'user').length || 0;
  const assistantMessageCount = conversation.message?.filter(m => m.role === 'assistant').length || 0;

  return (
    <Card
      hoverable
      className="conversation-card"
      actions={[
        <Tooltip title="View Conversation" key="view">
          <Button 
            type="text" 
            icon={<EyeOutlined />} 
            onClick={handleViewConversation}
          />
        </Tooltip>,
        <Tooltip title="Continue Chat" key="chat">
          <Button 
            type="text" 
            icon={<MessageOutlined />} 
            onClick={handleViewConversation}
          />
        </Tooltip>,
        <Popconfirm
          title="Delete Conversation"
          description="Are you sure you want to delete this conversation? This action cannot be undone."
          onConfirm={handleDeleteConversation}
          okText="Delete"
          cancelText="Cancel"
          okButtonProps={{ danger: true }}
          key="delete"
          overlayClassName="centered-popconfirm"
          overlayStyle={{
            maxWidth: '320px'
          }}
        >
          <Tooltip title="Delete Conversation">
            <Button 
              type="text" 
              icon={<DeleteOutlined />} 
              danger
            />
          </Tooltip>
        </Popconfirm>,
      ]}
    >
      <div style={{ flex: 1, display: 'flex', flexDirection: 'column' }}>
        {/* 对话标题 */}
        <div style={{ marginBottom: '8px' }}>
          <Title level={5} style={{ margin: 0, fontSize: '15px', lineHeight: '1.3' }} ellipsis={{ rows: 1 }}>
            {conversation.name || 'Untitled Conversation'}
          </Title>
        </div>

        {/* 消息统计 */}
        <div style={{ marginBottom: '8px' }}>
          <Space size="small">
            <Tag icon={<UserOutlined />} color="blue">
              {userMessageCount}
            </Tag>
            <Tag icon={<RobotOutlined />} color="green">
              {assistantMessageCount}
            </Tag>
            {messageCount > 0 && (
              <Tag color="default">
                {messageCount} total
              </Tag>
            )}
          </Space>
        </div>

        {/* 最后一条消息预览 */}
        {lastMessage && (
          <div style={{ marginBottom: '8px', flex: 1 }}>
            <div style={{ display: 'flex', alignItems: 'center', marginBottom: '2px' }}>
              <Avatar
                size={20}
                icon={lastMessage.role === 'user' ? <UserOutlined /> : <RobotOutlined />}
                style={{
                  backgroundColor: lastMessage.role === 'user' ? '#1890ff' : '#52c41a',
                  marginRight: '6px'
                }}
              />
              <Text type="secondary" style={{ fontSize: '11px' }}>
                {lastMessage.role === 'user' ? 'You' : 'Assistant'}
              </Text>
            </div>
            <Text
              type="secondary"
              style={{
                fontSize: '12px',
                display: 'block',
                lineHeight: '1.3'
              }}
              ellipsis={{ rows: 2 }}
            >
              {lastMessage.content}
            </Text>
          </div>
        )}

        {/* 时间信息 */}
        <div style={{ marginTop: 'auto' }}>
          <Space size="small">
            <ClockCircleOutlined style={{ color: '#999', fontSize: '12px' }} />
            <Text type="secondary" style={{ fontSize: '12px' }}>
              {formatRelativeTime(conversation.update_time)}
            </Text>
          </Space>
        </div>
      </div>
    </Card>
  );
};

export default ConversationCard;
