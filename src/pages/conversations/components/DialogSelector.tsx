import React, { useState } from 'react';
import {
  Modal,
  List,
  Avatar,
  Typography,
  Button,
  Input,
  Empty,
  Space,
  Tag,
} from 'antd';
import {
  MessageOutlined,
  SearchOutlined,
  RobotOutlined,
  BookOutlined,
} from '@ant-design/icons';
import { IDialog } from '@/interfaces/dialog';
import { formatRelativeTime } from '@/utils/date';
import { useTranslate } from '@/hooks/use-i18n';

const { Title, Text } = Typography;
const { Search } = Input;

interface DialogSelectorProps {
  visible: boolean;
  dialogs: IDialog[];
  onSelect: (dialog: IDialog) => void;
  onCancel: () => void;
  loading?: boolean;
}

const DialogSelector: React.FC<DialogSelectorProps> = ({
  visible,
  dialogs,
  onSelect,
  onCancel,
  loading = false,
}) => {
  const t = useTranslate();
  const [searchKeywords, setSearchKeywords] = useState('');

  // 过滤dialogs
  const filteredDialogs = dialogs.filter(dialog =>
    dialog.name?.toLowerCase().includes(searchKeywords.toLowerCase()) ||
    dialog.description?.toLowerCase().includes(searchKeywords.toLowerCase())
  );

  const handleSelectDialog = (dialog: IDialog) => {
    onSelect(dialog);
    setSearchKeywords(''); // 清空搜索
  };

  const handleCancel = () => {
    onCancel();
    setSearchKeywords(''); // 清空搜索
  };

  return (
    <Modal
      title={
        <div style={{ display: 'flex', alignItems: 'center' }}>
          <MessageOutlined style={{ marginRight: '8px', color: '#1890ff' }} />
          {t('conversations.selectDialogToStart', 'Select a Dialog to Start Conversation')}
        </div>
      }
      open={visible}
      onCancel={handleCancel}
      footer={null}
      width={600}
      bodyStyle={{ padding: '20px' }}
    >
      {/* 搜索框 */}
      <div style={{ marginBottom: '16px' }}>
        <Search
          placeholder={t('conversations.searchDialogsPlaceholder', 'Search dialogs by name or description...')}
          allowClear
          value={searchKeywords}
          onChange={(e) => setSearchKeywords(e.target.value)}
          prefix={<SearchOutlined />}
          style={{ width: '100%' }}
        />
      </div>

      {/* Dialog列表 */}
      {filteredDialogs.length === 0 ? (
        <Empty
          image={Empty.PRESENTED_IMAGE_SIMPLE}
          description={
            searchKeywords ?
              t('conversations.noDialogsFoundFor', 'No dialogs found for "{{keywords}}"', { keywords: searchKeywords }) :
              t('conversations.noDialogsAvailable', 'No dialogs available')
          }
          style={{ padding: '40px 0' }}
        >
          {!searchKeywords && (
            <Button type="primary" onClick={handleCancel}>
              {t('dialog.createFirstDialog', 'Create Your First Dialog')}
            </Button>
          )}
        </Empty>
      ) : (
        <List
          dataSource={filteredDialogs}
          renderItem={(dialog) => (
            <List.Item
              style={{ 
                cursor: 'pointer',
                padding: '16px',
                borderRadius: '8px',
                marginBottom: '8px',
                border: '1px solid #f0f0f0',
                transition: 'all 0.2s',
              }}
              onMouseEnter={(e) => {
                e.currentTarget.style.backgroundColor = '#f5f5f5';
                e.currentTarget.style.borderColor = '#d9d9d9';
              }}
              onMouseLeave={(e) => {
                e.currentTarget.style.backgroundColor = 'transparent';
                e.currentTarget.style.borderColor = '#f0f0f0';
              }}
              onClick={() => handleSelectDialog(dialog)}
            >
              <List.Item.Meta
                avatar={
                  <Avatar 
                    size={48}
                    icon={<RobotOutlined />}
                    style={{ backgroundColor: '#1890ff' }}
                  />
                }
                title={
                  <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                    <Title level={5} style={{ margin: 0 }}>
                      {dialog.name}
                    </Title>
                    <Space>
                      <Tag icon={<BookOutlined />} color="blue">
                        {t('dialog.title', 'Dialog')}
                      </Tag>
                    </Space>
                  </div>
                }
                description={
                  <div>
                    {dialog.description && (
                      <Text type="secondary" style={{ display: 'block', marginBottom: '8px' }}>
                        {dialog.description}
                      </Text>
                    )}
                    <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                      <Text type="secondary" style={{ fontSize: '12px' }}>
                        {t('conversations.updated', 'Updated')} {formatRelativeTime(dialog.update_time)}
                      </Text>
                      <Button
                        type="primary"
                        size="small"
                        icon={<MessageOutlined />}
                        onClick={(e) => {
                          e.stopPropagation();
                          handleSelectDialog(dialog);
                        }}
                      >
                        {t('dialog.startChat', 'Start Chat')}
                      </Button>
                    </div>
                  </div>
                }
              />
            </List.Item>
          )}
          style={{ maxHeight: '400px', overflowY: 'auto' }}
        />
      )}

      {/* 底部操作 */}
      <div style={{ 
        marginTop: '20px', 
        paddingTop: '16px', 
        borderTop: '1px solid #f0f0f0',
        textAlign: 'center'
      }}>
        <Space>
          <Button onClick={handleCancel}>
            {t('common.cancel', 'Cancel')}
          </Button>
          <Button type="link" onClick={() => window.open('/dialogs', '_blank')}>
            {t('conversations.manageDialogs', 'Manage Dialogs')}
          </Button>
        </Space>
      </div>
    </Modal>
  );
};

export default DialogSelector;
