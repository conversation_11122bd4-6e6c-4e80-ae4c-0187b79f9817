// ConversationCard组件样式

.conversation-card {
  height: 180px;
  min-height: 160px;
  
  .ant-card-body {
    padding: 8px;
    height: calc(100% - 32px);
    display: flex;
    flex-direction: column;
    overflow: hidden;
  }
  
  .ant-card-actions {
    padding: 4px 6px;
    
    li {
      margin: 0 2px;
    }
  }
}

// 居中的Popconfirm样式
:global(.centered-popconfirm) {
  // 整体内容居中
  .ant-popover-inner-content {
    text-align: center;
    padding: 16px 20px;
  }

  // 按钮容器居中
  .ant-popover-buttons {
    display: flex !important;
    justify-content: center !important;
    align-items: center !important;
    gap: 12px !important;
    margin-top: 20px !important;
    text-align: center !important;

    .ant-btn {
      margin: 0 !important;
      min-width: 80px;
    }
  }

  // 消息标题居中
  .ant-popover-message {
    text-align: center;
    margin-bottom: 12px;

    .ant-popover-message-title {
      text-align: center;
      font-weight: 600;
      font-size: 16px;
      margin-bottom: 0;
      color: rgba(0, 0, 0, 0.88);
    }
  }

  // 描述文字居中
  .ant-popover-description {
    text-align: center;
    margin-bottom: 0;
    line-height: 1.6;
    color: rgba(0, 0, 0, 0.65);
    font-size: 14px;
  }

  // 确保箭头正确显示
  .ant-popover-arrow {
    display: block;
  }
}
