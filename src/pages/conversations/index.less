.conversationsPage {
  padding-bottom: 40px; // 添加底部间距，确保不遮挡内容

  .pageHeader {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 24px;
    padding: 0 24px;

    .headerContent {
      display: flex;
      justify-content: space-between;
      align-items: center;
      width: 100%;

      .headerLeft {
        .title {
          display: flex;
          align-items: center;
          margin: 0;
          
          .icon {
            margin-right: 12px;
            color: #1890ff;
          }
        }

        .description {
          margin-top: 4px;
          color: rgba(0, 0, 0, 0.65);
        }
      }

      .headerRight {
        display: flex;
        align-items: center;
        gap: 16px;

        .searchInput {
          width: 300px;
        }

        .createButton {
          height: 40px;
          padding: 0 24px;
        }
      }
    }
  }

  .contentCard {
    .loadingContainer {
      text-align: center;
      padding: 50px;

      .loadingText {
        margin-top: 16px;
        color: rgba(0, 0, 0, 0.65);
      }
    }

    .emptyContainer {
      padding: 50px;
    }

    .dialogGroups {
      .ant-collapse-item {
        margin-bottom: 16px;
        border-radius: 8px;
        border: 1px solid #f0f0f0;

        .ant-collapse-header {
          padding: 16px 20px;
          background: #fafafa;
          border-radius: 8px 8px 0 0;

          &:hover {
            background: #f5f5f5;
          }
        }

        .ant-collapse-content {
          border-radius: 0 0 8px 8px;

          .ant-collapse-content-box {
            padding: 20px;
          }
        }
      }
    }

    .conversationsGrid {
      .conversationCol {
        margin-bottom: 12px;

        .ant-card {
          height: 240px; // 确保卡片高度一致

          .ant-card-body {
            height: calc(100% - 48px); // 减去actions区域高度
            overflow: hidden; // 防止内容溢出
          }

          .ant-card-actions {
            padding: 8px 12px; // 减少actions区域的内边距

            li {
              margin: 0 4px; // 减少按钮间距
            }
          }
        }
      }
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .conversationsPage {
    .pageHeader {
      padding: 0 16px;

      .headerContent {
        flex-direction: column;
        align-items: stretch;
        gap: 16px;

        .headerRight {
          justify-content: space-between;

          .searchInput {
            width: 100%;
            max-width: 300px;
          }
        }
      }
    }
  }
}

@media (max-width: 576px) {
  .conversationsPage {
    .pageHeader {
      .headerContent {
        .headerRight {
          flex-direction: column;
          gap: 12px;

          .searchInput {
            width: 100%;
          }

          .createButton {
            width: 100%;
          }
        }
      }
    }
  }
}
