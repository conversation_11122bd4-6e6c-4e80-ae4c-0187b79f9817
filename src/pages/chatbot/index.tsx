import React, { useState, useCallback, useRef } from 'react';
import { Card, Row, Col, Typography, Space, Divider, message, Tabs } from 'antd';
import { RobotOutlined, MessageOutlined, SettingOutlined } from '@ant-design/icons';
import AppLayout from '@/components/AppLayout';
import ChatBot from '@/components/ChatBot';
import ChatHistoryList, { ChatHistoryListRef } from '@/components/ChatBot/ChatHistoryList';
import { useTranslate } from '@/hooks/use-i18n';
import { useChatBotStats, useChatBotHistory } from '@/hooks/use-chatbot-hooks';
import { ASSISTANTS, getDefaultAssistant, AssistantType } from '@/constants/assistants';
import styles from './index.less';

const { Title, Text, Paragraph } = Typography;

const ChatBotPage: React.FC = () => {
  const t = useTranslate();
  const { stats, isLoading, fetchStats } = useChatBotStats();
  const { getSessionDetails } = useChatBotHistory();

  // 状态管理
  const [selectedSessionId, setSelectedSessionId] = useState<string>('');
  const [chatBotKey, setChatBotKey] = useState<number>(0); // 用于强制重新渲染ChatBot组件
  const [currentAssistant, setCurrentAssistant] = useState<AssistantType>(getDefaultAssistant());
  const chatHistoryListRef = useRef<ChatHistoryListRef>(null);

  React.useEffect(() => {
    fetchStats();
  }, [fetchStats]);

  // 处理会话选择
  const handleSelectSession = useCallback((sessionId: string) => {
    if (!sessionId) {
      // 只有当前有选中会话时，才需要重置ChatBot组件
      if (selectedSessionId) {
        setSelectedSessionId('');
        setChatBotKey(prev => prev + 1); // 重置ChatBot组件
      }
      return;
    }

    // 如果选择的是不同的会话，才需要重新渲染
    if (sessionId !== selectedSessionId) {
      setSelectedSessionId(sessionId);
      setChatBotKey(prev => prev + 1); // 强制重新渲染ChatBot组件
    }
  }, [selectedSessionId]);

  // 刷新对话记录列表
  const handleRefreshSessions = useCallback(() => {
    if (chatHistoryListRef.current) {
      chatHistoryListRef.current.refreshSessions();
    }
  }, []);

  // 处理助手切换
  const handleAssistantChange = useCallback((assistantId: string) => {
    const assistant = ASSISTANTS.find(a => a.id === assistantId);
    if (assistant && assistant.id !== currentAssistant.id) {
      // 切换助手时初始化对话页面
      setSelectedSessionId(''); // 清空当前选中的会话
      setCurrentAssistant(assistant);
      setChatBotKey(prev => prev + 1); // 强制重新渲染ChatBot组件，这会重置所有状态
      message.success(t('chatbot.success.assistantSwitched'));
    }
  }, [currentAssistant.id, t]);

  return (
    <AppLayout>
      <div className={styles.chatBotPage}>
        <div className={styles.pageHeader}>
          <Space size="large">
            <RobotOutlined className={styles.headerIcon} />
            <div>
              <Title level={2} style={{ margin: 0 }}>
                {t('chatbot.title')}
              </Title>
              <Text type="secondary">{t('chatbot.description')}</Text>
            </div>
          </Space>
        </div>

        <Row gutter={[24, 24]} className={styles.content}>
          {/* 聊天机器人主界面 */}
          <Col xs={24} lg={16}>
            <Card
              title={
                <Space>
                  <MessageOutlined />
                  {t('chatbot.subtitle')}
                </Space>
              }
              className={styles.chatCard}
              tabList={ASSISTANTS.map(assistant => ({
                key: assistant.id,
                tab: (
                  <Space>
                    <span style={{ fontSize: '16px' }}>{assistant.icon}</span>
                    <span>{assistant.name}</span>
                  </Space>
                )
              }))}
              activeTabKey={currentAssistant.id}
              onTabChange={handleAssistantChange}
              tabProps={{
                size: 'small',
                type: 'card'
              }}
            >
              <ChatBot
                key={chatBotKey}
                height={600}
                enableStream={true}
                systemPrompt={currentAssistant.systemPrompt}
                selectedSessionId={selectedSessionId}
                onRefreshSessions={handleRefreshSessions}
              />
            </Card>
          </Col>

          {/* 对话记录列表 */}
          <Col xs={24} lg={8}>
            <ChatHistoryList
              ref={chatHistoryListRef}
              onSelectSession={handleSelectSession}
              selectedSessionId={selectedSessionId}
              style={{ height: 600 }}
            />
          </Col>
        </Row>
      </div>
    </AppLayout>
  );
};

export default ChatBotPage;
