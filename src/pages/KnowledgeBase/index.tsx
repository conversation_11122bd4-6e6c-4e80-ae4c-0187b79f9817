import React, { useState } from 'react';
import {
  Card,
  Button,
  Table,
  Space,
  Input,
  Modal,
  Form,
  message,
  Popconfirm,
  Tag,
  Typography,
  Row,
  Col,
  Avatar,
} from 'antd';
import {
  PlusOutlined,
  SearchOutlined,
  EditOutlined,
  DeleteOutlined,
  EyeOutlined,
  DatabaseOutlined,
  FileTextOutlined,
  MessageOutlined,
} from '@ant-design/icons';
import { useNavigate } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import {
  useKnowledgeBaseList,
  useCreateKnowledgeBase,
  useUpdateKnowledgeBase,
  useDeleteKnowledgeBase,
} from '@/hooks/useKnowledgeBase';
import { formatTime } from '@/utils';
import type { KnowledgeBase } from '@/services/api';
import type { ColumnsType } from 'antd/es/table';
import './index.less';
import './index.less';


const { Title, Text } = Typography;
const { Search } = Input;

const KnowledgeBasePage: React.FC = () => {
  const navigate = useNavigate();
  const { t } = useTranslation();
  const [searchText, setSearchText] = useState('');
  const [createModalVisible, setCreateModalVisible] = useState(false);
  const [editModalVisible, setEditModalVisible] = useState(false);
  const [editingKb, setEditingKb] = useState<KnowledgeBase | null>(null);
  const [form] = Form.useForm();

  // API Hooks
  const { data: kbData, isLoading } = useKnowledgeBaseList({
    keywords: searchText,
    page_size: 50,
  });
  const createKb = useCreateKnowledgeBase();
  const updateKb = useUpdateKnowledgeBase();
  const deleteKb = useDeleteKnowledgeBase();

  // 处理创建知识库
  const handleCreate = async (values: any) => {
    try {
      await createKb.mutateAsync(values);
      setCreateModalVisible(false);
      form.resetFields();
    } catch (error) {
      console.error('Create knowledge base failed:', error);
    }
  };

  // 处理编辑知识库
  const handleEdit = (kb: KnowledgeBase) => {
    setEditingKb(kb);
    form.setFieldsValue({
      name: kb.name,
      description: kb.description,
      language: kb.language,
    });
    setEditModalVisible(true);
  };

  // 处理更新知识库
  const handleUpdate = async (values: any) => {
    if (!editingKb) return;
    
    try {
      await updateKb.mutateAsync({
        kb_id: editingKb.id,
        ...values,
      });
      setEditModalVisible(false);
      setEditingKb(null);
      form.resetFields();
    } catch (error) {
      console.error('Update knowledge base failed:', error);
    }
  };

  // 处理删除知识库
  const handleDelete = async (kb_id: string) => {
    try {
      await deleteKb.mutateAsync(kb_id);
    } catch (error) {
      console.error('Delete knowledge base failed:', error);
    }
  };

  // 查看知识库详情
  const handleView = (kb: KnowledgeBase) => {
    navigate(`/documents/${kb.id}`);
  };

  // 表格列定义
  const columns: ColumnsType<KnowledgeBase> = [
    {
      title: t('knowledge.title', '知识库'),
      dataIndex: 'name',
      key: 'name',
      render: (text, record) => (
        <div className="kb-info">
          <Avatar
            size={40}
            icon={<DatabaseOutlined />}
            src={record.avatar}
            style={{ backgroundColor: '#1890ff' }}
          />
          <div className="kb-details">
            <div className="kb-name">{text}</div>
            <div className="kb-description">
              {record.description || t('common.noDescription', '暂无描述')}
            </div>
          </div>
        </div>
      ),
    },
    {
      title: t('common.language', '语言'),
      dataIndex: 'language',
      key: 'language',
      width: 100,
      render: (language) => (
        <Tag color={language === 'Chinese' ? 'blue' : 'green'}>
          {language === 'Chinese' ? t('languages.chinese', '中文') : language}
        </Tag>
      ),
    },
    {
      title: '文档数',
      dataIndex: 'doc_num',
      key: 'doc_num',
      width: 100,
      render: (num) => (
        <Space>
          <FileTextOutlined style={{ color: '#1890ff' }} />
          <span>{num}</span>
        </Space>
      ),
    },
    {
      title: '分块数',
      dataIndex: 'chunk_num',
      key: 'chunk_num',
      width: 100,
      render: (num) => (
        <Space>
          <MessageOutlined style={{ color: '#52c41a' }} />
          <span>{num}</span>
        </Space>
      ),
    },
    {
      title: '权限',
      dataIndex: 'permission',
      key: 'permission',
      width: 100,
      render: (permission) => (
        <Tag color={permission === 'me' ? 'orange' : 'blue'}>
          {permission === 'me' ? '私有' : '公开'}
        </Tag>
      ),
    },
    {
      title: '创建时间',
      dataIndex: 'create_time',
      key: 'create_time',
      width: 180,
      render: (time) => formatTime(time),
    },
    {
      title: '操作',
      key: 'action',
      width: 200,
      render: (_, record) => (
        <Space size="small">
          <Button
            type="link"
            size="small"
            icon={<EyeOutlined />}
            onClick={() => handleView(record)}
          >
            查看
          </Button>
          <Button
            type="link"
            size="small"
            icon={<EditOutlined />}
            onClick={() => handleEdit(record)}
          >
            编辑
          </Button>
          <Popconfirm
            title="确定要删除这个知识库吗？"
            description="删除后将无法恢复，请谨慎操作。"
            onConfirm={() => handleDelete(record.id)}
            okText="确定"
            cancelText="取消"
          >
            <Button
              type="link"
              size="small"
              danger
              icon={<DeleteOutlined />}
            >
              删除
            </Button>
          </Popconfirm>
        </Space>
      ),
    },
  ];

  return (
    <div className="knowledge-base-container">
      {/* 页面头部 */}
      <div className="page-header">
        <div className="header-content">
          <div className="header-left">
            <Title level={2} className="page-title">
              知识库管理
            </Title>
            <Text type="secondary">
              创建和管理您的知识库，构建智能问答系统
            </Text>
          </div>
          <div className="header-right">
            <Button
              type="primary"
              icon={<PlusOutlined />}
              onClick={() => setCreateModalVisible(true)}
              size="large"
            >
              创建知识库
            </Button>
          </div>
        </div>
      </div>

      {/* 统计卡片 */}
      <Row gutter={[24, 24]} className="stats-cards">
        <Col xs={24} sm={8}>
          <Card>
            <div className="stat-item">
              <div className="stat-icon" style={{ color: '#1890ff' }}>
                <DatabaseOutlined />
              </div>
              <div className="stat-content">
                <div className="stat-value">{kbData?.total || 0}</div>
                <div className="stat-label">知识库总数</div>
              </div>
            </div>
          </Card>
        </Col>
        <Col xs={24} sm={8}>
          <Card>
            <div className="stat-item">
              <div className="stat-icon" style={{ color: '#52c41a' }}>
                <FileTextOutlined />
              </div>
              <div className="stat-content">
                <div className="stat-value">
                  {kbData?.kbs.reduce((sum, kb) => sum + kb.doc_num, 0) || 0}
                </div>
                <div className="stat-label">文档总数</div>
              </div>
            </div>
          </Card>
        </Col>
        <Col xs={24} sm={8}>
          <Card>
            <div className="stat-item">
              <div className="stat-icon" style={{ color: '#722ed1' }}>
                <MessageOutlined />
              </div>
              <div className="stat-content">
                <div className="stat-value">
                  {kbData?.kbs.reduce((sum, kb) => sum + kb.chunk_num, 0) || 0}
                </div>
                <div className="stat-label">分块总数</div>
              </div>
            </div>
          </Card>
        </Col>
      </Row>

      {/* 主内容区 */}
      <Card className="main-content">
        {/* 搜索栏 */}
        <div className="search-bar">
          <Search
            placeholder="搜索知识库名称..."
            value={searchText}
            onChange={(e) => setSearchText(e.target.value)}
            style={{ width: 300 }}
            allowClear
          />
        </div>

        {/* 知识库表格 */}
        <Table
          columns={columns}
          dataSource={kbData?.kbs || []}
          rowKey="id"
          loading={isLoading}
          pagination={{
            total: kbData?.total || 0,
            pageSize: 10,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total) => `共 ${total} 个知识库`,
          }}
          className="kb-table"
        />
      </Card>

      {/* 创建知识库模态框 */}
      <Modal
        title="创建知识库"
        open={createModalVisible}
        onCancel={() => {
          setCreateModalVisible(false);
          form.resetFields();
        }}
        footer={null}
        width={600}
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={handleCreate}
          autoComplete="off"
        >
          <Form.Item
            name="name"
            label="知识库名称"
            rules={[
              { required: true, message: '请输入知识库名称' },
              { max: 50, message: '名称不能超过50个字符' },
            ]}
          >
            <Input placeholder="请输入知识库名称" />
          </Form.Item>

          <Form.Item
            name="description"
            label="描述"
            rules={[{ max: 200, message: '描述不能超过200个字符' }]}
          >
            <Input.TextArea
              placeholder="请输入知识库描述（可选）"
              rows={3}
            />
          </Form.Item>

          <Form.Item>
            <Space>
              <Button
                type="primary"
                htmlType="submit"
                loading={createKb.isPending}
              >
                创建
              </Button>
              <Button
                onClick={() => {
                  setCreateModalVisible(false);
                  form.resetFields();
                }}
              >
                取消
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>

      {/* 编辑知识库模态框 */}
      <Modal
        title="编辑知识库"
        open={editModalVisible}
        onCancel={() => {
          setEditModalVisible(false);
          setEditingKb(null);
          form.resetFields();
        }}
        footer={null}
        width={600}
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={handleUpdate}
          autoComplete="off"
        >
          <Form.Item
            name="name"
            label="知识库名称"
            rules={[
              { required: true, message: '请输入知识库名称' },
              { max: 50, message: '名称不能超过50个字符' },
            ]}
          >
            <Input placeholder="请输入知识库名称" />
          </Form.Item>

          <Form.Item
            name="description"
            label="描述"
            rules={[{ max: 200, message: '描述不能超过200个字符' }]}
          >
            <Input.TextArea
              placeholder="请输入知识库描述（可选）"
              rows={3}
            />
          </Form.Item>

          <Form.Item>
            <Space>
              <Button
                type="primary"
                htmlType="submit"
                loading={updateKb.isPending}
              >
                保存
              </Button>
              <Button
                onClick={() => {
                  setEditModalVisible(false);
                  setEditingKb(null);
                  form.resetFields();
                }}
              >
                取消
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
};

export default KnowledgeBasePage;
