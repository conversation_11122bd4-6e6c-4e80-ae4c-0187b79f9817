.knowledge-base-container {
  .page-header {
    margin-bottom: 24px;
    
    .header-content {
      display: flex;
      justify-content: space-between;
      align-items: flex-start;
      
      .header-left {
        .page-title {
          margin-bottom: 8px !important;
          color: #001529;
          font-weight: 600;
        }
      }
      
      .header-right {
        .ant-btn {
          height: 40px;
          padding: 0 24px;
          font-weight: 500;
        }
      }
    }
  }
  
  .stats-cards {
    margin-bottom: 24px;
    
    .ant-card {
      border-radius: 12px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
      transition: all 0.3s ease;
      
      &:hover {
        box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
        transform: translateY(-2px);
      }
      
      .ant-card-body {
        padding: 20px;
      }
      
      .stat-item {
        display: flex;
        align-items: center;
        gap: 16px;
        
        .stat-icon {
          flex-shrink: 0;
          width: 48px;
          height: 48px;
          display: flex;
          align-items: center;
          justify-content: center;
          background: rgba(24, 144, 255, 0.1);
          border-radius: 12px;
          font-size: 24px;
        }
        
        .stat-content {
          flex: 1;
          
          .stat-value {
            font-size: 24px;
            font-weight: 600;
            color: #001529;
            line-height: 1;
            margin-bottom: 4px;
          }
          
          .stat-label {
            font-size: 14px;
            color: #666;
          }
        }
      }
    }
  }
  
  .main-content {
    border-radius: 12px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    
    .ant-card-body {
      padding: 24px;
    }
    
    .search-bar {
      margin-bottom: 24px;
      
      .ant-input-search {
        .ant-input {
          border-radius: 8px;
        }
        
        .ant-btn {
          border-radius: 0 8px 8px 0;
        }
      }
    }
    
    .kb-table {
      .ant-table {
        .ant-table-thead {
          .ant-table-cell {
            background: #fafafa;
            font-weight: 600;
            color: #333;
            border-bottom: 2px solid #f0f0f0;
          }
        }
        
        .ant-table-tbody {
          .ant-table-row {
            transition: all 0.3s ease;
            
            &:hover {
              background: #f8f9fa;
            }
            
            .ant-table-cell {
              border-bottom: 1px solid #f5f5f5;
              vertical-align: middle;
            }
          }
        }
      }
      
      .kb-info {
        display: flex;
        align-items: center;
        gap: 12px;
        
        .kb-details {
          flex: 1;
          
          .kb-name {
            font-size: 14px;
            font-weight: 500;
            color: #001529;
            margin-bottom: 2px;
          }
          
          .kb-description {
            font-size: 12px;
            color: #999;
            line-height: 1.4;
            max-width: 200px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
          }
        }
      }
      
      .ant-tag {
        border-radius: 4px;
        font-size: 12px;
        padding: 2px 8px;
      }
      
      .ant-btn-link {
        padding: 0;
        height: auto;
        
        &:hover {
          background: none;
        }
      }
    }
  }
  
  // 模态框样式
  .ant-modal {
    .ant-modal-header {
      border-bottom: 1px solid #f0f0f0;
      
      .ant-modal-title {
        font-weight: 600;
        color: #001529;
      }
    }
    
    .ant-modal-body {
      padding: 24px;
      
      .ant-form {
        .ant-form-item {
          margin-bottom: 20px;
          
          .ant-form-item-label {
            label {
              font-weight: 500;
              color: #333;
            }
          }
          
          .ant-input,
          .ant-input-affix-wrapper {
            border-radius: 6px;
            
            &:hover {
              border-color: #40a9ff;
            }
            
            &:focus,
            &.ant-input-affix-wrapper-focused {
              border-color: #1890ff;
              box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
            }
          }
          
          .ant-input {
            &::placeholder {
              color: #bfbfbf;
            }
          }
        }
        
        .ant-btn {
          border-radius: 6px;
          font-weight: 500;
          
          &.ant-btn-primary {
            background: #1890ff;
            border-color: #1890ff;
            
            &:hover {
              background: #40a9ff;
              border-color: #40a9ff;
            }
          }
        }
      }
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .knowledge-base-container {
    .page-header {
      .header-content {
        flex-direction: column;
        gap: 16px;
        align-items: stretch;
        
        .header-right {
          .ant-btn {
            width: 100%;
          }
        }
      }
    }
    
    .stats-cards {
      .stat-item {
        gap: 12px;
        
        .stat-icon {
          width: 40px;
          height: 40px;
          font-size: 20px;
        }
        
        .stat-content {
          .stat-value {
            font-size: 20px;
          }
          
          .stat-label {
            font-size: 12px;
          }
        }
      }
    }
    
    .main-content {
      .search-bar {
        .ant-input-search {
          width: 100% !important;
        }
      }
      
      .kb-table {
        .ant-table {
          font-size: 12px;
          
          .kb-info {
            gap: 8px;
            
            .ant-avatar {
              width: 32px !important;
              height: 32px !important;
            }
            
            .kb-details {
              .kb-name {
                font-size: 13px;
              }
              
              .kb-description {
                font-size: 11px;
                max-width: 120px;
              }
            }
          }
        }
      }
    }
  }
}

// 暗色主题支持
.dark-theme {
  .knowledge-base-container {
    .page-header {
      .header-content {
        .header-left {
          .page-title {
            color: #fff;
          }
        }
      }
    }
    
    .stats-cards,
    .main-content {
      .ant-card {
        background: #141414;
        border-color: #303030;
        
        .stat-item {
          .stat-content {
            .stat-value {
              color: #fff;
            }
          }
        }
      }
    }
    
    .main-content {
      .kb-table {
        .ant-table {
          .ant-table-thead {
            .ant-table-cell {
              background: #262626;
              color: #fff;
              border-color: #303030;
            }
          }
          
          .ant-table-tbody {
            .ant-table-row {
              &:hover {
                background: #262626;
              }
              
              .ant-table-cell {
                border-color: #303030;
              }
            }
          }
        }
        
        .kb-info {
          .kb-details {
            .kb-name {
              color: #fff;
            }
          }
        }
      }
    }
  }
}
