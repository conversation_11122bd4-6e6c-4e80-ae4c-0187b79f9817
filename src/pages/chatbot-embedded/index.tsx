import React from 'react';
import { Card, Row, Col, Typography, Space, Divider, Button } from 'antd';
import { 
  RobotOutlined, 
  MessageOutlined, 
  SettingOutlined,
  InfoCircleOutlined,
  BulbOutlined 
} from '@ant-design/icons';
import AppLayout from '@/components/AppLayout';
import ChatBot from '@/components/ChatBot';
import { useTranslate } from '@/hooks/use-i18n';
import { useChatBotStats } from '@/hooks/use-chatbot-hooks';
import styles from './index.less';

const { Title, Text, Paragraph } = Typography;

const ChatBotEmbeddedPage: React.FC = () => {
  const t = useTranslate();
  const { stats, isLoading, fetchStats } = useChatBotStats();

  React.useEffect(() => {
    fetchStats();
  }, [fetchStats]);

  return (
    <AppLayout>
      <div className={styles.chatBotEmbeddedPage}>
        <Row gutter={[24, 24]} className={styles.content}>
          {/* 聊天机器人主界面 */}
          <Col xs={24} lg={18}>
            <Card
              title={
                <Space>
                  <RobotOutlined style={{ color: '#667eea' }} />
                  <span>{t('chatbot.title')}</span>
                </Space>
              }
              className={styles.chatCard}
              bodyStyle={{ padding: 0 }}
            >
              <ChatBot
                height={window.innerHeight - 200}
                enableStream={true}
                systemPrompt="你是汉邦高科的专业AI助手，专门帮助用户使用汉邦高科系统。请用中文回答问题，保持友好和专业的语调。如果用户询问汉邦高科相关功能，请提供详细和准确的信息。"
              />
            </Card>
          </Col>

          {/* 右侧信息面板 */}
          <Col xs={24} lg={6}>
            <Space direction="vertical" size="middle" style={{ width: '100%' }}>
              {/* 功能介绍 */}
              <Card
                title={
                  <Space>
                    <InfoCircleOutlined />
                    <span>{t('chatbot.features', '功能介绍')}</span>
                  </Space>
                }
                size="small"
                className={styles.infoCard}
              >
                <div className={styles.featureList}>
                  <div className={styles.featureItem}>
                    <Text strong>🚀 智能对话</Text>
                    <br />
                    <Text type="secondary" className={styles.featureDesc}>
                      基于先进的大语言模型，提供智能问答服务
                    </Text>
                  </div>
                  <Divider style={{ margin: '12px 0' }} />
                  <div className={styles.featureItem}>
                    <Text strong>💬 流式回复</Text>
                    <br />
                    <Text type="secondary" className={styles.featureDesc}>
                      实时显示AI思考过程，提升交互体验
                    </Text>
                  </div>
                  <Divider style={{ margin: '12px 0' }} />
                  <div className={styles.featureItem}>
                    <Text strong>🧠 上下文记忆</Text>
                    <br />
                    <Text type="secondary" className={styles.featureDesc}>
                      维护完整对话历史，支持连续对话
                    </Text>
                  </div>
                  <Divider style={{ margin: '12px 0' }} />
                  <div className={styles.featureItem}>
                    <Text strong>🔒 安全可靠</Text>
                    <br />
                    <Text type="secondary" className={styles.featureDesc}>
                      内容过滤和隐私保护，确保使用安全
                    </Text>
                  </div>
                </div>
              </Card>

              {/* 服务状态 */}
              {stats && (
                <Card
                  title={
                    <Space>
                      <SettingOutlined />
                      <span>服务状态</span>
                    </Space>
                  }
                  size="small"
                  loading={isLoading}
                  className={styles.statsCard}
                >
                  <div className={styles.statsGrid}>
                    <div className={styles.statItem}>
                      <div className={styles.statValue}>
                        {stats.memory_stats?.total_sessions || 0}
                      </div>
                      <div className={styles.statLabel}>活跃会话</div>
                    </div>
                    <div className={styles.statItem}>
                      <div className={styles.statValue}>
                        {stats.memory_stats?.total_users || 0}
                      </div>
                      <div className={styles.statLabel}>在线用户</div>
                    </div>
                    <div className={styles.statItem}>
                      <div className={styles.statValue}>
                        {stats.processor_stats?.max_input_length || 0}
                      </div>
                      <div className={styles.statLabel}>最大输入</div>
                    </div>
                    <div className={styles.statItem}>
                      <div className={styles.statValue}>
                        {stats.processor_stats?.max_output_length || 0}
                      </div>
                      <div className={styles.statLabel}>最大输出</div>
                    </div>
                  </div>
                  
                  <Divider style={{ margin: '16px 0' }} />
                  
                  <div className={styles.configInfo}>
                    <Text type="secondary" style={{ fontSize: '12px' }}>
                      温度: {stats.default_gen_config?.temperature || 0.7} | 
                      Top-p: {stats.default_gen_config?.top_p || 0.9}
                    </Text>
                  </div>
                </Card>
              )}

              {/* 使用提示 */}
              <Card
                title={
                  <Space>
                    <BulbOutlined />
                    <span>使用提示</span>
                  </Space>
                }
                size="small"
                className={styles.tipsCard}
              >
                <div className={styles.tipsList}>
                  <div className={styles.tipItem}>
                    <Text className={styles.tipText}>
                      • {t('chatbot.tips.enterToSend')}
                    </Text>
                  </div>
                  <div className={styles.tipItem}>
                    <Text className={styles.tipText}>
                      • {t('chatbot.tips.streamingMode')}
                    </Text>
                  </div>
                  <div className={styles.tipItem}>
                    <Text className={styles.tipText}>
                      • 可以询问RAGFlow的使用方法和功能
                    </Text>
                  </div>
                  <div className={styles.tipItem}>
                    <Text className={styles.tipText}>
                      • 支持Markdown格式的丰富回复
                    </Text>
                  </div>
                  <div className={styles.tipItem}>
                    <Text className={styles.tipText}>
                      • 支持代码高亮和表格显示
                    </Text>
                  </div>
                </div>
              </Card>

              {/* 快速操作 */}
              <Card
                title="快速操作"
                size="small"
                className={styles.actionsCard}
              >
                <Space direction="vertical" style={{ width: '100%' }}>
                  <Button 
                    block 
                    type="primary" 
                    ghost
                    onClick={() => {
                      // 可以添加快速问题功能
                      console.log('Quick question clicked');
                    }}
                  >
                    如何使用RAGFlow？
                  </Button>
                  <Button 
                    block 
                    type="primary" 
                    ghost
                    onClick={() => {
                      console.log('Knowledge base question clicked');
                    }}
                  >
                    如何创建知识库？
                  </Button>
                  <Button 
                    block 
                    type="primary" 
                    ghost
                    onClick={() => {
                      console.log('Chat question clicked');
                    }}
                  >
                    如何配置对话？
                  </Button>
                </Space>
              </Card>
            </Space>
          </Col>
        </Row>
      </div>
    </AppLayout>
  );
};

export default ChatBotEmbeddedPage;
