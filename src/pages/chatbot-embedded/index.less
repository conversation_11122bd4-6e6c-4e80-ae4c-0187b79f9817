.chatBotEmbeddedPage {
  padding: 16px;
  background: #f5f5f5;
  min-height: calc(100vh - 64px);

  .content {
    height: 100%;
    
    .chatCard {
      border-radius: 12px;
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
      height: fit-content;
      
      .ant-card-head {
        border-bottom: 1px solid #f0f0f0;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border-radius: 12px 12px 0 0;
        
        .ant-card-head-title {
          color: white;
          font-weight: 600;
        }
      }
    }
  }

  .infoCard,
  .statsCard,
  .tipsCard,
  .actionsCard {
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
    border: 1px solid #e8e8e8;
    
    .ant-card-head {
      padding: 12px 16px;
      min-height: auto;
      
      .ant-card-head-title {
        font-size: 14px;
        font-weight: 600;
      }
    }
    
    .ant-card-body {
      padding: 16px;
    }
  }

  .featureList {
    .featureItem {
      margin-bottom: 0;
      
      .featureDesc {
        font-size: 12px;
        line-height: 1.4;
        margin-top: 4px;
        display: block;
      }
    }
  }

  .statsGrid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 12px;
    margin-bottom: 16px;

    .statItem {
      text-align: center;
      padding: 12px 8px;
      background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
      border-radius: 8px;
      border: 1px solid #dee2e6;
      
      .statValue {
        font-size: 18px;
        font-weight: 700;
        color: #667eea;
        line-height: 1;
        margin-bottom: 4px;
      }
      
      .statLabel {
        font-size: 12px;
        color: #6c757d;
        line-height: 1;
      }
    }
  }

  .configInfo {
    text-align: center;
    padding: 8px;
    background: #f8f9fa;
    border-radius: 6px;
  }

  .tipsList {
    .tipItem {
      margin-bottom: 8px;
      line-height: 1.5;
      
      &:last-child {
        margin-bottom: 0;
      }
      
      .tipText {
        font-size: 13px;
        color: #495057;
      }
    }
  }

  .actionsCard {
    .ant-btn {
      height: 36px;
      border-radius: 6px;
      font-size: 13px;
      
      &:not(:last-child) {
        margin-bottom: 8px;
      }
    }
  }
}

// 响应式设计
@media (max-width: 1200px) {
  .chatBotEmbeddedPage {
    .content {
      .statsGrid {
        grid-template-columns: 1fr;
        gap: 8px;
      }
    }
  }
}

@media (max-width: 768px) {
  .chatBotEmbeddedPage {
    padding: 8px;

    .content {
      .chatCard {
        margin-bottom: 16px;
      }
      
      .infoCard,
      .statsCard,
      .tipsCard,
      .actionsCard {
        margin-bottom: 12px;
      }
    }

    .statsGrid {
      grid-template-columns: 1fr 1fr;
      gap: 8px;
      
      .statItem {
        padding: 8px 6px;
        
        .statValue {
          font-size: 16px;
        }
        
        .statLabel {
          font-size: 11px;
        }
      }
    }

    .tipsList {
      .tipItem {
        .tipText {
          font-size: 12px;
        }
      }
    }
  }
}

// 暗色主题支持
@media (prefers-color-scheme: dark) {
  .chatBotEmbeddedPage {
    background: #1f1f1f;

    .chatCard {
      background: #262626;
      border-color: #404040;
      
      .ant-card-head {
        background: linear-gradient(135deg, #4a5568 0%, #2d3748 100%);
        border-bottom-color: #404040;
      }
    }

    .infoCard,
    .statsCard,
    .tipsCard,
    .actionsCard {
      background: #262626;
      border-color: #404040;
      
      .ant-card-head-title {
        color: #d9d9d9;
      }
    }

    .statsGrid {
      .statItem {
        background: linear-gradient(135deg, #2a2a2a 0%, #1f1f1f 100%);
        border-color: #404040;
        
        .statValue {
          color: #667eea;
        }
        
        .statLabel {
          color: #8c8c8c;
        }
      }
    }

    .configInfo {
      background: #1f1f1f;
    }

    .tipsList {
      .tipItem {
        .tipText {
          color: #d9d9d9;
        }
      }
    }
  }
}
