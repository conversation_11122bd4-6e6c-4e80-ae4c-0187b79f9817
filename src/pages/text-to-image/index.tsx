import React, { useEffect, useState } from 'react';
import { Card, Spin, Alert, Typography } from 'antd';
import { PictureOutlined } from '@ant-design/icons';
import AppLayout from '@/components/AppLayout';
import { useTranslate } from '@/hooks/use-i18n';
import styles from './index.less';

const { Title } = Typography;

const TextToImage: React.FC = () => {
  const t = useTranslate();
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  
  // 使用环境变量配置的Gradio URL
  // 在Docker部署环境中，使用完整的URL确保端口号正确
  const gradioUrl = process.env.TEXT_TO_IMAGE_URL || '/gradio';

  useEffect(() => {
    // 检查iframe是否加载成功
    const timer = setTimeout(() => {
      setLoading(false);
    }, 3000); // 3秒后停止loading状态

    return () => clearTimeout(timer);
  }, []);

  const handleIframeLoad = () => {
    setLoading(false);
    setError(null);
  };

  const handleIframeError = () => {
    setLoading(false);
    setError(t('textToImage.loadError'));
  };

  return (
    <AppLayout>
      <div className={styles.container}>
        {/* 页面标题 */}
        <div className={styles.pageHeader}>
          <div className={styles.headerContent}>
            <div className={styles.headerLeft}>
              <Title level={2} className={styles.pageTitle}>
                <PictureOutlined style={{ marginRight: 8 }} />
                {t('navigation.textToImage')}
              </Title>
              <Typography.Text type="secondary">
                {t('textToImage.description')}
              </Typography.Text>
            </div>
          </div>
        </div>

        {/* 内容区域 */}
        <div className={styles.contentContainer}>
          {loading && (
            <div className={styles.loadingContainer}>
              <Spin size="large" />
              <div className={styles.loadingText}>{t('textToImage.loading')}</div>
            </div>
          )}

          {error && (
            <div className={styles.errorContainer}>
              <Alert
                message={t('textToImage.serviceUnavailable')}
                description={error}
                type="error"
                showIcon
                className={styles.errorAlert}
              />
            </div>
          )}

          <iframe
            src={gradioUrl}
            className={styles.gradioFrame}
            title={t('textToImage.title')}
            onLoad={handleIframeLoad}
            onError={handleIframeError}
            style={{ display: loading ? 'none' : 'block' }}
          />
        </div>
      </div>
    </AppLayout>
  );
};

export default TextToImage;
