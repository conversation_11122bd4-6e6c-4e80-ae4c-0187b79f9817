.container {
  min-height: 100vh;
  background: #f0f2f5;
}

.pageHeader {
  background: #fff;
  padding: 24px;
  border-bottom: 1px solid #e8e8e8;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

.headerContent {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  width: 100%;
  margin: 0;
  padding: 0 5px;
}

.headerLeft {
  .pageTitle {
    color: #1f2937;
    font-weight: 600;
    margin-bottom: 8px !important;
  }
}

.contentContainer {
  position: relative;
  width: 100%;
  margin: 0;
  padding: 5px;
  height: calc(100vh - 140px); // 减去头部高度
}

.loadingContainer {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background: rgba(255, 255, 255, 0.9);
  z-index: 10;
  border-radius: 8px;
}

.loadingText {
  margin-top: 16px;
  color: #666;
  font-size: 14px;
}

.errorContainer {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: 10;
  width: 90%;
  max-width: 500px;
}

.errorAlert {
  margin: 0;
}

.gradioFrame {
  width: 100%;
  height: 100%;
  border: none;
  border-radius: 8px;
  background: white;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .pageHeader {
    padding: 16px 5px;
  }

  .contentContainer {
    padding: 5px;
    height: calc(100vh - 120px);
  }

  .errorContainer {
    width: 95%;
    padding: 0 5px;
  }
}

/* 暗色主题支持 */
@media (prefers-color-scheme: dark) {
  .container {
    background: #141414;
  }

  .pageHeader {
    background: #1f1f1f;
    border-bottom: 1px solid #303030;
  }

  .headerLeft .pageTitle {
    color: #fff !important;
  }

  .loadingContainer {
    background: rgba(31, 31, 31, 0.9);
  }

  .loadingText {
    color: #a6a6a6;
  }

  .gradioFrame {
    background: #1f1f1f;
  }
}
