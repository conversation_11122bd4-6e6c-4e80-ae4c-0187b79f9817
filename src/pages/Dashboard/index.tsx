import React from 'react';
import { Row, Col, Card, Statistic, Typography, Space, Button, Progress } from 'antd';
import {
  DatabaseOutlined,
  FileTextOutlined,
  MessageOutlined,
  FolderOutlined,
  UserOutlined,
  ArrowUpOutlined,
  ArrowDownOutlined,
  RightOutlined,
} from '@ant-design/icons';
import { useNavigate } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import { useKnowledgeBaseStats } from '@/hooks/useKnowledgeBase';
import { useDocumentStats } from '@/hooks/useDocument';
import { ROUTES } from '@/constants';
import './index.less';

const { Title, Text } = Typography;

interface QuickActionCardProps {
  title: string;
  description: string;
  icon: React.ReactNode;
  color: string;
  onClick: () => void;
}

const QuickActionCard: React.FC<QuickActionCardProps> = ({
  title,
  description,
  icon,
  color,
  onClick,
}) => (
  <Card 
    hoverable 
    className="quick-action-card"
    onClick={onClick}
    style={{ borderTop: `4px solid ${color}` }}
  >
    <div className="card-content">
      <div className="card-icon" style={{ color }}>
        {icon}
      </div>
      <div className="card-info">
        <Title level={5} className="card-title">
          {title}
        </Title>
        <Text type="secondary" className="card-description">
          {description}
        </Text>
      </div>
      <RightOutlined className="card-arrow" />
    </div>
  </Card>
);

const DashboardPage: React.FC = () => {
  const navigate = useNavigate();
  const { t } = useTranslation();
  const { data: kbStats, isLoading: kbStatsLoading } = useKnowledgeBaseStats();
  const { data: docStats, isLoading: docStatsLoading } = useDocumentStats();

  const quickActions = [
    {
      title: '创建知识库',
      description: '构建新的知识库，开始智能问答',
      icon: <DatabaseOutlined style={{ fontSize: '32px' }} />,
      color: '#52c41a',
      onClick: () => navigate(ROUTES.KNOWLEDGE_BASE),
    },
    {
      title: '上传文档',
      description: '添加文档到知识库中',
      icon: <FileTextOutlined style={{ fontSize: '32px' }} />,
      color: '#1890ff',
      onClick: () => navigate(ROUTES.DOCUMENTS),
    },
    {
      title: '开始对话',
      description: '与知识库进行智能对话',
      icon: <MessageOutlined style={{ fontSize: '32px' }} />,
      color: '#722ed1',
      onClick: () => navigate(ROUTES.CHAT),
    },
    {
      title: '管理文件',
      description: '组织和管理您的文件',
      icon: <FolderOutlined style={{ fontSize: '32px' }} />,
      color: '#fa8c16',
      onClick: () => navigate(ROUTES.FILES),
    },
  ];

  return (
    <div className="dashboard-container">
      {/* 页面标题 */}
      <div className="dashboard-header">
        <Title level={2} className="page-title">
          仪表板
        </Title>
        <Text type="secondary">
          欢迎使用HanBangGaoKe（汉邦高科）智能知识库系统
        </Text>
      </div>

      {/* 统计卡片 */}
      <Row gutter={[24, 24]} className="stats-section">
        <Col xs={24} sm={12} lg={6}>
          <Card>
            <Statistic
              title="知识库总数"
              value={kbStats?.total || 0}
              loading={kbStatsLoading}
              prefix={<DatabaseOutlined />}
              valueStyle={{ color: '#52c41a' }}
              suffix={
                <span style={{ fontSize: '12px', color: '#999' }}>
                  <ArrowUpOutlined /> 12%
                </span>
              }
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} lg={6}>
          <Card>
            <Statistic
              title="文档总数"
              value={kbStats?.totalDocs || 0}
              loading={kbStatsLoading}
              prefix={<FileTextOutlined />}
              valueStyle={{ color: '#1890ff' }}
              suffix={
                <span style={{ fontSize: '12px', color: '#999' }}>
                  <ArrowUpOutlined /> 8%
                </span>
              }
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} lg={6}>
          <Card>
            <Statistic
              title="分块总数"
              value={kbStats?.totalChunks || 0}
              loading={kbStatsLoading}
              prefix={<MessageOutlined />}
              valueStyle={{ color: '#722ed1' }}
              suffix={
                <span style={{ fontSize: '12px', color: '#999' }}>
                  <ArrowDownOutlined /> 3%
                </span>
              }
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} lg={6}>
          <Card>
            <Statistic
              title="活跃用户"
              value={1}
              prefix={<UserOutlined />}
              valueStyle={{ color: '#fa8c16' }}
              suffix={
                <span style={{ fontSize: '12px', color: '#999' }}>
                  <ArrowUpOutlined /> 100%
                </span>
              }
            />
          </Card>
        </Col>
      </Row>

      {/* 快速操作 */}
      <div className="quick-actions-section">
        <Title level={3} className="section-title">
          快速操作
        </Title>
        <Row gutter={[24, 24]}>
          {quickActions.map((action, index) => (
            <Col xs={24} sm={12} lg={6} key={index}>
              <QuickActionCard {...action} />
            </Col>
          ))}
        </Row>
      </div>

      {/* 系统状态 */}
      <Row gutter={[24, 24]} className="status-section">
        <Col xs={24} lg={12}>
          <Card title="系统状态" className="status-card">
            <Space direction="vertical" style={{ width: '100%' }} size="large">
              <div className="status-item">
                <div className="status-label">
                  <span>CPU使用率</span>
                  <span className="status-value">45%</span>
                </div>
                <Progress percent={45} strokeColor="#52c41a" />
              </div>
              <div className="status-item">
                <div className="status-label">
                  <span>内存使用率</span>
                  <span className="status-value">68%</span>
                </div>
                <Progress percent={68} strokeColor="#1890ff" />
              </div>
              <div className="status-item">
                <div className="status-label">
                  <span>存储使用率</span>
                  <span className="status-value">32%</span>
                </div>
                <Progress percent={32} strokeColor="#722ed1" />
              </div>
            </Space>
          </Card>
        </Col>
        <Col xs={24} lg={12}>
          <Card title="最近活动" className="activity-card">
            <div className="activity-list">
              <div className="activity-item">
                <div className="activity-icon">
                  <DatabaseOutlined style={{ color: '#52c41a' }} />
                </div>
                <div className="activity-content">
                  <div className="activity-title">创建了新知识库</div>
                  <div className="activity-time">2分钟前</div>
                </div>
              </div>
              <div className="activity-item">
                <div className="activity-icon">
                  <FileTextOutlined style={{ color: '#1890ff' }} />
                </div>
                <div className="activity-content">
                  <div className="activity-title">上传了3个文档</div>
                  <div className="activity-time">15分钟前</div>
                </div>
              </div>
              <div className="activity-item">
                <div className="activity-icon">
                  <MessageOutlined style={{ color: '#722ed1' }} />
                </div>
                <div className="activity-content">
                  <div className="activity-title">进行了对话交互</div>
                  <div className="activity-time">1小时前</div>
                </div>
              </div>
            </div>
            <div className="activity-footer">
              <Button type="link" size="small">
                查看更多活动
              </Button>
            </div>
          </Card>
        </Col>
      </Row>
    </div>
  );
};

export default DashboardPage;
