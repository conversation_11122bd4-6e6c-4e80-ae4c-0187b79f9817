import AuthGuard from '@/components/AuthGuard';
import { Layout } from 'antd';
import { Outlet } from 'umi';
import KnowledgeSidebar from './components/KnowledgeSidebar';
import styles from './index.less';

const { Content, Sider } = Layout;

const KnowledgeDetailContent = () => {
  return (
    <Layout className={styles.knowledgeDetailLayout}>
      <Sider width={280} className={styles.sidebar}>
        <KnowledgeSidebar />
      </Sider>
      <Layout>
        <Content className={styles.content}>
          <Outlet />
        </Content>
      </Layout>
    </Layout>
  );
};

const KnowledgeDetail = () => {
  return (
    <AuthGuard requireAuth={true}>
      <KnowledgeDetailContent />
    </AuthGuard>
  );
};

export default KnowledgeDetail;
