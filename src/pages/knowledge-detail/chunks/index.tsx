import React, { useState } from 'react';
import {
  Table,
  Button,
  Space,
  Input,
  Card,
  Typography,
  Breadcrumb,
  Tag,
  Tooltip,
  Switch,
  message,
} from 'antd';
import {
  ArrowLeftOutlined,
  SearchOutlined,
  FileTextOutlined,
  HomeOutlined,
  BookOutlined,
} from '@ant-design/icons';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { useParams, useNavigate } from 'umi';
import { useTranslate } from '@/hooks/use-i18n';
import documentService from '@/services/document-service';
import { formatDate } from '@/utils/date';
import type { IDocumentChunk } from '@/interfaces/document';
import type { ColumnsType } from 'antd/es/table';
import request from '@/utils/request';
import api from '@/utils/api';

const { Text, Paragraph } = Typography;
const { Search } = Input;

interface ChunksPageProps {}

const ChunksPage: React.FC<ChunksPageProps> = () => {
  const t = useTranslate();
  const { id: kbId, docId } = useParams<{ id: string; docId: string }>();
  const navigate = useNavigate();
  const queryClient = useQueryClient();

  // 状态管理
  const [searchKeywords, setSearchKeywords] = useState('');
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(20);
  const [loadingChunks, setLoadingChunks] = useState<Set<string>>(new Set());
  // 本地状态管理，用于乐观更新
  const [localChunkStates, setLocalChunkStates] = useState<Map<string, number>>(new Map());

  // 获取文档chunks
  const { data: chunksData, isLoading: chunksLoading } = useQuery({
    queryKey: ['document-chunks', docId, searchKeywords, currentPage, pageSize],
    queryFn: async () => {
      if (!docId) return { total: 0, chunks: [] };
      const { data } = await documentService.getDocumentChunks({
        doc_id: docId,
        keywords: searchKeywords,
        page: currentPage,
        size: pageSize,
      });
      return data?.data || { total: 0, chunks: [] };
    },
    enabled: !!docId,
  });

  const chunks = chunksData?.chunks || [];
  const total = chunksData?.total || 0;

  // 切换单个chunk状态的mutation - 使用专门的switch接口
  const switchChunkMutation = useMutation({
    mutationFn: async ({ chunk_id, available_int, doc_id }: {
      chunk_id: string,
      available_int: number,
      doc_id: string
    }) => {
      // 确保chunk_id不为空
      if (!chunk_id) {
        throw new Error(t('chunks.chunkIdRequired'));
      }

      const requestData = {
        chunk_ids: [chunk_id], // 确保将chunk_id正确放入数组
        available_int,
        doc_id
      };

      const { data } = await request.post(api.switch_chunk, {
        data: requestData
      });
      return { data, chunk_id, available_int };
    },
    onSuccess: ({ chunk_id, available_int }) => {
      // 移除loading状态
      setLoadingChunks(prev => {
        const newSet = new Set(prev);
        newSet.delete(chunk_id);
        return newSet;
      });

      // 确认本地状态与服务器状态一致
      setLocalChunkStates(prev => new Map(prev).set(chunk_id, available_int));

      // 使用精确更新，只更新被修改的chunk的状态，而不是重新获取整个列表
      queryClient.setQueryData(
        ['document-chunks', docId, searchKeywords, currentPage, pageSize],
        (oldData: any) => {
          if (!oldData) return oldData;

          return {
            ...oldData,
            chunks: oldData.chunks.map((chunk: IDocumentChunk) =>
              chunk.chunk_id === chunk_id
                ? { ...chunk, available_int }
                : chunk
            )
          };
        }
      );

      message.success(t('chunks.statusUpdated'));
    },
    onError: (error: any, variables) => {
      // 移除loading状态
      setLoadingChunks(prev => {
        const newSet = new Set(prev);
        newSet.delete(variables.chunk_id);
        return newSet;
      });

      // 回滚本地状态
      setLocalChunkStates(prev => {
        const newMap = new Map(prev);
        newMap.delete(variables.chunk_id);
        return newMap;
      });

      message.error(error?.message || t('chunks.statusUpdateFailed'));
    },
  });

  // 处理状态切换 - 针对单个chunk执行
  const handleStatusChange = (chunkId: string, checked: boolean) => {
    if (!docId) return;

    // 验证参数
    if (!chunkId) {
      message.error(t('chunks.chunkIdMissing'));
      return;
    }

    // 防止重复点击
    if (loadingChunks.has(chunkId)) return;

    const newStatus = checked ? 1 : 0;

    // 乐观更新：立即更新本地状态
    setLocalChunkStates(prev => new Map(prev).set(chunkId, newStatus));

    // 设置loading状态 - 只针对当前被点击的chunk
    setLoadingChunks(prev => new Set(prev).add(chunkId));

    // 使用switch接口更新单个chunk的状态
    switchChunkMutation.mutate({
      chunk_id: chunkId,
      available_int: newStatus,
      doc_id: docId,
    });
  };

  // 表格列定义
  const columns: ColumnsType<IDocumentChunk> = [
    {
      title: t('chunks.content'),
      dataIndex: 'content_with_weight',
      key: 'content_with_weight',
      width: '60%',
      render: (text: string) => (
        <div>
          <Paragraph
            ellipsis={{
              rows: 3,
              expandable: true,
              symbol: t('common.more'),
            }}
            style={{ marginBottom: 0 }}
          >
            {text}
          </Paragraph>
        </div>
      ),
    },

    {
      title: t('chunks.keywords'),
      dataIndex: 'important_kwd',
      key: 'important_kwd',
      width: 200,
      render: (keywords: string[]) => (
        <div>
          {keywords?.slice(0, 3).map((keyword, index) => (
            <Tag key={index} color="blue" style={{ marginBottom: 4 }}>
              {keyword}
            </Tag>
          ))}
          {keywords?.length > 3 && (
            <Tooltip title={keywords.slice(3).join(', ')}>
              <Tag color="default">+{keywords.length - 3}</Tag>
            </Tooltip>
          )}
        </div>
      ),
    },
    {
      title: t('chunks.status'),
      dataIndex: 'available_int',
      key: 'available_int',
      width: 100,
      render: (status: number, record: IDocumentChunk) => {
        // 使用正确的字段名：chunk_id
        const chunkId = record.chunk_id;

        // 检查chunk_id是否存在
        if (!chunkId) {
          console.error('Chunk ID is missing!', record);
          return <span>{t('chunks.idMissing')}</span>;
        }

        // 优先使用本地状态，如果没有则使用服务器状态
        const currentStatus = localChunkStates.has(chunkId)
          ? localChunkStates.get(chunkId)!
          : status;

        return (
          <Switch
            checked={currentStatus === 1}
            onChange={(checked) => handleStatusChange(chunkId, checked)}
            loading={loadingChunks.has(chunkId)}
            disabled={loadingChunks.has(chunkId)}
            checkedChildren={t('chunks.enabled')}
            unCheckedChildren={t('chunks.disabled')}
          />
        );
      },
    },
  ];

  const handleSearch = (value: string) => {
    setSearchKeywords(value);
    setCurrentPage(1);
  };

  const handleTableChange = (pagination: any) => {
    setCurrentPage(pagination.current);
    setPageSize(pagination.pageSize);
  };

  if (!kbId || !docId) {
    return (
      <div style={{ padding: 24, textAlign: 'center' }}>
        <h2>{t('chunks.idsRequired')}</h2>
      </div>
    );
  }

  return (
    <div style={{ padding: 24 }}>
      {/* Breadcrumb */}
      <Breadcrumb style={{ marginBottom: 16 }}>
        <Breadcrumb.Item>
          <HomeOutlined />
        </Breadcrumb.Item>
        <Breadcrumb.Item>
          <BookOutlined />
          <span
            style={{ cursor: 'pointer', marginLeft: 8 }}
            onClick={() => navigate('/knowledge')}
          >
            {t('knowledge.title')}
          </span>
        </Breadcrumb.Item>
        <Breadcrumb.Item>
          <FileTextOutlined />
          <span
            style={{ cursor: 'pointer', marginLeft: 8 }}
            onClick={() => navigate(`/knowledge/${kbId}`)}
          >
            {t('knowledge.documents')}
          </span>
        </Breadcrumb.Item>
        <Breadcrumb.Item>{t('knowledge.chunks')}</Breadcrumb.Item>
      </Breadcrumb>

      {/* Header */}
      <div style={{ marginBottom: 24 }}>
        <Space>
          <Button
            icon={<ArrowLeftOutlined />}
            onClick={() => navigate(`/knowledge/${kbId}`)}
          >
            {t('chunks.backToDocuments')}
          </Button>
        </Space>
        <h2 style={{ marginTop: 16, marginBottom: 8 }}>{t('chunks.documentChunks')}</h2>
        <Text type="secondary">
          {t('chunks.viewAndManage')}
        </Text>
      </div>

      {/* Toolbar */}
      <Card style={{ marginBottom: 16 }}>
        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <div>
            <Text strong>{t('chunks.totalChunks')}: {total}</Text>
          </div>
          <div>
            <Search
              placeholder={t('chunks.searchPlaceholder')}
              allowClear
              enterButton={<SearchOutlined />}
              size="middle"
              style={{ width: 300 }}
              onSearch={handleSearch}
            />
          </div>
        </div>
      </Card>

      {/* Chunks Table */}
      <Card>
        <Table
          columns={columns}
          dataSource={chunks}
          rowKey="id"
          loading={chunksLoading}
          pagination={{
            current: currentPage,
            pageSize: pageSize,
            total: total,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) =>
              t('chunks.chunksRange', { start: range[0], end: range[1], total }),
            pageSizeOptions: ['10', '20', '50', '100'],
          }}
          onChange={handleTableChange}
          scroll={{ x: 1000 }}
        />
      </Card>
    </div>
  );
};

export default ChunksPage;
