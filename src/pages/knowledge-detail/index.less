.knowledgeDetailLayout {
  min-height: 100vh;
  background: #f0f2f5;
}

.sidebar {
  background: #fff;
  border-right: 1px solid #e8e8e8;
  box-shadow: 2px 0 8px rgba(0, 0, 0, 0.06);
  
  .ant-layout-sider-children {
    display: flex;
    flex-direction: column;
    height: 100vh;
    overflow: hidden;
  }
}

.content {
  background: #f0f2f5;
  padding: 0;
  overflow: auto;
  
  .ant-layout-content {
    min-height: 100vh;
  }
}

@media (max-width: 768px) {
  .sidebar {
    width: 100% !important;
    max-width: 100% !important;
    flex: none !important;
  }
  
  .knowledgeDetailLayout {
    .ant-layout-sider {
      position: fixed;
      left: 0;
      top: 0;
      bottom: 0;
      z-index: 1000;
      transform: translateX(-100%);
      transition: transform 0.3s ease;
      
      &.ant-layout-sider-collapsed {
        transform: translateX(0);
      }
    }
  }
}
