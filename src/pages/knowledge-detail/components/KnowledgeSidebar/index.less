.sidebar {
  display: flex;
  flex-direction: column;
  height: 100vh;
  padding: 0;
  background: #fff;
}

.loading {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 200px;
  color: #666;
}

.header {
  padding: 16px 20px;
  border-bottom: 1px solid #f0f0f0;
}

.backButton {
  color: #666;
  font-size: 14px;
  padding: 4px 8px;
  height: auto;
  
  &:hover {
    color: #1890ff;
    background: rgba(24, 144, 255, 0.1);
  }
}

.knowledgeInfo {
  padding: 24px 20px;
  border-bottom: 1px solid #f0f0f0;
}

.avatarSection {
  display: flex;
  gap: 16px;
  margin-bottom: 20px;
}

.infoText {
  flex: 1;
  min-width: 0;
}

.knowledgeName {
  margin: 0 0 8px 0 !important;
  color: #1f2937;
  font-size: 18px;
  font-weight: 600;
  line-height: 1.3;
}

.description {
  font-size: 13px;
  line-height: 1.4;
  color: #6b7280;
}

.statistics {
  .ant-statistic {
    .ant-statistic-title {
      font-size: 12px;
      color: #9ca3af;
      margin-bottom: 4px;
    }
    
    .ant-statistic-content {
      font-size: 14px;
      font-weight: 600;
    }
  }
}

.divider {
  color: #e5e7eb;
  margin: 0 12px;
}

.metaInfo {
  margin-top: 16px;
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.metaText {
  font-size: 12px;
  color: #9ca3af;
}

.navigation {
  flex: 1;
  padding: 20px;
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.menuItem {
  height: 44px;
  display: flex;
  align-items: center;
  justify-content: flex-start;
  gap: 12px;
  padding: 0 16px;
  border-radius: 8px;
  font-weight: 500;
  transition: all 0.3s ease;
  
  &:not(.active) {
    color: #6b7280;
    background: transparent;
    border: 1px solid transparent;
    
    &:hover {
      color: #1890ff;
      background: rgba(24, 144, 255, 0.1);
      border-color: rgba(24, 144, 255, 0.2);
    }
  }
  
  &.active {
    color: #fff;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border: none;
    box-shadow: 0 2px 8px rgba(102, 126, 234, 0.3);
    
    &:hover {
      background: linear-gradient(135deg, #5a67d8 0%, #6b46c1 100%);
    }
  }
  
  .anticon {
    font-size: 16px;
  }
}

@media (max-width: 768px) {
  .sidebar {
    width: 100%;
  }
  
  .avatarSection {
    flex-direction: column;
    align-items: center;
    text-align: center;
    gap: 12px;
  }
  
  .knowledgeInfo {
    padding: 16px;
  }
  
  .navigation {
    padding: 16px;
  }
  
  .menuItem {
    height: 48px;
    font-size: 16px;
  }
}
