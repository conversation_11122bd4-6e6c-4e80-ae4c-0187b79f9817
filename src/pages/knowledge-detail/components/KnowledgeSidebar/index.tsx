import { useFetchKnowledgeDetail } from '@/hooks/knowledge-hooks';
import {
  DatabaseOutlined,
  SettingOutlined,
  ArrowLeftOutlined,
  SearchOutlined,
} from '@ant-design/icons';
import { Avatar, Button, Typography, Space, Statistic } from 'antd';
import { useMemo } from 'react';
import { useNavigate, useParams, useLocation } from 'umi';
import { useTranslate } from '@/hooks/use-i18n';
import styles from './index.less';

const { Title, Text } = Typography;

const KnowledgeSidebar = () => {
  const t = useTranslate();
  const navigate = useNavigate();
  const { id } = useParams<{ id: string }>();
  const location = useLocation();
  const { data: knowledgeDetail, loading } = useFetchKnowledgeDetail(id || '');

  const menuItems = useMemo(() => [
    {
      key: 'dataset',
      icon: <DatabaseOutlined />,
      label: t('knowledge.dataset'),
      path: `/knowledge/${id}/dataset`,
    },
    {
      key: 'retrieval-testing',
      icon: <SearchOutlined />,
      label: t('knowledge.retrievalTesting'),
      path: `/knowledge/${id}/retrieval-testing`,
    },
    {
      key: 'setting',
      icon: <SettingOutlined />,
      label: t('knowledge.setting'),
      path: `/knowledge/${id}/setting`,
    },
  ], [id, t]);

  const currentPath = location.pathname;

  const handleMenuClick = (path: string) => {
    navigate(path);
  };

  const handleBackClick = () => {
    navigate('/knowledge');
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString();
  };

  const formatBytes = (bytes: number) => {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  if (loading) {
    return (
      <div className={styles.sidebar}>
        <div className={styles.loading}>{t('common.loading')}</div>
      </div>
    );
  }

  return (
    <div className={styles.sidebar}>
      {/* Header */}
      <div className={styles.header}>
        <Button
          type="text"
          icon={<ArrowLeftOutlined />}
          onClick={handleBackClick}
          className={styles.backButton}
        >
          {t('knowledge.backToKnowledgeBase')}
        </Button>
      </div>

      {/* Knowledge Base Info */}
      <div className={styles.knowledgeInfo}>
        <div className={styles.avatarSection}>
          <Avatar
            size={64}
            src={knowledgeDetail?.avatar}
            style={{
              backgroundColor: '#667eea',
              fontSize: '24px',
            }}
          >
            {knowledgeDetail?.name?.charAt(0)?.toUpperCase()}
          </Avatar>
          <div className={styles.infoText}>
            <Title level={4} className={styles.knowledgeName} ellipsis={{ tooltip: true }}>
              {knowledgeDetail?.name || t('common.unknown', 'Unknown')}
            </Title>
            <Text type="secondary" className={styles.description} ellipsis={{ rows: 2, tooltip: true }}>
              {knowledgeDetail?.description || t('common.noDescription', 'No description')}
            </Text>
          </div>
        </div>

        {/* Statistics */}
        <div className={styles.statistics}>
          <Space split={<span className={styles.divider}>|</span>}>
            <Statistic
              title={t('knowledge.documents')}
              value={knowledgeDetail?.doc_num || 0}
              valueStyle={{ fontSize: '14px', color: '#1890ff' }}
            />
            <Statistic
              title={t('knowledge.chunks')}
              value={knowledgeDetail?.chunk_num || 0}
              valueStyle={{ fontSize: '14px', color: '#52c41a' }}
            />
          </Space>
          <div className={styles.metaInfo}>
            <Text type="secondary" className={styles.metaText}>
              {t('knowledge.created')} {formatDate(knowledgeDetail?.create_time || '')}
            </Text>
            <Text type="secondary" className={styles.metaText}>
              {t('knowledge.language')}: {knowledgeDetail?.language || t('languages.english')}
            </Text>
          </div>
        </div>
      </div>

      {/* Navigation Menu */}
      <div className={styles.navigation}>
        {menuItems.map((item) => {
          const isActive = currentPath === item.path;
          return (
            <Button
              key={item.key}
              type={isActive ? 'primary' : 'text'}
              icon={item.icon}
              onClick={() => handleMenuClick(item.path)}
              className={`${styles.menuItem} ${isActive ? styles.active : ''}`}
              block
            >
              {item.label}
            </Button>
          );
        })}
      </div>
    </div>
  );
};

export default KnowledgeSidebar;
