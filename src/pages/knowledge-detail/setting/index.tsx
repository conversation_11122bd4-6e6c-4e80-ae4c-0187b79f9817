import { useFetchKnowledgeDetail, useUpdateKnowledge } from '@/hooks/knowledge-hooks';
import {
  SaveOutlined,
  DeleteOutlined,
  ExclamationCircleOutlined,
} from '@ant-design/icons';
import {
  Form,
  Input,
  Select,
  Button,
  Card,
  Typography,
  Space,
  Modal,
  message,
  Divider,
  Row,
  Col,
  Switch,
  InputNumber,
  Tooltip,
  Alert,
} from 'antd';
import React, { useEffect } from 'react';
import { useParams, useNavigate } from 'umi';
import { useTranslate } from '@/hooks/use-i18n';
import { useDeleteKnowledge } from '@/hooks/knowledge-hooks';
import {
  useEmbeddingModelOptions,
  useRerankModelOptions,
  useParserOptions,
  useLanguageOptions
} from '@/hooks/llm-hooks';
import dialogService from '@/services/dialog-service';
import chatService from '@/services/chat-service';
import styles from './index.less';

const { Title, Text } = Typography;
const { TextArea } = Input;
const { Option } = Select;
const { confirm } = Modal;

// Parser类型描述 - 基于RAGFlow原版web目录的国际化文件
const getParserDescriptions = (t: any) => ({
  naive: {
    title: t('knowledge.parserDescriptions.naive.title', 'General'),
    description: t('knowledge.parserDescriptions.naive.description', `Supported file formats are MD, MDX, DOCX, XLSX, XLS (Excel 97-2003), PPT, PDF, TXT, JPEG, JPG, PNG, TIF, GIF, CSV, JSON, EML, HTML.

This method chunks files using a 'naive' method:
• Use vision detection model to split the texts into smaller segments.
• Then, combine adjacent segments until the token count exceeds the threshold specified by 'Chunk token number for text', at which point a chunk is created.`),
    type: 'info'
  },
  qa: {
    title: t('knowledge.parserDescriptions.qa.title', 'Q&A'),
    description: t('knowledge.parserDescriptions.qa.description', `This chunking method supports XLSX and CSV/TXT file formats.

• If a file is in XLSX or XLS (Excel 97-2003) format, it should contain two columns without headers: one for questions and the other for answers, with the question column preceding the answer column. Multiple sheets are acceptable, provided the columns are properly structured.
• If a file is in CSV/TXT format, it must be UTF-8 encoded with TAB as the delimiter to separate questions and answers.`),
    type: 'info'
  },
  resume: {
    title: t('knowledge.parserDescriptions.resume.title', 'Resume'),
    description: t('knowledge.parserDescriptions.resume.description', `Only PDF is supported.

We assume that the resume has a hierarchical section structure, using the lowest section titles as basic unit for chunking documents. Therefore, figures and tables in the same section will not be separated, which may result in larger chunk sizes.`),
    type: 'info'
  },
  manual: {
    title: t('knowledge.parserDescriptions.manual.title', 'Manual'),
    description: t('knowledge.parserDescriptions.manual.description', `Only PDF is supported.

We assume that the manual has a hierarchical section structure, using the lowest section titles as basic unit for chunking documents. Therefore, figures and tables in the same section will not be separated, which may result in larger chunk sizes.`),
    type: 'info'
  },
  table: {
    title: t('knowledge.parserDescriptions.table.title', 'Table'),
    description: t('knowledge.parserDescriptions.table.description', `Supported file formats are XLSX, XLS (Excel 97-2003), CSV.

This method is specifically designed for tabular data. Each row in the table is treated as a separate chunk, preserving the structure and relationships within the data.`),
    type: 'info'
  },
  paper: {
    title: t('knowledge.paper'),
    description: t('knowledge.parserDescriptions.paper.description'),
    type: 'warning'
  },
  book: {
    title: t('knowledge.book'),
    description: t('knowledge.parserDescriptions.book.description'),
    type: 'info'
  },
  laws: {
    title: t('knowledge.laws'),
    description: t('knowledge.parserDescriptions.laws.description'),
    type: 'info'
  },
  presentation: {
    title: t('knowledge.presentation'),
    description: t('knowledge.parserDescriptions.presentation.description'),
    type: 'info'
  },
  picture: {
    title: t('knowledge.picture'),
    description: t('knowledge.parserDescriptions.picture.description'),
    type: 'info'
  },
  one: {
    title: t('knowledge.one'),
    description: t('knowledge.parserDescriptions.one.description'),
    type: 'warning'
  },
  audio: {
    title: t('knowledge.audio'),
    description: t('knowledge.parserDescriptions.audio.description'),
    type: 'info'
  },
  email: {
    title: t('knowledge.email'),
    description: t('knowledge.parserDescriptions.email.description'),
    type: 'info'
  },
  tag: {
    title: t('knowledge.tag'),
    description: t('knowledge.parserDescriptions.tag.description'),
    type: 'warning'
  },
  knowledge_graph: {
    title: t('knowledge.parserDescriptions.knowledge_graph.title'),
    description: t('knowledge.parserDescriptions.knowledge_graph.description'),
    type: 'info'
  }
});

const KnowledgeSettings = () => {
  const { id: knowledgeId } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const [form] = Form.useForm();
  const [selectedParser, setSelectedParser] = React.useState<string>('naive');
  const t = useTranslate(); // Add the translation function

  const { data: knowledgeDetail, loading } = useFetchKnowledgeDetail(knowledgeId || '');
  const { updateKnowledge, loading: updating } = useUpdateKnowledge();
  const { deleteKnowledge, loading: deleting } = useDeleteKnowledge();

  // 获取模型选项
  const { models: embeddingModels, isLoading: embeddingLoading } = useEmbeddingModelOptions();
  const { models: rerankModels, isLoading: rerankLoading } = useRerankModelOptions();
  const parserOptions = useParserOptions();
  const languageOptions = useLanguageOptions();

  // 获取解析器描述
  const PARSER_DESCRIPTIONS = getParserDescriptions(t);

  // 监听RAPTOR开关状态
  const raptorEnabled = Form.useWatch('raptor', form);

  useEffect(() => {
    if (knowledgeDetail) {
      const parserId = knowledgeDetail.parser_id || 'naive';
      setSelectedParser(parserId);

      // 正确解析RAPTOR配置 - 处理可能的嵌套结构
      let raptorConfig = knowledgeDetail.parser_config?.raptor;
      let useRaptor = false;
      let raptorMaxCluster = 64;
      let raptorThreshold = 0.1;
      let raptorMaxToken = 256;
      let raptorRandomSeed = 0;
      let raptorPrompt = '';

      if (raptorConfig) {
        if (typeof raptorConfig === 'boolean') {
          // 简单的布尔值
          useRaptor = raptorConfig;
        } else if (typeof raptorConfig === 'object') {
          // 对象形式，可能有嵌套
          if (raptorConfig.use_raptor !== undefined) {
            useRaptor = raptorConfig.use_raptor;
            raptorMaxCluster = raptorConfig.raptor_max_cluster || raptorConfig.max_cluster || 64;
            raptorThreshold = raptorConfig.raptor_threshold || raptorConfig.threshold || 0.1;
            raptorMaxToken = raptorConfig.raptor_max_token || raptorConfig.max_token || 256;
            raptorRandomSeed = raptorConfig.raptor_random_seed || raptorConfig.random_seed || 0;
            raptorPrompt = raptorConfig.raptor_prompt || raptorConfig.prompt || '';
          } else {
            // 可能是旧格式或者嵌套格式，尝试提取最内层的值
            const extractValue = (obj: any, key: string, defaultValue: any) => {
              if (obj && typeof obj === 'object') {
                if (obj[key] !== undefined) {
                  return obj[key];
                }
                // 递归查找嵌套值
                for (const k in obj) {
                  if (typeof obj[k] === 'object') {
                    const result = extractValue(obj[k], key, null);
                    if (result !== null) return result;
                  }
                }
              }
              return defaultValue;
            };

            useRaptor = extractValue(raptorConfig, 'use_raptor', false);
            raptorMaxCluster = extractValue(raptorConfig, 'raptor_max_cluster', 64);
            raptorThreshold = extractValue(raptorConfig, 'raptor_threshold', 0.1);
            raptorMaxToken = extractValue(raptorConfig, 'raptor_max_token', 256);
            raptorRandomSeed = extractValue(raptorConfig, 'raptor_random_seed', 0);
            raptorPrompt = extractValue(raptorConfig, 'raptor_prompt', '') || extractValue(raptorConfig, 'prompt', '');
          }
        }
      }

      form.setFieldsValue({
        name: knowledgeDetail.name || '',
        description: knowledgeDetail.description || '',
        language: knowledgeDetail.language || 'English',
        permission: knowledgeDetail.permission || 'me',
        parser_id: parserId,
        embd_id: knowledgeDetail.embd_id || '',
        // Parser config fields
        chunk_token_num: knowledgeDetail.parser_config?.chunk_token_num || 512,
        delimiter: knowledgeDetail.parser_config?.delimiter || '\\n!?;。？！',
        auto_keywords: knowledgeDetail.parser_config?.auto_keywords || 0,
        auto_questions: knowledgeDetail.parser_config?.auto_questions || 0,
        html4excel: knowledgeDetail.parser_config?.html4excel || false,
        layout_recognize: knowledgeDetail.parser_config?.layout_recognize || 'DeepDOC',
        task_page_size: knowledgeDetail.parser_config?.task_page_size || 0,
        // 正确设置RAPTOR参数
        raptor: useRaptor,
        raptor_max_cluster: raptorMaxCluster,
        raptor_threshold: raptorThreshold,
        raptor_max_token: raptorMaxToken,
        raptor_random_seed: raptorRandomSeed,
        raptor_prompt: raptorPrompt,
        // Advanced settings (只包含后端支持的字段)
        pagerank: knowledgeDetail.pagerank || 0,
        // 注意：以下字段在RAGFlow知识库API中不被支持，但保留在表单中用于显示
        similarity_threshold: 0.1,
        vector_similarity_weight: 0.3,
        top_n: 6,
        rerank_id: '',
      });
    }
  }, [knowledgeDetail, form]);

  // Handle RAPTOR switch change
  const handleRaptorChange = (checked: boolean) => {
    if (!checked) {
      // Reset RAPTOR related fields when disabled
      form.setFieldsValue({
        raptor_max_cluster: 64,
        raptor_threshold: 0.1,
      });
    }
  };

  const handleSave = async () => {
    try {
      const values = await form.validateFields();

      // 根据RAGFlow后端API要求构建更新参数
      // 注意：kb_id作为路径参数传递，不包含在请求体中

      // 正确构建RAPTOR配置，避免嵌套问题
      const raptorConfig = values.raptor ? {
        use_raptor: true,
        max_cluster: values.raptor_max_cluster || 64,
        threshold: values.raptor_threshold || 0.1,
        max_token: values.raptor_max_token || 256,
        random_seed: values.raptor_random_seed || 0,
        prompt: values.raptor_prompt || '',
      } : {
        use_raptor: false
      };

      const updateParams = {
        kb_id: knowledgeId!, // 用于API调用，但会在service中处理
        name: values.name,
        description: values.description || '', // 确保description不为undefined
        language: values.language,
        permission: values.permission,
        parser_id: values.parser_id,
        embd_id: values.embd_id,
        parser_config: {
          chunk_token_num: values.chunk_token_num,
          delimiter: values.delimiter,
          auto_keywords: values.auto_keywords,
          auto_questions: values.auto_questions,
          html4excel: values.html4excel,
          layout_recognize: values.layout_recognize,
          task_page_size: values.task_page_size || 0,
          // 使用正确的RAPTOR配置格式，参考RAGFlow原版
          raptor: raptorConfig,
          graphrag: {
            use_graphrag: values.graphrag || false,
            ...(values.graphrag ? {
              // 添加graphrag相关配置
            } : {})
          },
        },
        // pagerank作为顶级参数传递，参考RAGFlow原版
        pagerank: values.pagerank || 0,
        // 注意：以下字段在RAGFlow知识库更新API中不被支持，暂时注释
        // similarity_threshold: values.similarity_threshold,
        // vector_similarity_weight: values.vector_similarity_weight,
        // top_n: values.top_n,
        // rerank_id: values.rerank_id,
      };

      console.log('Updating knowledge base with params:', updateParams);
      console.log('RAPTOR config:', updateParams.parser_config.raptor);
      console.log('PageRank:', updateParams.pagerank);
      console.log('Form values:', values);

      const result = await updateKnowledge(updateParams);
      console.log('Update result:', result);
      message.success(t('knowledge.updateSuccess', 'Knowledge base updated successfully!'));
    } catch (error: any) {
      console.error('Update failed:', error);
      message.error(error?.message || t('knowledge.updateFailed', 'Failed to update knowledge base'));
    }
  };

  const handleDelete = () => {
    confirm({
      title: t('knowledge.deleteKnowledgeBase', 'Delete Knowledge Base'),
      icon: <ExclamationCircleOutlined />,
      content: (
        <div>
          <p>{t('knowledge.deleteConfirmMessage', 'Are you sure you want to delete this knowledge base?')}</p>
          <p><strong>{t('knowledge.deleteCannotUndo', 'This action cannot be undone.')}</strong></p>
          <p>{t('knowledge.deleteAllData', 'All documents and data will be permanently removed.')}</p>
        </div>
      ),
      okText: t('common.delete', 'Delete'),
      okType: 'danger',
      cancelText: t('common.cancel', 'Cancel'),
      onOk: async () => {
        try {
          // 首先获取与该知识库关联的所有dialog
          const dialogsResponse = await dialogService.listDialog();

          // 确保 dialogsResponse.data 是数组
          const dialogsData = Array.isArray(dialogsResponse.data)
            ? dialogsResponse.data
            : (dialogsResponse.data && typeof dialogsResponse.data === 'object' && 'data' in dialogsResponse.data
               ? (dialogsResponse.data as any).data || []
               : []);

          const relatedDialogs = dialogsData.filter((dialog: any) =>
            dialog.kb_ids && dialog.kb_ids.includes(knowledgeId)
          );

          // 删除关联的conversations
          for (const dialog of relatedDialogs) {
            try {
              const conversationsResponse = await chatService.listConversations({ dialog_id: dialog.id });
              if (conversationsResponse.data && conversationsResponse.data.length > 0) {
                // 逐个删除conversations，因为API只支持单个删除
                for (const conv of conversationsResponse.data) {
                  await chatService.deleteConversation({ conversation_id: conv.id });
                }
              }
            } catch (error) {
              console.warn('Failed to delete conversations for dialog:', dialog.id, error);
            }
          }

          // 删除关联的dialogs
          if (relatedDialogs.length > 0) {
            const dialogIds = relatedDialogs.map((dialog: any) => dialog.id);
            await dialogService.removeDialog({ dialogIds });
          }

          // 最后删除知识库
          await deleteKnowledge(knowledgeId!);
          message.success(t('knowledge.deleteSuccess', 'Knowledge base and related dialogs deleted successfully!'));
          console.log('Knowledge base deleted, navigating to knowledge list');
          // 使用setTimeout确保消息显示后再跳转
          setTimeout(() => {
            navigate('/knowledge');
          }, 500);
        } catch (error) {
          console.error('Delete failed:', error);
          message.error(t('knowledge.deleteFailed', 'Failed to delete knowledge base'));
        }
      },
    });
  };

  if (loading) {
    return (
      <div className={styles.loading}>
        <div>{t('knowledge.loadingSettings', 'Loading knowledge base settings...')}</div>
      </div>
    );
  }

  return (
    <div className={styles.knowledgeSettings}>
      {/* Header */}
      <div className={styles.header}>
        <div className={styles.headerLeft}>
          <Title level={3} style={{ margin: 0 }}>
            {t('knowledge.settingsTitle', 'Knowledge Base Settings')}
          </Title>
          <Text type="secondary">
            {t('knowledge.settingsDescription', 'Configure your knowledge base properties and processing settings')}
          </Text>
        </div>
        <div className={styles.headerRight}>
          <Space>
            <Button
              type="primary"
              icon={<SaveOutlined />}
              onClick={handleSave}
              loading={updating}
            >
              {t('common.saveChanges', 'Save Changes')}
            </Button>
          </Space>
        </div>
      </div>

      <Form
        form={form}
        layout="vertical"
        className={styles.settingsForm}
      >
        {/* General Settings */}
        <Card title={t('knowledge.generalSettings', 'General Settings')} className={styles.settingsCard}>
          {/* 第一行：4个基本配置项 */}
          <Row gutter={[24, 16]}>
            <Col xs={24} sm={12} lg={6}>
              <Form.Item
                name="name"
                label={t('knowledge.knowledgeBaseName', 'Knowledge Base Name')}
                rules={[
                  { required: true, message: t('knowledge.nameRequired', 'Please enter knowledge base name') },
                  { max: 100, message: t('knowledge.nameMaxLength', 'Name cannot exceed 100 characters') },
                ]}
              >
                <Input placeholder={t('knowledge.namePlaceholder', 'Enter knowledge base name')} />
              </Form.Item>
            </Col>
            <Col xs={24} sm={12} lg={6}>
              <Form.Item
                name="language"
                label={t('knowledge.language', 'Language')}
                rules={[{ required: true, message: t('knowledge.languageRequired', 'Please select language') }]}
              >
                <Select placeholder={t('knowledge.languagePlaceholder', 'Select language')}>
                  {languageOptions.map(option => (
                    <Option key={option.value} value={option.value}>
                      {option.label}
                    </Option>
                  ))}
                </Select>
              </Form.Item>
            </Col>
            <Col xs={24} sm={12} lg={6}>
              <Form.Item
                name="permission"
                label={t('knowledge.permission', 'Permission')}
                rules={[{ required: true, message: t('knowledge.permissionRequired', 'Please select permission') }]}
              >
                <Select placeholder={t('knowledge.permissionPlaceholder', 'Select permission')}>
                  <Option value="me">{t('knowledge.permissionPrivate', 'Private (Only me)')}</Option>
                  <Option value="team">{t('knowledge.permissionTeam', 'Team')}</Option>
                  <Option value="public">{t('knowledge.permissionPublic', 'Public')}</Option>
                </Select>
              </Form.Item>
            </Col>
          </Row>

          {/* 第二行：PDF解析器配置 */}
          <Row gutter={[24, 16]}>
            <Col xs={24} sm={12} lg={6}>
              <Form.Item
                name="layout_recognize"
                label={t('knowledge.pdfParser', 'PDF Parser')}
                rules={[{ required: true, message: t('knowledge.pdfParserRequired', 'Please select PDF parser') }]}
              >
                <Select placeholder={t('knowledge.pdfParserPlaceholder', 'Select PDF parser')}>
                  <Option value="DeepDOC">DeepDOC</Option>
                  <Option value="OCR">OCR</Option>
                  <Option value="Manual">Manual</Option>
                </Select>
              </Form.Item>
            </Col>
            <Col xs={24} sm={12} lg={6}>
              <Form.Item
                name="parser_id"
                label={t('knowledge.parserType', 'Parser Type')}
                rules={[{ required: true, message: t('knowledge.parserTypeRequired', 'Please select parser type') }]}
              >
                <Select
                  placeholder={t('knowledge.parserTypePlaceholder', 'Select parser type')}
                  onChange={(value) => setSelectedParser(value)}
                >
                  {parserOptions.map(option => (
                    <Option key={option.value} value={option.value}>
                      {option.label}
                    </Option>
                  ))}
                </Select>
              </Form.Item>
            </Col>
          </Row>

          {/* 第二行：Description单独一行 */}
          <Row gutter={[24, 16]}>
            <Col span={24}>
              <Form.Item
                name="description"
                label={t('knowledge.description', 'Description')}
                rules={[
                  { max: 500, message: t('knowledge.descriptionMaxLength', 'Description cannot exceed 500 characters') },
                ]}
              >
                <TextArea
                  rows={4}
                  placeholder={t('knowledge.descriptionPlaceholder', 'Enter description (optional)')}
                  showCount
                  maxLength={500}
                />
              </Form.Item>
            </Col>
          </Row>

          {/* Parser Type Description */}
          {selectedParser && PARSER_DESCRIPTIONS[selectedParser as keyof typeof PARSER_DESCRIPTIONS] && (
            <Row gutter={[24, 0]} style={{ marginTop: 16 }} className={styles.parserDescription}>
              <Col span={24}>
                <Alert
                  message={t('knowledge.parserMethodTitle', '{{title}} Parser Method', {
                    title: PARSER_DESCRIPTIONS[selectedParser as keyof typeof PARSER_DESCRIPTIONS].title
                  })}
                  description={
                    <div style={{ whiteSpace: 'pre-line' }}>
                      {PARSER_DESCRIPTIONS[selectedParser as keyof typeof PARSER_DESCRIPTIONS].description}
                    </div>
                  }
                  type={PARSER_DESCRIPTIONS[selectedParser as keyof typeof PARSER_DESCRIPTIONS].type as any}
                  showIcon
                  style={{ marginBottom: 0 }}
                />
              </Col>
            </Row>
          )}
        </Card>

        {/* Processing Settings */}
        <Card title={t('knowledge.processingSettings', 'Processing Settings')} className={`${styles.settingsCard} ${styles.processingSettings}`}>
          {/* 第一行：8个配置项 */}
          <Row gutter={[12, 16]}>
            <Col xs={24} sm={12} md={8} lg={3}>
              <Form.Item
                name="chunk_token_num"
                label={t('knowledge.chunkTokens')}
                rules={[
                  { required: true, message: t('knowledge.chunkTokensRequired') },
                  { type: 'number', min: 1, max: 2048, message: t('knowledge.chunkTokensRange') },
                ]}
                tooltip={
                  <div style={{ whiteSpace: 'pre-line', maxWidth: 300 }}>
                    {t('knowledge.chunkTokensTooltip')}
                  </div>
                }
              >
                <InputNumber
                  min={1}
                  max={2048}
                  placeholder="512"
                />
              </Form.Item>
            </Col>
            <Col xs={24} sm={12} md={8} lg={3}>
              <Form.Item
                name="delimiter"
                label={t('knowledge.delimiter')}
                rules={[{ required: true, message: t('knowledge.delimiterRequired') }]}
                tooltip={
                  <div style={{ whiteSpace: 'pre-line', maxWidth: 300 }}>
                    {t('knowledge.delimiterTooltip')}
                  </div>
                }
              >
                <Input placeholder="\\n!?;。？！" />
              </Form.Item>
            </Col>
            <Col xs={24} sm={12} md={8} lg={3}>
              <Form.Item
                name="auto_keywords"
                label={t('knowledge.autoKeywords')}
                rules={[
                  { type: 'number', min: 0, max: 30, message: t('knowledge.autoKeywordsRange') },
                ]}
                tooltip={t('knowledge.autoKeywordsTooltip')}
              >
                <InputNumber
                  min={0}
                  max={10}
                  placeholder="0"
                />
              </Form.Item>
            </Col>
            <Col xs={24} sm={12} md={8} lg={3}>
              <Form.Item
                name="auto_questions"
                label={t('knowledge.autoQuestions')}
                rules={[
                  { type: 'number', min: 0, max: 10, message: t('knowledge.autoQuestionsRange') },
                ]}
                tooltip={t('knowledge.autoQuestionsTooltip')}
              >
                <InputNumber
                  min={0}
                  max={10}
                  placeholder="0"
                />
              </Form.Item>
            </Col>
            <Col xs={24} sm={12} md={8} lg={3}>
              <Form.Item
                name="html4excel"
                label={t('knowledge.html4excel')}
                valuePropName="checked"
                tooltip={t('knowledge.html4excelTooltip')}
              >
                <Switch />
              </Form.Item>
            </Col>

            <Col xs={24} sm={12} md={8} lg={3}>
              <Form.Item
                name="pagerank"
                label={t('knowledge.pageRank')}
                rules={[
                  { type: 'number', min: 0, max: 100, message: t('knowledge.pageRankRange') },
                ]}
                tooltip={t('knowledge.pageRankTooltip')}
              >
                <InputNumber
                  min={0}
                  max={100}
                  placeholder="0"
                />
              </Form.Item>
            </Col>
            <Col xs={24} sm={12} md={8} lg={3}>
              <Form.Item
                name="embd_id"
                label={t('knowledge.embedModel', 'Embed Model')}
                tooltip={
                  <div style={{ whiteSpace: 'pre-line', maxWidth: 300 }}>
                    {t('knowledge.embedModelTooltip')}
                  </div>
                }
              >
                <Select
                  placeholder={t('knowledge.selectModel', 'Select model')}
                  loading={embeddingLoading}
                  disabled={true}
                  notFoundContent={embeddingLoading ? t('common.loading', 'Loading...') : t('knowledge.noModelsAvailable', 'No models available')}
                >
                  {embeddingModels.map(model => (
                    <Option key={model.value} value={model.value}>
                      {model.label}
                    </Option>
                  ))}
                </Select>
              </Form.Item>
            </Col>
          </Row>

          {/* 第二行：RAPTOR配置（6个配置项） */}
          <Row gutter={[12, 16]}>
            <Col xs={24} sm={12} md={8} lg={3}>
              <Form.Item
                name="raptor"
                label={t('knowledge.raptor')}
                valuePropName="checked"
                tooltip={t('knowledge.raptorTooltip')}
              >
                <Switch />
              </Form.Item>
            </Col>
            <Col xs={24} sm={12} md={8} lg={3}>
              <Form.Item
                name="raptor_max_cluster"
                label={t('knowledge.maxClusters')}
                rules={[
                  { type: 'number', min: 1, max: 256, message: t('knowledge.maxClustersRange') },
                ]}
                tooltip={t('knowledge.maxClustersTooltip')}
              >
                <InputNumber
                  min={1}
                  max={256}
                  placeholder="64"
                  disabled={!raptorEnabled}
                />
              </Form.Item>
            </Col>
            <Col xs={24} sm={12} md={8} lg={3}>
              <Form.Item
                name="raptor_threshold"
                label={t('knowledge.threshold')}
                rules={[
                  { type: 'number', min: 0, max: 1, message: t('knowledge.thresholdRange') },
                ]}
                tooltip={t('knowledge.thresholdTooltip')}
              >
                <InputNumber
                  min={0}
                  max={1}
                  step={0.1}
                  placeholder="0.1"
                  disabled={!raptorEnabled}
                />
              </Form.Item>
            </Col>
            <Col xs={24} sm={12} md={8} lg={3}>
              <Form.Item
                name="raptor_max_token"
                label={t('knowledge.maxTokens')}
                rules={[
                  { type: 'number', min: 0, max: 2048, message: t('knowledge.maxTokensRange') },
                ]}
                tooltip={t('knowledge.maxTokensTooltip')}
              >
                <InputNumber
                  min={0}
                  max={2048}
                  placeholder="256"
                  disabled={!raptorEnabled}
                />
              </Form.Item>
            </Col>
            <Col xs={24} sm={12} md={8} lg={3}>
              <Form.Item
                name="raptor_random_seed"
                label={t('knowledge.randomSeed')}
                rules={[
                  { type: 'number', min: 0, max: 999999, message: t('knowledge.randomSeedRange') },
                ]}
                tooltip={t('knowledge.randomSeedTooltip')}
              >
                <InputNumber
                  min={0}
                  max={999999}
                  placeholder="0"
                  disabled={!raptorEnabled}
                />
              </Form.Item>
            </Col>
          </Row>

          {/* RAPTOR提示词配置 - 当RAPTOR启用时显示 */}
          {raptorEnabled && (
            <Row gutter={[12, 16]} style={{ marginTop: 16 }}>
              <Col span={24}>
                <Form.Item
                  name="raptor_prompt"
                  label={t('knowledge.raptorPrompt')}
                  tooltip={t('knowledge.raptorPromptTooltip')}
                >
                  <Input.TextArea
                    rows={4}
                    placeholder={t('knowledge.raptorPromptPlaceholder')}
                    disabled={!raptorEnabled}
                  />
                </Form.Item>
              </Col>
            </Row>
          )}
        </Card>



        {/* Danger Zone */}
        <Card title={t('knowledge.dangerZone', 'Danger Zone')} className={styles.dangerCard}>
          <div className={styles.dangerContent}>
            <div className={styles.dangerText}>
              <Title level={5} style={{ color: '#ff4d4f', margin: 0 }}>
                {t('knowledge.deleteKnowledgeBase', 'Delete Knowledge Base')}
              </Title>
              <Text type="secondary">
                {t('knowledge.deleteWarning', 'Once you delete a knowledge base, there is no going back. Please be certain.')}
              </Text>
            </div>
            <Button
              danger
              icon={<DeleteOutlined />}
              onClick={handleDelete}
              loading={deleting}
            >
              {t('knowledge.deleteKnowledgeBase', 'Delete Knowledge Base')}
            </Button>
          </div>
        </Card>
      </Form>
    </div>
  );
};

export default KnowledgeSettings;
