.knowledgeSettings {
  padding: 24px;
  background: #f0f2f5;
  min-height: 100vh;
}

.loading {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 400px;
  color: #666;
  font-size: 16px;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 24px;
  padding: 24px;
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

.headerLeft {
  h3 {
    color: #1f2937;
    font-weight: 600;
    margin-bottom: 4px;
  }
}

.headerRight {
  display: flex;
  gap: 12px;
}

.settingsForm {
  .ant-form-item-label > label {
    font-weight: 500;
    color: #374151;
  }
}

.settingsCard {
  margin-bottom: 24px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  
  .ant-card-head {
    border-bottom: 1px solid #e8e8e8;
    
    .ant-card-head-title {
      font-weight: 600;
      color: #1f2937;
      font-size: 16px;
    }
  }
  
  .ant-card-body {
    padding: 24px;
  }
  
  .ant-input,
  .ant-select-selector,
  .ant-input-number,
  .ant-input-number-input {
    border-radius: 6px;
    border: 1px solid #d1d5db;
    
    &:hover {
      border-color: #667eea;
    }
    
    &:focus,
    &.ant-input-focused,
    &.ant-select-focused .ant-select-selector,
    &.ant-input-number-focused {
      border-color: #667eea;
      box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.1);
    }
  }
  
  .ant-select-dropdown {
    border-radius: 6px;
  }
  
  .ant-switch {
    &.ant-switch-checked {
      background-color: #667eea;
    }
  }
}

.dangerCard {
  border: 1px solid #ffccc7;
  
  .ant-card-head {
    background: #fff2f0;
    border-bottom: 1px solid #ffccc7;
    
    .ant-card-head-title {
      color: #ff4d4f;
    }
  }
}

.dangerContent {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 24px;
}

.dangerText {
  flex: 1;
  
  h5 {
    margin-bottom: 4px !important;
  }
}

// 中等屏幕优化 (768px - 1199px)
@media (max-width: 1199px) and (min-width: 768px) {
  .settingsCard {
    // 8列布局在中等屏幕上改为4列
    .ant-row:has(.ant-col-lg-3) {
      .ant-col {
        .ant-form-item-label {
          font-size: 13px;
        }

        .ant-input,
        .ant-select-selector,
        .ant-input-number {
          font-size: 13px;
        }
      }
    }
  }

  // Processing Settings 中等屏幕优化
  .processingSettings {
    .ant-row {
      .ant-col {
        min-width: 160px;

        .ant-form-item {
          .ant-form-item-label > label {
            font-size: 12px;
          }

          .ant-form-item-control {
            .ant-input,
            .ant-input-number,
            .ant-select-selector {
              font-size: 12px;
              height: 30px;
            }
          }
        }
      }
    }
  }
}

@media (max-width: 768px) {
  .knowledgeSettings {
    padding: 16px;
  }

  .header {
    flex-direction: column;
    gap: 16px;
    align-items: stretch;
    padding: 16px;
  }

  .headerRight {
    justify-content: stretch;

    .ant-btn {
      flex: 1;
    }
  }

  .settingsCard {
    .ant-card-body {
      padding: 16px;
    }

    // 在中等屏幕上，每行显示2个配置项
    .ant-row {
      .ant-col {
        margin-bottom: 16px;

        // 恢复正常字体大小
        .ant-form-item-label {
          font-size: 14px !important;
        }

        .ant-input,
        .ant-select-selector,
        .ant-input-number {
          font-size: 14px !important;
        }
      }
    }
  }

  // Processing Settings 在小屏幕上的特殊处理
  .processingSettings {
    .ant-row {
      .ant-col {
        min-width: auto;

        .ant-form-item {
          .ant-form-item-label > label {
            font-size: 14px !important;
            white-space: normal !important;  // 小屏幕允许换行
          }

          .ant-form-item-control {
            .ant-input,
            .ant-input-number,
            .ant-select-selector {
              font-size: 14px !important;
              height: 32px !important;
            }

            .ant-switch {
              transform: scale(1) !important;
            }
          }
        }
      }
    }
  }

  .dangerContent {
    flex-direction: column;
    align-items: stretch;
    gap: 16px;
    text-align: center;
  }
}

// Parser描述样式
.parserDescription {
  .ant-alert {
    border-radius: 8px;

    .ant-alert-message {
      font-weight: 600;
      margin-bottom: 8px;
    }

    .ant-alert-description {
      line-height: 1.6;

      ul, ol {
        margin: 8px 0;
        padding-left: 20px;
      }

      li {
        margin: 4px 0;
      }

      strong, b {
        color: #1f2937;
        font-weight: 600;
      }

      code {
        background: #f3f4f6;
        padding: 2px 6px;
        border-radius: 4px;
        font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
        font-size: 0.9em;
      }
    }

    &.ant-alert-info {
      background-color: #f0f9ff;
      border-color: #bae6fd;

      .ant-alert-icon {
        color: #0284c7;
      }

      .ant-alert-message {
        color: #0c4a6e;
      }
    }

    &.ant-alert-warning {
      background-color: #fffbeb;
      border-color: #fed7aa;

      .ant-alert-icon {
        color: #d97706;
      }

      .ant-alert-message {
        color: #92400e;
      }
    }
  }
}

// Processing Settings 专用样式优化
.processingSettings {
  // 8列布局的特殊样式
  .ant-row {
    .ant-col {
      // 确保最小宽度，防止过度压缩
      min-width: 140px;

      .ant-form-item {
        margin-bottom: 16px;

        // 标签样式优化
        .ant-form-item-label {
          padding-bottom: 4px;

          > label {
            font-size: 11px;
            font-weight: 500;
            line-height: 1.2;
            white-space: nowrap;  // 防止标签换行
            overflow: hidden;
            text-overflow: ellipsis;
            max-width: 100%;
            display: block;
          }
        }

        // 控制组件容器
        .ant-form-item-control {
          .ant-form-item-control-input {
            min-height: auto;

            .ant-form-item-control-input-content {
              // 输入框样式
              .ant-input,
              .ant-input-number,
              .ant-select {
                font-size: 11px;
                height: 28px;

                &.ant-input-number {
                  .ant-input-number-input {
                    height: 26px;
                    font-size: 11px;
                  }
                }
              }

              // Select选择器
              .ant-select-selector {
                font-size: 11px;
                height: 28px !important;

                .ant-select-selection-item {
                  line-height: 26px;
                  font-size: 11px;
                }
              }

              // Switch开关
              .ant-switch {
                margin-top: 2px;
                transform: scale(0.8);
                transform-origin: left center;
              }
            }
          }
        }
      }
    }
  }
}

// 通用8列布局优化
.settingsCard {
  // 8列布局的特殊样式
  .ant-row {
    // 确保8列布局在大屏幕上的间距合适
    &:has(.ant-col-lg-3) {
      .ant-col {
        // 减小8列布局的标签字体大小以适应更窄的列
        .ant-form-item-label {
          font-size: 12px;
          line-height: 1.3;
        }

        // 优化输入框和选择器的大小
        .ant-input,
        .ant-select-selector,
        .ant-input-number {
          font-size: 12px;
        }

        // 优化Switch组件的对齐
        .ant-switch {
          margin-top: 4px;
        }
      }
    }
  }
}

@media (max-width: 480px) {
  .knowledgeSettings {
    padding: 12px;
  }

  .header {
    padding: 12px;
  }

  .settingsCard {
    .ant-card-body {
      padding: 12px;
    }

    // 在小屏幕上，每行显示1个配置项
    .ant-row {
      .ant-col {
        margin-bottom: 12px;
      }
    }

    // 优化表单项间距
    .ant-form-item {
      margin-bottom: 16px;

      .ant-form-item-label {
        padding-bottom: 4px;
      }
    }
  }
}
