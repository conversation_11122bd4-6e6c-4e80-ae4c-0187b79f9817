import React, { useState } from 'react';
import { useParams } from 'umi';
import {
  Card,
  Input,
  Button,
  List,
  Typography,
  Space,
  Tag,
  Divider,
  Row,
  Col,
  InputNumber,
  Switch,
  Form,
  Spin,
  Empty,
  message,
  Select,
} from 'antd';
import { SearchOutlined, SettingOutlined } from '@ant-design/icons';
import { useRetrievalTest } from '@/hooks/retrieval-hooks';
import { useRerankModelOptions } from '@/hooks/llm-hooks';
import { useTranslate } from '@/hooks/use-i18n';
import styles from './index.less';

const { TextArea } = Input;
const { Title, Text, Paragraph } = Typography;
const { Option } = Select;

interface RetrievalResult {
  chunk_id: string;
  content_with_weight: string;
  doc_id: string;
  docnm_kwd: string;
  similarity: number;
  important_kwd: string[];
  question_kwd: string[];
  image_id?: string;
  available_int?: number;
}

const RetrievalTesting: React.FC = () => {
  const t = useTranslate();
  const { id: knowledgeId } = useParams<{ id: string }>();
  const [form] = Form.useForm();
  const [question, setQuestion] = useState('');
  const [showSettings, setShowSettings] = useState(true); // 默认显示设置
  const [results, setResults] = useState<RetrievalResult[]>([]);
  
  const { testRetrieval, loading } = useRetrievalTest();
  const { models: rerankModels = [], isLoading: rerankLoading } = useRerankModelOptions();

  // 处理高亮内容的函数
  const processHighlightContent = (content: string, question: string) => {
    if (!content) return content;

    // 如果内容已经包含HTML标签，直接返回
    if (content.includes('<mark>') || content.includes('<em>') || content.includes('<strong>')) {
      return content;
    }

    // 如果没有高亮标签，尝试手动高亮关键词
    if (question && question.trim()) {
      const keywords = question.trim().split(/\s+/).filter(word => word.length > 2);
      let highlightedContent = content;

      keywords.forEach(keyword => {
        const regex = new RegExp(`(${keyword})`, 'gi');
        highlightedContent = highlightedContent.replace(regex, '<mark>$1</mark>');
      });

      return highlightedContent;
    }

    return content;
  };

  const handleTest = async () => {
    if (!question.trim()) {
      message.warning(t('retrieval.enterQuestion'));
      return;
    }

    try {
      const settings = await form.validateFields();
      const response = await testRetrieval({
        kb_id: knowledgeId!,
        question: question.trim(),
        similarity_threshold: settings.similarity_threshold || 0.0,
        vector_similarity_weight: settings.vector_similarity_weight || 0.3,
        top_k: settings.top_k || 10,
        use_kg: settings.use_kg || false,
        highlight: settings.highlight !== false,
        rerank_id: settings.rerank_id || '',
        page: 1,
        size: settings.top_k || 10,
      });
      
      console.log('Retrieval response:', response);
      console.log('First chunk content:', response.chunks?.[0]?.content_with_weight);
      setResults(response.chunks || []);
      message.success(t('retrieval.foundChunks', {
        count: response.chunks?.length || 0,
        total: response.total || 0
      }));
    } catch (error) {
      console.error('Retrieval test failed:', error);
      message.error(t('retrieval.testFailed'));
    }
  };

  const renderResult = (item: RetrievalResult, index: number) => (
    <List.Item key={item.chunk_id}>
      <Card 
        size="small" 
        className={styles.resultCard}
        title={
          <Space>
            <Text strong>#{index + 1}</Text>
            <Text type="secondary">{item.docnm_kwd}</Text>
            <Tag color="blue">{t('retrieval.score')}: {((item.similarity || 0) * 100).toFixed(2)}%</Tag>
          </Space>
        }
      >
        <Paragraph
          ellipsis={{ rows: 3, expandable: true, symbol: t('common.more') }}
          className={styles.content}
        >
          <div dangerouslySetInnerHTML={{
            __html: processHighlightContent(item.content_with_weight, question)
          }} />
        </Paragraph>
        
        {item.important_kwd && item.important_kwd.length > 0 && (
          <div className={styles.keywords}>
            <Text type="secondary" style={{ fontSize: '12px' }}>{t('retrieval.keywords')}: </Text>
            {item.important_kwd.map((keyword, idx) => (
              <Tag key={idx} size="small" color="orange">
                {keyword}
              </Tag>
            ))}
          </div>
        )}

        {item.question_kwd && item.question_kwd.length > 0 && (
          <div className={styles.questions}>
            <Text type="secondary" style={{ fontSize: '12px' }}>{t('retrieval.questions')}: </Text>
            {item.question_kwd.map((q, idx) => (
              <Tag key={idx} size="small" color="green">
                {q}
              </Tag>
            ))}
          </div>
        )}
      </Card>
    </List.Item>
  );

  return (
    <div className={styles.container}>
      <Card title={t('knowledge.retrievalTesting')} className={styles.mainCard}>
        <Row gutter={[24, 24]}>
          <Col xs={24} lg={showSettings ? 16 : 24}>
            <Space direction="vertical" style={{ width: '100%' }} size="large">
              <div>
                <Space style={{ marginBottom: 16 }}>
                  <Title level={5} style={{ margin: 0 }}>{t('retrieval.testQuestion')}</Title>
                  <Button
                    type="text"
                    icon={<SettingOutlined />}
                    onClick={() => setShowSettings(!showSettings)}
                  >
                    {showSettings ? t('common.hide') : t('common.show')} {t('retrieval.retrievalSettings')}
                  </Button>
                </Space>
                <TextArea
                  value={question}
                  onChange={(e) => setQuestion(e.target.value)}
                  placeholder={t('retrieval.questionPlaceholder')}
                  rows={3}
                  style={{ marginBottom: 16 }}
                />
                <Button
                  type="primary"
                  icon={<SearchOutlined />}
                  onClick={handleTest}
                  loading={loading}
                  size="large"
                >
                  {t('retrieval.testRetrieval')}
                </Button>
              </div>

              <Divider />

              <div>
                <Space>
                  <Title level={5} style={{ margin: 0 }}>{t('retrieval.results')} ({results.length})</Title>
                  {form.getFieldValue('highlight') !== false && (
                    <Tag color="green" size="small">{t('retrieval.highlightOn')}</Tag>
                  )}
                </Space>
                {loading ? (
                  <div style={{ textAlign: 'center', padding: '40px' }}>
                    <Spin size="large" />
                  </div>
                ) : results.length > 0 ? (
                  <List
                    dataSource={results}
                    renderItem={renderResult}
                    pagination={{
                      pageSize: 5,
                      showSizeChanger: true,
                      showQuickJumper: true,
                      showTotal: (total) => t('retrieval.totalResults', { total }),
                    }}
                  />
                ) : (
                  <Empty description={t('retrieval.noResults')} />
                )}
              </div>
            </Space>
          </Col>

          {showSettings && (
            <Col xs={24} lg={8}>
              <Card
                title={t('retrieval.retrievalSettings')}
                size="small"
                extra={
                  <Text type="secondary" style={{ fontSize: '12px' }}>
                    {t('retrieval.configureParameters')}
                  </Text>
                }
              >
                <div style={{ marginBottom: 16 }}>
                  <Text type="secondary" style={{ fontSize: '13px' }}>
                    {t('retrieval.adjustParameters')}
                  </Text>
                </div>
                <Form
                  form={form}
                  layout="vertical"
                  initialValues={{
                    similarity_threshold: 0.0,
                    vector_similarity_weight: 0.3,
                    top_k: 10,
                    use_kg: false,
                    highlight: true,
                    rerank_id: '',
                  }}
                >
                  <Form.Item
                    name="similarity_threshold"
                    label={t('retrieval.similarityThreshold')}
                    tooltip={t('retrieval.similarityThresholdTooltip')}
                  >
                    <InputNumber
                      min={0}
                      max={1}
                      step={0.1}
                      style={{ width: '100%' }}
                      placeholder="0.0"
                    />
                  </Form.Item>

                  <Form.Item
                    name="vector_similarity_weight"
                    label={t('retrieval.vectorSimilarityWeight')}
                    tooltip={t('retrieval.vectorSimilarityWeightTooltip')}
                  >
                    <InputNumber
                      min={0}
                      max={1}
                      step={0.1}
                      style={{ width: '100%' }}
                      placeholder="0.3"
                    />
                  </Form.Item>

                  <Form.Item
                    name="top_k"
                    label={t('retrieval.topKResults')}
                    tooltip={t('retrieval.topKResultsTooltip')}
                  >
                    <InputNumber
                      min={1}
                      max={100}
                      style={{ width: '100%' }}
                      placeholder="10"
                    />
                  </Form.Item>

                  <Form.Item
                    name="rerank_id"
                    label={t('retrieval.rerankModel')}
                    tooltip={t('retrieval.rerankModelTooltip')}
                  >
                    <Select
                      placeholder={t('retrieval.selectRerankModel')}
                      allowClear
                      loading={rerankLoading}
                      notFoundContent={rerankLoading ? t('common.loading') : t('retrieval.noModelsAvailable')}
                    >
                      {rerankModels.map(model => (
                        <Option key={model.value} value={model.value}>
                          {model.label}
                        </Option>
                      ))}
                    </Select>
                  </Form.Item>

                  <Form.Item
                    name="use_kg"
                    label={t('retrieval.useKnowledgeGraph')}
                    valuePropName="checked"
                    tooltip={t('retrieval.useKnowledgeGraphTooltip')}
                  >
                    <Switch />
                  </Form.Item>

                  <Form.Item
                    name="highlight"
                    label={t('retrieval.highlightKeywords')}
                    valuePropName="checked"
                    tooltip={t('retrieval.highlightKeywordsTooltip')}
                  >
                    <Switch
                      checkedChildren={t('common.on')}
                      unCheckedChildren={t('common.off')}
                    />
                  </Form.Item>
                </Form>
              </Card>
            </Col>
          )}
        </Row>
      </Card>
    </div>
  );
};

export default RetrievalTesting;
