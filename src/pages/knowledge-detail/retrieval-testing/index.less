.container {
  padding: 24px;
  background: #f5f5f5;
  min-height: 100vh;
}

.mainCard {
  .ant-card-body {
    padding: 24px;
  }
}

.resultCard {
  margin-bottom: 16px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;

  &:hover {
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
    transform: translateY(-2px);
  }

  .ant-card-head {
    border-bottom: 1px solid #f0f0f0;
    padding: 12px 16px;
    min-height: auto;

    .ant-card-head-title {
      padding: 0;
      font-size: 14px;
    }
  }

  .ant-card-body {
    padding: 16px;
  }
}

.content {
  margin-bottom: 12px;
  line-height: 1.6;

  :global {
    mark {
      background-color: #fff3cd;
      padding: 2px 4px;
      border-radius: 3px;
      font-weight: 500;
      color: #856404;
    }

    em {
      background-color: #e7f3ff;
      font-style: normal;
      padding: 1px 3px;
      border-radius: 2px;
      color: #0c5aa6;
    }

    // 支持更多高亮标签
    .highlight {
      background-color: #fff3cd;
      padding: 2px 4px;
      border-radius: 3px;
      font-weight: 500;
      color: #856404;
    }

    .match {
      background-color: #e7f3ff;
      font-style: normal;
      padding: 1px 3px;
      border-radius: 2px;
      color: #0c5aa6;
    }

    // 支持span标签的高亮
    span[style*="background"] {
      border-radius: 3px;
      padding: 1px 3px;
    }

    // 支持strong标签的高亮
    strong {
      background-color: #fff3cd;
      padding: 1px 3px;
      border-radius: 2px;
      color: #856404;
    }

    // 支持b标签的高亮
    b {
      background-color: #e7f3ff;
      padding: 1px 3px;
      border-radius: 2px;
      color: #0c5aa6;
      font-weight: 500;
    }
  }
}

.keywords {
  margin-bottom: 8px;

  .ant-tag {
    margin-bottom: 4px;
  }
}

.questions {
  .ant-tag {
    margin-bottom: 4px;
  }
}

.settingsPanel {
  background: #fafafa;
  border-radius: 8px;
  padding: 16px;
  border: 1px solid #e8e8e8;

  .ant-form-item {
    margin-bottom: 16px;

    &:last-child {
      margin-bottom: 0;
    }
  }

  .ant-form-item-label {
    padding-bottom: 4px;

    label {
      font-size: 13px;
      font-weight: 500;
    }
  }
}

.testSection {
  .ant-input {
    border-radius: 6px;
  }

  .ant-btn-primary {
    border-radius: 6px;
    height: 40px;
    font-weight: 500;
  }
}

.resultsSection {
  .ant-list-item {
    padding: 0;
    border: none;
    margin-bottom: 16px;
  }

  .ant-pagination {
    text-align: center;
    margin-top: 24px;
  }
}

.emptyState {
  text-align: center;
  padding: 40px 20px;
  color: #999;

  .ant-empty-description {
    color: #666;
  }
}

.loadingState {
  text-align: center;
  padding: 40px;

  .ant-spin {
    .ant-spin-text {
      margin-top: 12px;
      color: #666;
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .container {
    padding: 16px;
  }

  .mainCard {
    .ant-card-body {
      padding: 16px;
    }
  }

  .resultCard {
    .ant-card-head {
      padding: 8px 12px;
    }

    .ant-card-body {
      padding: 12px;
    }
  }

  .keywords,
  .questions {
    .ant-tag {
      font-size: 11px;
      padding: 2px 6px;
    }
  }
}

// 暗色主题支持
@media (prefers-color-scheme: dark) {
  .container {
    background: #141414;
  }

  .resultCard {
    background: #1f1f1f;
    border-color: #303030;

    &:hover {
      background: #262626;
    }
  }

  .content {
    color: #e6e6e6;

    :global {
      mark {
        background-color: #614700;
        color: #fff;
      }

      em {
        background-color: #003a8c;
        color: #91d5ff;
      }
    }
  }

  .settingsPanel {
    background: #1f1f1f;
    border-color: #303030;
  }
}
