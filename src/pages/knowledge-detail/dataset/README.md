# 知识库文档管理 - 自动刷新功能

## 新增功能

### 1. 自动刷新机制

在知识库文档解析过程中，页面会自动检测是否有文档处于解析状态，并启用自动刷新：

- **触发条件**：当有文档状态为 `RUNNING` 或 `UNSTART` 时
- **刷新频率**：每3秒刷新一次
- **自动停止**：当所有文档解析完成后自动停止刷新

### 2. 用户控制

在工具栏添加了自动刷新开关：

```tsx
<Tooltip title={autoRefreshEnabled ? "Disable auto-refresh during parsing" : "Enable auto-refresh during parsing"}>
  <Switch
    checked={autoRefreshEnabled}
    onChange={setAutoRefreshEnabled}
    checkedChildren="Auto"
    unCheckedChildren="Manual"
    size="small"
  />
</Tooltip>
```

### 3. 系统设置冲突检查

自动检查系统状态，防止在系统不健康时进行刷新：

- 检查 `doc_engine` 状态
- 检查 `database` 状态
- 当状态为 `red` 时禁用自动刷新
- 显示相应的警告信息

### 4. 用户提示

#### 系统健康警告
```tsx
{systemStatus && (
  systemStatus.doc_engine?.status === 'red' || 
  systemStatus.database?.status === 'red'
) && (
  <Alert
    message="System Health Warning"
    description="Auto-refresh has been disabled due to system health issues. Please check system status in settings."
    type="warning"
    showIcon
    closable
  />
)}
```

#### 自动刷新状态提示
```tsx
{documents.some(doc => doc.run === RunningStatus.RUNNING || doc.run === RunningStatus.UNSTART) && autoRefreshEnabled && (
  <Alert
    message="Auto-refresh Active"
    description="Documents are being processed. The list will refresh automatically every 3 seconds."
    type="info"
    showIcon
    closable
  />
)}
```

## 技术实现

### Hooks 层面 (hooks.ts)

1. **状态管理**：
   - `autoRefreshEnabled`: 控制自动刷新开关
   - `autoRefreshTimerRef`: 定时器引用

2. **系统状态检查**：
   - 使用 React Query 获取系统状态
   - 30秒缓存避免频繁请求

3. **自动刷新逻辑**：
   - 检测处理中的文档
   - 验证系统健康状态
   - 设置/清理定时器

### 组件层面 (index.tsx)

1. **UI 控件**：
   - 自动刷新开关
   - 状态提示信息

2. **状态显示**：
   - 系统健康警告
   - 自动刷新活动提示

## 使用方法

1. **启用自动刷新**：
   - 默认启用
   - 可通过工具栏开关控制

2. **监控解析进度**：
   - 上传文档后自动开始刷新
   - 实时查看解析状态

3. **系统健康监控**：
   - 自动检测系统状态
   - 异常时显示警告并禁用刷新

## 注意事项

- 自动刷新仅在有文档解析时启用
- 系统状态检查有缓存机制
- 组件卸载时自动清理定时器
- 不会影响手动刷新功能
