import {
  UploadOutlined,
  DeleteOutlined,
  EditOutlined,
  FileTextOutlined,
  ReloadOutlined,
  DownloadOutlined,
  PlayCircleOutlined,
  PauseCircleOutlined,
  RedoOutlined,
  ToolOutlined,
  EyeOutlined,
  GlobalOutlined,
} from '@ant-design/icons';
import {
  Button,
  Table,
  Input,
  Space,
  Tag,
  Dropdown,
  Modal,
  Upload,
  message,
  Progress,
  Typography,
  Card,
  Row,
  Col,
  Statistic,
  Switch,
  Popover,
  Popconfirm,
  Badge,
  Tooltip,
  Form,
  Select,
  Checkbox,
  Alert,
} from 'antd';
import { useState, useMemo } from 'react';
import { useParams, useNavigate } from 'umi';
import { useDocumentList, useUploadDocument, useDeleteDocument, useKnowledgeStats } from './hooks';
import userService from '@/services/user-service';
import { RunningStatus, RunningStatusMap } from '@/constants/document';
import documentService from '@/services/document-service';
import { useMutation, useQueryClient, useQuery } from '@tanstack/react-query';
import { formatDate } from '@/utils/date';
import { useTranslate } from '@/hooks/use-i18n';
import styles from './index.less';

const { Search, TextArea } = Input;
const { Title, Text } = Typography;
const { confirm } = Modal;
const { Option } = Select;

const DocumentManagement = () => {
  const t = useTranslate();
  const { id: knowledgeId } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const queryClient = useQueryClient();
  const [searchString, setSearchString] = useState('');
  const [uploadModalVisible, setUploadModalVisible] = useState(false);
  const [webCrawlModalVisible, setWebCrawlModalVisible] = useState(false);
  const [renameModalVisible, setRenameModalVisible] = useState(false);
  const [parserModalVisible, setParserModalVisible] = useState(false);
  const [previewModalVisible, setPreviewModalVisible] = useState(false);
  const [currentDocument, setCurrentDocument] = useState<any>(null);
  const [selectedRowKeys, setSelectedRowKeys] = useState<string[]>([]);

  // 表单
  const [webCrawlForm] = Form.useForm();
  const [renameForm] = Form.useForm();
  const [parserForm] = Form.useForm();

  console.log('DocumentManagement - knowledgeId:', knowledgeId); // Debug log
  
  const {
    documents,
    loading,
    pagination,
    setPagination,
    refetch,
    autoRefreshEnabled,
    setAutoRefreshEnabled,
  } = useDocumentList(knowledgeId || '', searchString);

  const { uploadDocument, uploading } = useUploadDocument(knowledgeId || '');
  const { deleteDocument, deleting } = useDeleteDocument();

  // 获取知识库统计信息
  const { stats, refetch: refetchStats } = useKnowledgeStats(knowledgeId || '');

  // 获取系统状态
  const { data: systemStatus } = useQuery({
    queryKey: ['system-status'],
    queryFn: async () => {
      try {
        const { data } = await userService.getSystemStatus();
        return data;
      } catch (error) {
        console.warn('Failed to get system status:', error);
        return null;
      }
    },
    enabled: autoRefreshEnabled,
    staleTime: 30000, // 30秒缓存
    retry: 1,
  });

  // 网页爬取
  const { mutateAsync: webCrawl, isPending: webCrawlLoading } = useMutation({
    mutationFn: async (params: { name: string; url: string }) => {
      if (!knowledgeId) throw new Error('Knowledge base ID is required');
      const { data } = await documentService.webCrawl({
        kb_id: knowledgeId,
        ...params,
      });
      return data;
    },
    onSuccess: () => {
      message.success(t('knowledge.webCrawlStarted'));
      setWebCrawlModalVisible(false);
      webCrawlForm.resetFields();
      refetch();
    },
    onError: (error: any) => {
      message.error(error?.message || t('knowledge.webCrawlFailed'));
    },
  });

  // 更改文档状态
  const { mutateAsync: changeStatus } = useMutation({
    mutationFn: async (params: { docId: string; status: string }) => {
      const { data } = await documentService.changeDocumentStatus({
        doc_id: params.docId,
        status: params.status,
      });
      return data;
    },
    onSuccess: () => {
      message.success(t('knowledge.statusUpdated'));
      refetch();
    },
    onError: (error: any) => {
      message.error(error?.message || t('knowledge.statusUpdateFailed'));
    },
  });

  // 运行/停止解析
  const { mutateAsync: runDocument } = useMutation({
    mutationFn: async (params: { docIds: string[]; run: string; delete?: boolean }) => {
      const { data } = await documentService.runDocument({
        doc_ids: params.docIds,
        run: params.run,
        delete: params.delete,
      });
      return data;
    },
    onSuccess: () => {
      message.success(t('knowledge.parsingUpdated'));
      refetch();
    },
    onError: (error: any) => {
      message.error(error?.message || t('knowledge.parsingUpdateFailed'));
    },
  });

  // 重命名文档
  const { mutateAsync: renameDocument } = useMutation({
    mutationFn: async (params: { docId: string; name: string }) => {
      const { data } = await documentService.renameDocument({
        doc_id: params.docId,
        name: params.name,
      });
      return data;
    },
    onSuccess: () => {
      message.success(t('knowledge.documentRenamed'));
      setRenameModalVisible(false);
      renameForm.resetFields();
      setCurrentDocument(null);
      refetch();
    },
    onError: (error: any) => {
      message.error(error?.message || t('knowledge.renameFailed'));
    },
  });

  // 更改解析器
  const { mutateAsync: changeParser } = useMutation({
    mutationFn: async (params: { docId: string; parserId: string; parserConfig?: any }) => {
      const { data } = await documentService.changeParser({
        doc_id: params.docId,
        parser_id: params.parserId,
        parser_config: params.parserConfig,
      });
      return data;
    },
    onSuccess: () => {
      message.success(t('knowledge.parserChanged'));
      setParserModalVisible(false);
      parserForm.resetFields();
      setCurrentDocument(null);
      refetch();
    },
    onError: (error: any) => {
      message.error(error?.message || t('knowledge.parserChangeFailed'));
    },
  });

  const handleSearch = (value: string) => {
    console.log('Search triggered:', value); // Debug log
    setSearchString(value);
    // Reset to first page when searching
    setPagination(prev => ({ ...prev, current: 1 }));
  };

  const handleUpload = async (file: File) => {
    try {
      await uploadDocument(file);
      setUploadModalVisible(false);
      refetch();
      refetchStats(); // 同时刷新统计数据
      message.success(t('knowledge.uploadSuccess'));
    } catch (error) {
      message.error(t('knowledge.uploadFailed'));
    }
  };

  const handleRefresh = () => {
    refetch();
    refetchStats(); // 同时刷新统计数据
  };

  const handleStatusChange = (docId: string, checked: boolean) => {
    changeStatus({
      docId,
      status: checked ? '1' : '0',
    });
  };

  const handleRunDocument = (docId: string, isRunning: boolean, shouldDelete: boolean = false) => {
    const run = isRunning ? RunningStatus.CANCEL : RunningStatus.RUNNING;
    runDocument({
      docIds: [docId],
      run,
      delete: shouldDelete,
    });
  };

  const handleWebCrawlSubmit = async () => {
    try {
      const values = await webCrawlForm.validateFields();
      await webCrawl(values);
    } catch (error) {
      console.error('Web crawl validation failed:', error);
    }
  };

  const handleBatchParse = () => {
    if (selectedRowKeys.length === 0) {
      message.warning(t('knowledge.selectDocumentsToParse'));
      return;
    }
    runDocument({
      docIds: selectedRowKeys,
      run: RunningStatus.RUNNING,
    });
  };

  const handleBatchDelete = () => {
    if (selectedRowKeys.length === 0) {
      message.warning(t('knowledge.selectDocumentsToDelete'));
      return;
    }
    Modal.confirm({
      title: t('knowledge.deleteDocuments'),
      content: t('knowledge.deleteDocumentsConfirm', { count: selectedRowKeys.length }),
      onOk: () => {
        selectedRowKeys.forEach(docId => {
          const doc = documents.find(d => d.id === docId);
          if (doc) {
            handleDelete(docId, doc.name);
          }
        });
        setSelectedRowKeys([]);
      },
    });
  };

  const handleDownload = (docId: string, filename: string) => {
    const url = documentService.downloadDocument(docId);
    const link = document.createElement('a');
    link.href = url;
    link.download = filename;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  const showRenameModal = (document: any) => {
    setCurrentDocument(document);
    renameForm.setFieldsValue({ name: document.name });
    setRenameModalVisible(true);
  };

  const showParserModal = (document: any) => {
    setCurrentDocument(document);
    parserForm.setFieldsValue({
      parser_id: document.parser_id,
      parser_config: document.parser_config,
    });
    setParserModalVisible(true);
  };

  const handleDelete = (documentId: string, documentName: string) => {
    confirm({
      title: t('knowledge.deleteDocuments'),
      content: t('knowledge.deleteDocumentConfirm', { name: documentName }),
      okText: t('common.delete'),
      okType: 'danger',
      cancelText: t('common.cancel'),
      onOk: async () => {
        try {
          await deleteDocument(documentId);
          refetch();
          refetchStats(); // 同时刷新统计数据
          message.success(t('knowledge.deleteDocumentSuccess'));
        } catch (error) {
          message.error(t('knowledge.deleteDocumentFailed'));
        }
      },
    });
  };

  const getStatusTag = (status: string, record: any) => {
    const statusInfo = RunningStatusMap[status as RunningStatus] || {
      color: 'default',
      text: status,
      label: status
    };
    const isRunning = status === RunningStatus.RUNNING;

    return (
      <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
        <Popover
          content={
            <div>
              <p><strong>{t('knowledge.processBegin')}:</strong> {formatDate(record.process_begin_at, 'datetime')}</p>
              <p><strong>{t('knowledge.duration')}:</strong> {record.process_duation?.toFixed(2)}s</p>
              <p><strong>{t('knowledge.progress')}:</strong> {(record.progress * 100).toFixed(2)}%</p>
              {record.progress_msg && (
                <p><strong>{t('knowledge.message')}:</strong> <span style={{ whiteSpace: 'pre-wrap', wordBreak: 'break-word' }}>{record.progress_msg}</span></p>
              )}
            </div>
          }
        >
          <Tag color={statusInfo.color}>
            {isRunning ? (
              <Space>
                <Badge color={statusInfo.color} />
                {statusInfo.text}
                <span>{(record.progress * 100).toFixed(2)}%</span>
              </Space>
            ) : (
              statusInfo.text
            )}
          </Tag>
        </Popover>

        <Popconfirm
          title={record.chunk_num > 0 ? t('knowledge.reparseWarning') : t('knowledge.startParsing')}
          onConfirm={() => handleRunDocument(record.id, isRunning, record.chunk_num > 0)}
          onCancel={() => handleRunDocument(record.id, isRunning, false)}
          disabled={record.chunk_num === 0}
          okText={t('common.yes')}
          cancelText={t('common.no')}
        >
          <Button
            type="text"
            size="small"
            icon={
              isRunning ? <PauseCircleOutlined /> :
              status === RunningStatus.DONE ? <RedoOutlined /> :
              <PlayCircleOutlined />
            }
            onClick={record.chunk_num === 0 ? () => handleRunDocument(record.id, isRunning, false) : undefined}
          />
        </Popconfirm>
      </div>
    );
  };

  const getDocumentActions = (record: any) => {
    const isRunning = record.run === RunningStatus.RUNNING;

    return [
      {
        key: 'rename',
        icon: <EditOutlined />,
        label: t('knowledge.rename'),
        disabled: isRunning,
        onClick: () => showRenameModal(record),
      },
      {
        key: 'parser',
        icon: <ToolOutlined />,
        label: t('knowledge.changeParser'),
        disabled: isRunning,
        onClick: () => showParserModal(record),
      },
      {
        key: 'preview',
        icon: <EyeOutlined />,
        label: t('knowledge.preview'),
        disabled: isRunning,
        onClick: () => {
          setCurrentDocument(record);
          setPreviewModalVisible(true);
        },
      },
      {
        key: 'download',
        icon: <DownloadOutlined />,
        label: t('knowledge.download'),
        disabled: isRunning,
        onClick: () => handleDownload(record.id, record.name),
      },
      {
        key: 'delete',
        icon: <DeleteOutlined />,
        label: t('common.delete'),
        danger: true,
        disabled: isRunning,
        onClick: () => handleDelete(record.id, record.name),
      },
    ];
  };

  const columns = [
    {
      title: t('knowledge.documentName'),
      dataIndex: 'name',
      key: 'name',
      fixed: 'left',
      width: 300,
      ellipsis: true,
      render: (text: string, record: any) => (
        <div style={{ display: 'flex', alignItems: 'center', gap: 8 }}>
          <FileTextOutlined style={{ color: '#1890ff' }} />
          <Tooltip title={text}>
            <span
              style={{
                cursor: 'pointer',
                color: '#1890ff',
                textDecoration: 'underline',
                fontWeight: 500,
              }}
              onClick={() => navigate(`/knowledge/${knowledgeId}/dataset/${record.id}/chunks`)}
            >
              {text.length > 40 ? `${text.substring(0, 40)}...` : text}
            </span>
          </Tooltip>
        </div>
      ),
    },
    {
      title: t('knowledge.type'),
      dataIndex: 'type',
      key: 'type',
      width: 100,
      render: (type: string) => <Tag>{type.toUpperCase()}</Tag>,
    },
    {
      title: t('knowledge.size'),
      dataIndex: 'size',
      key: 'size',
      width: 100,
      render: (size: number) => {
        if (!size) return '-';
        const units = ['B', 'KB', 'MB', 'GB'];
        let unitIndex = 0;
        let fileSize = size;
        while (fileSize >= 1024 && unitIndex < units.length - 1) {
          fileSize /= 1024;
          unitIndex++;
        }
        return `${fileSize.toFixed(1)} ${units[unitIndex]}`;
      },
    },
    {
      title: t('knowledge.chunks'),
      dataIndex: 'chunk_num',
      key: 'chunk_num',
      width: 100,
      render: (value: number) => value || 0,
    },
    {
      title: t('knowledge.tokens'),
      dataIndex: 'token_num',
      key: 'token_num',
      width: 100,
      render: (value: number) => value || 0,
    },
    {
      title: t('knowledge.status'),
      dataIndex: 'status',
      key: 'status',
      width: 100,
      render: (status: string, record: any) => (
        <Switch
          checked={status === '1'}
          onChange={(checked) => handleStatusChange(record.id, checked)}
          disabled={record.run === RunningStatus.RUNNING}
        />
      ),
    },
    {
      title: t('knowledge.parsingStatus'),
      dataIndex: 'run',
      key: 'parsing_status',
      width: 200,
      render: (status: string, record: any) => getStatusTag(status, record),
    },
    {
      title: t('knowledge.progress'),
      dataIndex: 'progress',
      key: 'progress',
      width: 120,
      render: (progress: number, record: any) => {
        if (record.run === 'processing') {
          return <Progress percent={Math.round(progress * 100)} size="small" />;
        }
        return '-';
      },
    },
    {
      title: t('knowledge.chunks'),
      dataIndex: 'chunk_num',
      key: 'chunk_num',
      width: 80,
      render: (chunks: number) => chunks || 0,
    },
    {
      title: t('knowledge.created'),
      dataIndex: 'create_time',
      key: 'create_time',
      width: 120,
      render: (time: string) => formatDate(time, 'date'),
    },
    {
      title: t('knowledge.actions'),
      key: 'actions',
      fixed: 'right',
      width: 120,
      render: (_: any, record: any) => {
        const isRunning = record.run === RunningStatus.RUNNING;
        return (
          <Dropdown
            menu={{ items: getDocumentActions(record) }}
            trigger={['click']}
            disabled={isRunning}
          >
            <Button
              type="text"
              size="small"
              disabled={isRunning}
              style={{
                color: isRunning ? '#ccc' : '#1890ff',
                cursor: isRunning ? 'not-allowed' : 'pointer'
              }}
            >
              {t('knowledge.actions')}
            </Button>
          </Dropdown>
        );
      },
    },
  ];

  // 使用全局统计数据，而不是当前页面的文档数据
  const totalDocuments = stats.totalDocuments;
  const completedDocuments = stats.completedDocuments;
  const processingDocuments = stats.processingDocuments;
  const totalChunks = stats.totalChunks;

  return (
    <div className={styles.documentManagement}>
      {/* Header */}
      <div className={styles.header}>
        <div className={styles.headerLeft}>
          <Title level={3} style={{ margin: 0 }}>
            {t('knowledge.documents')}
          </Title>
          <Text type="secondary">
            {t('knowledge.manageDocuments')}
          </Text>
        </div>
        <div className={styles.headerRight}>
          <Space>
            <Search
              placeholder={t('knowledge.searchDocuments')}
              value={searchString}
              onChange={(e) => setSearchString(e.target.value)}
              onSearch={handleSearch}
              style={{ width: 200 }}
            />

            {selectedRowKeys.length > 0 && (
              <>
                <Button
                  icon={<PlayCircleOutlined />}
                  onClick={handleBatchParse}
                >
                  {t('knowledge.batchParse')} ({selectedRowKeys.length})
                </Button>
                <Button
                  danger
                  icon={<DeleteOutlined />}
                  onClick={handleBatchDelete}
                >
                  {t('knowledge.batchDelete')} ({selectedRowKeys.length})
                </Button>
              </>
            )}

            <Button
              icon={<ReloadOutlined />}
              onClick={handleRefresh}
              loading={loading}
            >
              {t('knowledge.refresh')}
            </Button>
            <Tooltip title={t('knowledge.autoRefreshTooltip', {
              status: autoRefreshEnabled ? t('knowledge.disableAutoRefresh') : t('knowledge.enableAutoRefresh')
            })}>
              <Switch
                checked={autoRefreshEnabled}
                onChange={setAutoRefreshEnabled}
                checkedChildren={t('common.auto')}
                unCheckedChildren={t('common.manual')}
                size="small"
              />
            </Tooltip>
            <Button
              icon={<GlobalOutlined />}
              onClick={() => setWebCrawlModalVisible(true)}
            >
              {t('knowledge.webCrawl')}
            </Button>
            <Button
              type="primary"
              icon={<UploadOutlined />}
              onClick={() => setUploadModalVisible(true)}
            >
              {t('knowledge.uploadDocument')}
            </Button>
          </Space>
        </div>
      </div>

      {/* Statistics */}
      <Row gutter={[16, 16]} className={styles.statistics}>
        <Col xs={24} sm={6}>
          <Card>
            <Statistic
              title={t('knowledge.totalDocuments')}
              value={totalDocuments}
              prefix={<FileTextOutlined />}
              valueStyle={{ color: '#1890ff' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={6}>
          <Card>
            <Statistic
              title={t('knowledge.completed')}
              value={completedDocuments}
              valueStyle={{ color: '#52c41a' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={6}>
          <Card>
            <Statistic
              title={t('knowledge.processing')}
              value={processingDocuments}
              valueStyle={{ color: '#1890ff' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={6}>
          <Card>
            <Statistic
              title={t('knowledge.totalChunks')}
              value={totalChunks}
              valueStyle={{ color: '#722ed1' }}
            />
          </Card>
        </Col>
      </Row>

      {/* System Status Warning */}
      {systemStatus && (
        systemStatus.doc_engine?.status === 'red' ||
        systemStatus.database?.status === 'red'
      ) && (
        <Alert
          message={t('knowledge.systemHealthWarning')}
          description={t('knowledge.systemHealthDescription')}
          type="warning"
          showIcon
          closable
          style={{ marginBottom: 16 }}
        />
      )}



      {/* Search and Filters */}
      <div className={styles.toolbar}>
        <Search
          placeholder={t('knowledge.searchDocuments')}
          allowClear
          style={{ width: 300 }}
          onSearch={handleSearch}
          onChange={(e) => !e.target.value && handleSearch('')}
        />
      </div>

      {/* Documents Table */}
      <Card className={styles.tableCard}>
        <Table
          columns={columns}
          dataSource={documents}
          rowKey="id"
          loading={loading}
          rowSelection={{
            selectedRowKeys,
            onChange: (newSelectedRowKeys: React.Key[]) => {
              setSelectedRowKeys(newSelectedRowKeys as string[]);
            },
            getCheckboxProps: (record: any) => ({
              disabled: record.run === RunningStatus.RUNNING,
            }),
          }}
          pagination={{
            ...pagination,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) =>
              t('knowledge.documentsRange', { start: range[0], end: range[1], total }),
            onChange: (page, pageSize) => {
              console.log('Pagination changed - User clicked page:', page, 'pageSize:', pageSize, 'current state:', pagination); // Debug log
              setPagination(prev => {
                const newPagination = {
                  ...prev,
                  current: page, // 保持1-based页码
                  pageSize: pageSize || prev.pageSize
                };
                console.log('Setting new pagination (1-based):', newPagination, '→ Will call API with page:', page - 1); // Debug log
                return newPagination;
              });
            },
            onShowSizeChange: (current, size) => {
              console.log('Page size changed:', { current, size }); // Debug log
              setPagination(prev => {
                const newPagination = {
                  ...prev,
                  current: 1, // Reset to first page when page size changes
                  pageSize: size
                };
                console.log('Setting new pagination (size change):', newPagination); // Debug log
                return newPagination;
              });
            },
          }}
          scroll={{ x: 1400 }}
        />
      </Card>

      {/* Upload Modal */}
      <Modal
        title={t('knowledge.uploadDocument')}
        open={uploadModalVisible}
        onCancel={() => setUploadModalVisible(false)}
        footer={null}
        width={600}
      >
        <Upload.Dragger
          name="file"
          multiple={false}
          beforeUpload={(file) => {
            handleUpload(file);
            return false; // Prevent default upload
          }}
          disabled={uploading}
        >
          <p className="ant-upload-drag-icon">
            <UploadOutlined />
          </p>
          <p className="ant-upload-text">
            {t('knowledge.clickOrDragToUpload')}
          </p>
          <p className="ant-upload-hint">
            {t('knowledge.supportedFormats')}
          </p>
        </Upload.Dragger>
      </Modal>

      {/* Web Crawl Modal */}
      <Modal
        title={t('knowledge.webCrawl')}
        open={webCrawlModalVisible}
        onOk={handleWebCrawlSubmit}
        onCancel={() => {
          setWebCrawlModalVisible(false);
          webCrawlForm.resetFields();
        }}
        confirmLoading={webCrawlLoading}
      >
        <Form form={webCrawlForm} layout="vertical">
          <Form.Item
            name="name"
            label={t('knowledge.documentName')}
            rules={[{ required: true, message: t('knowledge.pleaseInputDocumentName') }]}
          >
            <Input placeholder={t('knowledge.enterDocumentName')} />
          </Form.Item>
          <Form.Item
            name="url"
            label={t('knowledge.url')}
            rules={[
              { required: true, message: t('knowledge.pleaseInputURL') },
              { type: 'url', message: t('knowledge.pleaseInputValidURL') },
            ]}
          >
            <Input placeholder="https://example.com" />
          </Form.Item>
        </Form>
      </Modal>

      {/* Rename Modal */}
      <Modal
        title={t('knowledge.rename')}
        open={renameModalVisible}
        onOk={async () => {
          try {
            const values = await renameForm.validateFields();
            if (currentDocument) {
              await renameDocument({
                docId: currentDocument.id,
                name: values.name,
              });
            }
          } catch (error) {
            console.error('Rename validation failed:', error);
          }
        }}
        onCancel={() => {
          setRenameModalVisible(false);
          renameForm.resetFields();
          setCurrentDocument(null);
        }}
      >
        <Form form={renameForm} layout="vertical">
          <Form.Item
            name="name"
            label={t('knowledge.documentName')}
            rules={[{ required: true, message: t('knowledge.pleaseInputDocumentName') }]}
          >
            <Input placeholder={t('knowledge.enterNewDocumentName')} />
          </Form.Item>
        </Form>
      </Modal>

      {/* Parser Modal */}
      <Modal
        title={t('knowledge.changeParser')}
        open={parserModalVisible}
        onOk={async () => {
          try {
            const values = await parserForm.validateFields();
            if (currentDocument) {
              await changeParser({
                docId: currentDocument.id,
                parserId: values.parser_id,
                parserConfig: values.parser_config,
              });
            }
          } catch (error) {
            console.error('Parser validation failed:', error);
          }
        }}
        onCancel={() => {
          setParserModalVisible(false);
          parserForm.resetFields();
          setCurrentDocument(null);
        }}
        width={600}
      >
        <Form form={parserForm} layout="vertical">
          <Form.Item
            name="parser_id"
            label={t('knowledge.parserType')}
            rules={[{ required: true, message: t('knowledge.pleaseSelectParserType') }]}
          >
            <Select placeholder={t('knowledge.parserType')}>
              <Option value="naive">{t('knowledge.general')}</Option>
              <Option value="qa">{t('knowledge.qa')}</Option>
              <Option value="resume">{t('knowledge.resume')}</Option>
              <Option value="manual">{t('knowledge.manual')}</Option>
              <Option value="table">{t('knowledge.table')}</Option>
              <Option value="paper">{t('knowledge.paper')}</Option>
              <Option value="book">{t('knowledge.book')}</Option>
              <Option value="laws">{t('knowledge.laws')}</Option>
              <Option value="presentation">{t('knowledge.presentation')}</Option>
              <Option value="picture">{t('knowledge.picture')}</Option>
              <Option value="one">{t('knowledge.one')}</Option>
              <Option value="audio">{t('knowledge.audio')}</Option>
              <Option value="email">{t('knowledge.email')}</Option>
              <Option value="tag">{t('knowledge.tag')}</Option>
              <Option value="knowledge_graph">{t('knowledge.knowledgeGraph')}</Option>
            </Select>
          </Form.Item>
          <Form.Item
            name="parser_config"
            label={t('knowledge.parserConfiguration')}
          >
            <TextArea
              rows={6}
              placeholder={t('knowledge.enterParserConfig')}
            />
          </Form.Item>
        </Form>
      </Modal>

      {/* Preview Modal */}
      <Modal
        title={t('knowledge.previewDocument', { name: currentDocument?.name })}
        open={previewModalVisible}
        onCancel={() => {
          setPreviewModalVisible(false);
          setCurrentDocument(null);
        }}
        footer={null}
        width={800}
      >
        <div>
          <p><strong>{t('knowledge.type')}:</strong> {currentDocument?.type}</p>
          <p><strong>{t('knowledge.size')}:</strong> {currentDocument?.size} bytes</p>
          <p><strong>{t('knowledge.parser')}:</strong> {currentDocument?.parser_id}</p>
          <p><strong>{t('knowledge.chunks')}:</strong> {currentDocument?.chunk_num}</p>
          <p><strong>{t('knowledge.tokens')}:</strong> {currentDocument?.token_num}</p>
          <p><strong>{t('knowledge.created')}:</strong> {formatDate(currentDocument?.create_time, 'datetime')}</p>
          {currentDocument?.thumbnail && (
            <div>
              <p><strong>{t('knowledge.thumbnail')}:</strong></p>
              <img src={currentDocument.thumbnail} alt={t('knowledge.thumbnail')} style={{ maxWidth: '100%', maxHeight: 200 }} />
            </div>
          )}
        </div>
      </Modal>
    </div>
  );
};

export default DocumentManagement;
