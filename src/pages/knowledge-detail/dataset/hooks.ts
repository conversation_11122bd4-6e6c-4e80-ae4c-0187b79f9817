import knowledgeService, { listDocument } from '@/services/knowledge-service';
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { message } from 'antd';
import { useState, useEffect, useRef } from 'react';
import { useDebounce } from 'ahooks';
import { RunningStatus } from '@/constants/document';
import userService from '@/services/user-service';

export interface IDocument {
  id: string;
  name: string;
  type: string;
  size: number;
  chunk_num: number;
  kb_id: string;
  parser_id: string;
  parser_config: Record<string, any>;
  source_type: string;
  created_by: string;
  create_time: string;
  update_time: string;
  run: string;
  progress: number;
  progress_msg: string;
  process_begin_at: string;
  process_duration: number;
}

export const useDocumentList = (knowledgeId: string, searchString: string = '') => {
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 10,
    total: 0,
  });

  const debouncedSearchString = useDebounce(searchString, { wait: 300 });
  const autoRefreshTimerRef = useRef<NodeJS.Timeout | null>(null);
  const [autoRefreshEnabled, setAutoRefreshEnabled] = useState(true);

  // 检查系统设置是否允许自动刷新
  const { data: systemStatus } = useQuery({
    queryKey: ['system-status'],
    queryFn: async () => {
      try {
        const { data } = await userService.getSystemStatus();
        return data;
      } catch (error) {
        console.warn('Failed to get system status:', error);
        return null;
      }
    },
    enabled: autoRefreshEnabled,
    staleTime: 30000, // 30秒缓存
    retry: 1,
  });

  const { data, isLoading: loading, refetch } = useQuery({
    queryKey: ['documentList', knowledgeId, pagination.current, pagination.pageSize, debouncedSearchString],
    queryFn: async () => {
      if (!knowledgeId) return { documents: [], total: 0 };

      // 前端显示1-based分页，后端使用0-based分页
      const params = {
        kb_id: knowledgeId,
        page: pagination.current , // 前端页码减1传给后端（0-based）
        page_size: pagination.pageSize,
        keywords: debouncedSearchString || '',
        orderby: 'create_time',
        desc: 'true', // 后端期望字符串
      };

      const body = {
        run_status: [],
        types: [],
      };

      console.log('Fetching documents - Frontend page:', pagination.current, '→ Backend page:', params.page, 'params:', params, 'body:', body); // Debug log

      const { data: res } = await listDocument(params, body);

      console.log('API Response:', res); // Debug log

      if (res?.code === 0) {
        const documents = res.data?.docs || [];
        const total = res.data?.total || 0;

        console.log('Received documents:', documents.length, 'Total:', total); // Debug log

        return { documents, total };
      } else {
        console.error('API Error:', res); // Debug log
      }

      return { documents: [], total: 0 };
    },
    enabled: !!knowledgeId,
    // 添加重试和错误处理
    retry: 1,
    retryDelay: 1000,
  });

  // 使用useEffect来更新total，避免在queryFn中更新状态
  useEffect(() => {
    if (data?.total !== undefined && data.total !== pagination.total) {
      console.log('Updating pagination total:', data.total); // Debug log
      setPagination(prev => ({ ...prev, total: data.total }));
    }
  }, [data?.total, pagination.total]);

  // 自动刷新逻辑：检查是否有正在解析的文档
  useEffect(() => {
    const documents = data?.documents || [];
    const hasProcessingDocuments = documents.some(doc =>
      doc.run === RunningStatus.RUNNING || doc.run === RunningStatus.UNSTART
    );

    // 检查系统状态是否健康
    const isSystemHealthy = !systemStatus || (
      systemStatus.doc_engine?.status !== 'red' &&
      systemStatus.database?.status !== 'red'
    );

    // 清除之前的定时器
    if (autoRefreshTimerRef.current) {
      clearInterval(autoRefreshTimerRef.current);
      autoRefreshTimerRef.current = null;
    }

    // 如果有正在解析的文档且自动刷新启用且系统健康，则设置3秒刷新
    if (hasProcessingDocuments && autoRefreshEnabled && !loading && isSystemHealthy) {
      console.log('Setting up auto-refresh for processing documents'); // Debug log
      autoRefreshTimerRef.current = setInterval(() => {
        console.log('Auto-refreshing document list'); // Debug log
        refetch();
      }, 5000); // 5秒刷新一次
    } else if (!isSystemHealthy && autoRefreshEnabled) {
      console.warn('Auto-refresh disabled due to system health issues'); // Debug log
    }

    // 清理函数
    return () => {
      if (autoRefreshTimerRef.current) {
        clearInterval(autoRefreshTimerRef.current);
        autoRefreshTimerRef.current = null;
      }
    };
  }, [data?.documents, autoRefreshEnabled, loading, refetch, systemStatus]);

  // 组件卸载时清理定时器
  useEffect(() => {
    return () => {
      if (autoRefreshTimerRef.current) {
        clearInterval(autoRefreshTimerRef.current);
        autoRefreshTimerRef.current = null;
      }
    };
  }, []);

  return {
    documents: data?.documents || [],
    loading,
    pagination,
    setPagination,
    refetch,
    autoRefreshEnabled,
    setAutoRefreshEnabled,
  };
};

export const useUploadDocument = (knowledgeId: string) => {
  const queryClient = useQueryClient();
  
  const { mutateAsync, isPending: uploading } = useMutation({
    mutationFn: async (file: File) => {
      console.log('Upload document - knowledgeId:', knowledgeId); // Debug log

      if (!knowledgeId) {
        throw new Error('Knowledge base ID is required for document upload');
      }

      const formData = new FormData();
      formData.append('file', file);
      formData.append('kb_id', knowledgeId);

      console.log('FormData contents:'); // Debug log
      for (let [key, value] of formData.entries()) {
        console.log(key, value);
      }

      const { data } = await knowledgeService.uploadDocument(formData);
      return data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['documentList', knowledgeId] });
      queryClient.invalidateQueries({ queryKey: ['knowledgeDetail', knowledgeId] });
    },
    onError: (error: any) => {
      message.error(error?.message || 'Failed to upload document');
    },
  });

  return { uploadDocument: mutateAsync, uploading };
};

export const useDeleteDocument = () => {
  const queryClient = useQueryClient();
  
  const { mutateAsync, isPending: deleting } = useMutation({
    mutationFn: async (documentId: string) => {
      const { data } = await knowledgeService.deleteDocument(documentId);
      return data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['documentList'] });
      queryClient.invalidateQueries({ queryKey: ['knowledgeDetail'] });
    },
    onError: (error: any) => {
      message.error(error?.message || 'Failed to delete document');
    },
  });

  return { deleteDocument: mutateAsync, deleting };
};

export const useChangeDocumentStatus = () => {
  const queryClient = useQueryClient();

  const { mutateAsync, isPending: changing } = useMutation({
    mutationFn: async (params: { doc_id: string; status: string }) => {
      const { data } = await knowledgeService.changeDocumentStatus(params);
      return data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['documentList'] });
    },
    onError: (error: any) => {
      message.error(error?.message || 'Failed to change document status');
    },
  });

  return { changeDocumentStatus: mutateAsync, changing };
};

// 获取知识库统计信息的hook
export const useKnowledgeStats = (knowledgeId: string) => {
  const { data, isLoading: loading, refetch } = useQuery({
    queryKey: ['knowledgeStats', knowledgeId],
    queryFn: async () => {
      if (!knowledgeId) return null;

      // 获取所有文档的统计信息（不分页）
      const params = {
        kb_id: knowledgeId,
        page: 0,
        page_size: 9999, // 获取所有文档
        keywords: '',
        orderby: 'create_time',
        desc: 'true',
      };

      const body = {
        run_status: [],
        types: [],
      };

      const { data: res } = await listDocument(params, body);

      if (res?.code === 0) {
        const documents = res.data?.docs || [];
        const total = res.data?.total || 0;

        // 计算统计信息
        const completedDocuments = documents.filter(doc => doc.run === 'completed').length;
        const processingDocuments = documents.filter(doc => doc.run === 'processing').length;
        const failedDocuments = documents.filter(doc => doc.run === 'failed').length;
        const totalChunks = documents.reduce((sum, doc) => sum + (doc.chunk_num || 0), 0);

        return {
          totalDocuments: total,
          completedDocuments,
          processingDocuments,
          failedDocuments,
          totalChunks,
        };
      }

      return {
        totalDocuments: 0,
        completedDocuments: 0,
        processingDocuments: 0,
        failedDocuments: 0,
        totalChunks: 0,
      };
    },
    enabled: !!knowledgeId,
    // 每30秒刷新一次统计数据
    refetchInterval: 30000,
  });

  return {
    stats: data || {
      totalDocuments: 0,
      completedDocuments: 0,
      processingDocuments: 0,
      failedDocuments: 0,
      totalChunks: 0,
    },
    loading,
    refetch,
  };
};
