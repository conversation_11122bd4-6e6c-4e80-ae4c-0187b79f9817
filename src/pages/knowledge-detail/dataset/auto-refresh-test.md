# 知识库文档自动刷新功能测试

## 功能说明

1. **自动刷新机制**：
   - 当有文档处于解析状态（RUNNING 或 UNSTART）时，自动每3秒刷新一次文档列表
   - 用户可以通过工具栏的开关控制自动刷新的启用/禁用

2. **系统设置冲突检查**：
   - 检查系统状态（doc_engine 和 database）
   - 当系统状态为 'red' 时，自动禁用刷新并显示警告
   - 显示系统健康警告信息

3. **用户界面**：
   - 工具栏添加自动刷新开关
   - 显示处理中文档的信息提示
   - 显示系统健康状态警告

## 测试步骤

### 1. 基本自动刷新测试
1. 上传一个文档到知识库
2. 开始解析文档
3. 观察页面是否每3秒自动刷新
4. 检查控制台是否有 "Auto-refreshing document list" 日志

### 2. 开关控制测试
1. 在有文档解析时，关闭自动刷新开关
2. 观察页面是否停止自动刷新
3. 重新开启开关，观察是否恢复自动刷新

### 3. 系统状态冲突测试
1. 模拟系统状态异常（需要后端配合）
2. 观察是否显示系统健康警告
3. 检查自动刷新是否被禁用

### 4. 性能测试
1. 长时间运行自动刷新
2. 检查内存使用情况
3. 确认定时器正确清理

## 预期结果

- ✅ 文档解析时自动刷新
- ✅ 用户可控制开关
- ✅ 系统状态检查
- ✅ 适当的用户提示
- ✅ 定时器正确清理
- ✅ 不影响其他功能

## 注意事项

1. 自动刷新只在有文档处于解析状态时启用
2. 系统状态检查有30秒缓存，避免频繁请求
3. 组件卸载时会自动清理定时器
4. 刷新间隔固定为3秒，符合需求要求
