.documentManagement {
  padding: 24px;
  background: #f0f2f5;
  min-height: 100vh;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 24px;
  padding: 24px;
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

.headerLeft {
  h3 {
    color: #1f2937;
    font-weight: 600;
    margin-bottom: 4px;
  }
}

.headerRight {
  display: flex;
  gap: 12px;
}

.statistics {
  margin-bottom: 24px;
  
  .ant-card {
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
    
    .ant-statistic-title {
      color: #6b7280;
      font-weight: 500;
      margin-bottom: 8px;
    }
    
    .ant-statistic-content {
      display: flex;
      align-items: center;
      gap: 8px;
    }
  }
}

.toolbar {
  margin-bottom: 16px;
  padding: 16px 24px;
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.tableCard {
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  
  .ant-card-body {
    padding: 0;
  }
  
  .ant-table {
    .ant-table-thead > tr > th {
      background: #fafafa;
      border-bottom: 1px solid #e8e8e8;
      font-weight: 600;
      color: #1f2937;
    }
    
    .ant-table-tbody > tr > td {
      border-bottom: 1px solid #f0f0f0;
    }
    
    .ant-table-tbody > tr:hover > td {
      background: #f8faff;
    }
  }
}

.uploadModal {
  .ant-upload-drag {
    border: 2px dashed #d1d5db;
    border-radius: 8px;
    background: #fafafa;
    
    &:hover {
      border-color: #1890ff;
    }
    
    .ant-upload-drag-icon {
      color: #1890ff;
      font-size: 48px;
      margin-bottom: 16px;
    }
    
    .ant-upload-text {
      color: #1f2937;
      font-size: 16px;
      font-weight: 500;
      margin-bottom: 8px;
    }
    
    .ant-upload-hint {
      color: #6b7280;
      font-size: 14px;
    }
  }
}

@media (max-width: 768px) {
  .documentManagement {
    padding: 16px;
  }
  
  .header {
    flex-direction: column;
    gap: 16px;
    align-items: stretch;
    padding: 16px;
  }
  
  .headerRight {
    justify-content: stretch;
    
    .ant-btn {
      flex: 1;
    }
  }
  
  .statistics {
    .ant-col {
      margin-bottom: 16px;
    }
  }
  
  .toolbar {
    flex-direction: column;
    gap: 12px;
    align-items: stretch;
    padding: 16px;
    
    .ant-input-search {
      width: 100%;
    }
  }
  
  .tableCard {
    .ant-table {
      font-size: 12px;
      
      .ant-table-thead > tr > th,
      .ant-table-tbody > tr > td {
        padding: 8px 4px;
      }
    }
  }
}

@media (max-width: 480px) {
  .documentManagement {
    padding: 12px;
  }
  
  .header {
    padding: 12px;
  }
  
  .toolbar {
    padding: 12px;
  }
  
  .statistics {
    .ant-col {
      margin-bottom: 12px;
    }
  }
}
