.dashboardContent {
  padding: 24px;
  background: #f0f2f5;
  min-height: calc(100vh - 64px);
}

.welcomeSection {
  margin-bottom: 32px;
  
  h2 {
    color: #1f2937;
    margin-bottom: 8px;
  }
  
  .ant-typography {
    font-size: 16px;
    color: #6b7280;
  }
}

.statsRow {
  margin-bottom: 32px;
  
  .ant-card {
    border-radius: 12px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
    border: 1px solid #e8e8e8;
    
    &:hover {
      box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
      transform: translateY(-2px);
      transition: all 0.3s ease;
    }
    
    .ant-statistic-title {
      color: #6b7280;
      font-weight: 500;
      margin-bottom: 8px;
    }
    
    .ant-statistic-content {
      display: flex;
      align-items: center;
      gap: 8px;
    }
  }
}

.quickActionsSection {
  margin-bottom: 32px;
  
  h3 {
    color: #1f2937;
    margin-bottom: 16px;
    font-weight: 600;
  }
}

.quickActionCard {
  border-radius: 12px;
  border: 1px solid #e8e8e8;
  transition: all 0.3s ease;
  cursor: pointer;
  
  &:hover {
    border-color: #1890ff;
    box-shadow: 0 4px 16px rgba(24, 144, 255, 0.1);
    transform: translateY(-2px);
  }
  
  .ant-card-body {
    padding: 20px;
    text-align: center;
  }
  
  .actionIcon {
    font-size: 32px;
    margin-bottom: 12px;
    display: block;
  }
  
  .actionTitle {
    font-size: 16px;
    font-weight: 600;
    color: #1f2937;
    margin-bottom: 8px;
  }
  
  .actionDescription {
    font-size: 14px;
    color: #6b7280;
    line-height: 1.4;
  }
}

.recentSection {
  h3 {
    color: #1f2937;
    margin-bottom: 16px;
    font-weight: 600;
  }
  
  .ant-card {
    border-radius: 12px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
    
    .ant-list-item {
      padding: 12px 0;
      border-bottom: 1px solid #f0f0f0;
      
      &:last-child {
        border-bottom: none;
      }
    }
    
    .ant-list-item-meta-title {
      font-size: 14px;
      color: #1f2937;
      margin-bottom: 4px;
    }
    
    .ant-list-item-meta-description {
      font-size: 12px;
      color: #9ca3af;
    }
    
    .ant-tag {
      border-radius: 12px;
      font-size: 11px;
      padding: 2px 8px;
      border: none;
    }
  }
}

@media (max-width: 768px) {
  .dashboardContent {
    padding: 16px;
  }
  
  .welcomeSection {
    margin-bottom: 24px;
    text-align: center;
    
    h2 {
      font-size: 20px;
    }
  }
  
  .statsRow {
    margin-bottom: 24px;
    
    .ant-col {
      margin-bottom: 16px;
    }
  }
  
  .quickActionsSection {
    margin-bottom: 24px;
  }
  
  .quickActionCard {
    margin-bottom: 16px;
    
    .ant-card-body {
      padding: 16px;
    }
    
    .actionIcon {
      font-size: 24px;
      margin-bottom: 8px;
    }
    
    .actionTitle {
      font-size: 14px;
    }
    
    .actionDescription {
      font-size: 12px;
    }
  }
}

@media (max-width: 480px) {
  .dashboardContent {
    padding: 12px;
  }
  
  .welcomeSection {
    h2 {
      font-size: 18px;
    }
  }
  
  .quickActionCard {
    .actionIcon {
      font-size: 20px;
    }
  }
}
