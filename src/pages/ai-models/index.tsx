import React, { useState } from 'react';
import {
  Card,
  Row,
  Col,
  Button,
  Modal,
  Form,
  Input,
  Select,
  message,
  Spin,
  Typography,
  Space,
  Tag,
  Divider,
  List,
  Tooltip,
  Popconfirm,
  Table,
  InputNumber,
  Switch,
  Collapse,
} from 'antd';
import {
  PlusOutlined,
  SettingOutlined,
  DeleteOutlined,
  ApiOutlined,
  EyeOutlined,
  EyeInvisibleOutlined,
  RobotOutlined,
  CloudOutlined,
} from '@ant-design/icons';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import llmService, { ILLMFactory, IMyLLMFactory, ISetApiKeyParams, IAddLLMParams } from '@/services/llm-service';
import userService from '@/services/user-service';
import AppLayout from '@/components/AppLayout';
import { useTranslate } from '@/hooks/use-i18n';
import styles from './index.less';

const { Title, Text } = Typography;
const { Option } = Select;
const { Panel } = Collapse;

interface ILLMModel {
  type: string;
  name: string;
  used_token: number;
}

const AIModels: React.FC = () => {
  const t = useTranslate();
  const [apiKeyModalVisible, setApiKeyModalVisible] = useState(false);
  const [addModelModalVisible, setAddModelModalVisible] = useState(false);
  const [defaultModelsModalVisible, setDefaultModelsModalVisible] = useState(false);
  const [selectedFactory, setSelectedFactory] = useState<string>('');
  const [selectedModel, setSelectedModel] = useState<any>(null);
  const [apiKeyForm] = Form.useForm();
  const [addModelForm] = Form.useForm();
  const [defaultModelsForm] = Form.useForm();
  const [showApiKey, setShowApiKey] = useState<{ [key: string]: boolean }>({});
  const queryClient = useQueryClient();

  // 获取LLM工厂列表
  const { data: factories, isLoading: factoriesLoading, error: factoriesError } = useQuery({
    queryKey: ['llm-factories'],
    queryFn: async () => {
      console.log('Fetching LLM factories...');
      const { data } = await userService.factories_list();
      console.log('LLM factories response:', data);
      return data?.data || [];
    },
  });

  // 获取我的LLM列表
  const { data: myLLMs, isLoading: myLLMsLoading, refetch: refetchMyLLMs, error: myLLMsError } = useQuery({
    queryKey: ['my-llms'],
    queryFn: async () => {
      console.log('Fetching my LLMs...');
      const { data } = await userService.my_llm();
      console.log('My LLMs response:', data);
      console.log('My LLMs data structure:', data?.data);
      return data?.data || {};
    },
  });

  // 获取可用模型列表
  const { data: availableModels, isLoading: modelsLoading } = useQuery({
    queryKey: ['available-models'],
    queryFn: async () => {
      const { data } = await userService.llm_list();
      return data?.data || {};
    },
  });

  // 设置API Key
  const { mutateAsync: setApiKey, isPending: settingApiKey } = useMutation({
    mutationFn: async (params: ISetApiKeyParams) => {
      const { data } = await userService.set_api_key(params);
      return data;
    },
    onSuccess: () => {
      message.success(t('aiModels.configureSuccess'));
      setApiKeyModalVisible(false);
      apiKeyForm.resetFields();
      refetchMyLLMs();
    },
    onError: (error: any) => {
      message.error(error?.message || t('aiModels.configureFailed', 'Failed to configure API Key'));
    },
  });

  // 添加LLM模型
  const { mutateAsync: addLLM, isPending: addingLLM } = useMutation({
    mutationFn: async (params: IAddLLMParams) => {
      const { data } = await userService.add_llm(params);
      return data;
    },
    onSuccess: () => {
      message.success('Model added successfully!');
      setAddModelModalVisible(false);
      addModelForm.resetFields();
      refetchMyLLMs();
    },
    onError: (error: any) => {
      message.error(error?.message || t('aiModels.addFailed', 'Failed to add model'));
    },
  });

  // 删除模型
  const { mutateAsync: deleteModel } = useMutation({
    mutationFn: async (params: { llm_factory: string; llm_name: string }) => {
      const { data } = await userService.delete_llm(params);
      return data;
    },
    onSuccess: () => {
      message.success('Model deleted successfully!');
      refetchMyLLMs();
    },
    onError: (error: any) => {
      message.error(error?.message || 'Failed to delete model');
    },
  });

  const handleSetApiKey = (factoryName: string) => {
    setSelectedFactory(factoryName);
    setApiKeyModalVisible(true);
  };

  const handleAddModel = (factoryName: string) => {
    setSelectedFactory(factoryName);
    setAddModelModalVisible(true);
  };

  const handleApiKeySubmit = async () => {
    try {
      const values = await apiKeyForm.validateFields();
      await setApiKey({
        llm_factory: selectedFactory,
        api_key: values.api_key,
        api_base: values.base_url,
      });
    } catch (error) {
      console.error('API Key form validation failed:', error);
    }
  };

  const handleAddModelSubmit = async () => {
    try {
      const values = await addModelForm.validateFields();
      await addLLM({
        llm_factory: values.llm_factory,
        llm_name: values.llm_name,
        model_type: values.model_type,
        api_key: values.api_key,
        api_base: values.base_url,
        max_tokens: values.max_tokens,
      });
    } catch (error) {
      console.error('Add model form validation failed:', error);
    }
  };

  const handleDeleteModel = async (factoryName: string, modelName: string) => {
    await deleteModel({
      llm_factory: factoryName,
      llm_name: modelName,
    });
  };

  const toggleApiKeyVisibility = (factoryName: string) => {
    setShowApiKey(prev => ({
      ...prev,
      [factoryName]: !prev[factoryName]
    }));
  };

  // 获取租户信息（包含默认模型设置）
  const { data: tenantInfo } = useQuery({
    queryKey: ['tenant-info'],
    queryFn: async () => {
      const { data } = await userService.get_tenant_info();
      return data?.data;
    },
  });

  // 设置默认模型
  const { mutateAsync: setDefaultModels, isPending: settingDefaultModels } = useMutation({
    mutationFn: async (params: any) => {
      console.log('Setting default models with params:', params);
      console.log('TenantInfo:', tenantInfo);

      // 按照原版web的格式，包含tenant_id和name
      const payload = {
        tenant_id: tenantInfo?.tenant_id || '',
        name: tenantInfo?.name || '',
        ...params,
      };

      console.log('Final payload:', payload);
      const { data } = await userService.set_tenant_info(payload);
      return data;
    },
    onSuccess: (data) => {
      console.log('Set default models success:', data);
      if (data?.code === 0) {
        message.success(t('aiModels.setDefaultSuccess', 'Default models set successfully!'));
        setDefaultModelsModalVisible(false);
        defaultModelsForm.resetFields();
        queryClient.invalidateQueries({ queryKey: ['tenant-info'] });
      } else {
        message.error(data?.message || t('aiModels.setDefaultFailed', 'Failed to set default models'));
      }
    },
    onError: (error: any) => {
      console.error('Failed to set default models:', error);
      message.error(error?.message || t('aiModels.setDefaultFailed', 'Failed to set default models'));
    },
  });

  const handleSetDefaultModels = () => {
    setDefaultModelsModalVisible(true);
    // 设置当前默认值
    if (tenantInfo) {
      defaultModelsForm.setFieldsValue({
        llm_id: tenantInfo.llm_id || '',
        embd_id: tenantInfo.embd_id || '',
        asr_id: tenantInfo.asr_id || '',
        img2txt_id: tenantInfo.img2txt_id || '',
        rerank_id: tenantInfo.rerank_id || '',
        tts_id: tenantInfo.tts_id || '',
      });
    }
  };

  const handleDefaultModelsSubmit = async () => {
    try {
      const values = await defaultModelsForm.validateFields();
      await setDefaultModels(values);
    } catch (error) {
      console.error('Validation failed:', error);
    }
  };

  const getModelTypeColor = (type: string) => {
    const colors: Record<string, string> = {
      'chat': 'blue',
      'embedding': 'green',
      'rerank': 'orange',
      'image2text': 'purple',
      'speech2text': 'cyan',
      'tts': 'magenta',
    };
    return colors[type] || 'default';
  };

  const getModelTypeIcon = (type: string) => {
    const icons: Record<string, React.ReactNode> = {
      'chat': <RobotOutlined />,
      'embedding': <CloudOutlined />,
      'rerank': <ApiOutlined />,
    };
    return icons[type] || <SettingOutlined />;
  };

  if (factoriesLoading || myLLMsLoading) {
    return (
      <AppLayout>
        <div className={styles.loadingContainer}>
          <Spin size="large" />
        </div>
      </AppLayout>
    );
  }

  return (
    <AppLayout>
      <div className={styles.aiModelsContainer}>
      <div className={styles.header}>
        <Title level={2}>{t('aiModels.title')}</Title>
        <Text type="secondary">
          {t('aiModels.manageDescription', 'Manage your AI model providers and configurations')}
        </Text>
      </div>

      <div className={styles.actions}>
        <Space>
          <Button
            icon={<SettingOutlined />}
            onClick={handleSetDefaultModels}
          >
            {t('aiModels.setDefaultModels', 'Set Default Models')}
          </Button>
          <Button
            type="primary"
            icon={<PlusOutlined />}
            onClick={() => setAddModelModalVisible(true)}
          >
            {t('aiModels.addModel')}
          </Button>
        </Space>
      </div>

      <Row gutter={[24, 24]}>
        {/* Available Factories */}
        <Col span={12}>
          <Card title={t('aiModels.availableProviders', 'Available Providers')} className={styles.factoryCard}>
            {factoriesLoading ? (
              <div style={{ textAlign: 'center', padding: '40px' }}>
                <Spin size="large" />
                <div style={{ marginTop: '16px' }}>
                  <Text type="secondary">{t('aiModels.loadingProviders', 'Loading providers...')}</Text>
                </div>
              </div>
            ) : factoriesError ? (
              <div style={{ textAlign: 'center', padding: '40px' }}>
                <Text type="danger">{t('aiModels.loadProvidersFailed', 'Failed to load providers: {{error}}', { error: factoriesError.message })}</Text>
                <div style={{ marginTop: '16px' }}>
                  <Button onClick={() => window.location.reload()}>{t('common.retry', 'Retry')}</Button>
                </div>
              </div>
            ) : !factories || factories.length === 0 ? (
              <div style={{ textAlign: 'center', padding: '40px' }}>
                <Text type="secondary">{t('aiModels.noProviders', 'No providers available')}</Text>
              </div>
            ) : (
              <List
                dataSource={factories}
                renderItem={(factory: ILLMFactory) => (
                <List.Item
                  actions={[
                    <Button
                      type="link"
                      icon={<SettingOutlined />}
                      onClick={() => handleSetApiKey(factory.name)}
                    >
                      {t('aiModels.configure')}
                    </Button>,
                  ]}
                >
                  <List.Item.Meta
                    avatar={<div className={styles.factoryLogo}>{factory.name[0]}</div>}
                    title={factory.name}
                    description={
                      <Space direction="vertical" size="small">
                        <Text type="secondary">{factory.tags}</Text>
                        <Space wrap>
                          {factory.model_types?.map((type) => (
                            <Tag
                              key={type}
                              color={getModelTypeColor(type)}
                              icon={getModelTypeIcon(type)}
                            >
                              {type}
                            </Tag>
                          ))}
                        </Space>
                      </Space>
                    }
                  />
                </List.Item>
              )}
            />
            )}
          </Card>
        </Col>

        {/* My Models */}
        <Col span={12}>
          <Card title={t('aiModels.myModels', 'My Models')} className={styles.myModelsCard}>
            {myLLMsLoading ? (
              <div style={{ textAlign: 'center', padding: '40px' }}>
                <Spin size="large" />
                <div style={{ marginTop: '16px' }}>
                  <Text type="secondary">{t('aiModels.loadingModels', 'Loading models...')}</Text>
                </div>
              </div>
            ) : myLLMsError ? (
              <div style={{ textAlign: 'center', padding: '40px' }}>
                <Text type="danger">Failed to load models: {myLLMsError.message}</Text>
                <div style={{ marginTop: '16px' }}>
                  <Button onClick={() => refetchMyLLMs()}>{t('common.retry', 'Retry')}</Button>
                </div>
              </div>
            ) : !myLLMs || Object.keys(myLLMs).length === 0 ? (
              <div className={styles.emptyState}>
                <Text type="secondary">{t('aiModels.noModels', 'No models configured yet')}</Text>
              </div>
            ) : (
              <Collapse>
                {myLLMs && Object.entries(myLLMs).map(([factoryName, factoryData]: [string, IMyLLMFactory]) => (
                  <Panel
                    key={factoryName}
                    header={
                      <Space>
                        <Text strong>{factoryName}</Text>
                        <Tag>{factoryData.llm?.length || 0} models</Tag>
                      </Space>
                    }
                  >
                    <List
                      dataSource={factoryData.llm || []}
                      renderItem={(model) => (
                        <List.Item
                          actions={[
                            <Tooltip title="Delete Model">
                              <Popconfirm
                                title="Are you sure to delete this model?"
                                onConfirm={() => handleDeleteModel(factoryName, model.name)}
                                okText="Yes"
                                cancelText="No"
                              >
                                <Button
                                  type="link"
                                  danger
                                  icon={<DeleteOutlined />}
                                />
                              </Popconfirm>
                            </Tooltip>,
                          ]}
                        >
                          <List.Item.Meta
                            title={model.name}
                            description={
                              <Space>
                                <Tag color={getModelTypeColor(model.type)}>
                                  {model.type}
                                </Tag>
                                <Text type="secondary">
                                  Used tokens: {model.used_token?.toLocaleString() || 0}
                                </Text>
                              </Space>
                            }
                          />
                        </List.Item>
                      )}
                    />
                  </Panel>
                ))}
              </Collapse>
            )}
          </Card>
        </Col>
      </Row>

      {/* API Key Modal */}
      <Modal
        title={t('aiModels.configureFactory', 'Configure {{factory}}', { factory: selectedFactory })}
        open={apiKeyModalVisible}
        onOk={handleApiKeySubmit}
        onCancel={() => {
          setApiKeyModalVisible(false);
          apiKeyForm.resetFields();
        }}
        confirmLoading={settingApiKey}
        width={600}
      >
        <Form form={apiKeyForm} layout="vertical">
          {/* API Key - 对于本地部署的模型（Ollama, VLLM, Xinference）不是必需的 */}
          <Form.Item
            name="api_key"
            label={t('aiModels.apiKey')}
            tooltip={t('aiModels.apiKeyTooltip')}
            rules={[
              {
                required: !['Ollama', 'VLLM', 'Xinference'].includes(selectedFactory),
                message: t('aiModels.enterApiKey')
              }
            ]}
          >
            <Input.Password placeholder={t('aiModels.apiKeyPlaceholder', 'Enter API key (optional for local models)')} />
          </Form.Item>

          {/* Base URL - 对于本地部署的模型是必需的 */}
          <Form.Item
            name="base_url"
            label={t('aiModels.baseUrl', 'Base URL')}
            tooltip={t('aiModels.baseUrlTooltip', 'The base URL for the model API endpoint')}
            rules={[
              {
                required: ['Ollama', 'VLLM', 'Xinference'].includes(selectedFactory),
                message: t('aiModels.baseUrlRequired', 'Please input Base URL!')
              }
            ]}
          >
            <Input
              placeholder={
                selectedFactory === 'Ollama' ? 'http://localhost:11434' :
                selectedFactory === 'VLLM' ? 'http://localhost:8000' :
                selectedFactory === 'Xinference' ? 'http://localhost:9997' :
                t('aiModels.enterBaseUrl', 'Enter base URL')
              }
            />
          </Form.Item>
        </Form>
      </Modal>

      {/* Add Model Modal */}
      <Modal
        title={t('aiModels.addModel')}
        open={addModelModalVisible}
        onOk={handleAddModelSubmit}
        onCancel={() => {
          setAddModelModalVisible(false);
          addModelForm.resetFields();
        }}
        confirmLoading={addingLLM}
        width={600}
      >
        <Form form={addModelForm} layout="vertical">
          <Form.Item
            name="llm_factory"
            label="Provider"
            rules={[{ required: true, message: 'Please select a provider!' }]}
          >
            <Select placeholder="Select provider">
              {factories?.map((factory: ILLMFactory) => (
                <Option key={factory.name} value={factory.name}>
                  {factory.name}
                </Option>
              ))}
            </Select>
          </Form.Item>

          <Form.Item
            name="model_type"
            label="Model Type"
            rules={[{ required: true, message: 'Please select model type!' }]}
            initialValue="chat"
          >
            <Select placeholder="Select model type">
              <Option value="chat">Chat</Option>
              <Option value="embedding">Embedding</Option>
              <Option value="rerank">Rerank</Option>
              <Option value="image2text">Image to Text</Option>
              <Option value="speech2text">Speech to Text</Option>
              <Option value="tts">Text to Speech</Option>
            </Select>
          </Form.Item>

          <Form.Item noStyle dependencies={['llm_factory']}>
            {({ getFieldValue }) => {
              const selectedProvider = getFieldValue('llm_factory');
              return (
                <Form.Item
                  name="llm_name"
                  label={selectedProvider === 'Xinference' ? t('aiModels.modelUID', 'Model UID') : t('aiModels.modelName', 'Model Name')}
                  tooltip={
                    selectedProvider === 'Xinference'
                      ? 'The unique identifier of the model in Xinference'
                      : 'The name of the model'
                  }
                  rules={[{ required: true, message: t('aiModels.modelNameRequired', 'Please input model name!') }]}
                >
                  <Input
                    placeholder={
                      selectedProvider === 'Ollama' ? 'e.g., llama2, mistral' :
                      selectedProvider === 'VLLM' ? 'e.g., meta-llama/Llama-2-7b-chat-hf' :
                      selectedProvider === 'Xinference' ? 'e.g., model-uid-from-xinference' :
                      t('aiModels.enterModelName', 'Enter model name')
                    }
                  />
                </Form.Item>
              );
            }}
          </Form.Item>

          <Form.Item noStyle dependencies={['llm_factory']}>
            {({ getFieldValue }) => {
              const selectedProvider = getFieldValue('llm_factory');
              const isLocalProvider = ['Ollama', 'VLLM', 'Xinference'].includes(selectedProvider);
              return (
                <Form.Item
                  name="base_url"
                  label={t('aiModels.apiBaseUrl', 'API Base URL')}
                  tooltip="The base URL for the model API endpoint"
                  rules={[
                    {
                      required: isLocalProvider,
                      message: t('aiModels.apiBaseUrlRequired', 'Please input API Base URL!')
                    }
                  ]}
                >
                  <Input
                    placeholder={
                      selectedProvider === 'Ollama' ? 'http://localhost:11434' :
                      selectedProvider === 'VLLM' ? 'http://localhost:8000' :
                      selectedProvider === 'Xinference' ? 'http://localhost:9997' :
                      t('aiModels.enterApiBaseUrl', 'Enter API base URL')
                    }
                  />
                </Form.Item>
              );
            }}
          </Form.Item>

          <Form.Item noStyle dependencies={['llm_factory']}>
            {({ getFieldValue }) => {
              const selectedProvider = getFieldValue('llm_factory');
              const isLocalProvider = ['Ollama', 'VLLM', 'Xinference'].includes(selectedProvider);
              return (
                <Form.Item
                  name="api_key"
                  label="API Key"
                  tooltip="API key for authentication (optional for local deployments)"
                  rules={[
                    {
                      required: !isLocalProvider,
                      message: 'Please input API Key!'
                    }
                  ]}
                >
                  <Input.Password placeholder={isLocalProvider ? "Optional for local models" : "Enter API key"} />
                </Form.Item>
              );
            }}
          </Form.Item>

          <Form.Item
            name="max_tokens"
            label="Max Tokens"
            tooltip="Maximum number of tokens the model can process"
            rules={[
              { required: true, message: 'Please input max tokens!' },
              { type: 'number', min: 1, message: 'Max tokens must be greater than 0!' }
            ]}
            initialValue={8192}
          >
            <InputNumber
              placeholder="e.g., 8192"
              style={{ width: '100%' }}
              min={1}
            />
          </Form.Item>

          {/* Vision Support - 仅对chat模型显示 */}
          <Form.Item noStyle dependencies={['model_type']}>
            {({ getFieldValue }) =>
              getFieldValue('model_type') === 'chat' && (
                <Form.Item
                  name="vision"
                  label="Vision Support"
                  tooltip="Enable if this model supports image understanding"
                  valuePropName="checked"
                >
                  <Switch />
                </Form.Item>
              )
            }
          </Form.Item>
        </Form>
      </Modal>

      {/* Set Default Models Modal */}
      <Modal
        title={t('aiModels.setDefaultModels', 'Set Default Models')}
        open={defaultModelsModalVisible}
        onOk={handleDefaultModelsSubmit}
        onCancel={() => {
          setDefaultModelsModalVisible(false);
          defaultModelsForm.resetFields();
        }}
        confirmLoading={settingDefaultModels}
        width={600}
      >
        <Form form={defaultModelsForm} layout="vertical">
          <Form.Item
            name="llm_id"
            label="Chat Model"
            tooltip="Default model for chat conversations"
          >
            <Select placeholder="Select chat model" allowClear showSearch>
              {myLLMs && Object.entries(myLLMs).map(([factoryName, factoryData]: [string, IMyLLMFactory]) =>
                factoryData.llm?.filter(model => model.type === 'chat').map(model => (
                  <Option key={`${model.name}@${factoryName}`} value={`${model.name}@${factoryName}`}>
                    {model.name} ({factoryName})
                  </Option>
                ))
              )}
            </Select>
          </Form.Item>

          <Form.Item
            name="embd_id"
            label="Embedding Model"
            tooltip="Default model for text embeddings"
          >
            <Select placeholder="Select embedding model" allowClear showSearch>
              {myLLMs && Object.entries(myLLMs).map(([factoryName, factoryData]: [string, IMyLLMFactory]) =>
                factoryData.llm?.filter(model => model.type === 'embedding').map(model => (
                  <Option key={`${model.name}@${factoryName}`} value={`${model.name}@${factoryName}`}>
                    {model.name} ({factoryName})
                  </Option>
                ))
              )}
            </Select>
          </Form.Item>

          <Form.Item
            name="rerank_id"
            label="Rerank Model"
            tooltip="Default model for reranking search results"
          >
            <Select placeholder="Select rerank model" allowClear showSearch>
              {myLLMs && Object.entries(myLLMs).map(([factoryName, factoryData]: [string, IMyLLMFactory]) =>
                factoryData.llm?.filter(model => model.type === 'rerank').map(model => (
                  <Option key={`${model.name}@${factoryName}`} value={`${model.name}@${factoryName}`}>
                    {model.name} ({factoryName})
                  </Option>
                ))
              )}
            </Select>
          </Form.Item>

          <Form.Item
            name="img2txt_id"
            label="Image to Text Model"
            tooltip="Default model for image to text conversion"
          >
            <Select placeholder="Select image to text model" allowClear showSearch>
              {myLLMs && Object.entries(myLLMs).map(([factoryName, factoryData]: [string, IMyLLMFactory]) =>
                factoryData.llm?.filter(model => model.type === 'image2text').map(model => (
                  <Option key={`${model.name}@${factoryName}`} value={`${model.name}@${factoryName}`}>
                    {model.name} ({factoryName})
                  </Option>
                ))
              )}
            </Select>
          </Form.Item>

          <Form.Item
            name="asr_id"
            label="Speech to Text Model"
            tooltip="Default model for speech recognition"
          >
            <Select placeholder="Select speech to text model" allowClear showSearch>
              {myLLMs && Object.entries(myLLMs).map(([factoryName, factoryData]: [string, IMyLLMFactory]) =>
                factoryData.llm?.filter(model => model.type === 'speech2text').map(model => (
                  <Option key={`${model.name}@${factoryName}`} value={`${model.name}@${factoryName}`}>
                    {model.name} ({factoryName})
                  </Option>
                ))
              )}
            </Select>
          </Form.Item>

          <Form.Item
            name="tts_id"
            label="Text to Speech Model"
            tooltip="Default model for text to speech conversion"
          >
            <Select placeholder="Select text to speech model" allowClear showSearch>
              {myLLMs && Object.entries(myLLMs).map(([factoryName, factoryData]: [string, IMyLLMFactory]) =>
                factoryData.llm?.filter(model => model.type === 'tts').map(model => (
                  <Option key={`${model.name}@${factoryName}`} value={`${model.name}@${factoryName}`}>
                    {model.name} ({factoryName})
                  </Option>
                ))
              )}
            </Select>
          </Form.Item>
        </Form>
      </Modal>
      </div>
    </AppLayout>
  );
};

export default AIModels;
