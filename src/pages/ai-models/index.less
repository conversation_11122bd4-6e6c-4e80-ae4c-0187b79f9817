.aiModelsContainer {
  padding: 24px;
  background: #f5f5f5;
  min-height: 100vh;

  .header {
    margin-bottom: 24px;
    
    h2 {
      margin-bottom: 8px;
    }
  }

  .actions {
    margin-bottom: 24px;
    display: flex;
    justify-content: flex-end;
  }

  .loadingContainer {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 400px;
  }

  .factoryCard,
  .myModelsCard {
    height: 600px;
    overflow-y: auto;

    .ant-card-body {
      padding: 16px;
    }
  }

  .factoryLogo {
    width: 40px;
    height: 40px;
    border-radius: 8px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: bold;
    font-size: 16px;
  }

  .emptyState {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 200px;
    flex-direction: column;
    
    .ant-typography {
      margin-bottom: 16px;
    }
  }

  .ant-list-item {
    padding: 16px 0;
    border-bottom: 1px solid #f0f0f0;

    &:last-child {
      border-bottom: none;
    }
  }

  .ant-collapse {
    .ant-collapse-item {
      margin-bottom: 8px;
      border: 1px solid #d9d9d9;
      border-radius: 6px;

      .ant-collapse-header {
        padding: 12px 16px;
        background: #fafafa;
        border-radius: 6px 6px 0 0;
      }

      .ant-collapse-content {
        border-top: 1px solid #d9d9d9;
        
        .ant-collapse-content-box {
          padding: 16px;
        }
      }
    }
  }

  .ant-tag {
    margin-right: 4px;
    margin-bottom: 4px;
    border-radius: 4px;
    
    .anticon {
      margin-right: 4px;
    }
  }

  .ant-modal {
    .ant-form-item {
      margin-bottom: 16px;
    }

    .ant-input,
    .ant-select {
      border-radius: 6px;
    }

    .ant-btn {
      border-radius: 6px;
    }
  }

  .ant-card {
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

    .ant-card-head {
      border-bottom: 1px solid #f0f0f0;
      
      .ant-card-head-title {
        font-weight: 600;
        font-size: 16px;
      }
    }
  }

  .ant-btn {
    border-radius: 6px;
    
    &.ant-btn-primary {
      background: #1890ff;
      border-color: #1890ff;
      
      &:hover {
        background: #40a9ff;
        border-color: #40a9ff;
      }
    }
    
    &.ant-btn-link {
      padding: 4px 8px;
      height: auto;
    }
  }

  .ant-list-item-meta {
    .ant-list-item-meta-title {
      font-weight: 500;
      margin-bottom: 4px;
    }

    .ant-list-item-meta-description {
      color: #666;
    }
  }

  .ant-space {
    &.ant-space-vertical {
      width: 100%;
    }
  }
}

// Dark theme support
[data-theme='dark'] {
  .aiModelsContainer {
    background: #141414;
    color: #fff;

    .ant-card {
      background: #1f1f1f;
      border-color: #303030;

      .ant-card-head {
        background: #1f1f1f;
        border-color: #303030;
        color: #fff;
      }

      .ant-card-body {
        background: #1f1f1f;
        color: #fff;
      }
    }

    .factoryLogo {
      background: linear-gradient(135deg, #434343 0%, #000000 100%);
    }

    .ant-list-item {
      border-color: #303030;
    }

    .ant-collapse {
      .ant-collapse-item {
        background: #1f1f1f;
        border-color: #303030;

        .ant-collapse-header {
          background: #262626;
          color: #fff;
        }

        .ant-collapse-content {
          background: #1f1f1f;
          border-color: #303030;
        }
      }
    }
  }
}
