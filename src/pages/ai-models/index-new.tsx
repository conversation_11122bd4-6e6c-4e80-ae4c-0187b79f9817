import React, { useState } from 'react';
import {
  Card,
  Row,
  Col,
  Button,
  Modal,
  Form,
  Input,
  Select,
  message,
  Spin,
  Typography,
  Space,
  Tag,
  Divider,
  List,
  Tooltip,
  Popconfirm,
  Table,
} from 'antd';
import {
  PlusOutlined,
  SettingOutlined,
  DeleteOutlined,
  ApiOutlined,
  EyeOutlined,
  EyeInvisibleOutlined,
} from '@ant-design/icons';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import llmService, { ILLMFactory, IMyLLMFactory, ISetApiKeyParams, IAddLLMParams } from '@/services/llm-service';
import AppLayout from '@/components/AppLayout';
import styles from './index.less';

const { Title, Text } = Typography;
const { Option } = Select;

interface ILLMModel {
  type: string;
  name: string;
  used_token: number;
}

const AIModels: React.FC = () => {
  const [apiKeyModalVisible, setApiKeyModalVisible] = useState(false);
  const [addModelModalVisible, setAddModelModalVisible] = useState(false);
  const [selectedFactory, setSelectedFactory] = useState<string>('');
  const [selectedModel, setSelectedModel] = useState<any>(null);
  const [apiKeyForm] = Form.useForm();
  const [addModelForm] = Form.useForm();
  const [showApiKey, setShowApiKey] = useState<{ [key: string]: boolean }>({});
  const queryClient = useQueryClient();

  // 获取LLM工厂列表
  const { data: factories, isLoading: factoriesLoading, error: factoriesError } = useQuery({
    queryKey: ['llm-factories'],
    queryFn: async () => {
      console.log('Fetching LLM factories...');
      const { data } = await llmService.getFactories();
      console.log('LLM factories response:', data);
      return data?.data || [];
    },
  });

  // 获取我的LLM列表
  const { data: myLLMs, isLoading: myLLMsLoading, refetch: refetchMyLLMs, error: myLLMsError } = useQuery({
    queryKey: ['my-llms'],
    queryFn: async () => {
      console.log('Fetching my LLMs...');
      const { data } = await llmService.getMyLLMs();
      console.log('My LLMs response:', data);
      return data?.data || {};
    },
  });

  // 获取可用模型列表
  const { data: availableModels, isLoading: modelsLoading } = useQuery({
    queryKey: ['available-models'],
    queryFn: async () => {
      const { data } = await llmService.getModelList();
      return data?.data || {};
    },
  });

  // 设置API Key
  const { mutateAsync: setApiKey, isPending: settingApiKey } = useMutation({
    mutationFn: async (params: ISetApiKeyParams) => {
      const { data } = await llmService.setApiKey(params);
      return data;
    },
    onSuccess: () => {
      message.success('API Key configured successfully!');
      setApiKeyModalVisible(false);
      apiKeyForm.resetFields();
      refetchMyLLMs();
    },
    onError: (error: any) => {
      message.error(error?.message || 'Failed to configure API Key');
    },
  });

  // 添加模型
  const { mutateAsync: addModel, isPending: addingModel } = useMutation({
    mutationFn: async (params: IAddLLMParams) => {
      const { data } = await llmService.addModel(params);
      return data;
    },
    onSuccess: () => {
      message.success('Model added successfully!');
      setAddModelModalVisible(false);
      addModelForm.resetFields();
      refetchMyLLMs();
    },
    onError: (error: any) => {
      message.error(error?.message || 'Failed to add model');
    },
  });

  // 删除模型
  const { mutateAsync: deleteModel } = useMutation({
    mutationFn: async (params: { llm_factory: string; llm_name: string }) => {
      const { data } = await llmService.deleteModel(params);
      return data;
    },
    onSuccess: () => {
      message.success('Model deleted successfully!');
      refetchMyLLMs();
    },
    onError: (error: any) => {
      message.error(error?.message || 'Failed to delete model');
    },
  });

  const handleSetApiKey = (factoryName: string) => {
    setSelectedFactory(factoryName);
    setApiKeyModalVisible(true);
  };

  const handleAddModel = (factoryName: string) => {
    setSelectedFactory(factoryName);
    setAddModelModalVisible(true);
  };

  const handleApiKeySubmit = async () => {
    try {
      const values = await apiKeyForm.validateFields();
      await setApiKey({
        llm_factory: selectedFactory,
        api_key: values.api_key,
        base_url: values.base_url,
      });
    } catch (error) {
      console.error('API Key form validation failed:', error);
    }
  };

  const handleAddModelSubmit = async () => {
    try {
      const values = await addModelForm.validateFields();
      await addModel({
        llm_factory: selectedFactory,
        llm_name: values.llm_name,
        model_type: values.model_type,
        api_key: values.api_key,
        base_url: values.base_url,
        max_tokens: values.max_tokens,
      });
    } catch (error) {
      console.error('Add model form validation failed:', error);
    }
  };

  const handleDeleteModel = async (factoryName: string, modelName: string) => {
    await deleteModel({
      llm_factory: factoryName,
      llm_name: modelName,
    });
  };

  const toggleApiKeyVisibility = (factoryName: string) => {
    setShowApiKey(prev => ({
      ...prev,
      [factoryName]: !prev[factoryName]
    }));
  };

  // 渲染工厂卡片
  const renderFactoryCard = (factory: ILLMFactory) => {
    const isConfigured = myLLMs && myLLMs[factory.name];
    
    return (
      <Card
        key={factory.name}
        className={styles.factoryCard}
        title={
          <Space>
            <ApiOutlined />
            <span>{factory.name}</span>
            {isConfigured && <Tag color="green">Configured</Tag>}
          </Space>
        }
        extra={
          <Space>
            <Button
              type="primary"
              size="small"
              icon={<SettingOutlined />}
              onClick={() => handleSetApiKey(factory.name)}
            >
              Configure
            </Button>
            <Button
              size="small"
              icon={<PlusOutlined />}
              onClick={() => handleAddModel(factory.name)}
            >
              Add Model
            </Button>
          </Space>
        }
      >
        <div className={styles.factoryContent}>
          <div className={styles.modelTypes}>
            <Text type="secondary">Supported Types: </Text>
            {factory.model_types?.map((type: string) => (
              <Tag key={type} size="small">
                {type}
              </Tag>
            ))}
          </div>
          
          {isConfigured && (
            <div className={styles.configuredModels}>
              <Divider />
              <Text strong>Configured Models:</Text>
              <List
                size="small"
                dataSource={myLLMs[factory.name]?.llm || []}
                renderItem={(model: ILLMModel) => (
                  <List.Item
                    actions={[
                      <Popconfirm
                        title="Are you sure to delete this model?"
                        onConfirm={() => handleDeleteModel(factory.name, model.name)}
                        okText="Yes"
                        cancelText="No"
                      >
                        <Button
                          type="text"
                          size="small"
                          icon={<DeleteOutlined />}
                          danger
                        />
                      </Popconfirm>
                    ]}
                  >
                    <List.Item.Meta
                      title={
                        <Space>
                          <span>{model.name}</span>
                          <Tag size="small" color="blue">{model.type}</Tag>
                        </Space>
                      }
                      description={`Used tokens: ${model.used_token || 0}`}
                    />
                  </List.Item>
                )}
              />
            </div>
          )}
        </div>
      </Card>
    );
  };

  return (
    <AppLayout>
      <div className={styles.container}>
        <div className={styles.header}>
          <Title level={2}>AI Models</Title>
          <Text type="secondary">
            Configure and manage your AI model providers
          </Text>
        </div>

        <Row gutter={[24, 24]}>
          {factoriesLoading ? (
            <Col span={24}>
              <div style={{ textAlign: 'center', padding: '40px' }}>
                <Spin size="large" />
                <div style={{ marginTop: '16px' }}>
                  <Text type="secondary">Loading providers...</Text>
                </div>
              </div>
            </Col>
          ) : factoriesError ? (
            <Col span={24}>
              <div style={{ textAlign: 'center', padding: '40px' }}>
                <Text type="danger">Failed to load providers: {factoriesError.message}</Text>
                <div style={{ marginTop: '16px' }}>
                  <Button onClick={() => window.location.reload()}>Retry</Button>
                </div>
              </div>
            </Col>
          ) : !factories || factories.length === 0 ? (
            <Col span={24}>
              <div style={{ textAlign: 'center', padding: '40px' }}>
                <Text type="secondary">No providers available</Text>
              </div>
            </Col>
          ) : (
            factories.map((factory: ILLMFactory) => (
              <Col xs={24} lg={12} xl={8} key={factory.name}>
                {renderFactoryCard(factory)}
              </Col>
            ))
          )}
        </Row>

        {/* API Key Configuration Modal */}
        <Modal
          title={`Configure ${selectedFactory} API Key`}
          open={apiKeyModalVisible}
          onOk={handleApiKeySubmit}
          onCancel={() => {
            setApiKeyModalVisible(false);
            apiKeyForm.resetFields();
          }}
          confirmLoading={settingApiKey}
          width={600}
        >
          <Form
            form={apiKeyForm}
            layout="vertical"
            initialValues={{
              base_url: '',
            }}
          >
            <Form.Item
              name="api_key"
              label="API Key"
              rules={[{ required: true, message: 'Please enter API Key' }]}
            >
              <Input.Password placeholder="Enter your API Key" />
            </Form.Item>

            <Form.Item
              name="base_url"
              label="Base URL"
              tooltip="Optional: Custom API endpoint URL"
            >
              <Input placeholder="https://api.example.com/v1" />
            </Form.Item>
          </Form>
        </Modal>

        {/* Add Model Modal */}
        <Modal
          title={`Add Model to ${selectedFactory}`}
          open={addModelModalVisible}
          onOk={handleAddModelSubmit}
          onCancel={() => {
            setAddModelModalVisible(false);
            addModelForm.resetFields();
          }}
          confirmLoading={addingModel}
          width={600}
        >
          <Form
            form={addModelForm}
            layout="vertical"
          >
            <Form.Item
              name="llm_name"
              label="Model Name"
              rules={[{ required: true, message: 'Please enter model name' }]}
            >
              <Input placeholder="e.g., gpt-3.5-turbo" />
            </Form.Item>

            <Form.Item
              name="model_type"
              label="Model Type"
              rules={[{ required: true, message: 'Please select model type' }]}
            >
              <Select placeholder="Select model type">
                <Option value="chat">Chat</Option>
                <Option value="embedding">Embedding</Option>
                <Option value="rerank">Rerank</Option>
                <Option value="image2text">Image to Text</Option>
                <Option value="speech2text">Speech to Text</Option>
                <Option value="tts">Text to Speech</Option>
              </Select>
            </Form.Item>

            <Form.Item
              name="api_key"
              label="API Key"
              tooltip="Leave empty to use factory-level API key"
            >
              <Input.Password placeholder="Optional: Model-specific API Key" />
            </Form.Item>

            <Form.Item
              name="base_url"
              label="Base URL"
              tooltip="Optional: Model-specific endpoint URL"
            >
              <Input placeholder="Optional: Custom endpoint" />
            </Form.Item>

            <Form.Item
              name="max_tokens"
              label="Max Tokens"
              tooltip="Maximum number of tokens for this model"
            >
              <Input type="number" placeholder="e.g., 4096" />
            </Form.Item>
          </Form>
        </Modal>
      </div>
    </AppLayout>
  );
};

export default AIModels;
