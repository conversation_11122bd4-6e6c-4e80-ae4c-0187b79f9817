import { useState, useEffect, useCallback } from 'react';
import { message } from 'antd';
import knowledgeService, { listDocument } from '@/services/knowledge-service';
import { IDocument } from '@/interfaces/document';

interface DocumentItem {
  id: string;
  name: string;
  type: string;
  size: number;
  uploadTime: string;
  status: 'processing' | 'completed' | 'failed';
  documentStatus: string; // 原始的document status字段 (1: 启用, 0: 禁用)
  run: string; // 解析状态字段
  progress?: number;
  kb_id?: string;
}

interface PaginationConfig {
  current: number;
  pageSize: number;
  total: number;
}

// 获取用户的第一个知识库ID
const useFirstKnowledgeBase = () => {
  const [kbId, setKbId] = useState<string | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchFirstKB = async () => {
      try {
        const response = await knowledgeService.getKnowledgeList({ page: 0, page_size: 1 });
        if (response.data?.data?.kbs?.length > 0) {
          setKbId(response.data.data.kbs[0].id);
        }
      } catch (error) {
        console.error('Failed to fetch knowledge bases:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchFirstKB();
  }, []);

  return { kbId, loading };
};

// 转换API文档数据为组件需要的格式
const transformDocument = (doc: IDocument): DocumentItem => {
  // 根据run字段确定状态，参考RunningStatus枚举
  let status: 'processing' | 'completed' | 'failed' = 'completed';
  switch (doc.run) {
    case '0': // UNSTART
      status = 'processing'; // 待开始也算作处理中
      break;
    case '1': // RUNNING
      status = 'processing'; // 正在解析
      break;
    case '2': // CANCEL
      status = 'failed'; // 已取消算作失败
      break;
    case '3': // DONE
      status = 'completed'; // 成功完成
      break;
    case '4': // FAIL
      status = 'failed'; // 失败
      break;
    default:
      // 如果run字段为空或其他值，根据progress判断
      if (doc.progress < 1) {
        status = 'processing';
      } else if (doc.progress_msg?.includes('fail')) {
        status = 'failed';
      } else {
        status = 'completed';
      }
  }

  return {
    id: doc.id,
    name: doc.name,
    type: doc.type,
    size: doc.size,
    uploadTime: doc.create_time,
    status,
    documentStatus: doc.status, // 保留原始的document status
    run: doc.run, // 保留原始的run字段
    progress: status === 'processing' ? doc.progress : undefined,
    kb_id: doc.kb_id,
  };
};

// 文档列表Hook
export const useDocumentList = (searchString: string = '') => {
  const [documents, setDocuments] = useState<DocumentItem[]>([]);
  const [loading, setLoading] = useState(false);
  const [pagination, setPagination] = useState<PaginationConfig>({
    current: 1,
    pageSize: 20,
    total: 0,
  });

  const { kbId, loading: kbLoading } = useFirstKnowledgeBase();

  const fetchDocuments = useCallback(async () => {
    if (!kbId) return;

    setLoading(true);
    try {
      const params = {
        kb_id: kbId,
        page: pagination.current - 1, // API使用0-based分页
        page_size: pagination.pageSize,
        keywords: searchString || undefined,
        orderby: 'create_time',
        desc: true,
      };

      const body = {
        run_status: [], // 不过滤状态
        types: [], // 不过滤类型
      };

      const response = await listDocument(params, body);

      if (response.data?.code === 0) {
        const apiDocuments = response.data.data?.docs || [];
        const transformedDocuments = apiDocuments.map(transformDocument);

        setDocuments(transformedDocuments);
        setPagination(prev => ({
          ...prev,
          total: response.data.data?.total || 0,
        }));
      } else {
        throw new Error(response.data?.message || 'Failed to fetch documents');
      }
    } catch (error) {
      console.error('Failed to fetch documents:', error);
      message.error('Failed to load documents');
      setDocuments([]);
    } finally {
      setLoading(false);
    }
  }, [kbId, searchString, pagination.current, pagination.pageSize]);

  useEffect(() => {
    if (!kbLoading && kbId) {
      fetchDocuments();
    }
  }, [fetchDocuments, kbLoading, kbId]);

  const refetch = useCallback(() => {
    fetchDocuments();
  }, [fetchDocuments]);

  return {
    documents,
    loading: loading || kbLoading,
    pagination,
    setPagination,
    refetch,
    kbId, // 返回知识库ID，用于上传等操作
  };
};

// 文档上传Hook
export const useUploadDocument = (kbId?: string) => {
  const [uploading, setUploading] = useState(false);

  const uploadDocument = useCallback(async (file: File) => {
    if (!kbId) {
      throw new Error('No knowledge base available for upload');
    }

    setUploading(true);
    try {
      // 文件大小检查
      const maxSize = 10 * 1024 * 1024; // 10MB
      if (file.size > maxSize) {
        throw new Error(`File size too large. Maximum size is ${(maxSize / 1024 / 1024).toFixed(0)}MB.`);
      }

      // 文件类型检查
      const allowedTypes = [
        'application/pdf',
        'application/msword',
        'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
        'text/plain',
        'text/markdown',
      ];

      if (!allowedTypes.includes(file.type) && !file.name.toLowerCase().match(/\.(pdf|doc|docx|txt|md)$/)) {
        throw new Error('Unsupported file format. Please upload PDF, DOC, DOCX, TXT, or MD files.');
      }

      // 调用真实的上传API
      const formData = new FormData();
      formData.append('kb_id', kbId);
      formData.append('file', file);

      const response = await knowledgeService.uploadDocument(formData);

      if (response.data?.code === 0) {
        return { success: true };
      } else {
        throw new Error(response.data?.message || 'Upload failed');
      }
    } catch (error) {
      console.error('Upload failed:', error);
      throw error;
    } finally {
      setUploading(false);
    }
  }, [kbId]);

  return {
    uploadDocument,
    uploading,
  };
};

// 文档删除Hook
export const useDeleteDocument = () => {
  const [deleting, setDeleting] = useState(false);

  const deleteDocument = useCallback(async (documentId: string) => {
    setDeleting(true);
    try {
      // 调用真实的删除API
      const response = await knowledgeService.deleteDocument(documentId);

      if (response.data?.code === 0) {
        return { success: true };
      } else {
        throw new Error(response.data?.message || 'Delete failed');
      }
    } catch (error) {
      console.error('Delete failed:', error);
      throw error;
    } finally {
      setDeleting(false);
    }
  }, []);

  return {
    deleteDocument,
    deleting,
  };
};

// 文档下载Hook
export const useDownloadDocument = () => {
  const [downloading, setDownloading] = useState(false);

  const downloadDocument = useCallback(async (documentId: string, fileName: string) => {
    setDownloading(true);
    try {
      // 模拟下载过程
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // 在实际实现中，这里会获取文件的下载URL
      const downloadUrl = `#`; // 模拟下载URL
      
      // 创建下载链接
      const link = document.createElement('a');
      link.href = downloadUrl;
      link.download = fileName;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      
      return { success: true };
    } catch (error) {
      console.error('Download failed:', error);
      throw error;
    } finally {
      setDownloading(false);
    }
  }, []);

  return {
    downloadDocument,
    downloading,
  };
};
