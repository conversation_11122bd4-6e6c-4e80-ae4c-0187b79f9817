import React, { useState, useCallback } from 'react';
import {
  Layout,
  Table,
  Button,
  Space,
  Input,
  Upload,
  Modal,
  message,
  Tooltip,
  Tag,
  Typography,
  Card,
  Statistic,
  Row,
  Col,
  Dropdown,
  Popconfirm,
} from 'antd';
import {
  UploadOutlined,
  SearchOutlined,
  EyeOutlined,
  DownloadOutlined,
  DeleteOutlined,
  FileTextOutlined,
  FilePdfOutlined,
  FileWordOutlined,
  FileOutlined,
  ReloadOutlined,
  MoreOutlined,
} from '@ant-design/icons';
import { useTranslate } from '@/hooks/use-i18n';
import AppLayout from '@/components/AppLayout';
import DocumentPreview from '@/components/DocumentPreview';
import { useDocumentList, useUploadDocument, useDeleteDocument } from './hooks';
import documentService from '@/services/document-service';
import { RunningStatus, RunningStatusMap } from '@/constants/document';
import styles from './index.less';

const { Content } = Layout;
const { Title, Text } = Typography;
const { Search } = Input;
const { Dragger } = Upload;

interface DocumentItem {
  id: string;
  name: string;
  type: string;
  size: number;
  uploadTime: string;
  status: 'processing' | 'completed' | 'failed';
  documentStatus: string; // 原始的document status字段 (1: 启用, 0: 禁用)
  run: string; // 解析状态字段
  progress?: number;
}

const DocumentsPage: React.FC = () => {
  const t = useTranslate();
  const [searchString, setSearchString] = useState('');
  const [uploadModalVisible, setUploadModalVisible] = useState(false);
  const [previewModalVisible, setPreviewModalVisible] = useState(false);
  const [currentDocument, setCurrentDocument] = useState<DocumentItem | null>(null);
  const [selectedRowKeys, setSelectedRowKeys] = useState<string[]>([]);

  // 使用hooks获取数据
  const {
    documents,
    loading,
    pagination,
    setPagination,
    refetch,
    kbId,
  } = useDocumentList(searchString);

  const { uploadDocument, uploading } = useUploadDocument(kbId || undefined);
  const { deleteDocument, deleting } = useDeleteDocument();

  // 获取文件图标
  const getFileIcon = (fileName: string, fileType: string) => {
    const ext = fileName.toLowerCase().split('.').pop();
    const type = fileType.toLowerCase();

    if (type.includes('pdf') || ext === 'pdf') {
      return <FilePdfOutlined style={{ color: '#ff4d4f' }} />;
    }
    if (type.includes('word') || ['doc', 'docx'].includes(ext || '')) {
      return <FileWordOutlined style={{ color: '#1890ff' }} />;
    }
    if (type.includes('text') || ['txt', 'md'].includes(ext || '')) {
      return <FileTextOutlined style={{ color: '#52c41a' }} />;
    }
    return <FileOutlined style={{ color: '#8c8c8c' }} />;
  };

  // 格式化文件大小
  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  // 获取处理状态标签
  const getStatusTag = (status: string) => {
    switch (status) {
      case 'processing':
        return <Tag color="processing">{t('documents.processing')}</Tag>;
      case 'completed':
        return <Tag color="success">{t('documents.completed')}</Tag>;
      case 'failed':
        return <Tag color="error">{t('documents.failed')}</Tag>;
      default:
        return <Tag color="default">{t('documents.unknown')}</Tag>;
    }
  };

  // 获取解析状态标签 (基于run字段)
  const getParsingStatusTag = (runStatus: string) => {
    const statusInfo = RunningStatusMap[runStatus as RunningStatus] || {
      color: 'default',
      text: runStatus || 'Unknown',
      label: runStatus || 'Unknown'
    };

    return (
      <Tag color={statusInfo.color}>
        {statusInfo.text}
      </Tag>
    );
  };

  // 获取文档状态标签 (1: 启用, 0: 禁用)
  const getDocumentStatusTag = (documentStatus: string) => {
    return (
      <Tag color={documentStatus === '1' ? 'green' : 'red'}>
        {documentStatus === '1' ? '启用' : '禁用'}
      </Tag>
    );
  };

  // 处理文件上传
  const handleUpload = useCallback(async (file: File) => {
    try {
      await uploadDocument(file);
      message.success(t('documents.uploadSuccess'));
      setUploadModalVisible(false);
      refetch();
    } catch (error) {
      console.error('Upload failed:', error);
      message.error(t('documents.uploadFailed'));
    }
  }, [uploadDocument, refetch]);

  // 处理文件下载
  const handleDownload = useCallback((record: DocumentItem) => {
    try {
      // 使用真实的下载URL
      const downloadUrl = documentService.downloadDocument(record.id);
      const link = document.createElement('a');
      link.href = downloadUrl;
      link.download = record.name;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      message.success(t('documents.downloadStarted'));
    } catch (error) {
      console.error('Download failed:', error);
      message.error('Download failed');
    }
  }, [t]);

  // 处理文件预览
  const handlePreview = useCallback((record: DocumentItem) => {
    setCurrentDocument(record);
    setPreviewModalVisible(true);
  }, []);

  // 处理文件删除
  const handleDelete = useCallback(async (record: DocumentItem) => {
    try {
      await deleteDocument(record.id);
      message.success(t('documents.deleteSuccess'));
      refetch();
    } catch (error) {
      console.error('Delete failed:', error);
      message.error(t('documents.deleteFailed'));
    }
  }, [deleteDocument, refetch]);

  // 批量删除
  const handleBatchDelete = useCallback(async () => {
    try {
      await Promise.all(selectedRowKeys.map(id => deleteDocument(id)));
      message.success(`${selectedRowKeys.length} ${t('documents.batchDeleteSuccess')}`);
      setSelectedRowKeys([]);
      refetch();
    } catch (error) {
      console.error('Batch delete failed:', error);
      message.error(t('documents.batchDeleteFailed'));
    }
  }, [selectedRowKeys, deleteDocument, refetch]);

  // 表格列定义
  const columns = [
    {
      title: t('documents.documentName'),
      dataIndex: 'name',
      key: 'name',
      fixed: 'left' as const,
      width: 300,
      render: (text: string, record: DocumentItem) => (
        <Space>
          {getFileIcon(record.name, record.type)}
          <Tooltip title={text}>
            <Button
              type="link"
              onClick={() => handlePreview(record)}
              style={{ padding: 0, height: 'auto', fontWeight: 500 }}
            >
              {text.length > 40 ? `${text.substring(0, 40)}...` : text}
            </Button>
          </Tooltip>
        </Space>
      ),
    },
    {
      title: t('documents.type'),
      dataIndex: 'type',
      key: 'type',
      width: 120,
      render: (type: string) => (
        <Tag color="blue">{type || t('documents.unknown')}</Tag>
      ),
    },
    {
      title: t('documents.size'),
      dataIndex: 'size',
      key: 'size',
      width: 100,
      render: (size: number) => formatFileSize(size),
    },
    {
      title: t('documents.status'),
      dataIndex: 'status',
      key: 'status',
      width: 120,
      render: (status: string) => getStatusTag(status),
    },
    {
      title: '解析状态',
      dataIndex: 'run',
      key: 'parsing_status',
      width: 120,
      render: (runStatus: string) => getParsingStatusTag(runStatus),
    },
    {
      title: '文档状态',
      dataIndex: 'documentStatus',
      key: 'documentStatus',
      width: 100,
      render: (documentStatus: string) => getDocumentStatusTag(documentStatus),
    },
    {
      title: t('documents.uploadTime'),
      dataIndex: 'uploadTime',
      key: 'uploadTime',
      width: 180,
      render: (time: string) => new Date(time).toLocaleString(),
      sorter: true,
      defaultSortOrder: 'descend' as const,
    },
    {
      title: t('documents.actions'),
      key: 'actions',
      fixed: 'right' as const,
      width: 120,
      render: (_: any, record: DocumentItem) => (
        <Dropdown
          menu={{
            items: [
              {
                key: 'preview',
                icon: <EyeOutlined />,
                label: t('documents.preview'),
                onClick: () => handlePreview(record),
              },
              {
                key: 'download',
                icon: <DownloadOutlined />,
                label: t('documents.download'),
                onClick: () => handleDownload(record),
              },
              {
                type: 'divider',
              },
              {
                key: 'delete',
                icon: <DeleteOutlined />,
                label: t('documents.delete'),
                danger: true,
                onClick: () => handleDelete(record),
              },
            ],
          }}
          trigger={['click']}
        >
          <Button type="text" icon={<MoreOutlined />} />
        </Dropdown>
      ),
    },
  ];

  // 行选择配置
  const rowSelection = {
    selectedRowKeys,
    onChange: (keys: React.Key[]) => setSelectedRowKeys(keys as string[]),
  };

  return (
    <AppLayout>
      <Content className={styles.documentsContent}>
        <div className={styles.container}>
          {/* 页面标题 */}
          <div className={styles.header}>
            <Title level={2}>
              <FileTextOutlined style={{ marginRight: 8 }} />
              {t('documents.title')}
            </Title>
            <Text type="secondary">
              {t('documents.description')}
            </Text>
          </div>

          {/* 统计卡片 */}
          <Row gutter={16} className={styles.statsRow}>
            <Col xs={24} sm={8}>
              <Card>
                <Statistic
                  title={t('documents.totalDocuments')}
                  value={documents?.length || 0}
                  prefix={<FileTextOutlined />}
                />
              </Card>
            </Col>
            <Col xs={24} sm={8}>
              <Card>
                <Statistic
                  title={t('documents.completed')}
                  value={documents?.filter(d => d.status === 'completed').length || 0}
                  prefix={<FileTextOutlined />}
                  valueStyle={{ color: '#3f8600' }}
                />
              </Card>
            </Col>
            <Col xs={24} sm={8}>
              <Card>
                <Statistic
                  title={t('documents.processing')}
                  value={documents?.filter(d => d.status === 'processing').length || 0}
                  prefix={<FileTextOutlined />}
                  valueStyle={{ color: '#1890ff' }}
                />
              </Card>
            </Col>
          </Row>

          {/* 操作栏 */}
          <Card className={styles.actionBar}>
            <Row justify="space-between" align="middle">
              <Col>
                <Space>
                  <Search
                    placeholder={t('documents.searchPlaceholder')}
                    allowClear
                    style={{ width: 300 }}
                    onSearch={setSearchString}
                    onChange={(e) => !e.target.value && setSearchString('')}
                  />
                  <Button
                    icon={<ReloadOutlined />}
                    onClick={() => refetch()}
                    loading={loading}
                  >
                    {t('documents.refresh')}
                  </Button>
                </Space>
              </Col>
              <Col>
                <Space>
                  {selectedRowKeys.length > 0 && (
                    <Popconfirm
                      title={`${t('documents.batchDeleteConfirm')} (${selectedRowKeys.length})`}
                      onConfirm={handleBatchDelete}
                      okText={t('common.yes')}
                      cancelText={t('common.no')}
                    >
                      <Button danger loading={deleting}>
                        {t('documents.deleteSelected')} ({selectedRowKeys.length})
                      </Button>
                    </Popconfirm>
                  )}
                  <Button
                    type="primary"
                    icon={<UploadOutlined />}
                    onClick={() => setUploadModalVisible(true)}
                  >
                    {t('documents.uploadDocument')}
                  </Button>
                </Space>
              </Col>
            </Row>
          </Card>

          {/* 文档表格 */}
          <Card className={styles.tableCard}>
            <Table
              columns={columns}
              dataSource={documents}
              rowKey="id"
              loading={loading}
              pagination={{
                ...pagination,
                showSizeChanger: true,
                showQuickJumper: true,
                showTotal: (total, range) =>
                  `${range[0]}-${range[1]} ${t('common.of')} ${total} ${t('documents.showTotal')}`,
                onChange: (page, pageSize) => {
                  setPagination(prev => ({ ...prev, current: page, pageSize }));
                },
              }}
              rowSelection={rowSelection}
              scroll={{ x: 1200 }}
            />
          </Card>
        </div>

        {/* 上传模态框 */}
        <Modal
          title={t('documents.uploadDocument')}
          open={uploadModalVisible}
          onCancel={() => setUploadModalVisible(false)}
          footer={null}
          width={600}
        >
          <Dragger
            name="file"
            multiple={false}
            beforeUpload={(file) => {
              handleUpload(file);
              return false;
            }}
            disabled={uploading}
            accept=".pdf,.doc,.docx,.txt,.md"
          >
            <p className="ant-upload-drag-icon">
              <UploadOutlined />
            </p>
            <p className="ant-upload-text">
              {t('aiRead.uploadArea')}
            </p>
            <p className="ant-upload-hint">
              {t('documents.uploadHint')}
            </p>
          </Dragger>
        </Modal>

        {/* 预览模态框 */}
        <Modal
          title={`${t('documents.preview')}: ${currentDocument?.name}`}
          open={previewModalVisible}
          onCancel={() => {
            setPreviewModalVisible(false);
            setCurrentDocument(null);
          }}
          footer={null}
          width={800}
          style={{ top: 20 }}
        >
          {currentDocument && (
            <DocumentPreview
              document={{
                id: currentDocument.id,
                name: currentDocument.name,
                content: t('documents.mockContent', `Mock content for ${currentDocument.name}. In a real implementation, this would be the actual document content.`, { name: currentDocument.name }),
                type: currentDocument.type,
                size: currentDocument.size,
                uploadTime: currentDocument.uploadTime,
                url: documentService.downloadDocument(currentDocument.id), // 添加下载URL用于预览
              }}
            />
          )}
        </Modal>
      </Content>
    </AppLayout>
  );
};

export default DocumentsPage;
