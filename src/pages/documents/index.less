.documentsContent {
  background: #f0f2f5;
  min-height: calc(100vh - 64px);
  padding: 24px;
}

.container {
  max-width: 1400px;
  margin: 0 auto;
}

.header {
  margin-bottom: 24px;
  text-align: center;
  
  h2 {
    margin-bottom: 8px;
    color: #1890ff;
  }
}

.statsRow {
  margin-bottom: 24px;
  
  .ant-card {
    text-align: center;
    
    .ant-statistic-title {
      color: #8c8c8c;
      font-size: 14px;
    }
    
    .ant-statistic-content {
      font-size: 24px;
      font-weight: 600;
    }
  }
}

.actionBar {
  margin-bottom: 16px;
  
  .ant-card-body {
    padding: 16px 24px;
  }
  
  .ant-input-search {
    .ant-input {
      border-radius: 6px;
    }
    
    .ant-btn {
      border-radius: 0 6px 6px 0;
    }
  }
}

.tableCard {
  .ant-card-body {
    padding: 0;
  }
  
  .ant-table {
    .ant-table-thead > tr > th {
      background: #fafafa;
      font-weight: 600;
      color: #262626;
      border-bottom: 2px solid #f0f0f0;
    }
    
    .ant-table-tbody > tr {
      &:hover {
        background: #f5f5f5;
      }
      
      > td {
        border-bottom: 1px solid #f0f0f0;
        padding: 12px 16px;
        
        .ant-btn-link {
          color: #1890ff;
          text-decoration: none;
          
          &:hover {
            text-decoration: underline;
          }
        }
      }
    }
    
    .ant-table-selection-column {
      width: 60px;
    }
    
    .ant-table-fixed-left {
      box-shadow: 6px 0 6px -4px rgba(0, 0, 0, 0.15);
    }
    
    .ant-table-fixed-right {
      box-shadow: -6px 0 6px -4px rgba(0, 0, 0, 0.15);
    }
  }
  
  .ant-pagination {
    margin: 16px 0;
    text-align: right;
    
    .ant-pagination-total-text {
      color: #8c8c8c;
    }
  }
}

// 文件图标样式
.fileIcon {
  font-size: 16px;
  margin-right: 8px;
}

// 状态标签样式
.statusTag {
  &.processing {
    background: #e6f7ff;
    border-color: #91d5ff;
    color: #1890ff;
  }
  
  &.completed {
    background: #f6ffed;
    border-color: #b7eb8f;
    color: #52c41a;
  }
  
  &.failed {
    background: #fff2f0;
    border-color: #ffccc7;
    color: #ff4d4f;
  }
}

// 操作按钮样式
.actionButton {
  &.preview {
    color: #1890ff;
    
    &:hover {
      background: #e6f7ff;
    }
  }
  
  &.download {
    color: #52c41a;
    
    &:hover {
      background: #f6ffed;
    }
  }
  
  &.delete {
    color: #ff4d4f;
    
    &:hover {
      background: #fff2f0;
    }
  }
}

// 上传拖拽区域样式
.uploadDragger {
  .ant-upload-drag {
    border: 2px dashed #d9d9d9;
    border-radius: 8px;
    background: #fafafa;
    transition: border-color 0.3s ease;
    
    &:hover {
      border-color: #1890ff;
    }
    
    .ant-upload-drag-icon {
      font-size: 48px;
      color: #1890ff;
      margin-bottom: 16px;
    }
    
    .ant-upload-text {
      font-size: 16px;
      font-weight: 500;
      color: #262626;
      margin-bottom: 8px;
    }
    
    .ant-upload-hint {
      color: #8c8c8c;
      font-size: 14px;
    }
  }
}

// 响应式设计
@media (max-width: 1200px) {
  .documentsContent {
    padding: 16px;
  }
  
  .container {
    max-width: 100%;
  }
  
  .actionBar {
    .ant-row {
      flex-direction: column;
      gap: 16px;
      
      .ant-col {
        width: 100%;
      }
    }
  }
}

@media (max-width: 768px) {
  .documentsContent {
    padding: 12px;
  }
  
  .header {
    margin-bottom: 16px;
    
    h2 {
      font-size: 20px;
    }
  }
  
  .statsRow {
    .ant-col {
      margin-bottom: 16px;
    }
  }
  
  .actionBar {
    .ant-card-body {
      padding: 12px 16px;
    }
    
    .ant-input-search {
      width: 100% !important;
      margin-bottom: 12px;
    }
  }
  
  .tableCard {
    .ant-table {
      .ant-table-thead > tr > th,
      .ant-table-tbody > tr > td {
        padding: 8px 12px;
        font-size: 12px;
      }
    }
  }
  
  .uploadDragger {
    .ant-upload-drag {
      .ant-upload-drag-icon {
        font-size: 36px;
      }
      
      .ant-upload-text {
        font-size: 14px;
      }
      
      .ant-upload-hint {
        font-size: 12px;
      }
    }
  }
}

@media (max-width: 576px) {
  .documentsContent {
    padding: 8px;
  }
  
  .tableCard {
    .ant-table {
      font-size: 11px;
      
      .ant-table-thead > tr > th,
      .ant-table-tbody > tr > td {
        padding: 6px 8px;
      }
    }
    
    .ant-pagination {
      .ant-pagination-options {
        display: none;
      }
    }
  }
}
