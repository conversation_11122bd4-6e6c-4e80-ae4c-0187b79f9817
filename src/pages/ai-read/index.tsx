import React, { useState, useCallback } from 'react';
import {
  Layout,
  Card,
  Upload,
  Tabs,
  Typography,
  Button,
  Space,
  Spin,
  Alert,
  message,
  Row,
  Col,
  Divider,
  Empty,
  Progress,
  Tag,
  Modal,
} from 'antd';
import {
  UploadOutlined,
  FileTextOutlined,
  MessageOutlined,
  RobotOutlined,
  FileSearchOutlined,
  QuestionCircleOutlined,
  ReloadOutlined,
  PlayCircleOutlined,
  DeleteOutlined,
} from '@ant-design/icons';
import { useTranslate } from '@/hooks/use-i18n';
import AppLayout from '@/components/AppLayout';
import MarkdownContent from '@/components/MarkdownContent';
import DocumentSummaryCard from './components/DocumentSummaryCard';
import DocumentChatCard from './components/DocumentChatCard';
import PendingCard from './components/PendingCard';
import DocumentPreview from '@/components/DocumentPreview';
import { useAIReading } from '@/hooks/use-ai-reading-hooks';
import styles from './index.less';

const { Content } = Layout;
const { Title, Text } = Typography;
const { Dragger } = Upload;

const AIReadPage: React.FC = () => {
  const t = useTranslate();
  const [activeTab, setActiveTab] = useState('preview');

  // 使用AI阅读hook
  const {
    files,
    currentFile,
    conversations,
    loading,
    uploading,
    processing,
    uploadFile,
    processFile,
    deleteFile,
    chatWithDocument,
    fetchConversations,
    selectFile,
    refreshFileStatus,
  } = useAIReading();

  // 处理文档上传
  const handleDocumentUpload = useCallback(async (fileList: File[]) => {
    // 检查文件数量限制
    if (files.length + fileList.length > 25) {
      message.error(t('aiRead.maxFilesError'));
      return;
    }

    try {
      // 处理每个文件
      for (const file of fileList) {
        // 检查文件类型
        const allowedTypes = [
          'application/pdf',
          'application/msword',
          'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
          'application/vnd.openxmlformats-officedocument.presentationml.presentation',
          'application/vnd.ms-powerpoint',
          'text/plain',
          'text/markdown',
        ];

        const allowedExtensions = /\.(pdf|doc|docx|ppt|pptx|txt|md)$/i;

        if (!allowedTypes.includes(file.type) && !allowedExtensions.test(file.name)) {
          message.error(t('aiRead.unsupportedFormat', { filename: file.name }));
          continue;
        }

        // 检查文件大小 (50MB限制)
        const maxSize = 50 * 1024 * 1024; // 50MB
        if (file.size > maxSize) {
          message.error(t('aiRead.fileTooLarge', {
            filename: file.name,
            maxSize: (maxSize / 1024 / 1024).toFixed(0)
          }));
          continue;
        }

        // 上传文件
        await uploadFile(file);
      }
    } catch (error) {
      console.error(t('aiRead.uploadFailedError'), error);
      message.error(t('aiRead.uploadFailed'));
    }
  }, [files.length, uploadFile, t]);

  // 获取状态标签颜色
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'uploaded': return 'blue';
      case 'converting_to_pdf': return 'orange';
      case 'calling_parser': return 'purple';
      case 'processing': return 'cyan';
      case 'saving_results': return 'geekblue';
      case 'generating_summary': return 'magenta';
      case 'completed': return 'green';
      case 'failed': return 'red';
      default: return 'default';
    }
  };

  // 获取状态文本
  const getStatusText = (status: string) => {
    const statusKey = `aiRead.status.${status}`;
    const translatedStatus = t(statusKey);
    // 如果翻译键不存在，返回原状态值
    return translatedStatus !== statusKey ? translatedStatus : status;
  };

  // 上传配置
  const uploadProps = {
    name: 'file',
    multiple: true,
    beforeUpload: (file: File, fileList: File[]) => {
      handleDocumentUpload(fileList);
      return false; // 阻止默认上传
    },
    disabled: uploading,
    accept: '.pdf,.doc,.docx,.ppt,.pptx,.txt,.md',
    showUploadList: false, // 隐藏默认的上传列表
  };

  // Tab配置
  const tabItems = [
    {
      key: 'preview',
      label: (
        <Space>
          <FileTextOutlined />
          {t('aiRead.preview')}
        </Space>
      ),
      children: (
        <DocumentPreview
          document={currentFile ? {
            id: currentFile.file_id,
            name: currentFile.original_filename,
            content: currentFile.content_summary || '',
            type: currentFile.file_type,
            size: currentFile.file_size,
            uploadTime: currentFile.create_time,
            // 对于Word文件，总是提供PDF预览URL；对于PDF文件，也提供URL
            url: ['pdf', 'doc', 'docx', 'ppt', 'pptx'].includes(currentFile.file_type.toLowerCase())
              ? `/api/v1/ai-reading/files/${currentFile.file_id}/pdf`
              : undefined,
          } : null}
          loading={loading}
        />
      ),
    },
    {
      key: 'summary',
      label: (
        <Space>
          <FileSearchOutlined />
          {t('aiRead.summary')}
        </Space>
      ),
      children: (
        <DocumentSummaryCard
          file={currentFile}
          loading={loading}
          onRefresh={() => currentFile && refreshFileStatus(currentFile.file_id)}
        />
      ),
    },
    {
      key: 'pending',
      label: (
        <Space>
          <QuestionCircleOutlined />
          {t('aiRead.pending')}
        </Space>
      ),
      children: (
        <PendingCard />
      ),
    },
    {
      key: 'chat',
      label: (
        <Space>
          <MessageOutlined />
          {t('aiRead.chat')}
        </Space>
      ),
      children: (
        <DocumentChatCard
          file={currentFile}
          conversations={conversations}
          loading={loading}
          onSendMessage={(question, sessionId) =>
            currentFile && chatWithDocument(currentFile.file_id, question, sessionId)
          }
          onLoadConversations={(sessionId) =>
            currentFile && fetchConversations(currentFile.file_id, sessionId)
          }
        />
      ),
    },
  ];

  return (
    <AppLayout>
      <Content className={styles.aiReadContent}>
        <div className={styles.container}>
          {/* 页面标题 */}
          <Row gutter={[16, 16]}>
              <Col xs={24} sm={16}>
              
                <div className={styles.header}>
                  <Title level={2}>
                    <RobotOutlined style={{ marginRight: 8 }} />
                    {t('aiRead.title')}
                  </Title>
                  <Text type="secondary">
                    {t('aiRead.description')}
                  </Text>
                </div>
              </Col>
              <Col xs={24} sm={8}>
                {/* 文件上传激励区域 */}
                  <div className={styles.uploadButton}>
                    <Dragger {...uploadProps} className={styles.compactUploadArea}>
                      <div className="ant-upload-drag-container">
                        <div className="ant-upload-drag-icon">
                          <UploadOutlined />
                        </div>
                        <div className="upload-content">
                          <div className="ant-upload-text">
                            {t('aiRead.uploadArea')}
                          </div>
                          <div className="ant-upload-hint">
                            {t('aiRead.uploadHint')} ({t('aiRead.maxFiles')})
                          </div>
                        </div>
                      </div>
                    </Dragger>
                  </div>
                </Col>
          </Row>

          <div className={styles.fileUploadSection}>
            {/* 文件列表 */}
            {files.length > 0 && (
              <div className={styles.fileList}>
                <Title level={4}>{t('aiRead.uploadedFiles')} ({files.length}/25)</Title>
                <div className={styles.fileItems}>
                  {files.map((file) => (
                    <div
                      key={file.file_id}
                      className={`${styles.fileItem} ${currentFile?.file_id === file.file_id ? styles.active : ''}`}
                      onClick={() => selectFile(file)}
                    >
                      <div className={styles.fileInfo}>
                        <div className={styles.fileName}>{file.original_filename}</div>
                        <div className={styles.fileSize}>
                          {(file.file_size / 1024).toFixed(2)} KB
                        </div>
                        <div className={styles.fileStatus}>
                          <Tag color={getStatusColor(file.processing_status)}>
                            {getStatusText(file.processing_status)}
                          </Tag>
                        </div>
                        {['processing', 'converting_to_pdf', 'calling_parser', 'saving_results', 'generating_summary'].includes(file.processing_status) && (
                          <Progress
                            percent={file.processing_progress}
                            size="small"
                            status="active"
                          />
                        )}
                      </div>
                      <div className={styles.fileActions}>
                        {processing[file.file_id] && (
                          <Spin size="small" />
                        )}
                        {file.processing_status === 'uploaded' && (
                          <Button
                            type="text"
                            size="small"
                            icon={<PlayCircleOutlined />}
                            onClick={(e) => {
                              e.stopPropagation();
                              processFile(file.file_id);
                            }}
                            title={t('aiRead.startProcessing')}
                          />
                        )}
                        <Button
                          type="text"
                          size="small"
                          icon={<ReloadOutlined />}
                          onClick={(e) => {
                            e.stopPropagation();
                            refreshFileStatus(file.file_id);
                          }}
                          title={t('aiRead.refreshStatus')}
                        />
                        <Button
                          type="text"
                          size="small"
                          icon={<DeleteOutlined />}
                          danger
                          onClick={(e) => {
                            e.stopPropagation();
                            Modal.confirm({
                              title: t('aiRead.confirmDelete'),
                              content: t('aiRead.confirmDeleteMessage', { filename: file.original_filename }),
                              okText: t('common.delete'),
                              okType: 'danger',
                              cancelText: t('common.cancel'),
                              onOk: () => deleteFile(file.file_id),
                            });
                          }}
                          title={t('aiRead.deleteFile')}
                        />
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            )}
          </div>
              
          <Row gutter={24} className={styles.mainContent}>
            {/* 左侧：文档上传和内容显示 */}
            <Col xs={24} lg={12} className={styles.leftPanel}>
              <Card 
                title={
                  <Space>
                    <FileTextOutlined />
                    {t('aiRead.documentContent')}
                  </Space>
                }
                className={styles.documentCard}
              >
                {currentFile ? (
                  <div>
                    <div className={styles.fileDetails}>
                      <Title level={4}>{currentFile.original_filename}</Title>
                      <Space direction="vertical" size="small" style={{ width: '100%' }}>
                        <div>
                          <Row gutter={[16, 16]}>
                            <Col xs={24} sm={8}>
                              <Text strong>{t('aiRead.fileSize')} </Text>
                              <Text>{(currentFile.file_size / 1024).toFixed(2)} KB</Text>
                            </Col>
                            <Col xs={24} sm={8}>
                                <Text strong>{t('aiRead.fileType')} </Text>
                                <Text>{currentFile.file_type}</Text>
                            </Col>
                            <Col xs={24} sm={8}>
                              <Text strong>{t('aiRead.processingStatus')} </Text>
                              <Tag color={getStatusColor(currentFile.processing_status)}>
                                {getStatusText(currentFile.processing_status)}
                              </Tag>
                            </Col>
                          </Row>                        </div>
                        {['processing', 'converting_to_pdf', 'calling_parser', 'saving_results', 'generating_summary'].includes(currentFile.processing_status) && (
                          <div>
                            <Text strong>{t('aiRead.processingProgress')} </Text>
                            <Progress
                              percent={currentFile.processing_progress}
                              status="active"
                            />
                            <Text type="secondary">{currentFile.processing_message}</Text>
                          </div>
                        )}
                        {currentFile.content_summary && (
                          <div>
                            <div className={styles.summaryHeader}>
                              <Space>
                                <RobotOutlined style={{ color: '#1890ff' }} />
                                <Text strong>{t('aiRead.aiGeneratedSummary')}</Text>
                              </Space>
                            </div>
                            <div className={styles.leftPanelSummary}>
                              <MarkdownContent content={currentFile.content_summary} />
                            </div>
                          </div>
                        )}
                      </Space>
                    </div>
                  </div>
                ) : (
                  <Empty
                    description={t('aiRead.selectFileToView')}
                    image={Empty.PRESENTED_IMAGE_SIMPLE}
                  />
                )}
              </Card>
            </Col>

            {/* 右侧：三张卡片 */}
            <Col xs={24} lg={12} className={styles.rightPanel}>
              <Card className={styles.tabsCard}>
                <Tabs
                  activeKey={activeTab}
                  onChange={setActiveTab}
                  items={tabItems}
                  size="large"
                  tabPosition="top"
                />
              </Card>
            </Col>
          </Row>
        </div>
      </Content>
    </AppLayout>
  );
};

export default AIReadPage;
