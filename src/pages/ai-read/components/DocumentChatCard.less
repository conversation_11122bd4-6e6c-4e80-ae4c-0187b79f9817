.chatCard {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.cardHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  padding-bottom: 12px;
  border-bottom: 1px solid #f0f0f0;
}

.documentInfo {
  margin-bottom: 16px;
  
  .infoCard {
    background: #fafafa;
    border: 1px solid #f0f0f0;
    
    .ant-card-body {
      padding: 12px;
    }
  }
}

.messagesArea {
  flex: 1;
  display: flex;
  flex-direction: column;
  min-height: 300px;
  margin-bottom: 16px;
}

.welcomeMessage {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  text-align: center;
}

.messagesList {
  flex: 1;
  overflow-y: auto;
  padding: 8px;
  background: #fafafa;
  border-radius: 8px;
  border: 1px solid #f0f0f0;
  
  // 自定义滚动条样式
  &::-webkit-scrollbar {
    width: 6px;
  }
  
  &::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 3px;
  }
  
  &::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 3px;
    
    &:hover {
      background: #a8a8a8;
    }
  }
}

.messageItem {
  display: flex;
  margin-bottom: 16px;
  
  &.userMessage {
    flex-direction: row-reverse;
    
    .messageAvatar {
      margin-left: 8px;
      margin-right: 0;
      background: #1890ff;
      color: white;
    }
    
    .messageContent {
      background: #1890ff;
      color: white;
      border-radius: 12px 12px 4px 12px;
      
      .messageTime {
        color: rgba(255, 255, 255, 0.8);
      }
    }
  }
  
  &.assistantMessage {
    .messageAvatar {
      margin-right: 8px;
      background: #f0f0f0;
      color: #666;
    }
    
    .messageContent {
      background: white;
      border: 1px solid #f0f0f0;
      border-radius: 12px 12px 12px 4px;
    }
  }
}

.messageAvatar {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
  font-size: 14px;
}

.messageContent {
  max-width: 70%;
  padding: 12px 16px;
  position: relative;
  
  .messageTime {
    font-size: 11px;
    opacity: 0.7;
    margin-top: 4px;
    text-align: right;
  }
}

.inputArea {
  border-top: 1px solid #f0f0f0;
  padding-top: 16px;
}

.inputContainer {
  display: flex;
  gap: 8px;
  align-items: flex-end;
  
  .ant-input {
    flex: 1;
    resize: none;
  }
  
  .ant-btn {
    flex-shrink: 0;
  }
}

.emptyState {
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  text-align: center;
}

// 响应式设计
@media (max-width: 768px) {
  .messageItem {
    &.userMessage,
    &.assistantMessage {
      .messageContent {
        max-width: 85%;
      }
    }
  }
  
  .inputContainer {
    flex-direction: column;
    align-items: stretch;
    gap: 12px;
  }
}

// Markdown内容样式
.markdownWrapper {
  // 确保markdown内容在聊天消息中正确显示
  .markdown-content {
    font-size: 14px;
    line-height: 1.6;
    color: #333;

    // 调整标题样式
    h1, h2, h3, h4, h5, h6 {
      margin-top: 0.8em;
      margin-bottom: 0.4em;
      color: #262626;
    }

    h1 { font-size: 1.3em; }
    h2 { font-size: 1.2em; }
    h3 { font-size: 1.1em; }
    h4, h5, h6 { font-size: 1em; }

    // 调整段落间距
    p {
      margin-bottom: 0.6em;

      &:last-child {
        margin-bottom: 0;
      }
    }

    // 调整列表样式
    ul, ol {
      margin-bottom: 0.6em;
      padding-left: 1.2em;
    }

    li {
      margin-bottom: 0.2em;
    }

    // 调整代码块样式
    pre {
      background-color: #f6f8fa;
      border: 1px solid #e1e4e8;
      border-radius: 6px;
      padding: 8px 12px;
      margin: 0.6em 0;
      font-size: 13px;
      overflow-x: auto;
    }

    // 调整行内代码样式
    code {
      background-color: #f6f8fa;
      border: 1px solid #e1e4e8;
      border-radius: 3px;
      padding: 1px 4px;
      font-size: 13px;
    }

    // 调整引用样式
    blockquote {
      border-left: 3px solid #1890ff;
      background-color: #f6f8fa;
      padding: 6px 10px;
      margin: 0.6em 0;
      border-radius: 0 4px 4px 0;
    }

    // 调整表格样式
    table {
      font-size: 13px;
      margin: 0.6em 0;

      th, td {
        padding: 6px 8px;
      }
    }

    // 调整链接样式
    a {
      color: #1890ff;

      &:hover {
        color: #40a9ff;
      }
    }
  }
}
