import React, { useState, useEffect } from 'react';
import {
  Button,
  Typography,
  Space,
  Spin,
  Alert,
  Empty,
  Card,
  message,
  Tag,
  Progress,
} from 'antd';
import {
  FileSearchOutlined,
  ReloadOutlined,
  RobotOutlined,
  PlayCircleOutlined,
} from '@ant-design/icons';
import { useTranslate } from '@/hooks/use-i18n';
import { AIReadingFile } from '@/hooks/use-ai-reading-hooks';
import MarkdownContent from '@/components/MarkdownContent';
import styles from './DocumentSummaryCard.less';

const { Title, Text, Paragraph } = Typography;

interface DocumentSummaryCardProps {
  file: AIReadingFile | null;
  loading?: boolean;
  onRefresh?: () => void;
}

const DocumentSummaryCard: React.FC<DocumentSummaryCardProps> = ({
  file,
  loading = false,
  onRefresh,
}) => {
  const t = useTranslate();

  // 获取状态标签颜色
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'uploaded': return 'blue';
      case 'converting_to_pdf': return 'orange';
      case 'calling_parser': return 'purple';
      case 'processing': return 'cyan';
      case 'completed': return 'green';
      case 'failed': return 'red';
      default: return 'default';
    }
  };

  // 获取状态文本
  const getStatusText = (status: string) => {
    switch (status) {
      case 'uploaded': return '已上传';
      case 'converting_to_pdf': return '转换PDF中';
      case 'calling_parser': return '调用解析API';
      case 'processing': return '处理中';
      case 'completed': return '已完成';
      case 'failed': return '失败';
      default: return status;
    }
  };

  // 如果没有选择文件
  if (!file) {
    return (
      <div className={styles.emptyState}>
        <Empty
          image={Empty.PRESENTED_IMAGE_SIMPLE}
          description={
            <div>
              <Text type="secondary">请先上传并选择一个文件</Text>
              <br />
              <Text type="secondary">然后查看文档摘要</Text>
            </div>
          }
        />
      </div>
    );
  }

  return (
    <div className={styles.summaryCard}>
      {/* 卡片头部 */}
      <div className={styles.cardHeader}>
        <Space>
          <FileSearchOutlined style={{ color: '#1890ff' }} />
          <Title level={4} style={{ margin: 0 }}>
            文档摘要
          </Title>
        </Space>
        <Button
          type="primary"
          icon={<ReloadOutlined />}
          onClick={onRefresh}
          disabled={!file}
        >
          刷新状态
        </Button>
      </div>

      {/* 文档信息 */}
      <div className={styles.documentInfo}>
        <Card size="small" className={styles.infoCard}>
          <Space direction="vertical" size="small" style={{ width: '100%' }}>
            <div>
              <Text strong>文档名称: </Text>
              <Text>{file.original_filename}</Text>
            </div>
            <div>
              <Text strong>文件大小: </Text>
              <Text>{(file.file_size / 1024).toFixed(2)} KB</Text>
            </div>
            <div>
              <Text strong>文件类型: </Text>
              <Text>{file.file_type}</Text>
            </div>
            <div>
              <Text strong>处理状态: </Text>
              <Tag color={getStatusColor(file.processing_status)}>
                {getStatusText(file.processing_status)}
              </Tag>
            </div>
            <div>
              <Text strong>上传时间: </Text>
              <Text>{new Date(file.create_time).toLocaleString()}</Text>
            </div>
          </Space>
        </Card>
      </div>

      {/* 处理进度 */}
      {file.processing_status === 'processing' && (
        <div className={styles.progressSection}>
          <Card size="small">
            <Space direction="vertical" style={{ width: '100%' }}>
              <div>
                <Text strong>处理进度:</Text>
              </div>
              <Progress
                percent={file.processing_progress}
                status="active"
                strokeColor={{
                  '0%': '#108ee9',
                  '100%': '#87d068',
                }}
              />
              <Text type="secondary">{file.processing_message}</Text>
            </Space>
          </Card>
        </div>
      )}

      {/* 摘要内容区域 */}
      <div className={styles.summaryContent}>
        {file.processing_status === 'failed' && (
          <Alert
            message="处理失败"
            description={file.processing_message}
            type="error"
            style={{ marginBottom: 16 }}
            action={
              <Button size="small" onClick={onRefresh}>
                <ReloadOutlined /> 重试
              </Button>
            }
          />
        )}

        {file.processing_status === 'completed' && file.content_summary && (
          <div className={styles.summaryResult}>
            <Card className={styles.summaryCard}>
              <div className={styles.summaryHeader}>
                <Space>
                  <RobotOutlined style={{ color: '#1890ff' }} />
                  <Title level={5} style={{ margin: 0 }}>
                    AI生成的文档摘要
                  </Title>
                </Space>
              </div>
              <div className={styles.summaryBody}>
                <MarkdownContent content={file.content_summary} />
              </div>
            </Card>
          </div>
        )}

        {file.processing_status === 'completed' && !file.content_summary && (
          <div className={styles.promptState}>
            <Empty
              image={Empty.PRESENTED_IMAGE_SIMPLE}
              description={
                <div>
                  <Text type="secondary">文档处理完成，但未生成摘要</Text>
                  <br />
                  <Text type="secondary">请刷新状态或重新处理</Text>
                </div>
              }
            />
          </div>
        )}

        {['uploaded', 'converting_to_pdf', 'calling_parser'].includes(file.processing_status) && (
          <div className={styles.promptState}>
            <Empty
              image={Empty.PRESENTED_IMAGE_SIMPLE}
              description={
                <div>
                  <Text type="secondary">文档正在处理中</Text>
                  <br />
                  <Text type="secondary">处理完成后将自动显示摘要</Text>
                </div>
              }
            />
          </div>
        )}
      </div>
    </div>
  );
};

export default DocumentSummaryCard;
