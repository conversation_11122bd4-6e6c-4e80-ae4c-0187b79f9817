.summaryCard {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.cardHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  padding-bottom: 12px;
  border-bottom: 1px solid #f0f0f0;
}

.documentInfo {
  margin-bottom: 16px;
  
  .infoCard {
    background: #fafafa;
    border: 1px solid #f0f0f0;
    
    .ant-card-body {
      padding: 12px;
    }
  }
}

.summaryContent {
  flex: 1;
  display: flex;
  flex-direction: column;
  min-height: 300px;
}

.loadingState {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-align: center;
}

.summaryResult {
  flex: 1;

  .summaryCard {
    height: 100%;

    .ant-card-body {
      height: 100%;
      max-height: 400px;
      overflow-y: auto;
      padding: 16px;

      // 自定义滚动条样式
      &::-webkit-scrollbar {
        width: 6px;
      }

      &::-webkit-scrollbar-track {
        background: #f1f1f1;
        border-radius: 3px;
      }

      &::-webkit-scrollbar-thumb {
        background: #c1c1c1;
        border-radius: 3px;

        &:hover {
          background: #a8a8a8;
        }
      }
    }
  }
}

.summaryHeader {
  margin-bottom: 16px;
  padding-bottom: 12px;
  border-bottom: 1px solid #f0f0f0;
}

.summaryBody {
  // 确保markdown内容样式正确
  .markdown-content {
    font-size: 14px;
    line-height: 1.6;

    // 调整标题样式
    h1, h2, h3, h4, h5, h6 {
      margin-top: 1em;
      margin-bottom: 0.5em;
      color: #262626;
    }

    // 调整段落间距
    p {
      margin-bottom: 0.8em;
    }

    // 调整列表样式
    ul, ol {
      margin-bottom: 0.8em;
      padding-left: 1.2em;
    }

    // 调整代码块样式
    pre {
      background-color: #f6f8fa;
      border: 1px solid #e1e4e8;
      border-radius: 6px;
      padding: 12px;
      margin: 0.8em 0;
      font-size: 13px;
    }

    // 调整行内代码样式
    code {
      background-color: #f6f8fa;
      border: 1px solid #e1e4e8;
      border-radius: 3px;
      padding: 2px 4px;
      font-size: 13px;
    }

    // 调整引用样式
    blockquote {
      border-left: 4px solid #1890ff;
      background-color: #f6f8fa;
      padding: 8px 12px;
      margin: 0.8em 0;
      border-radius: 0 4px 4px 0;
    }
  }
}

.promptState {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
}

.emptyState {
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  text-align: center;
}

// 响应式设计
@media (max-width: 768px) {
  .cardHeader {
    flex-direction: column;
    align-items: stretch;
    gap: 12px;
    
    .ant-space {
      justify-content: center;
    }
  }
  
  .summaryResult {
    .summaryCard {
      .ant-card-body {
        max-height: 300px;
      }
    }
  }
}
