import React, { useState, useEffect, useRef } from 'react';
import {
  Typography,
  Space,
  Empty,
  Input,
  Button,
  message,
  Spin,
  Card,
  Tag,
  Tooltip,
} from 'antd';
import {
  MessageOutlined,
  SendOutlined,
  RobotOutlined,
  UserOutlined,
  CopyOutlined,
  RedoOutlined,
} from '@ant-design/icons';
import { useTranslate } from '@/hooks/use-i18n';
import { AIReadingFile, AIReadingConversation } from '@/hooks/use-ai-reading-hooks';
import { copyToClipboard } from '@/utils/clipboard';
import MarkdownContent from '@/components/MarkdownContent';
import styles from './DocumentChatCard.less';

const { Title, Text } = Typography;
const { TextArea } = Input;

interface DocumentChatCardProps {
  file: AIReadingFile | null;
  conversations: AIReadingConversation[];
  loading?: boolean;
  onSendMessage: (question: string, sessionId?: string) => Promise<any>;
  onLoadConversations: (sessionId?: string) => void;
}

const DocumentChatCard: React.FC<DocumentChatCardProps> = ({
  file,
  conversations,
  loading = false,
  onSendMessage,
  onLoadConversations,
}) => {
  const t = useTranslate();
  const [inputValue, setInputValue] = useState('');
  const [sending, setSending] = useState(false);
  const [sessionId] = useState(() => `session_${Date.now()}`);
  const messagesEndRef = useRef<HTMLDivElement>(null);

  // 自动滚动到底部
  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  useEffect(() => {
    scrollToBottom();
  }, [conversations]);

  // 当文件改变时加载对话历史
  useEffect(() => {
    if (file && file.processing_status === 'completed') {
      console.log('Loading conversations for file:', file.file_id, 'session:', sessionId);
      // 暂时不使用session_id过滤，获取该文件的所有对话
      onLoadConversations();
    }
  }, [file?.file_id, file?.processing_status]); // 移除onLoadConversations和sessionId依赖，避免频繁调用

  // 调试：打印conversations数据
  useEffect(() => {
    console.log('Conversations updated:', conversations);
  }, [conversations]);

  // 发送消息
  const handleSendMessage = async () => {
    if (!inputValue.trim()) {
      message.warning('请输入问题');
      return;
    }

    if (!file) {
      message.warning('请先选择一个文件');
      return;
    }

    if (file.processing_status !== 'completed') {
      message.warning('文件尚未处理完成，无法进行问答');
      return;
    }

    const question = inputValue.trim();
    setInputValue('');
    setSending(true);

    try {
      console.log('Sending message:', question, 'to file:', file?.file_id, 'session:', sessionId);
      const result = await onSendMessage(question, sessionId);
      console.log('Send message result:', result);
      if (result) {
        message.success('问答成功');
        // 重新加载对话历史
        console.log('Reloading conversations after successful send');
        onLoadConversations();
      } else {
        message.error('问答失败');
      }
    } catch (err: any) {
      console.error('问答失败:', err);
      message.error('问答失败');
    } finally {
      setSending(false);
    }
  };

  // 处理键盘事件
  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  };

  // 复制文本到剪贴板
  const handleCopy = async (text: string) => {
    const success = await copyToClipboard(text);
    if (success) {
      message.success('已复制到剪贴板');
    } else {
      message.error('复制失败');
    }
  };

  // 重新发送消息
  const handleResend = async (question: string) => {
    if (sending) {
      message.warning('正在发送中，请稍候');
      return;
    }

    setSending(true);
    try {
      console.log('Resending message:', question, 'to file:', file?.file_id, 'session:', sessionId);
      const result = await onSendMessage(question, sessionId);
      console.log('Resend message result:', result);
      if (result) {
        message.success('重新发送成功');
        // 重新加载对话历史
        console.log('Reloading conversations after successful resend');
        onLoadConversations();
      } else {
        message.error('重新发送失败');
      }
    } catch (err: any) {
      console.error('重新发送失败:', err);
      message.error('重新发送失败');
    } finally {
      setSending(false);
    }
  };

  // 如果没有选择文件
  if (!file) {
    return (
      <div className={styles.emptyState}>
        <Empty
          image={Empty.PRESENTED_IMAGE_SIMPLE}
          description={
            <div>
              <Text type="secondary">请先上传并选择一个文件</Text>
              <br />
              <Text type="secondary">然后开始与文档对话</Text>
            </div>
          }
        />
      </div>
    );
  }

  // 如果文件未处理完成
  if (file.processing_status !== 'completed') {
    return (
      <div className={styles.emptyState}>
        <Empty
          image={Empty.PRESENTED_IMAGE_SIMPLE}
          description={
            <div>
              <Text type="secondary">文件正在处理中</Text>
              <br />
              <Text type="secondary">处理完成后即可开始问答</Text>
              <br />
              <Tag color="blue">{file.processing_status}</Tag>
            </div>
          }
        />
      </div>
    );
  }

  return (
    <div className={styles.chatCard}>
      {/* 卡片头部 */}
      <div className={styles.cardHeader}>
        <Space>
          <MessageOutlined style={{ color: '#1890ff' }} />
          <Title level={4} style={{ margin: 0 }}>
            文档问答
          </Title>
        </Space>
      </div>

      {/* 文档信息 */}
      <div className={styles.documentInfo}>
        <Card size="small" className={styles.infoCard}>
          <Text strong>正在与文档对话: </Text>
          <Text>{file.original_filename}</Text>
        </Card>
      </div>

      {/* 聊天消息区域 */}
      <div className={styles.messagesArea}>
        {conversations.length === 0 ? (
          <div className={styles.welcomeMessage}>
            <Empty
              image={Empty.PRESENTED_IMAGE_SIMPLE}
              description={
                <div>
                  <Text type="secondary">开始与文档对话</Text>
                  <br />
                  <Text type="secondary">您可以询问关于文档的任何问题</Text>
                  <br />
                  <Text type="secondary" style={{ fontSize: '12px', marginTop: '8px' }}>
                    💡 试试问："这个文档的主要内容是什么？"
                  </Text>
                </div>
              }
            />
          </div>
        ) : (
          <div className={styles.messagesList}>
            {conversations.map((conversation) => (
              <div key={conversation.conversation_id}>
                {/* 用户问题 */}
                <div className={`${styles.messageItem} ${styles.userMessage}`}>
                  <div className={styles.messageAvatar}>
                    <UserOutlined />
                  </div>
                  <div className={styles.messageContent}>
                    <div style={{ whiteSpace: 'pre-wrap', lineHeight: '1.6' }}>
                      {conversation.question}
                    </div>
                    <div className={styles.messageTime}>
                      <Space>
                        <span>{new Date(conversation.create_time * 1000).toLocaleTimeString()}</span>
                        {/* 操作按钮 */}
                        <Space size="small">
                          <Tooltip title="复制">
                            <Button
                              type="text"
                              size="small"
                              icon={<CopyOutlined />}
                              onClick={() => handleCopy(conversation.question)}
                              style={{
                                color: 'rgba(255, 255, 255, 0.8)',
                                opacity: 0.7
                              }}
                            />
                          </Tooltip>
                          <Tooltip title="重新发送">
                            <Button
                              type="text"
                              size="small"
                              icon={<RedoOutlined />}
                              onClick={() => handleResend(conversation.question)}
                              loading={sending}
                              style={{
                                color: 'rgba(255, 255, 255, 0.8)',
                                opacity: 0.7
                              }}
                            />
                          </Tooltip>
                        </Space>
                      </Space>
                    </div>
                  </div>
                </div>

                {/* AI回答 */}
                {conversation.answer ? (
                  <div className={`${styles.messageItem} ${styles.assistantMessage}`}>
                    <div className={styles.messageAvatar}>
                      <RobotOutlined />
                    </div>
                    <div className={styles.messageContent}>
                      <div className={styles.markdownWrapper}>
                        <MarkdownContent content={conversation.answer} />
                      </div>
                      <div className={styles.messageTime}>
                        <Space>
                          <span>{new Date(conversation.update_time * 1000).toLocaleTimeString()}</span>
                          {conversation.confidence_score && (
                            <Tag color="blue">
                              置信度: {(conversation.confidence_score * 100).toFixed(1)}%
                            </Tag>
                          )}
                          {/* 复制按钮 */}
                          <Tooltip title="复制">
                            <Button
                              type="text"
                              size="small"
                              icon={<CopyOutlined />}
                              onClick={() => handleCopy(conversation.answer)}
                              style={{
                                color: '#666',
                                opacity: 0.7
                              }}
                            />
                          </Tooltip>
                        </Space>
                      </div>
                    </div>
                  </div>
                ) : conversation.processing_status === 'pending' || conversation.processing_status === 'processing' ? (
                  <div className={`${styles.messageItem} ${styles.assistantMessage}`}>
                    <div className={styles.messageAvatar}>
                      <RobotOutlined />
                    </div>
                    <div className={styles.messageContent}>
                      <div style={{ color: '#999', fontStyle: 'italic' }}>
                        正在思考中...
                      </div>
                    </div>
                  </div>
                ) : conversation.processing_status === 'failed' ? (
                  <div className={`${styles.messageItem} ${styles.assistantMessage}`}>
                    <div className={styles.messageAvatar}>
                      <RobotOutlined />
                    </div>
                    <div className={styles.messageContent}>
                      <div style={{ color: '#ff4d4f' }}>
                        回答生成失败，请重试
                      </div>
                    </div>
                  </div>
                ) : null}
              </div>
            ))}

            {sending && (
              <div className={`${styles.messageItem} ${styles.assistantMessage}`}>
                <div className={styles.messageAvatar}>
                  <RobotOutlined />
                </div>
                <div className={styles.messageContent}>
                  <Spin size="small" />
                  <Text type="secondary" style={{ marginLeft: 8 }}>
                    正在思考中...
                  </Text>
                </div>
              </div>
            )}
            <div ref={messagesEndRef} />
          </div>
        )}
      </div>

      {/* 输入区域 */}
      <div className={styles.inputArea}>
        <div className={styles.inputContainer}>
          <TextArea
            value={inputValue}
            onChange={(e) => setInputValue(e.target.value)}
            onKeyPress={handleKeyPress}
            placeholder="请输入您的问题..."
            autoSize={{ minRows: 1, maxRows: 4 }}
            disabled={sending}
          />
          <Button
            type="primary"
            icon={<SendOutlined />}
            onClick={handleSendMessage}
            loading={sending}
            disabled={!inputValue.trim()}
          >
            发送
          </Button>
        </div>
      </div>
    </div>
  );
};

export default DocumentChatCard;
