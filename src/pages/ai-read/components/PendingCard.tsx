import React from 'react';
import {
  Typography,
  Space,
  Empty,
  Card,
  Timeline,
  Tag,
} from 'antd';
import {
  QuestionCircleOutlined,
  ClockCircleOutlined,
  ExperimentOutlined,
  BulbOutlined,
  ToolOutlined,
} from '@ant-design/icons';
import { useTranslate } from '@/hooks/use-i18n';
import styles from './PendingCard.less';

const { Title, Text, Paragraph } = Typography;

const PendingCard: React.FC = () => {
  const t = useTranslate();

  const upcomingFeatures = [
    {
      title: 'Document Analysis',
      description: 'Advanced document structure analysis and key information extraction',
      status: 'planning',
      icon: <ExperimentOutlined />,
    },
    {
      title: 'Multi-language Support',
      description: 'Support for documents in multiple languages with automatic translation',
      status: 'development',
      icon: <BulbOutlined />,
    },
    {
      title: 'Document Comparison',
      description: 'Compare multiple documents and highlight differences',
      status: 'planning',
      icon: <ToolOutlined />,
    },
    {
      title: 'Export & Sharing',
      description: 'Export summaries and share insights with team members',
      status: 'planning',
      icon: <ToolOutlined />,
    },
  ];

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'development':
        return 'processing';
      case 'planning':
        return 'default';
      default:
        return 'default';
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'development':
        return 'In Development';
      case 'planning':
        return 'Planned';
      default:
        return 'Unknown';
    }
  };

  return (
    <div className={styles.pendingCard}>
      {/* 卡片头部 */}
      <div className={styles.cardHeader}>
        <Space>
          <QuestionCircleOutlined style={{ color: '#1890ff' }} />
          <Title level={4} style={{ margin: 0 }}>
            {t('aiRead.pending')}
          </Title>
        </Space>
      </div>

      {/* 内容区域 */}
      <div className={styles.contentArea}>
        <Card className={styles.infoCard}>
          <div className={styles.comingSoonSection}>
            <div className={styles.iconSection}>
              <ClockCircleOutlined className={styles.mainIcon} />
            </div>
            
            <Title level={3} className={styles.title}>
              Coming Soon
            </Title>
            
            <Paragraph className={styles.description}>
              We're working on exciting new features to enhance your document reading experience. 
              Stay tuned for updates!
            </Paragraph>
          </div>

          <div className={styles.featuresSection}>
            <Title level={4} className={styles.sectionTitle}>
              Upcoming Features
            </Title>
            
            <Timeline className={styles.timeline}>
              {upcomingFeatures.map((feature, index) => (
                <Timeline.Item
                  key={index}
                  dot={feature.icon}
                  className={styles.timelineItem}
                >
                  <div className={styles.featureItem}>
                    <div className={styles.featureHeader}>
                      <Text strong className={styles.featureTitle}>
                        {feature.title}
                      </Text>
                      <Tag color={getStatusColor(feature.status)}>
                        {getStatusText(feature.status)}
                      </Tag>
                    </div>
                    <Text type="secondary" className={styles.featureDescription}>
                      {feature.description}
                    </Text>
                  </div>
                </Timeline.Item>
              ))}
            </Timeline>
          </div>

          <div className={styles.feedbackSection}>
            <Card size="small" className={styles.feedbackCard}>
              <Title level={5}>Have suggestions?</Title>
              <Text type="secondary">
                We'd love to hear your ideas for new features. Contact our team to share your feedback!
              </Text>
            </Card>
          </div>
        </Card>
      </div>
    </div>
  );
};

export default PendingCard;
