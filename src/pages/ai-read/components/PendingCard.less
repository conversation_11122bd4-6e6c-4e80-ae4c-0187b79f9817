.pendingCard {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.cardHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  padding-bottom: 12px;
  border-bottom: 1px solid #f0f0f0;
}

.contentArea {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.infoCard {
  flex: 1;
  
  .ant-card-body {
    height: 100%;
    display: flex;
    flex-direction: column;
  }
}

.comingSoonSection {
  text-align: center;
  margin-bottom: 32px;
  padding: 24px;
  background: linear-gradient(135deg, #f6f9fc 0%, #e9f4ff 100%);
  border-radius: 12px;
  border: 1px solid #e6f7ff;
}

.iconSection {
  margin-bottom: 16px;
  
  .mainIcon {
    font-size: 48px;
    color: #1890ff;
  }
}

.title {
  color: #1890ff;
  margin-bottom: 12px !important;
}

.description {
  color: #666;
  font-size: 14px;
  line-height: 1.6;
  margin-bottom: 0 !important;
}

.featuresSection {
  flex: 1;
  margin-bottom: 24px;
}

.sectionTitle {
  margin-bottom: 16px !important;
  color: #262626;
  border-bottom: 2px solid #f0f0f0;
  padding-bottom: 8px;
}

.timeline {
  .ant-timeline-item {
    padding-bottom: 20px;
    
    .ant-timeline-item-content {
      margin-left: 20px;
    }
  }
}

.timelineItem {
  .ant-timeline-item-head {
    background: #fff;
    border: 2px solid #1890ff;
    width: 16px;
    height: 16px;
    
    .anticon {
      color: #1890ff;
      font-size: 12px;
    }
  }
}

.featureItem {
  .featureHeader {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 8px;
    
    .featureTitle {
      font-size: 15px;
      color: #262626;
    }
  }
  
  .featureDescription {
    font-size: 13px;
    line-height: 1.5;
    display: block;
  }
}

.feedbackSection {
  .feedbackCard {
    background: #f9f9f9;
    border: 1px solid #f0f0f0;
    
    .ant-card-body {
      padding: 16px;
      text-align: center;
    }
    
    h5 {
      margin-bottom: 8px !important;
      color: #1890ff;
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .comingSoonSection {
    padding: 16px;
    margin-bottom: 24px;
    
    .iconSection {
      .mainIcon {
        font-size: 36px;
      }
    }
    
    .title {
      font-size: 20px;
    }
  }
  
  .featureItem {
    .featureHeader {
      flex-direction: column;
      align-items: flex-start;
      gap: 8px;
    }
  }
}
