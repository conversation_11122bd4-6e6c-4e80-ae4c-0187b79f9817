.loginPage {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
  position: relative;
}

.languageSwitcher {
  position: absolute;
  top: 20px;
  right: 20px;
  z-index: 10;

  .ant-space-item {
    color: rgba(255, 255, 255, 0.9);
  }

  .ant-select-selection-item {
    color: rgba(255, 255, 255, 0.9) !important;
  }

  .ant-select-arrow {
    color: rgba(255, 255, 255, 0.7) !important;
  }

  &:hover {
    .ant-space-item,
    .ant-select-selection-item,
    .ant-select-arrow {
      color: white !important;
    }
  }
}

.loginContainer {
  width: 100%;
  max-width: 400px;
}

.loginCard {
  border-radius: 12px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
  border: none;
  
  .ant-card-body {
    padding: 40px;
  }
}

.loginHeader {
  text-align: center;
  margin-bottom: 32px;
}

.loginTitle {
  margin-bottom: 8px !important;
  color: #1f2937;
  font-weight: 600;
}

.loginForm {
  .ant-form-item-label > label {
    font-weight: 500;
    color: #374151;
  }
  
  .ant-input,
  .ant-input-password {
    border-radius: 8px;
    border: 1px solid #d1d5db;
    
    &:hover {
      border-color: #667eea;
    }
    
    &:focus,
    &.ant-input-focused {
      border-color: #667eea;
      box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.1);
    }
  }
}

.submitButton {
  height: 48px;
  border-radius: 8px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border: none;
  font-weight: 500;
  font-size: 16px;
  
  &:hover {
    background: linear-gradient(135deg, #5a67d8 0%, #6b46c1 100%);
  }
}

.switchMode {
  text-align: center;
  margin-top: 16px;
}

.linkButton {
  padding: 0;
  height: auto;
  font-weight: 500;
  color: #667eea;
  
  &:hover {
    color: #5a67d8;
  }
}

.oauthButton {
  height: 44px;
  border-radius: 8px;
  border: 1px solid #e5e7eb;
  color: #374151;
  font-weight: 500;
  
  &:hover {
    border-color: #667eea;
    color: #667eea;
  }
}

// 注册成功倒计时样式
.countdownContainer {
  text-align: center;
  padding: 20px 0;

  .ant-result-title {
    color: #1f2937;
    font-weight: 600;
    font-size: 24px;
  }

  .ant-result-subtitle {
    color: #6b7280;
    font-size: 16px;
    margin-bottom: 32px;
  }
}

.countdownContent {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 24px;
}

.initializingText {
  font-size: 18px;
  color: #374151;
  margin-bottom: 8px;
}

.progressContainer {
  display: flex;
  justify-content: center;
  align-items: center;

  .ant-progress-circle .ant-progress-text {
    font-size: 24px;
    font-weight: 600;
    color: #667eea;
  }
}

.countdownText {
  font-size: 16px;
  color: #6b7280;
  margin-top: 8px;
}

.skipButton {
  color: #667eea;
  font-weight: 500;
  padding: 0;
  height: auto;

  &:hover {
    color: #5a67d8;
  }
}

@media (max-width: 480px) {
  .loginPage {
    padding: 16px;
  }

  .loginCard .ant-card-body {
    padding: 24px;
  }

  .countdownContainer {
    padding: 16px 0;

    .ant-result-title {
      font-size: 20px;
    }

    .ant-result-subtitle {
      font-size: 14px;
    }
  }

  .initializingText {
    font-size: 16px;
  }

  .progressContainer .ant-progress-circle .ant-progress-text {
    font-size: 20px;
  }

  .countdownText {
    font-size: 14px;
  }
}
