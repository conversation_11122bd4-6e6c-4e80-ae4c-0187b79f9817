import React, { useState } from 'react';
import {
  Card,
  Row,
  Col,
  Form,
  Input,
  Button,
  message,
  Spin,
  Typography,
  Divider,
  Space,
  Avatar,
  Upload,
  Badge,
  Descriptions,
  Alert,
} from 'antd';
import {
  UserOutlined,
  MailOutlined,
  LockOutlined,
  SaveOutlined,
  UploadOutlined,
  CheckCircleOutlined,
  ExclamationCircleOutlined,
  CloseCircleOutlined,
  InfoCircleOutlined,
} from '@ant-design/icons';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import userService, { IUserInfo, IUserSettingParams, ISystemStatus } from '@/services/user-service';
import AppLayout from '@/components/AppLayout';
import { useTranslate } from '@/hooks/use-i18n';
import styles from './index.less';

const { Title, Text } = Typography;

const Settings: React.FC = () => {
  const t = useTranslate();
  const [profileForm] = Form.useForm();
  const [passwordForm] = Form.useForm();
  const queryClient = useQueryClient();

  // 获取用户信息
  const { data: userInfo, isLoading: userInfoLoading } = useQuery({
    queryKey: ['user-info'],
    queryFn: async () => {
      const { data } = await userService.getUserInfo();
      return data?.data;
    },
  });

  // 当用户信息加载完成后，设置表单默认值
  React.useEffect(() => {
    if (userInfo) {
      profileForm.setFieldsValue({
        nickname: userInfo.nickname || '',
        email: userInfo.email || '',
      });
    }
  }, [userInfo, profileForm]);

  // 获取系统状态
  const { data: systemStatus, isLoading: systemStatusLoading } = useQuery({
    queryKey: ['system-status'],
    queryFn: async () => {
      const { data } = await userService.getSystemStatus();
      return data?.data;
    },
  });

  // 获取系统版本
  const { data: systemVersion } = useQuery({
    queryKey: ['system-version'],
    queryFn: async () => {
      const { data } = await userService.getSystemVersion();
      return data?.data;
    },
  });

  // 更新用户设置
  const { mutateAsync: updateUserSetting, isPending: updatingProfile } = useMutation({
    mutationFn: async (params: IUserSettingParams) => {
      const { data } = await userService.updateUserSetting(params);
      return data;
    },
    onSuccess: () => {
      message.success(t('settings.updateSuccess'));
      queryClient.invalidateQueries({ queryKey: ['user-info'] });
    },
    onError: (error: any) => {
      message.error(error?.message || t('settings.updateFailed', 'Failed to update profile'));
    },
  });

  // 更新密码
  const { mutateAsync: updatePassword, isPending: updatingPassword } = useMutation({
    mutationFn: async (params: IUserSettingParams) => {
      const { data } = await userService.updateUserSetting(params);
      return data;
    },
    onSuccess: () => {
      message.success('Password updated successfully!');
      passwordForm.resetFields();
    },
    onError: (error: any) => {
      message.error(error?.message || t('settings.passwordUpdateFailed', 'Failed to update password'));
    },
  });

  const handleProfileSubmit = async () => {
    try {
      const values = await profileForm.validateFields();
      await updateUserSetting(values);
    } catch (error) {
      console.error('Validation failed:', error);
    }
  };

  const handlePasswordSubmit = async () => {
    try {
      const values = await passwordForm.validateFields();
      await updatePassword(values);
    } catch (error) {
      console.error('Validation failed:', error);
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'green':
        return <CheckCircleOutlined style={{ color: '#52c41a' }} />;
      case 'yellow':
        return <ExclamationCircleOutlined style={{ color: '#faad14' }} />;
      case 'red':
        return <CloseCircleOutlined style={{ color: '#ff4d4f' }} />;
      default:
        return <InfoCircleOutlined style={{ color: '#1890ff' }} />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'green':
        return 'success';
      case 'yellow':
        return 'warning';
      case 'red':
        return 'error';
      default:
        return 'default';
    }
  };

  if (userInfoLoading) {
    return (
      <AppLayout>
        <div className={styles.loadingContainer}>
          <Spin size="large" />
        </div>
      </AppLayout>
    );
  }

  return (
    <AppLayout>
      <div className={styles.settingsContainer}>
      <div className={styles.header}>
        <Title level={2}>{t('settings.title')}</Title>
        <Text type="secondary">
          {t('settings.manageDescription', 'Manage your account settings and system information')}
        </Text>
      </div>

      <Row gutter={[24, 24]}>
        {/* Profile Settings */}
        <Col span={12}>
          <Card title={t('settings.profileSettings', 'Profile Settings')} className={styles.profileCard}>
            <div className={styles.avatarSection}>
              <Avatar
                size={80}
                icon={<UserOutlined />}
                src={userInfo?.avatar}
                className={styles.avatar}
              />
              <Upload
                showUploadList={false}
                beforeUpload={() => false}
              >
                <Button icon={<UploadOutlined />} size="small">
                  {t('settings.changeAvatar', 'Change Avatar')}
                </Button>
              </Upload>
            </div>

            <Divider />

            <Form
              form={profileForm}
              layout="vertical"
              onFinish={handleProfileSubmit}
            >
              <Form.Item
                name="nickname"
                label={t('auth.nickname')}
                rules={[{ required: true, message: t('auth.nicknameRequired') }]}
              >
                <Input
                  prefix={<UserOutlined />}
                  placeholder={t('settings.nicknamePlaceholder', 'Enter your nickname')}
                />
              </Form.Item>

              <Form.Item
                name="email"
                label={t('auth.email', 'Email')}
                rules={[
                  { required: true, message: t('settings.emailRequired', 'Please input your email!') },
                  { type: 'email', message: t('settings.emailInvalid', 'Please enter a valid email!') },
                ]}
              >
                <Input
                  prefix={<MailOutlined />}
                  placeholder={t('settings.emailPlaceholder', 'Enter your email')}
                />
              </Form.Item>

              <Form.Item>
                <Button
                  type="primary"
                  htmlType="submit"
                  icon={<SaveOutlined />}
                  loading={updatingProfile}
                  block
                >
                  {t('settings.updateProfile')}
                </Button>
              </Form.Item>
            </Form>
          </Card>
        </Col>

        {/* Password Settings */}
        <Col span={12}>
          <Card title={t('settings.passwordSettings', 'Password Settings')} className={styles.passwordCard}>
            <Form
              form={passwordForm}
              layout="vertical"
              onFinish={handlePasswordSubmit}
            >
              <Form.Item
                name="password"
                label={t('settings.currentPassword', 'Current Password')}
                rules={[{ required: true, message: t('settings.currentPasswordRequired', 'Please input your current password!') }]}
              >
                <Input.Password
                  prefix={<LockOutlined />}
                  placeholder={t('settings.currentPasswordPlaceholder', 'Enter current password')}
                />
              </Form.Item>

              <Form.Item
                name="new_password"
                label={t('settings.newPassword', 'New Password')}
                rules={[
                  { required: true, message: t('settings.newPasswordRequired', 'Please input your new password!') },
                  { min: 6, message: t('settings.passwordMinLength', 'Password must be at least 6 characters!') },
                ]}
              >
                <Input.Password
                  prefix={<LockOutlined />}
                  placeholder={t('settings.newPasswordPlaceholder', 'Enter new password')}
                />
              </Form.Item>

              <Form.Item
                name="confirm_password"
                label={t('settings.confirmPassword', 'Confirm New Password')}
                dependencies={['new_password']}
                rules={[
                  { required: true, message: t('settings.confirmPasswordRequired', 'Please confirm your new password!') },
                  ({ getFieldValue }) => ({
                    validator(_, value) {
                      if (!value || getFieldValue('new_password') === value) {
                        return Promise.resolve();
                      }
                      return Promise.reject(new Error(t('settings.passwordMismatch', 'Passwords do not match!')));
                    },
                  }),
                ]}
              >
                <Input.Password
                  prefix={<LockOutlined />}
                  placeholder={t('settings.confirmPasswordPlaceholder', 'Confirm new password')}
                />
              </Form.Item>

              <Form.Item>
                <Button
                  type="primary"
                  htmlType="submit"
                  icon={<SaveOutlined />}
                  loading={updatingPassword}
                  block
                >
                  {t('settings.updatePassword', 'Update Password')}
                </Button>
              </Form.Item>
            </Form>
          </Card>
        </Col>

        {/* System Information */}
        <Col span={24}>
          <Card title={t('settings.systemInformation', 'System Information')} className={styles.systemCard}>
            <Row gutter={[24, 24]}>
              <Col span={12}>
                <Descriptions title={t('settings.versionInformation', 'Version Information')} bordered size="small">
                  <Descriptions.Item label={t('settings.version', 'Version')} span={3}>
                    {systemVersion?.version || t('common.unknown', 'Unknown')}
                  </Descriptions.Item>
                  <Descriptions.Item label={t('settings.userId', 'User ID')} span={3}>
                    {userInfo?.id}
                  </Descriptions.Item>
                  <Descriptions.Item label={t('settings.created', 'Created')} span={3}>
                    {userInfo?.create_time}
                  </Descriptions.Item>
                  <Descriptions.Item label={t('settings.lastUpdated', 'Last Updated')} span={3}>
                    {userInfo?.update_time}
                  </Descriptions.Item>
                </Descriptions>
              </Col>

              <Col span={12}>
                <div className={styles.systemStatus}>
                  <Title level={4}>{t('settings.systemStatus', 'System Status')}</Title>
                  {systemStatusLoading ? (
                    <Spin />
                  ) : (
                    <Space direction="vertical" style={{ width: '100%' }}>
                      {systemStatus && Object.entries(systemStatus).map(([key, status]: [string, any]) => (
                        <div key={key} className={styles.statusItem}>
                          <Space>
                            <Badge
                              status={getStatusColor(status.status) as any}
                              text={
                                <Space>
                                  {getStatusIcon(status.status)}
                                  <Text strong>{key.replace('_', ' ').toUpperCase()}</Text>
                                  <Text type="secondary">({status.elapsed}ms)</Text>
                                </Space>
                              }
                            />
                          </Space>
                          {status.error && (
                            <Alert
                              message={status.error}
                              type="error"
                              size="small"
                              style={{ marginTop: 8 }}
                            />
                          )}
                        </div>
                      ))}
                    </Space>
                  )}
                </div>
              </Col>
            </Row>
          </Card>
        </Col>
      </Row>
      </div>
    </AppLayout>
  );
};

export default Settings;
