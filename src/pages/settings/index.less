.settingsContainer {
  padding: 24px;
  background: #f5f5f5;
  min-height: 100vh;

  .header {
    margin-bottom: 24px;
    
    h2 {
      margin-bottom: 8px;
    }
  }

  .loadingContainer {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 400px;
  }

  .profileCard,
  .passwordCard,
  .systemCard {
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

    .ant-card-head {
      border-bottom: 1px solid #f0f0f0;
      
      .ant-card-head-title {
        font-weight: 600;
        font-size: 16px;
      }
    }

    .ant-card-body {
      padding: 24px;
    }
  }

  .avatarSection {
    display: flex;
    flex-direction: column;
    align-items: center;
    margin-bottom: 16px;

    .avatar {
      margin-bottom: 16px;
      border: 3px solid #f0f0f0;
    }
  }

  .systemStatus {
    .statusItem {
      padding: 12px;
      border: 1px solid #f0f0f0;
      border-radius: 6px;
      margin-bottom: 8px;
      background: #fafafa;

      &:last-child {
        margin-bottom: 0;
      }

      .ant-badge {
        width: 100%;

        .ant-badge-status-text {
          width: 100%;
        }
      }
    }
  }

  .ant-form {
    .ant-form-item {
      margin-bottom: 16px;
    }

    .ant-input,
    .ant-input-password {
      border-radius: 6px;
      padding: 8px 12px;

      .ant-input-prefix {
        margin-right: 8px;
        color: #bfbfbf;
      }
    }

    .ant-btn {
      border-radius: 6px;
      height: 40px;
      font-weight: 500;
      
      &.ant-btn-primary {
        background: #1890ff;
        border-color: #1890ff;
        
        &:hover {
          background: #40a9ff;
          border-color: #40a9ff;
        }
      }
    }
  }

  .ant-descriptions {
    .ant-descriptions-title {
      font-weight: 600;
      margin-bottom: 16px;
    }

    .ant-descriptions-item-label {
      font-weight: 500;
      background: #fafafa;
    }

    .ant-descriptions-item-content {
      background: #fff;
    }
  }

  .ant-divider {
    margin: 16px 0;
  }

  .ant-upload {
    .ant-btn {
      border-radius: 4px;
      height: 32px;
      font-size: 12px;
    }
  }

  .ant-alert {
    border-radius: 4px;
    
    &.ant-alert-error {
      background: #fff2f0;
      border: 1px solid #ffccc7;
    }
  }

  .ant-space {
    &.ant-space-vertical {
      width: 100%;
    }
  }

  .ant-badge {
    .ant-badge-status-dot {
      width: 8px;
      height: 8px;
    }

    &.ant-badge-status-success .ant-badge-status-dot {
      background-color: #52c41a;
    }

    &.ant-badge-status-warning .ant-badge-status-dot {
      background-color: #faad14;
    }

    &.ant-badge-status-error .ant-badge-status-dot {
      background-color: #ff4d4f;
    }

    &.ant-badge-status-default .ant-badge-status-dot {
      background-color: #d9d9d9;
    }
  }
}

// Dark theme support
[data-theme='dark'] {
  .settingsContainer {
    background: #141414;
    color: #fff;

    .profileCard,
    .passwordCard,
    .systemCard {
      background: #1f1f1f;
      border-color: #303030;

      .ant-card-head {
        background: #1f1f1f;
        border-color: #303030;
        color: #fff;
      }

      .ant-card-body {
        background: #1f1f1f;
        color: #fff;
      }
    }

    .avatarSection {
      .avatar {
        border-color: #303030;
      }
    }

    .systemStatus {
      .statusItem {
        background: #262626;
        border-color: #303030;
        color: #fff;
      }
    }

    .ant-form {
      .ant-input,
      .ant-input-password {
        background: #262626;
        border-color: #303030;
        color: #fff;

        &:focus,
        &:hover {
          border-color: #1890ff;
        }

        .ant-input-prefix {
          color: #8c8c8c;
        }
      }
    }

    .ant-descriptions {
      .ant-descriptions-item-label {
        background: #262626;
        color: #fff;
      }

      .ant-descriptions-item-content {
        background: #1f1f1f;
        color: #fff;
      }
    }

    .ant-alert {
      &.ant-alert-error {
        background: #2a1215;
        border-color: #58181c;
        color: #fff;
      }
    }
  }
}

// Responsive design
@media (max-width: 768px) {
  .settingsContainer {
    padding: 16px;

    .ant-col {
      margin-bottom: 16px;
    }

    .profileCard,
    .passwordCard,
    .systemCard {
      .ant-card-body {
        padding: 16px;
      }
    }

    .avatarSection {
      .avatar {
        width: 60px;
        height: 60px;
      }
    }
  }
}
