#!/bin/sh

# Docker entrypoint script for HBGK frontend
# 处理环境变量替换和nginx配置

set -e

# 设置默认环境变量
export API_BASE_URL=${API_BASE_URL:-"http://localhost:9380"}
export API_KEY=${API_KEY:-"hbgk-lhYmE2YTc2M2FiYjExZjA5YTdlMDAwYz"}
export TEXT_TO_IMAGE_URL=${TEXT_TO_IMAGE_URL:-"http://*************:8090/gradio"}
export MINERU_SERVER_URL=${MINERU_SERVER_URL:-"http://*************:7860"}
export MINERU_API_KEY=${MINERU_API_KEY:-""}

echo "=== HBGK Frontend Docker Container Starting ==="
echo "API_BASE_URL: $API_BASE_URL"
echo "API_KEY: ${API_KEY:0:20}..."
echo "TEXT_TO_IMAGE_URL: $TEXT_TO_IMAGE_URL"
echo "MINERU_SERVER_URL: $MINERU_SERVER_URL"

# 使用envsubst替换nginx配置模板中的环境变量
echo "Generating nginx configuration from template..."
envsubst '${API_BASE_URL} ${TEXT_TO_IMAGE_URL}' < /etc/nginx/templates/default.conf.template > /etc/nginx/conf.d/default.conf

# 验证nginx配置
echo "Testing nginx configuration..."
nginx -t

echo "=== Starting nginx ==="

# 执行传入的命令
exec "$@"
