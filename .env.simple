# 汉邦高科 简化部署环境变量配置文件
# 用于简化部署模式，连接外部数据库服务

# ===========================================
# 服务端口配置
# ===========================================
# 前端服务端口
FRONTEND_PORT=3000

# 后端API服务端口
BACKEND_PORT=9380

# ===========================================
# API配置
# ===========================================
# API密钥
API_KEY=hbgk-lhYmE2YTc2M2FiYjExZjA5YTdlMDAwYz

# 文生图服务URL
TEXT_TO_IMAGE_URL=http://localhost:8090/gradio

# ===========================================
# AI Reading - Mineru服务配置
# ===========================================
# Mineru服务器地址 (用于PDF文档解析)
MINERU_SERVER_URL=http://*************:7860
# Mineru API密钥 (如果需要)
MINERU_API_KEY=

# ===========================================
# 外部数据库配置 (MySQL)
# ===========================================
# 使用宿主机上的MySQL服务
MYSQL_HOST=host.docker.internal
MYSQL_PORT=5455
MYSQL_USER=root
MYSQL_PASSWORD=infini_rag_flow
MYSQL_DBNAME=rag_flow

# ===========================================
# 外部Redis配置
# ===========================================
# 使用宿主机上的Redis服务
REDIS_HOST=host.docker.internal
REDIS_PORT=6379
REDIS_PASSWORD=infini_rag_flow

# ===========================================
# 外部MinIO对象存储配置
# ===========================================
# 使用宿主机上的MinIO服务
MINIO_HOST=host.docker.internal
MINIO_PORT=9000
MINIO_CONSOLE_PORT=9001
MINIO_USER=rag_flow
MINIO_PASSWORD=infini_rag_flow

# ===========================================
# 外部Elasticsearch配置
# ===========================================
# 使用宿主机上的Elasticsearch服务
ES_HOST=127.0.0.1
ES_PORT=9200
ES_USER=elastic
ELASTIC_PASSWORD=infini_rag_flow

# ===========================================
# 部署模式标识
# ===========================================
DEPLOYMENT_MODE=simple

# ===========================================
# 说明
# ===========================================
# 此配置文件用于简化部署模式
# 
# 前提条件：
# 1. 宿主机上已安装并运行MySQL
# 2. 宿主机上已安装并运行Redis
# 3. 宿主机上已安装并运行MinIO
# 4. 宿主机上已安装并运行Elasticsearch
#
# 如果您的外部服务不在宿主机上，请修改对应的HOST配置
# 例如：
# MYSQL_HOST=*************
# REDIS_HOST=*************
# ES_HOST=*************
# MINIO_HOST=*************
