declare module '*.css';
declare module '*.less';
declare module '*.scss';
declare module '*.sass';
declare module '*.svg';
declare module '*.png';
declare module '*.jpg';
declare module '*.jpeg';
declare module '*.gif';
declare module '*.bmp';
declare module '*.tiff';

declare namespace NodeJS {
  interface ProcessEnv {
    NODE_ENV: 'development' | 'production' | 'test';
    API_BASE_URL: string;
    API_KEY: string;
    PORT?: string;
  }
}

declare module 'jsencrypt' {
  export default class JSEncrypt {
    setPublicKey(key: string): void;
    setPrivateKey(key: string): void;
    encrypt(text: string): string | false;
    decrypt(text: string): string | false;
  }
}

declare module 'js-base64' {
  export const Base64: {
    encode(str: string): string;
    decode(str: string): string;
  };
}
